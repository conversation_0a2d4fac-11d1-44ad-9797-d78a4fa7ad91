{"version": "1.0.0", "security_policies": {"network": {"allowed_hosts": ["api.compound-beta.example.com", "api.allam-models.example.com"], "allowed_ports": [443, 8000, 5432, 9090, 3000], "tls_min_version": "1.3", "require_certificate_validation": true}, "api": {"rate_limits": {"default": {"requests_per_minute": 100, "requests_per_hour": 1000}, "analyze": {"requests_per_minute": 20, "requests_per_hour": 200}, "predict": {"requests_per_minute": 10, "requests_per_hour": 100}}, "require_api_key": true, "key_rotation_days": 30}, "data": {"input_validation": {"max_text_length": 4096, "allowed_content_types": ["text/plain", "application/json"], "sanitize_inputs": true}, "output_validation": {"max_response_size_kb": 1024, "sanitize_outputs": true}}, "model": {"max_memory_mb": 500, "max_batch_size": 32, "max_sequence_length": 512, "allow_external_weights": false}, "monitoring": {"log_level": "INFO", "log_requests": true, "log_responses": false, "metrics_collection_interval_seconds": 10, "alert_on_memory_threshold_percent": 90, "alert_on_error_rate_percent": 5}}}