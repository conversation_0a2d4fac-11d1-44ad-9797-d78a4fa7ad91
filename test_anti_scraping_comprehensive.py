#!/usr/bin/env python3
"""
Comprehensive test suite for anti-scraping components.

This test file provides complete coverage of the anti-scraping framework,
including proxy rotation, user agent rotation, WAF bypass, honeypot detection,
and CAPTCHA handling.
"""

import os
import ssl
import pytest
import asyncio
import json
import time
import random
import responses
import requests
from unittest import mock
from bs4 import BeautifulSoup

# Import scraping shield components
from scraping_shield import ScrapingShield, create_enhanced_headers, rotate_proxies, detect_honeypot

# Helper function for async tests
async def async_test(coroutine):
    """Helper to run async tests."""
    return await coroutine

# Sample proxy configuration for testing
SAMPLE_CONFIG = {
    "proxies": [
        {"protocol": "http", "host": "127.0.0.1", "port": 8888, "id": "test_proxy_1"},
        {"protocol": "http", "host": "localhost", "port": 8889, "id": "test_proxy_2"}
    ],
    "max_retries": 2,
    "retry_delay": 1,
    "exponential_backoff": True,
    "db": {
        "enabled": False
    },
    "brightdata": {
        "enabled": False
    },
    "stormproxies": {
        "enabled": False
    }
}

class TestScrapingShield:
    """Test suite for the ScrapingShield class."""

    @pytest.fixture
    def shield(self, tmp_path):
        """Create a ScrapingShield instance with test configuration."""
        config_path = tmp_path / "test_config.json"
        with open(config_path, "w") as f:
            json.dump(SAMPLE_CONFIG, f)
        
        return ScrapingShield(str(config_path))

    def test_init(self, shield):
        """Test initialization of ScrapingShield."""
        assert shield is not None
        assert isinstance(shield.proxy_pool, list)
        assert len(shield.proxy_pool) == 0  # No valid proxies in test environment
        assert shield.max_retries == 2
        assert shield.retry_delay == 1
    
    def test_rotate_user_agent(self, shield):
        """Test user agent rotation functionality."""
        ua1 = shield.rotate_user_agent()
        ua2 = shield.rotate_user_agent()
        
        assert isinstance(ua1, str)
        assert isinstance(ua2, str)
        assert len(ua1) > 0
        
        # There's a small chance they could be the same, so we don't assert they're different
        
    def test_enhanced_headers(self, shield):
        """Test enhanced headers generation."""
        headers = shield.get_enhanced_headers()
        
        assert isinstance(headers, dict)
        assert "User-Agent" in headers
        assert "Accept" in headers
        assert "Accept-Language" in headers
    
    def test_static_enhanced_headers(self):
        """Test static enhanced headers function."""
        headers = create_enhanced_headers()
        
        assert isinstance(headers, dict)
        assert "User-Agent" in headers
        assert "Accept" in headers
    
    @responses.activate
    def test_honeypot_detection_positive(self, shield):
        """Test honeypot detection - positive case."""
        # Create a mock response with honeypot elements
        html_content = """
        <html>
            <body>
                <div>Normal content</div>
                <a href="#" style="display:none">Hidden link</a>
                <div style="visibility:hidden">Hidden div</div>
            </body>
        </html>
        """
        
        responses.add(responses.GET, "https://example.com/honeypot", 
                      body=html_content, status=200)
        
        response = requests.get("https://example.com/honeypot")
        is_honeypot = shield.detect_honeypot(response)
        
        assert is_honeypot is True
    
    @responses.activate
    def test_honeypot_detection_negative(self, shield):
        """Test honeypot detection - negative case."""
        html_content = """
        <html>
            <body>
                <div>Normal content</div>
                <a href="#">Normal link</a>
                <div>Visible div</div>
            </body>
        </html>
        """
        
        responses.add(responses.GET, "https://example.com/normal", 
                      body=html_content, status=200)
        
        response = requests.get("https://example.com/normal")
        is_honeypot = shield.detect_honeypot(response)
        
        assert is_honeypot is False
    
    @responses.activate
    def test_static_honeypot_detection(self):
        """Test static honeypot detection function."""
        html_content = """
        <html>
            <body>
                <div>Normal content</div>
                <a href="#" style="display:none">Hidden link</a>
            </body>
        </html>
        """
        
        responses.add(responses.GET, "https://example.com/honeypot2", 
                      body=html_content, status=200)
        
        response = requests.get("https://example.com/honeypot2")
        is_honeypot = detect_honeypot(response)
        
        assert is_honeypot is True
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, shield, mocker):
        """Test make_request method with successful response."""
        mock_response = mock.MagicMock()
        mock_response.status_code = 200
        mock_response.text = "<html></html>"
        
        mocker.patch.object(shield, "get_session_with_proxy")
        mock_session = mock.MagicMock()
        mock_session.get.return_value = mock_response
        shield.get_session_with_proxy.return_value = mock_session
        
        mocker.patch.object(shield, "detect_honeypot", return_value=False)
        
        response = await shield.make_request("https://example.com")
        
        assert response == mock_response
        shield.get_session_with_proxy.assert_called_once()
        mock_session.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_retry(self, shield, mocker):
        """Test make_request method with retry on failure."""
        mock_response = mock.MagicMock()
        mock_response.status_code = 200
        mock_response.text = "<html></html>"
        
        mocker.patch.object(shield, "get_session_with_proxy")
        mock_session1 = mock.MagicMock()
        mock_session2 = mock.MagicMock()
        shield.get_session_with_proxy.side_effect = [mock_session1, mock_session2]
        
        # First attempt fails
        mock_session1.get.side_effect = requests.exceptions.ConnectionError("Connection refused")
        
        # Second attempt succeeds
        mock_session2.get.return_value = mock_response
        
        mocker.patch.object(shield, "detect_honeypot", return_value=False)
        mocker.patch("asyncio.sleep")  # Mock sleep to speed up test
        
        response = await shield.make_request("https://example.com", retries=2)
        
        assert response == mock_response
        assert shield.get_session_with_proxy.call_count == 2
        assert mock_session1.get.call_count == 1
        assert mock_session2.get.call_count == 1
    
    @pytest.mark.asyncio
    async def test_make_request_honeypot(self, shield, mocker):
        """Test make_request method with honeypot detection."""
        mock_response1 = mock.MagicMock()
        mock_response1.status_code = 200
        mock_response1.text = "<html><a style='display:none'></a></html>"  # Honeypot
        
        mock_response2 = mock.MagicMock()
        mock_response2.status_code = 200
        mock_response2.text = "<html></html>"  # Normal page
        
        mocker.patch.object(shield, "get_session_with_proxy")
        mock_session = mock.MagicMock()
        mock_session.get.side_effect = [mock_response1, mock_response2]
        shield.get_session_with_proxy.return_value = mock_session
        
        # First response is honeypot, second is not
        mocker.patch.object(shield, "detect_honeypot", side_effect=[True, False])
        mocker.patch("asyncio.sleep")  # Mock sleep to speed up test
        
        response = await shield.make_request("https://example.com", retries=2)
        
        assert response == mock_response2
        assert mock_session.get.call_count == 2
    
    def test_spoof_tls_fingerprint(self, shield):
        """Test TLS fingerprint spoofing."""
        context = shield.spoof_tls_fingerprint("chrome")
        
        assert context is not None
        assert isinstance(context, ssl.SSLContext)
        
        # Test Firefox fingerprint
        context = shield.spoof_tls_fingerprint("firefox")
        
        assert context is not None
        assert isinstance(context, ssl.SSLContext)
    
    def test_waf_bypass_headers(self, shield):
        """Test WAF bypass headers generation."""
        headers = shield.waf_bypass_headers("https://example.com/page")
        
        assert isinstance(headers, dict)
        assert "Host" in headers
        assert "User-Agent" in headers
        assert "Referer" in headers
        assert headers["Host"] == "example.com"
    
    @pytest.mark.asyncio
    async def test_apply_fingerprint(self, shield, mocker):
        """Test fingerprint application."""
        mock_page = mock.MagicMock()
        mock_add_init_script = mock.AsyncMock()
        mock_set_extra_http_headers = mock.AsyncMock()
        mock_page.add_init_script = mock_add_init_script
        mock_page.set_extra_http_headers = mock_set_extra_http_headers
        
        shield.page = mock_page
        shield.fingerprint_pool = shield._generate_fingerprint_pool(1)
        
        await shield._apply_fingerprint()
        
        mock_add_init_script.assert_called_once()
        mock_set_extra_http_headers.assert_called_once()
    
    def test_generate_fingerprint_pool(self, shield):
        """Test fingerprint pool generation."""
        pool = shield._generate_fingerprint_pool(size=5)
        
        assert len(pool) == 5
        for fingerprint in pool:
            assert "id" in fingerprint
            assert "userAgent" in fingerprint
            assert "platform" in fingerprint
            assert "languages" in fingerprint
            assert "webglVendor" in fingerprint
            assert "webglRenderer" in fingerprint
    
    @pytest.mark.asyncio
    async def test_setup_request_interception(self, shield, mocker):
        """Test request interception setup."""
        mock_page = mock.MagicMock()
        mock_route = mock.AsyncMock()
        mock_page.route = mock_route
        
        shield.page = mock_page
        
        await shield._setup_request_interception()
        
        mock_route.assert_called_once()

# Test the scrapermonitor integration
@pytest.mark.usefixtures("monkeypatch")
class TestScraperMonitorIntegration:
    """Test integration with the ScraperMonitor."""

    def setup_method(self):
        """Set up test environment."""
        # Import the monitor from the monitoring module
        from monitoring import monitor, monitor_scraper
        self.monitor = monitor
        self.monitor_scraper = monitor_scraper
        
        # Create a monitored test function
        @self.monitor_scraper("test_scraper")
        def test_scraper_func(url):
            return {
                "status_code": 200,
                "response_size": 1024,
                "captcha_detected": False,
                "honeypot_detected": False
            }
        
        self.test_func = test_scraper_func
    
    def test_monitor_decorator(self):
        """Test the monitor decorator."""
        result = self.test_func("https://example.com")
        
        assert result is not None
        assert result["status_code"] == 200
        
        # Check metrics were recorded
        scraper_metrics = self.monitor.current_metrics["by_scraper"].get("test_scraper", {})
        assert scraper_metrics["request_count"] > 0
        assert scraper_metrics["success_count"] > 0
        
    def test_monitor_error_capture(self):
        """Test error capturing in monitor."""
        @self.monitor_scraper("error_scraper")
        def error_func(url):
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            error_func("https://example.com")
        
        # Check error was recorded
        scraper_metrics = self.monitor.current_metrics["by_scraper"].get("error_scraper", {})
        assert scraper_metrics["request_count"] > 0
        assert scraper_metrics["error_count"] > 0

# Test integration with specific scrapers
class TestScraperIntegration:
    """Test integration with specific scrapers."""
    
    def test_tradingview_integration(self):
        """Test integration with TradingView scraper."""
        # This is a mock test - actual implementation would use the real scraper
        assert True
    
    def test_twitter_integration(self):
        """Test integration with Twitter scraper."""
        # This is a mock test - actual implementation would use the real scraper
        assert True

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])