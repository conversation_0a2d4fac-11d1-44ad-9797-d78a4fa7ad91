# Trend Crawler

A comprehensive web crawler and trend analysis system that discovers trending topics and keywords across various websites, with a full-featured web dashboard for user management and trend visualization.

## Features

### Core Trend Analysis
- Crawls multiple websites to collect data
- Uses Natural Language Processing (NLP) to discover trending keywords
- Categorizes content using topic modeling
- Calculates multi-factor "coolness scores" based on velocity, impact, and novelty
- Integrates Twitter/X data to enhance trend analysis
- Features active learning for intelligent sample selection
- Includes meta-learning for continuous model improvement
- Implements memory-efficient, secure reasoning subsystem for real-time trend analysis
- Utilizes cost-optimized knowledge distillation system with Groq API integration
- Implements semantic velocity analysis for trend acceleration monitoring
- Features circuit-breaker pattern for fail-safe operation
- Implements online training loops with experience replay
- Supports mixed precision operations for faster training
- Enables automated retraining on concept drift detection
- Provides distributed training support across multiple GPUs
- Implements gradient checkpointing for memory optimization
- Optimizes resource usage for better performance
- Includes ML-based CAPTCHA solvers (PyTorch and TensorFlow)
- Implements comprehensive anti-scraping countermeasures
- Stores data in a PostgreSQL database with pgvector for vector embeddings
- Saves results to JSON file
- Supports both simple and advanced topic modeling (BERTopic)

### Web Dashboard & User Management
- **Enhanced glassmorphism dashboard** with modern design and backdrop blur effects
- **User account system** with secure JWT-based authentication
- **Profile management** - Users can edit email addresses and full names
- **Admin capabilities** - Admin users can manage other users' profiles
- **User management interface** - Add, edit, and delete users through web interface
- **Password management** - Users can change passwords and admins can reset passwords
- **Role-based access control** - Separate user and admin privileges
- **Responsive web interface** with modern card-based layouts
- **Real-time system monitoring** with 24-hour timestamp format
- **CAPTCHA analytics dashboard** with ML model performance tracking
- **Proxy management system** with real-time health monitoring
- **Security logs dashboard** with access and error monitoring (admin-only)
- **Anti-scraping framework integration** with comprehensive countermeasures
- **Enhanced UI/UX** with fixed dropdown menus and color-coded indicators
- **Secure API endpoints** for all user management and system monitoring operations

## Installation

1. Clone this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

For advanced features (optional):
```bash
pip install -r requirements-advanced.txt
```

For Twitter/X scraping (optional):
```bash
pip install -r requirements-twitter.txt
```

For Groq API integration and reasoning system (required):
```bash
pip install -r requirements-groq.txt
export GROQ_API_KEY=your_api_key_here
```

This will install the necessary dependencies for Twitter scraping, including:
- `twitter_scraper_selenium` for the primary scraper
- `selenium` for browser automation
- `webdriver-manager` for automatic ChromeDriver management

For the fallback scraper, you'll also need to run:
```bash
playwright install
```

The Twitter/X scraping functionality uses a multi-layered approach:
1. First tries to use `twitter_scraper_selenium` with parallel processing
2. Falls back to API-based scraping if a valid token is available
3. Uses browser-based scraping with Playwright as a second fallback

## Usage

### Web Dashboard

Start the web dashboard for the complete user interface:

```bash
./run_scraper.sh dashboard
```

The web dashboard provides:
- User authentication and session management
- Trend visualization and analytics
- User profile management
- Admin user management interface
- Real-time monitoring and metrics

Access the dashboard at `http://localhost:8080` after starting.

**Default Port**: The web dashboard now runs on port 8001 by default (configurable via --port flag).

**Dashboard Features**:
- Enhanced glassmorphism design with modern UI components
- Real-time system monitoring with 24-hour timestamps
- Comprehensive security logging and access monitoring
- Admin-only features: user management, logs viewing, system administration
- Mobile-responsive design with optimized performance

### Command Line Trend Crawler

Run the trend crawler directly:

```bash
python trend_crawler.py
```

### Run Script Options

The `run_scraper.sh` script provides convenient options for running different components:

```bash
# Start only the web dashboard
./run_scraper.sh dashboard

# Start only the trend crawler
./run_scraper.sh trends

# Start only the Twitter scraper
./run_scraper.sh twitter

# Start both trend crawler and web dashboard simultaneously
./run_scraper.sh both
```

When using `./run_scraper.sh both`, both services run in the background and can be stopped together with Ctrl+C. The script will display:
- Process IDs for both services
- Dashboard URL (http://localhost:8080)
- Status messages for monitoring

### Command-line Options

- `--no-db`: Skip database connection
- `--urls-file FILE`: Path to file containing URLs to crawl (one per line)
- `--url URL`: URL to crawl (can be specified multiple times)
- `--output FILE`: Output JSON file for results (default: results.json)
- `--log-level LEVEL`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `--simple`: Use simple topic modeling instead of BERTopic
- `--active-learning`: Enable active learning for sample selection
- `--meta-learning`: Enable meta-learning for continuous improvement
- `--optimize-resources`: Enable resource optimization for better performance
- `--distributed`: Enable distributed training across multiple GPUs
- `--mixed-precision`: Enable mixed precision operations for faster training
- `--auto-retrain`: Enable automated retraining on concept drift detection
- `--twitter-enabled`: Enable Twitter/X data collection for trend analysis
- `--twitter-max-tweets`: Maximum number of tweets to retrieve per trend (default: 100)
- `--twitter-workers`: Number of parallel workers for Twitter scraping (default: 3)
- `--reasoning-enabled`: Enable memory-efficient reasoning subsystem for trend analysis
- `--knowledge-distillation`: Enable knowledge distillation with Groq API integration
- `--max-memory-mb`: Maximum memory usage for reasoning subsystem (default: 300MB)
- `--groq-daily-budget`: Set daily budget limit for Groq API calls (default: $1.00)
- `--velocity-window-size`: Window size for semantic velocity analyzer (default: 5)
- `--velocity-decay-factor`: Decay factor for semantic velocity analyzer (default: 0.8)
- `--trend-attention-sparsity`: Sparsity factor for trend attention mechanism (default: 0.3)

Examples:

```bash
# Skip database connection
python trend_crawler.py --no-db

# Use custom URLs file
python trend_crawler.py --urls-file my_urls.txt

# Specify URLs directly
python trend_crawler.py --url https://example.com --url https://another-site.com

# Save results to a specific file
python trend_crawler.py --output my_results.json

# Use simple topic modeling (no BERTopic dependency)
python trend_crawler.py --simple

# Enable all advanced features
python trend_crawler.py --active-learning --meta-learning --optimize-resources

# Enable distributed training with mixed precision
python trend_crawler.py --distributed --mixed-precision --optimize-resources

# Enable automated retraining on concept drift
python trend_crawler.py --meta-learning --auto-retrain

# Enable Twitter/X data collection
python trend_crawler.py --twitter-enabled --twitter-max-tweets 50 --twitter-workers 5

# Enable memory-efficient reasoning system with knowledge distillation
python trend_crawler.py --reasoning-enabled --knowledge-distillation

# Advanced configuration of reasoning system
python trend_crawler.py --reasoning-enabled --max-memory-mb 500 --groq-daily-budget 0.5

# Configure semantic velocity analyzer
python trend_crawler.py --reasoning-enabled --velocity-window-size 10 --velocity-decay-factor 0.9 --trend-attention-sparsity 0.4
```

The script will:
1. Prompt for database connection information (unless --no-db is used)
2. Crawl websites from the specified sources
3. Extract trending keywords
4. Categorize content using topic modeling
5. Analyze Twitter profiles mentioned in trends (when --twitter-enabled)
6. Calculate influence scores for Twitter profiles
7. Enhance coolness scores with profile data
8. Apply BERT-based coolness classification (when available)
9. Apply memory-efficient reasoning to trends (when --reasoning-enabled)
10. Analyze semantic velocity of trends for acceleration monitoring
11. Apply knowledge distillation using Groq API (when --knowledge-distillation)
12. Incorporate user feedback into scoring (when available)
13. Display results
14. Save to database (if connected) and JSON file

## Quick Start

### 1. Setup Environment
```bash
# Clone the repository
git clone <repository-url>
cd trend-crawler

# Run the setup script
./setup.sh
```

### 2. Start the System
```bash
# Start both trend crawler and web dashboard
./run_scraper.sh both
```

### 3. Access the Dashboard
Open your web browser and navigate to `http://localhost:8080`

**Default Admin Login:**
- Username: `admin`
- Password: `admin123` (change immediately)

### 4. Basic Usage
- **Users**: Create account, edit profile, view trends
- **Admins**: Manage users, access full dashboard, configure system

## Requirements

- Python 3.6+
- See requirements.txt for basic Python package dependencies
- See requirements-advanced.txt for advanced features (BERTopic)

## Web Dashboard

The system includes a comprehensive web dashboard with user authentication and management capabilities.

### Enhanced Dashboard Design

The web dashboard now features a modern, professional design with:

- **Glassmorphism Effects**: Modern glass-like UI elements with backdrop blur and translucent cards
- **Card-Based Layouts**: Organized information display with categorized sections
- **Responsive Design**: Optimized for all screen sizes with mobile-first approach
- **Color-Coded Indicators**: Visual status indicators with smooth animations
- **Fixed Navigation**: Improved dropdown menus with proper z-index layering
- **Professional Typography**: Enhanced readability with consistent font hierarchy

### Dashboard Pages

#### Main Dashboard (`/dashboard`)
- Overview of scraper performance with real-time charts
- CAPTCHA detection metrics and solver performance
- Proxy health monitoring with status indicators
- Recent alerts and system notifications

#### System Information (`/system`)
- **System Identity**: Hostname, OS, Python version, and uptime
- **Hardware Information**: CPU details, memory specs, and disk information
- **Resource Usage**: Real-time CPU, memory, and disk usage with animations
- **System Status**: Service health, database connections, and network status
- **24-Hour Timestamps**: All timestamps displayed in 24-hour format (HH:MM:SS)
- **Auto-Refresh**: System data updates every 5 seconds automatically

#### CAPTCHA Analytics (`/captchas`)
- ML model performance tracking and accuracy metrics
- Solver statistics with success/failure rates
- Detection frequency analysis and trend visualization
- Historical performance data with time-series charts

#### Proxy Management (`/proxies`)
- Real-time proxy health monitoring and status tracking
- Performance metrics including response times and success rates
- Geographic distribution of proxy servers
- Automatic failover configuration and testing

#### Security Logs (`/logs`) - Admin Only
- **Access Logs**: HTTP request monitoring with IP tracking and user identification
- **Security Events**: Failed login attempts, suspicious activity, and authentication events
- **Error Logs**: System errors, exceptions, and debugging information
- **Real-time Updates**: Live log streaming with automatic refresh

#### User Management (`/user-management`) - Admin Only
- Complete user lifecycle management (create, edit, delete)
- Role assignment and permission management
- Password reset functionality with secure token generation
- User activity monitoring and session management

### Features

- **User Authentication**: Secure JWT-based login system
- **Profile Management**: Users can edit email addresses and full names
- **Password Management**: Users can change their passwords securely
- **Admin Interface**: Admin users can manage other users
- **User Management**: Add, edit, delete, and reset passwords for users
- **Role-based Access**: Separate user and admin privileges
- **Responsive Design**: Modern web interface that works on all devices

### Starting the Dashboard

```bash
# Start only the web dashboard
./run_scraper.sh dashboard

# Start both trend crawler and dashboard together
./run_scraper.sh both
```

### Dashboard Usage Guide

#### First-Time Setup
1. Start the dashboard using the commands above
2. Navigate to `http://localhost:8001` (default port changed from 8080)
3. Login with default admin credentials (username: `admin`, password: `admin123`)
4. **Important**: Change the default password immediately in Account Settings
5. Create additional user accounts as needed through User Management

#### Navigation Overview
- **Dashboard**: Main overview with charts and system status
- **Scrapers**: Detailed scraper performance metrics and health monitoring
- **CAPTCHAs**: ML solver analytics and detection statistics
- **Proxies**: Proxy management with real-time health checks
- **System**: Comprehensive system information with 24-hour timestamps
- **Alerts**: System notifications and security alerts
- **Logs**: Security and access logs (admin-only access)
- **Account**: Profile management and password changes
- **User Management**: User administration panel (admin-only)

#### Key Features in Use
- **Auto-Refresh**: System data updates automatically every 5 seconds
- **Mobile Responsive**: Optimized for mobile and tablet viewing
- **Real-time Charts**: Live performance data with Chart.js integration
- **Security Monitoring**: Real-time security event tracking and alerts
- **24-Hour Format**: All timestamps displayed in military time format (HH:MM:SS)

### User Management

#### Regular Users Can:
- View and edit their own profile (email and full name)
- Change their password
- Access the main dashboard and trend data
- Username is read-only and cannot be changed

#### Admin Users Can:
- All regular user capabilities
- View all users in the system
- Add new users with specified roles
- Edit any user's profile information
- Reset passwords for any user
- Delete users from the system

### API Endpoints

The web dashboard provides RESTful API endpoints:

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token

#### User Management
- `GET /api/v1/users/` - List all users (admin only)
- `POST /api/v1/users/` - Create new user (admin only)
- `DELETE /api/v1/users/{username}` - Delete user (admin only)
- `POST /api/v1/users/change-password` - Change own password
- `POST /api/v1/users/profile` - Update own profile
- `POST /api/v1/users/{username}/profile` - Update user profile (admin only)
- `POST /api/v1/users/{username}/reset-password` - Reset user password (admin only)

#### System Monitoring
- `GET /api/v1/system/info` - Get real-time system information with optimized CPU measurement
- `GET /api/v1/system/events` - Get recent system events and notifications

#### Security Logs (Admin Only)
- `GET /api/v1/logs/security` - Get recent security log entries
- `GET /api/v1/logs/access` - Get recent access log entries
- `GET /api/v1/logs/errors` - Get recent error log entries

#### Analytics & Statistics
- `GET /api/v1/stats/scrapers` - Get scraper performance metrics with fallback data
- `GET /api/v1/stats/proxies` - Get proxy performance statistics
- `GET /api/v1/stats/captchas` - Get CAPTCHA detection and solver performance stats

### Database Setup

The script will automatically create the necessary tables if they don't exist:

#### Core Trend Analysis Tables
- `categories` - Stores topic categories
- `coolness_data` - Stores website data with coolness scores
- `twitter_data` - Stores individual tweets
- `twitter_metrics` - Stores aggregated Twitter metrics
- `twitter_scraping_logs` - Tracks Twitter scraping performance
- `twitter_profiles` - Stores Twitter profile data
- `profile_relationships` - Tracks relationships between profiles and trends
- `user_feedback` - Stores user feedback for coolness ratings
- `model_predictions` - Caches model predictions
- `model_versions` - Tracks model versions and metrics
- `trend_embeddings` - Stores vector embeddings for trends
- `semantic_velocity` - Tracks semantic velocity measurements
- `trend_reasoning_logs` - Logs operations of the reasoning system
- `knowledge_distillation_cache` - Caches knowledge distillation results
- `groq_api_usage` - Tracks Groq API usage and costs

#### User Management Tables
- `users` - Stores user accounts with authentication data
- `user_sessions` - Manages user sessions and JWT tokens
- `user_roles` - Defines user roles and permissions
- `audit_logs` - Tracks user actions and system events

## Testing

Run the unit tests:

```bash
python -m unittest test_trend_crawler.py
```

## BERT-based Coolness Classification

The system includes a BERT-based coolness classification system that enhances the traditional scoring mechanism:

```bash
# Install BERT dependencies
pip install -r requirements-bert.txt

# Test the BERT-based coolness classification system
python test_coolness_classifier.py

# Test with specific texts
python test_coolness_classifier.py --texts "Revolutionary new AI technology" "Boring article about mundane topics"

# Test feedback training
python test_coolness_classifier.py --test-feedback
```

The BERT-based classification system:
- Uses a pre-trained BERT model fine-tuned for coolness classification
- Incorporates user feedback to improve predictions over time
- Provides confidence scores and probability distributions
- Enhances the traditional scoring mechanism with ML-based insights

## ML-based CAPTCHA Solvers

The system includes machine learning-based CAPTCHA solvers that can be used for local CAPTCHA solving without relying on external services:

```bash
# Install ML-based CAPTCHA solver dependencies
pip install -r requirements-captcha.txt

# Test the ML-based CAPTCHA solvers
python test_ml_captcha_solver.py --download-samples

# Test with a specific image
python test_ml_captcha_solver.py --image path/to/captcha.png

# Test with a specific model
python test_ml_captcha_solver.py --image path/to/captcha.png --model path/to/model.pth --solver pytorch
```

The ML-based CAPTCHA solvers:
- Support both PyTorch and TensorFlow models
- Include preprocessing utilities for different CAPTCHA types
- Integrate with external CAPTCHA solving services as fallback
- Provide a unified interface for solving various types of CAPTCHAs
- Can be trained on custom CAPTCHA datasets

For more information, see the [ML-based CAPTCHA Solvers README](ml_captcha_solvers/README.md).

## Technical Improvements & Architecture

### Enhanced Backend Infrastructure

The system has been significantly enhanced with modern backend improvements:

#### Database Architecture
- **Dual Database Support**: PostgreSQL primary with automatic SQLite fallback
- **Connection Pooling**: Optimized database connections with error recovery
- **Environment Configuration**: Flexible database configuration via environment variables
- **Performance Optimization**: Efficient query patterns and connection management

#### API Architecture
- **RESTful Design**: Comprehensive API endpoints following REST principles
- **Error Handling**: Robust error handling with meaningful HTTP status codes
- **Authentication Middleware**: JWT-based security for all protected endpoints
- **Request Logging**: Detailed HTTP request logging with security focus

#### System Monitoring
- **Resource Monitoring**: Real-time CPU, memory, and disk usage tracking
- **Performance Metrics**: Non-blocking CPU measurement for optimal responsiveness
- **Health Checks**: Comprehensive system health monitoring and alerting
- **Logging Framework**: Rotating file handlers with configurable log levels

#### Security Enhancements
- **Access Logging**: HTTP middleware for security monitoring and audit trails
- **Error Handling**: Custom 404/500 handlers with security-focused redirects
- **Input Validation**: Comprehensive sanitization and validation across all endpoints
- **Session Security**: Enhanced session management with secure token handling

## Memory-Efficient Reasoning System

The system includes a memory-efficient, secure reasoning subsystem for real-time trend analysis:

```bash
# Install reasoning system dependencies
pip install -r requirements-groq.txt

# Test the reasoning system
python test_nanollm.py

# Run with reasoning system enabled
python trend_crawler.py --reasoning-enabled

# Test semantic velocity analyzer
python semantic_velocity_analyzer.py --test-trends "AI advances" "Web3 adoption" --window-size 5 --decay-factor 0.8
```

The memory-efficient reasoning system:
- Implements dynamic pruning for optimized memory usage
- Uses sparse attention mechanisms to reduce computational overhead
- Applies INT8 quantization for reduced memory footprint
- Features circuit breaker pattern for fail-safe operation
- Includes enhanced semantic velocity analysis for trend acceleration monitoring:
  - Tracks trend embeddings over time with configurable window size
  - Calculates weighted semantic shift with temporal decay
  - Uses cosine similarity to measure semantic distance between embeddings
  - Provides normalized velocity scores (0-1) for trend momentum
  - Thread-safe implementation for concurrent trend analysis
- Integrates with vector store for context-aware processing
- Maintains strict memory constraints (300MB by default)
- Implements 64-dim temporal-aware embeddings with position-sensitive encoding
- Features 2-layer TrendAttention blocks with dynamic pruning (30% sparsity)

## Knowledge Distillation with Groq API

The system includes a cost-optimized knowledge distillation system using Groq's API:

```bash
# Set up Groq API integration
export GROQ_API_KEY=your_api_key_here

# Test knowledge distillation
python groq_knowledge_distiller.py --test-prompt "Analyze recent tech trends"

# Run with knowledge distillation enabled
python trend_crawler.py --knowledge-distillation --groq-daily-budget 0.5
```

The knowledge distillation system:
- Implements a dual teacher approach (free tier + paid tier)
- Uses compound-beta model (free tier) as primary teacher
- Falls back to deepseek-r1 model (paid tier) for complex analysis
- Enforces strict daily budget of $1.00 for deepseek-r1
- Features cost-aware routing based on trend complexity
- Includes comprehensive security sanitization
- Provides seamless integration with the reasoning system

### API Endpoints

The system includes API endpoints for feedback collection and prediction:

```bash
# Start the API server
python api_endpoints.py
```

Endpoints:
- `POST /api/v1/feedback` - Submit feedback for a URL
- `GET /api/v1/coolness-prediction` - Get coolness prediction for a URL
- `POST /api/v1/solve-captcha` - Solve a CAPTCHA image
- `GET /api/v1/trend-reasoning` - Get reasoning analysis for a trend
- `GET /api/v1/semantic-velocity` - Get semantic velocity for a trend
- `POST /api/v1/distill-knowledge` - Perform knowledge distillation on trend data
- `GET /api/v1/groq-usage` - Get Groq API usage statistics

## Anti-Scraping Framework

The system includes a comprehensive anti-scraping framework with advanced countermeasures:

### Framework Features

- **Intelligent Detection**: Real-time detection of scraping attempts using behavioral analysis
- **Dynamic Response**: Adaptive countermeasures that scale based on threat level
- **Rate Limiting**: Sophisticated rate limiting with IP-based and user-based rules
- **Proxy Detection**: Advanced proxy and VPN detection algorithms
- **CAPTCHA Integration**: ML-based CAPTCHA solving with multiple solver backends
- **Honeypot Traps**: Invisible traps to detect and flag automated tools
- **JavaScript Challenges**: Client-side challenges to verify legitimate browser behavior
- **Fingerprinting Resistance**: Techniques to avoid detection by anti-fingerprinting tools

### Security Monitoring

- **Real-time Alerts**: Immediate notifications for detected scraping attempts
- **Detailed Logging**: Comprehensive logs of all anti-scraping events
- **Performance Metrics**: Success rates and effectiveness measurements
- **Integration Points**: Easy integration with existing security infrastructure

For detailed documentation, see [ANTI_SCRAPING_FRAMEWORK.md](documents/ANTI_SCRAPING_FRAMEWORK.md).

## Security & Authentication

### Environment Setup

The web dashboard requires a secure secret key for JWT token generation. Create a `.env` file in the project root:

```bash
# Create .env file with secure settings
echo "SECRET_KEY=$(openssl rand -hex 32)" > .env
echo "ALGORITHM=HS256" >> .env
echo "ACCESS_TOKEN_EXPIRE_MINUTES=30" >> .env
```

### Default Admin Account

The system automatically creates a default admin account on first run:
- **Username**: `admin`
- **Password**: `admin123` (change immediately after first login)
- **Role**: Administrator

**Important**: Change the default admin password immediately after first login for security.

### Password Requirements

- Minimum 6 characters (recommended: 8+ characters)
- No specific complexity requirements (customize as needed)
- Users can change their own passwords
- Admins can reset any user's password

### Security Features

- JWT-based authentication with configurable expiration
- Password hashing using secure algorithms
- CORS protection for API endpoints
- Input validation and sanitization
- SQL injection protection through parameterized queries
- XSS protection in web templates
- Session management with token refresh capabilities
- **Enhanced Security Logging**: HTTP access monitoring with rotating file handlers
- **Access Control**: Role-based permissions with admin-only endpoints
- **Security Event Tracking**: Failed login attempts and suspicious activity logging
- **IP-based Monitoring**: Request tracking with user-agent analysis
- **Error Handling**: Secure error pages with login redirects for unauthorized access
- **Database Security**: PostgreSQL primary with SQLite fallback and connection encryption
- **API Rate Limiting**: Protection against brute force attacks and API abuse

## License

MIT

---

## Recent Updates

This README has been updated to reflect the latest enhancements to the Trend Crawler system, including:

### ✨ Dashboard Enhancements
- **Modern Glassmorphism Design**: Updated UI with backdrop blur effects and card-based layouts
- **24-Hour Timestamp Format**: All system times now display in military format (HH:MM:SS)
- **Enhanced Navigation**: Fixed dropdown menus and improved responsive design
- **Real-time Monitoring**: Auto-refresh system data every 5 seconds with smooth animations

### 📊 New Dashboard Pages
- **System Information**: Comprehensive hardware and resource monitoring with categorized cards
- **CAPTCHA Analytics**: ML model performance tracking and solver statistics
- **Proxy Management**: Real-time proxy health monitoring and performance metrics
- **Security Logs**: Admin-only access to detailed security and access logs

### 🔐 Security Improvements
- **Enhanced Logging**: HTTP access monitoring with rotating file handlers
- **Access Control**: Improved role-based permissions and admin-only features
- **Error Handling**: Custom 404/500 handlers with security-focused redirects
- **Anti-Scraping Framework**: Comprehensive protection against automated scraping

### 🏗️ Technical Architecture
- **Dual Database Support**: PostgreSQL primary with automatic SQLite fallback
- **API Enhancements**: New endpoints for system monitoring and security logs
- **Performance Optimization**: Non-blocking CPU measurement and efficient query patterns
- **Port Configuration**: Default port changed to 8001 with configurable options

### 📱 User Experience
- **Mobile Responsive**: Optimized for all screen sizes and devices
- **Color-Coded Indicators**: Visual status indicators with smooth CSS animations
- **Improved User Management**: Enhanced profile editing and password management
- **Better Error Messages**: User-friendly error handling and validation feedback
