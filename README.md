# Trend Crawler

A comprehensive web crawler and trend analysis system that discovers trending topics and keywords across various websites, with a full-featured web dashboard for user management and trend visualization.

## Features

### Core Trend Analysis
- Crawls multiple websites to collect data
- Uses Natural Language Processing (NLP) to discover trending keywords
- Categorizes content using topic modeling
- Calculates multi-factor "coolness scores" based on velocity, impact, and novelty
- Integrates Twitter/X data to enhance trend analysis
- Features active learning for intelligent sample selection
- Includes meta-learning for continuous model improvement
- Implements memory-efficient, secure reasoning subsystem for real-time trend analysis
- Utilizes cost-optimized knowledge distillation system with Groq API integration
- Implements semantic velocity analysis for trend acceleration monitoring
- Features circuit-breaker pattern for fail-safe operation
- Implements online training loops with experience replay
- Supports mixed precision operations for faster training
- Enables automated retraining on concept drift detection
- Provides distributed training support across multiple GPUs
- Implements gradient checkpointing for memory optimization
- Optimizes resource usage for better performance
- Includes ML-based CAPTCHA solvers (PyTorch and TensorFlow)
- Implements comprehensive anti-scraping countermeasures
- Stores data in a PostgreSQL database with pgvector for vector embeddings
- Saves results to JSON file
- Supports both simple and advanced topic modeling (BERTopic)

### Web Dashboard & User Management
- **Full-featured web dashboard** with user authentication and session management
- **User account system** with secure JWT-based authentication
- **Profile management** - Users can edit email addresses and full names
- **Admin capabilities** - Admin users can manage other users' profiles
- **User management interface** - Add, edit, and delete users through web interface
- **Password management** - Users can change passwords and admins can reset passwords
- **Role-based access control** - Separate user and admin privileges
- **Responsive web interface** with modern UI components
- **Real-time trend visualization** and analytics dashboard
- **Secure API endpoints** for all user management operations

## Installation

1. Clone this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

For advanced features (optional):
```bash
pip install -r requirements-advanced.txt
```

For Twitter/X scraping (optional):
```bash
pip install -r requirements-twitter.txt
```

For Groq API integration and reasoning system (required):
```bash
pip install -r requirements-groq.txt
export GROQ_API_KEY=your_api_key_here
```

This will install the necessary dependencies for Twitter scraping, including:
- `twitter_scraper_selenium` for the primary scraper
- `selenium` for browser automation
- `webdriver-manager` for automatic ChromeDriver management

For the fallback scraper, you'll also need to run:
```bash
playwright install
```

The Twitter/X scraping functionality uses a multi-layered approach:
1. First tries to use `twitter_scraper_selenium` with parallel processing
2. Falls back to API-based scraping if a valid token is available
3. Uses browser-based scraping with Playwright as a second fallback

## Usage

### Web Dashboard

Start the web dashboard for the complete user interface:

```bash
./run_scraper.sh dashboard
```

The web dashboard provides:
- User authentication and session management
- Trend visualization and analytics
- User profile management
- Admin user management interface
- Real-time monitoring and metrics

Access the dashboard at `http://localhost:8080` after starting.

### Command Line Trend Crawler

Run the trend crawler directly:

```bash
python trend_crawler.py
```

### Run Script Options

The `run_scraper.sh` script provides convenient options for running different components:

```bash
# Start only the web dashboard
./run_scraper.sh dashboard

# Start only the trend crawler
./run_scraper.sh trends

# Start only the Twitter scraper
./run_scraper.sh twitter

# Start both trend crawler and web dashboard simultaneously
./run_scraper.sh both
```

When using `./run_scraper.sh both`, both services run in the background and can be stopped together with Ctrl+C. The script will display:
- Process IDs for both services
- Dashboard URL (http://localhost:8080)
- Status messages for monitoring

### Command-line Options

- `--no-db`: Skip database connection
- `--urls-file FILE`: Path to file containing URLs to crawl (one per line)
- `--url URL`: URL to crawl (can be specified multiple times)
- `--output FILE`: Output JSON file for results (default: results.json)
- `--log-level LEVEL`: Set the logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `--simple`: Use simple topic modeling instead of BERTopic
- `--active-learning`: Enable active learning for sample selection
- `--meta-learning`: Enable meta-learning for continuous improvement
- `--optimize-resources`: Enable resource optimization for better performance
- `--distributed`: Enable distributed training across multiple GPUs
- `--mixed-precision`: Enable mixed precision operations for faster training
- `--auto-retrain`: Enable automated retraining on concept drift detection
- `--twitter-enabled`: Enable Twitter/X data collection for trend analysis
- `--twitter-max-tweets`: Maximum number of tweets to retrieve per trend (default: 100)
- `--twitter-workers`: Number of parallel workers for Twitter scraping (default: 3)
- `--reasoning-enabled`: Enable memory-efficient reasoning subsystem for trend analysis
- `--knowledge-distillation`: Enable knowledge distillation with Groq API integration
- `--max-memory-mb`: Maximum memory usage for reasoning subsystem (default: 300MB)
- `--groq-daily-budget`: Set daily budget limit for Groq API calls (default: $1.00)
- `--velocity-window-size`: Window size for semantic velocity analyzer (default: 5)
- `--velocity-decay-factor`: Decay factor for semantic velocity analyzer (default: 0.8)
- `--trend-attention-sparsity`: Sparsity factor for trend attention mechanism (default: 0.3)

Examples:

```bash
# Skip database connection
python trend_crawler.py --no-db

# Use custom URLs file
python trend_crawler.py --urls-file my_urls.txt

# Specify URLs directly
python trend_crawler.py --url https://example.com --url https://another-site.com

# Save results to a specific file
python trend_crawler.py --output my_results.json

# Use simple topic modeling (no BERTopic dependency)
python trend_crawler.py --simple

# Enable all advanced features
python trend_crawler.py --active-learning --meta-learning --optimize-resources

# Enable distributed training with mixed precision
python trend_crawler.py --distributed --mixed-precision --optimize-resources

# Enable automated retraining on concept drift
python trend_crawler.py --meta-learning --auto-retrain

# Enable Twitter/X data collection
python trend_crawler.py --twitter-enabled --twitter-max-tweets 50 --twitter-workers 5

# Enable memory-efficient reasoning system with knowledge distillation
python trend_crawler.py --reasoning-enabled --knowledge-distillation

# Advanced configuration of reasoning system
python trend_crawler.py --reasoning-enabled --max-memory-mb 500 --groq-daily-budget 0.5

# Configure semantic velocity analyzer
python trend_crawler.py --reasoning-enabled --velocity-window-size 10 --velocity-decay-factor 0.9 --trend-attention-sparsity 0.4
```

The script will:
1. Prompt for database connection information (unless --no-db is used)
2. Crawl websites from the specified sources
3. Extract trending keywords
4. Categorize content using topic modeling
5. Analyze Twitter profiles mentioned in trends (when --twitter-enabled)
6. Calculate influence scores for Twitter profiles
7. Enhance coolness scores with profile data
8. Apply BERT-based coolness classification (when available)
9. Apply memory-efficient reasoning to trends (when --reasoning-enabled)
10. Analyze semantic velocity of trends for acceleration monitoring
11. Apply knowledge distillation using Groq API (when --knowledge-distillation)
12. Incorporate user feedback into scoring (when available)
13. Display results
14. Save to database (if connected) and JSON file

## Quick Start

### 1. Setup Environment
```bash
# Clone the repository
git clone <repository-url>
cd trend-crawler

# Run the setup script
./setup.sh
```

### 2. Start the System
```bash
# Start both trend crawler and web dashboard
./run_scraper.sh both
```

### 3. Access the Dashboard
Open your web browser and navigate to `http://localhost:8080`

**Default Admin Login:**
- Username: `admin`
- Password: `admin123` (change immediately)

### 4. Basic Usage
- **Users**: Create account, edit profile, view trends
- **Admins**: Manage users, access full dashboard, configure system

## Requirements

- Python 3.6+
- See requirements.txt for basic Python package dependencies
- See requirements-advanced.txt for advanced features (BERTopic)

## Web Dashboard

The system includes a comprehensive web dashboard with user authentication and management capabilities.

### Features

- **User Authentication**: Secure JWT-based login system
- **Profile Management**: Users can edit email addresses and full names
- **Password Management**: Users can change their passwords securely
- **Admin Interface**: Admin users can manage other users
- **User Management**: Add, edit, delete, and reset passwords for users
- **Role-based Access**: Separate user and admin privileges
- **Responsive Design**: Modern web interface that works on all devices

### Starting the Dashboard

```bash
# Start only the web dashboard
./run_scraper.sh dashboard

# Start both trend crawler and dashboard together
./run_scraper.sh both
```

### User Management

#### Regular Users Can:
- View and edit their own profile (email and full name)
- Change their password
- Access the main dashboard and trend data
- Username is read-only and cannot be changed

#### Admin Users Can:
- All regular user capabilities
- View all users in the system
- Add new users with specified roles
- Edit any user's profile information
- Reset passwords for any user
- Delete users from the system

### API Endpoints

The web dashboard provides RESTful API endpoints:

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh JWT token

#### User Management
- `GET /api/v1/users/` - List all users (admin only)
- `POST /api/v1/users/` - Create new user (admin only)
- `DELETE /api/v1/users/{username}` - Delete user (admin only)
- `POST /api/v1/users/change-password` - Change own password
- `POST /api/v1/users/profile` - Update own profile
- `POST /api/v1/users/{username}/profile` - Update user profile (admin only)
- `POST /api/v1/users/{username}/reset-password` - Reset user password (admin only)

### Database Setup

The script will automatically create the necessary tables if they don't exist:

#### Core Trend Analysis Tables
- `categories` - Stores topic categories
- `coolness_data` - Stores website data with coolness scores
- `twitter_data` - Stores individual tweets
- `twitter_metrics` - Stores aggregated Twitter metrics
- `twitter_scraping_logs` - Tracks Twitter scraping performance
- `twitter_profiles` - Stores Twitter profile data
- `profile_relationships` - Tracks relationships between profiles and trends
- `user_feedback` - Stores user feedback for coolness ratings
- `model_predictions` - Caches model predictions
- `model_versions` - Tracks model versions and metrics
- `trend_embeddings` - Stores vector embeddings for trends
- `semantic_velocity` - Tracks semantic velocity measurements
- `trend_reasoning_logs` - Logs operations of the reasoning system
- `knowledge_distillation_cache` - Caches knowledge distillation results
- `groq_api_usage` - Tracks Groq API usage and costs

#### User Management Tables
- `users` - Stores user accounts with authentication data
- `user_sessions` - Manages user sessions and JWT tokens
- `user_roles` - Defines user roles and permissions
- `audit_logs` - Tracks user actions and system events

## Testing

Run the unit tests:

```bash
python -m unittest test_trend_crawler.py
```

## BERT-based Coolness Classification

The system includes a BERT-based coolness classification system that enhances the traditional scoring mechanism:

```bash
# Install BERT dependencies
pip install -r requirements-bert.txt

# Test the BERT-based coolness classification system
python test_coolness_classifier.py

# Test with specific texts
python test_coolness_classifier.py --texts "Revolutionary new AI technology" "Boring article about mundane topics"

# Test feedback training
python test_coolness_classifier.py --test-feedback
```

The BERT-based classification system:
- Uses a pre-trained BERT model fine-tuned for coolness classification
- Incorporates user feedback to improve predictions over time
- Provides confidence scores and probability distributions
- Enhances the traditional scoring mechanism with ML-based insights

## ML-based CAPTCHA Solvers

The system includes machine learning-based CAPTCHA solvers that can be used for local CAPTCHA solving without relying on external services:

```bash
# Install ML-based CAPTCHA solver dependencies
pip install -r requirements-captcha.txt

# Test the ML-based CAPTCHA solvers
python test_ml_captcha_solver.py --download-samples

# Test with a specific image
python test_ml_captcha_solver.py --image path/to/captcha.png

# Test with a specific model
python test_ml_captcha_solver.py --image path/to/captcha.png --model path/to/model.pth --solver pytorch
```

The ML-based CAPTCHA solvers:
- Support both PyTorch and TensorFlow models
- Include preprocessing utilities for different CAPTCHA types
- Integrate with external CAPTCHA solving services as fallback
- Provide a unified interface for solving various types of CAPTCHAs
- Can be trained on custom CAPTCHA datasets

For more information, see the [ML-based CAPTCHA Solvers README](ml_captcha_solvers/README.md).

## Memory-Efficient Reasoning System

The system includes a memory-efficient, secure reasoning subsystem for real-time trend analysis:

```bash
# Install reasoning system dependencies
pip install -r requirements-groq.txt

# Test the reasoning system
python test_nanollm.py

# Run with reasoning system enabled
python trend_crawler.py --reasoning-enabled

# Test semantic velocity analyzer
python semantic_velocity_analyzer.py --test-trends "AI advances" "Web3 adoption" --window-size 5 --decay-factor 0.8
```

The memory-efficient reasoning system:
- Implements dynamic pruning for optimized memory usage
- Uses sparse attention mechanisms to reduce computational overhead
- Applies INT8 quantization for reduced memory footprint
- Features circuit breaker pattern for fail-safe operation
- Includes enhanced semantic velocity analysis for trend acceleration monitoring:
  - Tracks trend embeddings over time with configurable window size
  - Calculates weighted semantic shift with temporal decay
  - Uses cosine similarity to measure semantic distance between embeddings
  - Provides normalized velocity scores (0-1) for trend momentum
  - Thread-safe implementation for concurrent trend analysis
- Integrates with vector store for context-aware processing
- Maintains strict memory constraints (300MB by default)
- Implements 64-dim temporal-aware embeddings with position-sensitive encoding
- Features 2-layer TrendAttention blocks with dynamic pruning (30% sparsity)

## Knowledge Distillation with Groq API

The system includes a cost-optimized knowledge distillation system using Groq's API:

```bash
# Set up Groq API integration
export GROQ_API_KEY=your_api_key_here

# Test knowledge distillation
python groq_knowledge_distiller.py --test-prompt "Analyze recent tech trends"

# Run with knowledge distillation enabled
python trend_crawler.py --knowledge-distillation --groq-daily-budget 0.5
```

The knowledge distillation system:
- Implements a dual teacher approach (free tier + paid tier)
- Uses compound-beta model (free tier) as primary teacher
- Falls back to deepseek-r1 model (paid tier) for complex analysis
- Enforces strict daily budget of $1.00 for deepseek-r1
- Features cost-aware routing based on trend complexity
- Includes comprehensive security sanitization
- Provides seamless integration with the reasoning system

### API Endpoints

The system includes API endpoints for feedback collection and prediction:

```bash
# Start the API server
python api_endpoints.py
```

Endpoints:
- `POST /api/v1/feedback` - Submit feedback for a URL
- `GET /api/v1/coolness-prediction` - Get coolness prediction for a URL
- `POST /api/v1/solve-captcha` - Solve a CAPTCHA image
- `GET /api/v1/trend-reasoning` - Get reasoning analysis for a trend
- `GET /api/v1/semantic-velocity` - Get semantic velocity for a trend
- `POST /api/v1/distill-knowledge` - Perform knowledge distillation on trend data
- `GET /api/v1/groq-usage` - Get Groq API usage statistics

## Security & Authentication

### Environment Setup

The web dashboard requires a secure secret key for JWT token generation. Create a `.env` file in the project root:

```bash
# Create .env file with secure settings
echo "SECRET_KEY=$(openssl rand -hex 32)" > .env
echo "ALGORITHM=HS256" >> .env
echo "ACCESS_TOKEN_EXPIRE_MINUTES=30" >> .env
```

### Default Admin Account

The system automatically creates a default admin account on first run:
- **Username**: `admin`
- **Password**: `admin123` (change immediately after first login)
- **Role**: Administrator

**Important**: Change the default admin password immediately after first login for security.

### Password Requirements

- Minimum 6 characters (recommended: 8+ characters)
- No specific complexity requirements (customize as needed)
- Users can change their own passwords
- Admins can reset any user's password

### Security Features

- JWT-based authentication with configurable expiration
- Password hashing using secure algorithms
- CORS protection for API endpoints
- Input validation and sanitization
- SQL injection protection through parameterized queries
- XSS protection in web templates
- Session management with token refresh capabilities

## License

MIT
