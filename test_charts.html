<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1"></script>
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
            padding: 10px;
            margin: 20px;
            border: 1px solid #ccc;
        }
        .chart-container canvas {
            max-height: 280px;
        }
    </style>
</head>
<body>
    <h1>Chart Test Page</h1>
    
    <div class="chart-container">
        <h2>System Resource Usage Test</h2>
        <canvas id="systemResourcesChart"></canvas>
    </div>

    <div class="chart-container">
        <h2>API Health Test</h2>
        <canvas id="apiHealthChart"></canvas>
    </div>

    <div class="chart-container">
        <h2>Database Performance Test</h2>
        <canvas id="dbPerformanceChart"></canvas>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Chart test page loaded");
        
        // Test System Resources Chart
        function testSystemResourcesChart() {
            const canvas = document.getElementById('systemResourcesChart');
            if (!canvas) {
                console.error("System Resources Chart canvas not found!");
                return;
            }

            console.log("Creating test system resources chart...");
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['CPU Usage', 'Memory Usage', 'Disk Usage'],
                    datasets: [{
                        data: [45.5, 70.1, 79.9],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ],
                        borderColor: [
                            'rgb(255, 99, 132)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 205, 86)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Test System Resource Usage'
                        },
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
            console.log("System Resources Chart created successfully!");
        }

        // Test API Health Chart
        function testApiHealthChart() {
            const canvas = document.getElementById('apiHealthChart');
            if (!canvas) {
                console.error("API Health Chart canvas not found!");
                return;
            }

            console.log("Creating test API health chart...");
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Uptime', 'Success Rate', 'Requests/Min', 'Error Rate'],
                    datasets: [{
                        label: 'API Health Metrics',
                        data: [100, 95, 25, 5],
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 206, 86, 0.6)',
                            'rgba(255, 99, 132, 0.6)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(255, 99, 132, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Test API Service Health Metrics'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            console.log("API Health Chart created successfully!");
        }

        // Test DB Performance Chart
        function testDbPerformanceChart() {
            const canvas = document.getElementById('dbPerformanceChart');
            if (!canvas) {
                console.error("DB Performance Chart canvas not found!");
                return;
            }

            console.log("Creating test database performance chart...");
            
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Queries/Min', 'Success Rate'],
                    datasets: [{
                        label: 'Database Performance',
                        data: [150, 98],
                        backgroundColor: [
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)'
                        ],
                        borderColor: [
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Test Database Performance Metrics'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            console.log("DB Performance Chart created successfully!");
        }

        // Initialize test charts
        console.log("Initializing test charts...");
        setTimeout(() => {
            console.log("Starting test chart initialization...");
            testSystemResourcesChart();
            testApiHealthChart();
            testDbPerformanceChart();
            console.log("Test chart initialization complete.");
        }, 1000);
    });
    </script>
</body>
</html>
