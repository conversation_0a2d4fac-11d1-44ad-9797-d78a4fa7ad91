import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import tempfile
import requests

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import functions from trend-crawler.py
from trend_crawler import (
    crawl_website,
    connect_to_database
)


class TestTrendCrawler(unittest.TestCase):
    """Test cases for trend-crawler.py functions"""

    @patch('trend_crawler.requests.get')
    def test_crawl_website_success(self, mock_get):
        """Test successful website crawling"""
        # Mock the response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '<html><body><p>Test content</p></body></html>'
        mock_get.return_value = mock_response

        # Call the function
        result = crawl_website('https://example.com')

        # Verify the result
        self.assertIsNotNone(result)
        self.assertIn('Test content', result)
        mock_get.assert_called_once_with('https://example.com', headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }, timeout=15)

    @patch('trend_crawler.requests.get')
    def test_crawl_website_failure(self, mock_get):
        """Test failed website crawling"""
        # Mock the response for failure
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_get.return_value = mock_response

        # Call the function
        result = crawl_website('https://example.com')

        # Verify the result
        self.assertIsNone(result)
        mock_get.assert_called_with('https://example.com', headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }, timeout=15)

    @patch('trend_crawler.requests.get')
    def test_crawl_website_exception(self, mock_get):
        """Test exception handling in website crawling"""
        # Mock the get method to raise an exception
        mock_get.side_effect = requests.exceptions.RequestException(
            "Connection error"
        )

        # Call the function
        result = crawl_website('https://example.com')

        # Verify the result
        self.assertIsNone(result)
        mock_get.assert_called_with('https://example.com', headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }, timeout=15)

    @patch('trend_crawler.psycopg2.connect')
    def test_connect_to_database_success(self, mock_connect):
        """Test successful database connection"""
        # Mock the connection
        mock_db = MagicMock()
        mock_connect.return_value = mock_db

        # Call the function
        result = connect_to_database(
            'localhost', 'user', 'password', 'db_name'
        )

        # Verify the result
        self.assertEqual(result, mock_db)
        mock_connect.assert_called_once_with(
            host='localhost', port='5432', dbname='db_name', 
            user='user', password='password'
        )

    @patch('trend_crawler.psycopg2.connect')
    def test_connect_to_database_failure(self, mock_connect):
        """Test failed database connection"""
        # Mock the connect method to raise an exception
        mock_connect.side_effect = Exception("Connection error")

        # Call the function and verify it raises an exception
        with self.assertRaises(Exception):
            connect_to_database('localhost', 'user', 'password', 'db_name')

    def test_url_file_handling(self):
        """Test URL file handling"""
        # Create a temporary file with URLs
        with tempfile.NamedTemporaryFile(mode='w+', delete=False) as temp_file:
            temp_file.write("https://example.com\nhttps://test.com\n")
            temp_file_name = temp_file.name

        try:
            # Read the file and verify the URLs
            with open(temp_file_name, 'r') as f:
                urls = [line.strip() for line in f if line.strip()]
                self.assertEqual(len(urls), 2)
                self.assertEqual(urls[0], "https://example.com")
                self.assertEqual(urls[1], "https://test.com")
        finally:
            # Clean up
            os.unlink(temp_file_name)


if __name__ == '__main__':
    unittest.main()
