#!/bin/bash

# Setup script for Groq Knowledge Distillation System
# This script installs dependencies and sets up environment variables

echo "Setting up Groq Knowledge Distillation System..."

# Install Python dependencies
echo "Installing dependencies..."
pip install -r requirements-groq.txt

# Check if API key is already set
if [ -z "$GROQ_API_KEY" ]; then
    # Try to read from .env file if it exists
    if [ -f .env ] && grep -q "GROQ_API_KEY" .env; then
        echo "Found GROQ_API_KEY in .env file, loading it..."
        export GROQ_API_KEY=$(grep "GROQ_API_KEY" .env | cut -d "=" -f2 | tr -d '"')
        echo "Loaded GROQ_API_KEY from .env file"
    else
        echo ""
        echo "You need to set up your Groq API key:"
        echo ""
        read -p "Enter your Groq API key: " api_key

        # Export for current session
        export GROQ_API_KEY=$api_key

        # Add to .env file for persistence
        echo "# Groq API key" >> .env
        echo "GROQ_API_KEY=\"$api_key\"" >> .env

        echo ""
        echo "API key has been set and saved to .env file"
    fi
else
    echo "Groq API key is already set in the environment"
fi

# Create a simple monitoring dashboard using Prometheus
echo "Setting up monitoring..."
if ! command -v prometheus &> /dev/null; then
    echo "Prometheus is not installed. Setting up basic metrics via Python."

    # Create a simple metrics script
    cat > groq_metrics.py << 'EOF'
#!/usr/bin/env python3
"""
Simple metrics server for Groq Knowledge Distillation System.
"""

from prometheus_client import start_http_server, Counter, Gauge, Summary
import time
import os
import threading
import json

# Metrics
FREE_REQUESTS = Counter('groq_free_requests', 'Number of requests to free tier')
PAID_REQUESTS = Counter('groq_paid_requests', 'Number of requests to paid tier')
PAID_TIER_COST = Gauge('groq_paid_cost', 'Cost of paid tier in USD')
MODEL_SWITCHES = Counter('groq_model_switches', 'Number of switches between models')
REQUEST_LATENCY = Summary('groq_request_latency', 'Request latency in seconds')
MEMORY_USAGE = Gauge('groq_memory_usage', 'Memory usage in MB')

# Status file path for monitoring
STATUS_FILE = '/tmp/groq_distiller_status.json'

def update_metrics():
    """Read status file and update metrics."""
    while True:
        try:
            if os.path.exists(STATUS_FILE):
                with open(STATUS_FILE, 'r') as f:
                    status = json.load(f)

                    # Update metrics
                    FREE_REQUESTS._value.set(status.get('free_requests', 0))
                    PAID_REQUESTS._value.set(status.get('paid_requests', 0))
                    PAID_TIER_COST.set(status.get('paid_cost', 0))
                    MODEL_SWITCHES._value.set(status.get('model_switches', 0))
                    REQUEST_LATENCY._sum.set(status.get('latency_sum', 0))
                    REQUEST_LATENCY._count.set(status.get('request_count', 0))
                    MEMORY_USAGE.set(status.get('memory_usage_mb', 0))

                    print(f"Updated metrics from {STATUS_FILE}")
        except Exception as e:
            print(f"Error updating metrics: {e}")

        time.sleep(15)  # Update every 15 seconds

if __name__ == '__main__':
    # Start up the server to expose the metrics.
    start_http_server(8000)
    print("Metrics server started on port 8000")

    # Start metrics updater in background
    updater = threading.Thread(target=update_metrics, daemon=True)
    updater.start()

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Metrics server stopped")
EOF

    chmod +x groq_metrics.py
    echo "Metrics script created at groq_metrics.py"
    echo "Run with: python groq_metrics.py"
    echo "Then access metrics at: http://localhost:8000/"
else
    echo "Prometheus is installed. You can configure it to scrape metrics from http://localhost:8000/"
fi

# Test the distillation system
echo ""
echo "Testing Groq Knowledge Distillation System..."
python -c "
import os
from groq_knowledge_distiller import CostAwareDistiller

if __name__ == '__main__':
    try:
        # Initialize distiller
        distiller = CostAwareDistiller()

        # Test with a sample trend
        trend = 'Bitcoin showing bullish divergence on hourly charts with increased volume'
        print(f'Testing with trend: \"{trend}\"')

        # Process trend
        response, used_paid = distiller.distill_knowledge(trend)
        tier = 'paid' if used_paid else 'free'

        # Print result
        print(f'Using {tier} tier model')
        print(f'Response: \"{response[:100]}...\"')
        print('Test successful!')
    except Exception as e:
        print(f'Test failed with error: {e}')
        print('Please check your API keys and network connection.')
"

echo ""
echo "Setup complete! To use the Groq Knowledge Distillation System:"
echo "1. Make sure GROQ_API_KEY is set in your environment (it's saved in the .env file)"
echo "2. Import and use the CostAwareDistiller or HybridDistillationGate classes in your code"
echo "3. Run the metrics server with: python groq_metrics.py"
echo ""
echo "Example usage:"
echo "  from groq_knowledge_distiller import CostAwareDistiller"
echo "  distiller = CostAwareDistiller()"
echo "  response, used_paid = distiller.distill_knowledge('Your trend text here')"
echo ""
