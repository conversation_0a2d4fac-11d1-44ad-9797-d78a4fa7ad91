<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Trend Crawler</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .nav-link { display: inline-block; margin: 5px; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .nav-link:hover { background: #0056b3; }
        #console-output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: scroll; font-family: monospace; font-size: 12px; }
        .test-instructions { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 10px; border-radius: 3px; margin-bottom: 15px; }
        .status { padding: 5px 10px; border-radius: 3px; margin: 5px 0; }
        .status.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .status.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .status.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
    <!-- Load the auth guard first -->
    <script src="/static/js/auth-guard.js"></script>
</head>
<body>
    <h1>Navigation Click Interception Test</h1>
    
    <div class="test-instructions">
        <h3>🧪 Test Instructions:</h3>
        <ol>
            <li><strong>Without Login:</strong> Click any navigation link below. You should be redirected to the login page automatically.</li>
            <li><strong>With Login:</strong> <a href="/login" target="_blank">Login first</a>, then come back and click navigation links. They should work normally.</li>
            <li><strong>Check Console:</strong> Watch the console output below for debugging information.</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>🧭 Navigation Links (Should be intercepted by auth-guard.js):</h3>
        <a href="/scrapers" class="nav-link">Scrapers</a>
        <a href="/proxies" class="nav-link">Proxies</a>
        <a href="/captchas" class="nav-link">CAPTCHAs</a>
        <a href="/alerts" class="nav-link">Alerts</a>
        <a href="/system" class="nav-link">System</a>
        <a href="/account" class="nav-link">Account</a>
    </div>
    
    <div class="test-section">
        <h3>🚫 Control Links (Should NOT be intercepted):</h3>
        <a href="/login" class="nav-link" style="background: #28a745;">Login (Public)</a>
        <a href="/logout" class="nav-link" style="background: #dc3545;">Logout (Public)</a>
        <a href="https://google.com" class="nav-link" style="background: #6c757d;" target="_blank">External Link</a>
        <a href="#section" class="nav-link" style="background: #6c757d;">Hash Link</a>
    </div>
    
    <div class="test-section">
        <h3>📊 Current Status:</h3>
        <div id="auth-status" class="status warning">Checking authentication...</div>
        <div id="token-status" class="status warning">Checking token...</div>
    </div>
    
    <div class="test-section">
        <h3>📝 Console Output:</h3>
        <div id="console-output"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>
    
    <script>
        // Console output capture
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
            line.textContent = `[${timestamp}] ${message}`;
            consoleOutput.appendChild(line);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const authStatus = document.getElementById('auth-status');
            const tokenStatus = document.getElementById('token-status');
            
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const now = Date.now() / 1000;
                    
                    if (payload.exp < now) {
                        authStatus.className = 'status error';
                        authStatus.textContent = '❌ Token expired';
                        tokenStatus.className = 'status error';
                        tokenStatus.textContent = '⏰ Token expired at: ' + new Date(payload.exp * 1000).toLocaleString();
                    } else {
                        authStatus.className = 'status success';
                        authStatus.textContent = '✅ Authenticated as: ' + payload.sub;
                        tokenStatus.className = 'status success';
                        tokenStatus.textContent = '⏰ Token expires at: ' + new Date(payload.exp * 1000).toLocaleString();
                    }
                } catch (error) {
                    authStatus.className = 'status error';
                    authStatus.textContent = '❌ Invalid token format';
                    tokenStatus.className = 'status error';
                    tokenStatus.textContent = '🔧 Token parsing error: ' + error.message;
                }
            } else {
                authStatus.className = 'status warning';
                authStatus.textContent = '⚠️ Not authenticated (no token)';
                tokenStatus.className = 'status warning';
                tokenStatus.textContent = '📝 No access token in localStorage';
            }
        }
        
        // Check status when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Navigation test page loaded');
            checkAuthStatus();
            
            // Refresh status every 5 seconds
            setInterval(checkAuthStatus, 5000);
        });
        
        // Add click logging to all links
        document.addEventListener('click', function(event) {
            const link = event.target.closest('a');
            if (link) {
                console.log('Click detected on link:', link.href);
                console.log('Link href attribute:', link.getAttribute('href'));
                console.log('Link target:', link.target || 'none');
                console.log('Link hostname:', link.hostname || 'none');
            }
        });
    </script>
</body>
</html>
