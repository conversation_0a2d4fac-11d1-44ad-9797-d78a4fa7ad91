#!/usr/bin/env python3
"""
Minimal test to isolate ScrapingShield initialization issues
"""

import logging
import time
import traceback

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_step_by_step():
    """Test ScrapingShield initialization step by step."""
    logger.info("=== Step-by-step ScrapingShield Test ===")
    
    try:
        logger.info("Step 1: Testing import...")
        from scraping_shield import ScrapingShield
        logger.info("✓ Import successful")
        
        logger.info("Step 2: Creating minimal config...")
        # Create a minimal config that won't try to validate proxies or connect to DB
        import tempfile
        import json
        import os
        
        minimal_config = {
            "proxies": [],  # No proxies to validate
            "db": {"enabled": False},  # No database connection
            "max_retries": 1,
            "retry_delay": 1,
            "exponential_backoff": False
        }
        
        # Write config to temp file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(minimal_config, f)
            config_path = f.name
        
        logger.info(f"Step 3: Initializing ScrapingShield with config: {config_path}")
        start_time = time.time()
        
        shield = ScrapingShield(config_path)
        
        elapsed = time.time() - start_time
        logger.info(f"✓ ScrapingShield initialized successfully in {elapsed:.2f}s")
        
        logger.info("Step 4: Testing basic methods...")
        
        # Test user agent
        ua = shield.rotate_user_agent()
        logger.info(f"✓ User agent: {ua[:50]}...")
        
        # Test headers
        headers = shield.waf_bypass_headers("https://example.com")
        logger.info(f"✓ Generated {len(headers)} headers")
        
        # Clean up
        os.unlink(config_path)
        logger.info("✓ All tests passed!")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Error: {e}")
        logger.debug(f"Error details: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_step_by_step()
    if success:
        print("\n🎉 SUCCESS: ScrapingShield is working correctly!")
        exit(0)
    else:
        print("\n❌ FAILED: ScrapingShield has issues")
        exit(1)
