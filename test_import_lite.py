#!/usr/bin/env python3
"""
Test script to check if ScrapingShieldLite can be imported without hanging.
"""

import logging
import sys
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_lite_import():
    """Test importing ScrapingShieldLite."""
    try:
        logger.info("Starting ScrapingShieldLite import test...")
        start_time = time.time()
        
        from scraping_shield_lite import ScrapingShieldLite
        
        import_time = time.time() - start_time
        logger.info(f"ScrapingShieldLite imported successfully in {import_time:.2f} seconds")
        
        # Test basic initialization
        start_time = time.time()
        shield = ScrapingShieldLite()
        init_time = time.time() - start_time
        logger.info(f"ScrapingShieldLite initialized successfully in {init_time:.2f} seconds")
        
        # Test basic methods
        ua = shield.rotate_user_agent()
        logger.info(f"User agent rotation works: {ua[:50]}...")
        
        headers = shield.get_enhanced_headers()
        logger.info(f"Enhanced headers work: {len(headers)} headers generated")
        
        logger.info("✅ ScrapingShieldLite import and basic functionality test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ ScrapingShieldLite import test FAILED: {e}")
        logger.debug(f"Error details: {sys.exc_info()}")
        return False

def test_heavy_import():
    """Test importing the heavy ScrapingShield to compare."""
    try:
        logger.info("Starting heavy ScrapingShield import test...")
        start_time = time.time()
        
        # Set a timeout for this test
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Import took too long")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10 second timeout
        
        try:
            from scraping_shield import ScrapingShield
            signal.alarm(0)  # Cancel the alarm
            
            import_time = time.time() - start_time
            logger.info(f"Heavy ScrapingShield imported successfully in {import_time:.2f} seconds")
            return True
            
        except TimeoutError:
            logger.warning("Heavy ScrapingShield import timed out after 10 seconds")
            return False
            
    except Exception as e:
        logger.error(f"Heavy ScrapingShield import failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("=== Import Testing Started ===")
    
    # Test lite version first
    lite_success = test_lite_import()
    
    print("\n" + "="*50 + "\n")
    
    # Test heavy version
    heavy_success = test_heavy_import()
    
    print("\n" + "="*50 + "\n")
    
    if lite_success:
        logger.info("✅ RECOMMENDATION: Use ScrapingShieldLite for testing and basic functionality")
    else:
        logger.error("❌ Even ScrapingShieldLite has import issues")
    
    if not heavy_success:
        logger.warning("⚠️  Heavy ScrapingShield has import issues - needs dependency optimization")
    
    logger.info("=== Import Testing Complete ===")
