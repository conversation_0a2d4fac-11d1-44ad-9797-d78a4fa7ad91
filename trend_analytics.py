"""
trend_analytics.py - Advanced trend analysis features using pgvector

This module implements innovative trend analysis features using PostgreSQL with pgvector:
1. Trend summarization with LLMs
2. Predictive trend analysis using semantic velocity
3. Identifying "Sleeper" trends
4. Detecting semantic shifts in ongoing trends
"""

import logging
import json
import os
import time
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
import requests
from datetime import datetime, timedelta
from psycopg2.extras import DictCursor, execute_values

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Optional imports - will be used if available
try:
    import openai
    from openai import OpenAI
    OPENAI_AVAILABLE = True
    # Initialize the OpenAI client if API key is available
    if os.environ.get("OPENAI_API_KEY"):
        openai_client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
    else:
        openai_client = None
        logger.warning("OPENAI_API_KEY not found in environment variables")
except ImportError:
    OPENAI_AVAILABLE = False
    openai_client = None
    logger.warning("OpenAI package not available. LLM summarization will be limited")
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI not available. LLM summarization will use a fallback method.")

# Check for environment variable
if OPENAI_AVAILABLE:
    OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
    if not OPENAI_API_KEY:
        logger.warning("No OPENAI_API_KEY found in environment. LLM summarization will use a fallback method.")
        OPENAI_AVAILABLE = False
    else:
        openai.api_key = OPENAI_API_KEY


class TrendAnalytics:
    """Advanced trend analysis features using pgvector."""

    def __init__(self, db, llm_service="openai", vector_dimensions=384):
        """
        Initialize the TrendAnalytics module.

        Args:
            db: Database connection object
            llm_service: Service to use for LLM summarization ('openai' or 'fallback')
            vector_dimensions: Dimension of vector embeddings
        """
        self.db = db
        self.llm_service = llm_service
        self.vector_dimensions = vector_dimensions

        self._ensure_sql_functions()

    def _ensure_sql_functions(self):
        """Ensure all required SQL functions exist in the database."""
        if not self.db:
            logger.warning("No database connection available")
            return

        cursor = None
        try:
            cursor = self.db.cursor()
            # Check if zero_vector function exists as a simple test
            cursor.execute("""
                SELECT 1
                FROM pg_proc p
                JOIN pg_namespace n ON p.pronamespace = n.oid
                WHERE p.proname = 'zero_vector'
                AND n.nspname = 'public'
            """)

            if not cursor.fetchone():
                logger.info("SQL functions not found. Installing trend analysis SQL functions...")
                try:
                    # Load and execute the SQL file
                    sql_file_path = os.path.join(
                        os.path.dirname(os.path.abspath(__file__)),
                        "trend_analysis_features.sql"
                    )

                    with open(sql_file_path, 'r') as f:
                        sql = f.read()

                    cursor.execute(sql)
                    self.db.commit()
                    logger.info("Successfully installed trend analysis SQL functions")
                except Exception as e:
                    logger.error(f"Failed to install SQL functions: {e}")
                    self.db.rollback()
            else:
                logger.info("Trend analysis SQL functions already installed")

        except Exception as e:
            logger.error(f"Error checking SQL functions: {e}")
        finally:
            if cursor:
                cursor.close()

    def summarize_trend(self, trend_id: int) -> Optional[str]:
        """
        Generate an LLM-powered summary for a specific trend.

        Args:
            trend_id: The ID of the trend to summarize

        Returns:
            Generated summary text or None if summarization failed
        """
        try:
            # Get the prompt for the specific trend
            prompt = self._get_trend_summary_prompt(trend_id)
            if not prompt:
                logger.warning(f"Failed to get summarization prompt for trend {trend_id}")
                return None

            # Generate summary using configured LLM service
            summary = None
            if self.llm_service == 'openai' and OPENAI_AVAILABLE:
                summary = self._summarize_with_openai(prompt)
            else:
                # Use fallback summarization method
                summary = self._summarize_with_fallback(prompt)

            if summary:
                # Update the trend with the generated summary
                self._update_trend_with_summary(trend_id, summary)
                return summary
            else:
                logger.warning(f"Failed to generate summary for trend {trend_id}")
                return None
        except Exception as e:
            logger.error(f"Error summarizing trend {trend_id}: {e}")
            return None

    def _get_trend_summary_prompt(self, trend_id: int) -> Optional[str]:
        """Get a prompt for trend summarization."""
        if not self.db:
            return None

        cursor = None
        try:
            cursor = self.db.cursor()
            cursor.execute("SELECT generate_trend_summary_prompt(%s)", (trend_id,))
            result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error generating summary prompt: {e}")
            return None
        finally:
            if cursor:
                cursor.close()

    def _summarize_with_openai(self, prompt: str) -> Optional[str]:
        """Generate a summary using OpenAI API."""
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI not available for summarization")
            return None

        try:
            # Use the OpenAI API to generate a summary
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that summarizes trend information concisely and professionally."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.5
            )

            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content.strip()
            return None
        except Exception as e:
            logger.error(f"Error using OpenAI for summarization: {e}")
            return None

    def _summarize_with_fallback(self, prompt: str) -> str:
        """Generate a simple summary without using an LLM API."""
        try:
            # Parse the prompt to extract trend information
            trend_name = "Unknown Trend"
            first_seen = "Unknown"
            last_updated = "Unknown"
            status = "active"
            content_samples = []

            lines = prompt.strip().split('\n')

            # Extract trend name
            if lines and lines[0].startswith("Summarize"):
                trend_name = lines[0].replace("Summarize", "").strip()

            # Extract metadata
            for line in lines:
                if "- First seen:" in line:
                    first_seen = line.replace('- First seen:', '').strip()
                elif "- Last updated:" in line:
                    last_updated = line.replace('- Last updated:', '').strip()
                elif "Status:" in line:
                    status = line.replace('- Status:', '').strip()

            # Extract content samples
            content_mode = False
            for line in lines:
                if "--- Item " in line:
                    content_mode = True
                elif content_mode and line.strip() and "---" not in line:
                    if line.startswith("- "):
                        sample = line.replace('- ', '').replace('...', '').strip()
                        if len(sample) > 20:  # Minimum length for meaningful content
                            content_samples.append(sample)

            # Create a simple summary
            summary = f"Trend: {trend_name}\n\n"

            if content_samples:
                topics = set()
                for sample in content_samples:
                    # Extract potential topic words (basic NLP)
                    words = sample.split()
                    for word in words:
                        if len(word) > 5 and word.isalpha() and word.lower() not in ['the', 'and', 'that', 'this', 'these', 'those']:
                            topics.add(word)

                summary += "This trend encompasses "
                if topics:
                    topic_list = list(topics)[:5]  # Limit to 5 topics
                    summary += f"topics related to {', '.join(topic_list)}. "
                else:
                    summary += "multiple related topics. "

                summary += f"It has been active since {first_seen}. "
                summary += f"The trend is currently {status}."

                return summary
            else:
                return "Unable to generate summary due to insufficient data."
        except Exception as e:
            logger.error(f"Error in fallback summarization: {e}")
            return "Error generating trend summary."

    def _update_trend_with_summary(self, trend_id: int, summary: str) -> bool:
        """Update the trend in the database with the generated summary."""
        if not self.db:
            return False

        try:
            cursor = self.db.cursor()
            cursor.execute("SELECT update_trend_with_summary(%s, %s)", (trend_id, summary))
            self.db.commit()
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"Error updating trend with summary: {e}")
            self.db.rollback()
            return False

    def calculate_semantic_velocity(self,
                                   trend_id: Optional[int] = None,
                                   time_window_hours: int = 168) -> List[Dict[str, Any]]:
        """
        Calculate semantic velocity for one or all trends.

        Args:
            trend_id: Optional specific trend ID to analyze, or None for all active trends
            time_window_hours: Time window in hours for velocity calculation

        Returns:
            List of dictionaries with velocity metrics for trends
        """
        if not self.db:
            return []

        cursor = None
        results = []
        try:
            # Convert hours to SQL interval string
            time_window = f"{time_window_hours} hours"

            cursor = self.db.cursor(cursor_factory=DictCursor)

            if trend_id:
                # Calculate for a specific trend
                cursor.execute(
                    "SELECT * FROM calculate_semantic_velocity(%s, %s::interval)",
                    (trend_id, time_window)
                )
                row = cursor.fetchone()
                if row:
                    results.append({
                        "trend_id": trend_id,
                        "velocity": float(row["velocity"]),
                        "semantic_shift": float(row["semantic_shift"]),
                        "growth_rate": float(row["growth_rate"])
                    })
            else:
                # Calculate for all active trends
                cursor.execute(
                    """
                    SELECT
                        t.id as trend_id,
                        t.name,
                        (calculate_semantic_velocity(t.id, %s::interval)).*
                    FROM
                        trends t
                    WHERE
                        t.status = 'active'
                    """,
                    (time_window,)
                )

                for row in cursor:
                    results.append(dict(row))
        except Exception as e:
            logger.error(f"Error calculating semantic velocity: {e}")
        finally:
            if cursor:
                cursor.close()

        return results

    def identify_sleeper_trends(
        self,
        cohesion_threshold: float = 0.75,
        min_items: int = 3,
        max_items: int = 20,
        max_velocity: float = 0.3,
        max_age_days: int = 90,
    ) -> List[Dict[str, Any]]:
        """
        Identify potential "sleeper" trends with high coherence but low velocity.

        Args:
            cohesion_threshold: Minimum coherence score (0-1)
            min_items: Minimum number of items in a trend
            max_items: Maximum number of items in a trend
            max_velocity: Maximum velocity for a trend to be considered a "sleeper"
            max_age_days: Maximum age in days for a trend to be considered

        Returns:
            List of dictionaries with sleeper trend information
        """
        if not self.db:
            return []

        cursor = None
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)
            cursor.execute(
                """
                SELECT * FROM identify_sleeper_trends(%s, %s, %s, %s)
                WHERE first_seen_at >= NOW() - %s::interval
                """,
                (cohesion_threshold, min_items, max_items, max_velocity, f"{max_age_days} days")
            )

            return [dict(row) for row in cursor]
        except Exception as e:
            logger.error(f"Error identifying sleeper trends: {e}")
            return []
        finally:
            if cursor:
                cursor.close()

    def detect_semantic_shifts(
        self,
        trend_id: Optional[int] = None,
        min_periods: int = 3,
        min_items_per_period: int = 5,
        shift_threshold: float = 0.15
    ) -> List[Dict[str, Any]]:
        """
        Detect significant semantic shifts in trends over time.

        Args:
            trend_id: Optional specific trend ID to analyze, or None for all active trends
            min_periods: Minimum number of time periods to analyze
            min_items_per_period: Minimum number of items per period
            shift_threshold: Minimum semantic distance to consider a shift

        Returns:
            List of dictionaries with semantic shift information
        """
        if not self.db:
            return []

        cursor = None
        results = []
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)

            # Create a time window interval

            if trend_id:
                # Analyze a specific trend
                cursor.execute(
                    "SELECT * FROM detect_semantic_shifts(%s, %s, %s) WHERE trend_id = %s",
                    (shift_threshold, min_periods, min_items_per_period, trend_id)
                )
                row = cursor.fetchone()
                if row:
                    results.append(dict(row))
            else:
                # Analyze all active trends
                cursor.execute(
                    """
                    SELECT * FROM detect_semantic_shifts(%s, %s, %s)
                    ORDER BY shift_magnitude DESC
                    """,
                    (shift_threshold, min_periods, min_items_per_period)
                )

                for row in cursor:
                    results.append(dict(row))
        except Exception as e:
            logger.error(f"Error detecting semantic shifts: {e}")
        finally:
            if cursor:
                cursor.close()

        return results

    def get_top_velocity_trends(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top trends by semantic velocity prediction score.

        Args:
            limit: Maximum number of trends to return

        Returns:
            List of dictionaries with trend information
        """
        if not self.db:
            return []

        cursor = None
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)
            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.metadata->'semantic_velocity'->>'velocity' AS velocity,
                    t.metadata->'semantic_velocity'->>'growth_rate' AS growth_rate,
                    t.metadata->'semantic_velocity'->>'prediction_score' AS prediction_score,
                    t.metadata->'semantic_velocity'->>'calculated_at' AS calculated_at
                FROM
                    trends t
                WHERE
                    t.status IN ('emerging', 'growing', 'peaking') AND
                    t.metadata->'semantic_velocity'->>'prediction_score' IS NOT NULL
                ORDER BY
                    (t.metadata->'semantic_velocity'->>'prediction_score')::float DESC
                LIMIT %s
                """,
                (limit,)
            )

            results = [dict(row) for row in cursor]
            cursor.close()
            return results
        except Exception as e:
            logger.error(f"Error getting top velocity trends: {e}")
            return []

    def get_significant_semantic_shifts(self, limit: int = 5, min_magnitude: float = 0.25) -> List[Dict[str, Any]]:
        """
        Get trends with significant semantic shifts.

        Args:
            limit: Maximum number of trends to return
            min_magnitude: Minimum shift magnitude to include

        Returns:
            List of dictionaries with trend information
        """
        if not self.db:
            return []

        cursor = None
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)
            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.metadata->'semantic_shift'->>'magnitude' AS shift_magnitude,
                    t.metadata->'semantic_shift'->>'direction' AS shift_direction,
                    t.metadata->'semantic_shift'->>'start_date' AS shift_start_date,
                    t.metadata->'semantic_shift'->>'end_date' AS shift_end_date,
                    t.metadata->'semantic_shift'->>'analyzed_at' AS analyzed_at
                FROM
                    trends t
                WHERE
                    t.metadata->'semantic_shift'->>'detected' = 'true' AND
                    (t.metadata->'semantic_shift'->>'magnitude')::float >= %s
                ORDER BY
                    (t.metadata->'semantic_shift'->>'magnitude')::float DESC
                LIMIT %s
                """,
                (min_magnitude, limit)
            )

            results = [dict(row) for row in cursor]
            cursor.close()
            return results
        except Exception as e:
            logger.error(f"Error getting semantic shifts: {e}")
            return []

    def get_top_sleeper_trends(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top potential sleeper trends.

        Args:
            limit: Maximum number of trends to return

        Returns:
            List of dictionaries with trend information
        """
        if not self.db:
            return []

        cursor = None
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)
            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.metadata->'sleeper_analysis'->>'coherence_score' AS coherence_score,
                    t.metadata->'sleeper_analysis'->>'velocity_score' AS velocity_score,
                    t.metadata->'sleeper_analysis'->>'sleeper_score' AS sleeper_score,
                    t.metadata->'sleeper_analysis'->>'analyzed_at' AS analyzed_at
                WHERE
                    t.metadata->'sleeper_analysis'->>'sleeper_score' IS NOT NULL
                ORDER BY
                    (t.metadata->'sleeper_analysis'->>'sleeper_score')::float DESC
                LIMIT %s
                """,
                (limit,)
            )

            results = [dict(row) for row in cursor]
            return results
        except Exception as e:
            logger.error(f"Error getting top sleeper trends: {e}")
            return []

    def run_comprehensive_analysis(self, summarize_top_trends: bool = True) -> Dict[str, Any]:
        """
        Run all advanced trend analysis features.

        Args:
            summarize_top_trends: Whether to generate LLM summaries for top trends

        Returns:
            Dictionary with results from all analyses
        """
        results = {
            "velocity_trends": [],
            "sleeper_trends": [],
            "semantic_shifts": [],
            "summarized_trends": []
        }

        try:
            # Calculate semantic velocity for all active trends
            logger.info("Calculating semantic velocity for active trends...")
            velocity_results = self.calculate_semantic_velocity()
            results["velocity_trends"] = self.get_top_velocity_trends()
            logger.info(f"Calculated velocity for {len(velocity_results)} trends")

            # Identify sleeper trends
            logger.info("Identifying potential sleeper trends...")
            sleeper_results = self.identify_sleeper_trends()
            results["sleeper_trends"] = sleeper_results
            logger.info(f"Identified {len(sleeper_results)} potential sleeper trends")

            # Detect semantic shifts
            logger.info("Detecting semantic shifts in trends...")
            shift_results = self.detect_semantic_shifts()
            results["semantic_shifts"] = shift_results
            logger.info(f"Analyzed shifts for {len(shift_results)} trends")

            # Summarize top trends if requested
            if summarize_top_trends and OPENAI_AVAILABLE:
                logger.info("Generating summaries for top trends...")
                # Get top trends from velocity and sleeper analysis
                top_trend_ids = []

                for trend in results["velocity_trends"][:3]:
                    if "id" in trend:
                        top_trend_ids.append(trend["id"])

                for trend in results["sleeper_trends"][:2]:
                    if "id" in trend and trend["id"] not in top_trend_ids:
                        top_trend_ids.append(trend["id"])

                for trend_id in top_trend_ids:
                    summary = self.summarize_trend(trend_id)
                    if summary:
                        results["summarized_trends"].append({
                            "trend_id": trend_id,
                            "summary": summary
                        })

                logger.info(f"Generated {len(results['summarized_trends'])} trend summaries")

        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {e}")

        return results

    def __init__(self, db, llm_service: str = 'fallback', vector_dimensions: int = 768):
        """
        Initialize the TrendAnalytics module.

        Args:
            db: Database connection
            llm_service: LLM service to use for summarization ('openai', or 'fallback')
            vector_dimensions: Dimensions of vector embeddings
        """
        self.db = db
        self.vector_dimensions = vector_dimensions
        self.llm_service = llm_service

        # Check if required SQL functions exist, create them if not
        self._ensure_sql_functions()

    def _ensure_sql_functions(self) -> None:
        """Ensure all required SQL functions exist in the database."""
        try:
            cursor = self.db.cursor()

            # Check if zero_vector function exists
            cursor.execute("""
                SELECT 1
                FROM pg_proc p
                JOIN pg_namespace n ON p.pronamespace = n.oid
                WHERE p.proname = 'zero_vector'
            """)

            if not cursor.fetchone():
                logger.info("Installing SQL functions for advanced trend analysis...")

                # Read SQL file
                try:
                    sql_file_path = os.path.join(
                        os.path.dirname(os.path.abspath(__file__)),
                        "trend_analysis_features.sql"
                    )
                    with open(sql_file_path, "r") as f:
                        sql = f.read()

                    cursor.execute(sql)
                    self.db.commit()
                    logger.info("SQL functions installed successfully")
                except Exception as e:
                    logger.error(f"Error installing SQL functions: {e}")
                    self.db.rollback()

        except Exception as e:
            logger.error(f"Error checking SQL functions: {e}")
        finally:
            cursor.close()

    def summarize_trend(self, trend_id: int) -> Optional[str]:
        """
        Generate an LLM-powered summary of a trend.

        Args:
            trend_id: The ID of the trend to summarize

        Returns:
            Summary text or None if summarization failed
        """
        try:
            # Get the prompt
            prompt = self._get_trend_summary_prompt(trend_id)
            if not prompt:
                return None

            # Generate summary using selected LLM service
            if self.llm_service == 'openai' and OPENAI_AVAILABLE:
                summary = self._summarize_with_openai(prompt)
            else:
                summary = self._summarize_with_fallback(prompt)

            if not summary:
                return None

            # Update trend with summary
            self._update_trend_with_summary(trend_id, summary)

            return summary

        except Exception as e:
            logger.error(f"Error summarizing trend {trend_id}: {e}")
            return None

    def _get_trend_summary_prompt(self, trend_id: int) -> Optional[str]:
        """Get a prompt for trend summarization."""
        try:
            cursor = self.db.cursor()
            cursor.execute("SELECT generate_trend_summary_prompt(%s)", (trend_id,))
            prompt = cursor.fetchone()[0]
            cursor.close()
            return prompt
        except Exception as e:
            logger.error(f"Error generating trend summary prompt: {e}")
            return None

    def _summarize_with_openai(self, prompt: str) -> Optional[str]:
        """Generate a summary using OpenAI API."""
        try:
            if not OPENAI_AVAILABLE:
                return self._summarize_with_fallback(prompt)

            response = openai.ChatCompletion.create(
                model="gpt-4",  # Can be configured based on needs
                messages=[
                    {"role": "system", "content": "You are a trend analyst providing concise, insightful summaries of emerging trends."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.5
            )

            if response and response.choices and len(response.choices) > 0:
                summary = response.choices[0].message.content.strip()
                return summary

            return None

        except Exception as e:
            logger.error(f"Error with OpenAI summarization: {e}")
            return self._summarize_with_fallback(prompt)

    def _summarize_with_fallback(self, prompt: str) -> str:
        """Generate a simple summary when no LLM is available."""
        try:
            # Extract info from the prompt
            lines = prompt.strip().split('\n')

            # Extract trend name
            trend_name = lines[0].replace('Summarize the following trend based on top related content:', '').strip()

            # Get metadata
            first_seen = "Unknown"
            last_updated = "Unknown"
            status = "Unknown"

            for line in lines:
                if "First seen:" in line:
                    first_seen = line.replace('- First seen:', '').strip()
                elif "Last updated:" in line:
                    last_updated = line.replace('- Last updated:', '').strip()
                elif "Status:" in line:
                    status = line.replace('- Status:', '').strip()

            # Extract content samples
            content_samples = []
            content_mode = False

            for line in lines:
                if line.startswith('Content samples:'):
                    content_mode = True
                    continue

                if content_mode and line.startswith('- '):
                    sample = line.replace('- ', '').replace('...', '').strip()
                    if sample:
                        content_samples.append(sample)

            # Create a simple summary
            summary = f"Trend: {trend_name}\n\n"
            summary += f"This trend was first observed on {first_seen} and was last updated on {last_updated}. "
            summary += f"It is currently classified as '{status}'. "

            if content_samples:
                summary += "Based on the available content, this trend appears to involve "
                summary += "topics such as " + ", ".join(content_samples[:3]) + "."

            return summary

        except Exception as e:
            logger.error(f"Error with fallback summarization: {e}")
            return "Unable to generate summary due to insufficient data."

    def _update_trend_with_summary(self, trend_id: int, summary: str) -> bool:
        """Update a trend with a generated summary."""
        try:
            cursor = self.db.cursor()
            cursor.execute("SELECT update_trend_with_summary(%s, %s)", (trend_id, summary))
            self.db.commit()
            cursor.close()
            return True
        except Exception as e:
            logger.error(f"Error updating trend with summary: {e}")
            self.db.rollback()
            return False

    def calculate_semantic_velocity(
        self,
        trend_id: Optional[int] = None,
        time_window_hours: int = 24
    ) -> List[Dict[str, Any]]:
        """
        Calculate semantic velocity for trends.

        Args:
            trend_id: Optional specific trend ID to analyze, or None for all active trends
            time_window_hours: Time window in hours for velocity calculation

        Returns:
            List of trend velocity results
        """
        try:
            results = []
            cursor = self.db.cursor(cursor_factory=DictCursor)

            # Create a time window interval
            time_window = f"{time_window_hours} hours"

            if trend_id:
                # Calculate for a specific trend
                cursor.execute(
                    "SELECT * FROM calculate_semantic_velocity(%s, %s::interval)",
                    (trend_id, time_window)
                )
                results = [dict(row) for row in cursor]
            else:
                # Calculate for all active trends
                cursor.execute(
                    """
                    SELECT id
                    FROM trends
                    WHERE status IN ('emerging', 'growing', 'peaking')
                    """
                )

                trend_ids = [row[0] for row in cursor]
                for tid in trend_ids:
                    cursor.execute(
                        "SELECT * FROM calculate_semantic_velocity(%s, %s::interval)",
                        (tid, time_window)
                    )
                    for row in cursor:
                        results.append(dict(row))

            return results

        except Exception as e:
            logger.error(f"Error calculating semantic velocity: {e}")
            return []
        finally:
            if cursor:
                cursor.close()

    def identify_sleeper_trends(
        self,
        min_age_days: int = 7,
        max_age_days: int = 90,
        min_coherence: float = 0.7,
        min_content_count: int = 5,
        max_content_count: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Identify potential sleeper trends.

        Args:
            min_age_days: Minimum age of trends to consider in days
            max_age_days: Maximum age of trends to consider in days
            min_coherence: Minimum coherence score (0-1)
            min_content_count: Minimum content items per trend
            max_content_count: Maximum content items per trend

        Returns:
            List of sleeper trend candidates
        """
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)

            min_age = f"{min_age_days} days"
            max_age = f"{max_age_days} days"

            cursor.execute(
                """
                SELECT *
                FROM identify_sleeper_trends(
                    %s::interval, %s::interval, %s, %s, %s
                )
                ORDER BY sleeper_score DESC
                """,
                (min_age, max_age, min_coherence, min_content_count, max_content_count)
            )

            results = [dict(row) for row in cursor]
            cursor.close()

            return results

        except Exception as e:
            logger.error(f"Error identifying sleeper trends: {e}")
            return []

    def detect_semantic_shifts(
        self,
        trend_id: Optional[int] = None,
        time_window_days: int = 7,
        shift_threshold: float = 0.15
    ) -> List[Dict[str, Any]]:
        """
        Detect semantic shifts in trends.

        Args:
            trend_id: Optional specific trend ID to analyze, or None for all active trends
            time_window_days: Time window in days for recent content
            shift_threshold: Threshold for considering a shift significant (0-1)

        Returns:
            List of detected semantic shifts
        """
        try:
            results = []
            cursor = self.db.cursor(cursor_factory=DictCursor)

            # Create a time window interval
            time_window = f"{time_window_days} days"

            if trend_id:
                # Detect shifts for a specific trend
                cursor.execute(
                    "SELECT * FROM detect_semantic_shifts(%s, %s::interval, %s)",
                    (trend_id, time_window, shift_threshold)
                )
                results = [dict(row) for row in cursor]
            else:
                # Detect shifts for all active trends
                cursor.execute(
                    """
                    SELECT id
                    FROM trends
                    WHERE status IN ('emerging', 'growing', 'peaking', 'fading')
                    """
                )

                trend_ids = [row[0] for row in cursor]
                for tid in trend_ids:
                    cursor.execute(
                        "SELECT * FROM detect_semantic_shifts(%s, %s::interval, %s)",
                        (tid, time_window, shift_threshold)
                    )
                    for row in cursor:
                        results.append(dict(row))

            return results

        except Exception as e:
            logger.error(f"Error detecting semantic shifts: {e}")
            return []
        finally:
            if cursor:
                cursor.close()

    def get_top_trends_by_velocity(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top trends by velocity prediction score.

        Args:
            limit: Maximum number of trends to return

        Returns:
            List of top velocity trends
        """
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)

            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.status,
                    t.metadata->'semantic_velocity'->>'magnitude' AS velocity_magnitude,
                    t.metadata->'semantic_velocity'->>'prediction_score' AS prediction_score,
                    t.metadata->'semantic_velocity'->>'acceleration' AS acceleration,
                    t.metadata->'semantic_velocity'->>'calculated_at' AS calculated_at
                FROM trends t
                WHERE
                    t.status IN ('emerging', 'growing', 'peaking') AND
                    t.metadata->'semantic_velocity'->>'prediction_score' IS NOT NULL
                ORDER BY
                    (t.metadata->'semantic_velocity'->>'prediction_score')::float DESC
                LIMIT %s
                """,
                (limit,)
            )

            results = [dict(row) for row in cursor]
            cursor.close()

            return results

        except Exception as e:
            logger.error(f"Error getting top velocity trends: {e}")
            return []

    def get_top_sleeper_trends(self, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get top sleeper trends by sleeper score.

        Args:
            limit: Maximum number of trends to return

        Returns:
            List of top sleeper trends
        """
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)

            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.first_seen_at,
                    t.status,
                    t.metadata->'sleeper_analysis'->>'sleeper_score' AS sleeper_score,
                    t.metadata->'sleeper_analysis'->>'content_count' AS content_count,
                    t.metadata->'sleeper_analysis'->>'coherence_score' AS coherence_score,
                    t.metadata->'sleeper_analysis'->>'analyzed_at' AS analyzed_at
                FROM trends t
                WHERE
                    t.metadata->'sleeper_analysis'->>'sleeper_score' IS NOT NULL
                ORDER BY
                    (t.metadata->'sleeper_analysis'->>'sleeper_score')::float DESC
                LIMIT %s
                """,
                (limit,)
            )

            results = [dict(row) for row in cursor]
            cursor.close()

            return results

        except Exception as e:
            logger.error(f"Error getting top sleeper trends: {e}")
            return []

    def get_significant_semantic_shifts(self, min_magnitude: float = 0.2, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get trends with significant semantic shifts.

        Args:
            min_magnitude: Minimum shift magnitude to consider significant
            limit: Maximum number of trends to return

        Returns:
            List of trends with significant shifts
        """
        try:
            cursor = self.db.cursor(cursor_factory=DictCursor)

            cursor.execute(
                """
                SELECT
                    t.id,
                    t.name,
                    t.description,
                    t.status,
                    t.metadata->'semantic_shift'->>'detected' AS shift_detected,
                    t.metadata->'semantic_shift'->>'magnitude' AS shift_magnitude,
                    t.metadata->'semantic_shift'->>'unique_keywords' AS unique_keywords,
                    t.metadata->'semantic_shift'->>'analyzed_at' AS analyzed_at
                FROM trends t
                WHERE
                    t.metadata->'semantic_shift'->>'detected' = 'true' AND
                    (t.metadata->'semantic_shift'->>'magnitude')::float >= %s
                ORDER BY
                    (t.metadata->'semantic_shift'->>'magnitude')::float DESC
                LIMIT %s
                """,
                (min_magnitude, limit)
            )

            results = [dict(row) for row in cursor]
            cursor.close()

            return results

        except Exception as e:
            logger.error(f"Error getting semantic shift trends: {e}")
            return []

    def analyze_all_trends(self) -> Dict[str, Any]:
        """
        Perform a comprehensive analysis on all trends.

        Returns:
            Dictionary with analysis results
        """
        start_time = time.time()

        # Results container
        results = {
            "velocity_analysis": [],
            "sleeper_trends": [],
            "semantic_shifts": [],
            "summarized_trends": []
        }

        try:
            # 1. Calculate semantic velocity for all active trends
            logger.info("Calculating semantic velocity for active trends...")
            velocity_results = self.calculate_semantic_velocity()
            results["velocity_analysis"] = velocity_results
            logger.info(f"Analyzed velocities for {len(velocity_results)} trends")

            # 2. Identify sleeper trends
            logger.info("Identifying sleeper trends...")
            sleeper_results = self.identify_sleeper_trends()
            results["sleeper_trends"] = sleeper_results
            logger.info(f"Found {len(sleeper_results)} potential sleeper trends")

            # 3. Detect semantic shifts
            logger.info("Detecting semantic shifts...")
            shift_results = self.detect_semantic_shifts()
            results["semantic_shifts"] = shift_results
            logger.info(f"Analyzed shifts for {len(shift_results)} trends")

            # 4. Summarize top trends if LLM is available
            if OPENAI_AVAILABLE:
                logger.info("Generating trend summaries with LLM...")
                # Get top trends from velocity and sleeper analysis
                top_trend_ids = []

                # Add top velocity trends
                top_velocity = self.get_top_trends_by_velocity(3)
                for trend in top_velocity:
                    if trend["id"] not in top_trend_ids:
                        top_trend_ids.append(trend["id"])

                # Add top sleeper trends
                top_sleepers = self.get_top_sleeper_trends(2)
                for trend in top_sleepers:
                    if trend["id"] not in top_trend_ids:
                        top_trend_ids.append(trend["id"])

                # Summarize each top trend
                for trend_id in top_trend_ids:
                    summary = self.summarize_trend(trend_id)
                    if summary:
                        results["summarized_trends"].append({
                            "trend_id": trend_id,
                            "summary": summary
                        })

                logger.info(f"Generated summaries for {len(results['summarized_trends'])} trends")

            elapsed_time = time.time() - start_time
            logger.info(f"Comprehensive trend analysis completed in {elapsed_time:.2f} seconds")

            return results

        except Exception as e:
            logger.error(f"Error in comprehensive trend analysis: {e}")
            return results


def test_trend_analytics():
    """Test the TrendAnalytics functionality with a mock database."""
    import psycopg2

    # Use environment variables for connection or defaults
    db_host = os.environ.get('PGHOST', 'localhost')
    db_port = os.environ.get('PGPORT', '5432')
    db_name = os.environ.get('PGDATABASE', 'trend_crawler')
    db_user = os.environ.get('PGUSER', 'postgres')
    db_password = os.environ.get('PGPASSWORD', 'postgres')

    try:
        # Connect to database
        db = psycopg2.connect(
            host=db_host,
            port=db_port,
            dbname=db_name,
            user=db_user,
            password=db_password
        )

        logger.info("Connected to database")

        # Initialize TrendAnalytics
        analytics = TrendAnalytics(db)

        # Run comprehensive analysis
        results = analytics.analyze_all_trends()

        # Print results
        print("\n=== Trend Analytics Results ===")

        if results["velocity_analysis"]:
            print("\nTop trends by velocity:")
            top_velocity = analytics.get_top_trends_by_velocity(3)
            for i, trend in enumerate(top_velocity):
                print(f"{i+1}. {trend.get('name', 'Unnamed')} - Score: {trend.get('prediction_score', '0.0')}")
                print(f"   Velocity: {trend.get('velocity_magnitude', '0.0')}, Acceleration: {trend.get('acceleration', '0.0')}")

        if results["sleeper_trends"]:
            print("\nPotential sleeper trends:")
            top_sleepers = analytics.get_top_sleeper_trends(3)
            for i, trend in enumerate(top_sleepers):
                print(f"{i+1}. {trend.get('name', 'Unnamed')} - Score: {trend.get('sleeper_score', '0.0')}")
                print(f"   First seen: {trend.get('first_seen_at', 'Unknown')}")
                print(f"   Coherence: {trend.get('coherence_score', '0.0')}")

        shift_trends = analytics.get_significant_semantic_shifts()
        if shift_trends:
            print("\nTrends with semantic shifts:")
            for i, trend in enumerate(shift_trends):
                print(f"{i+1}. {trend.get('name', 'Unnamed')} - Magnitude: {trend.get('shift_magnitude', '0.0')}")
                print(f"   New keywords: {trend.get('unique_keywords', '[]')}")

        if results["summarized_trends"]:
            print("\nTrend summaries:")
            for i, trend in enumerate(results["summarized_trends"]):
                print(f"\n--- Trend {trend['trend_id']} summary ---")
                print(trend["summary"])

        # Close connection
        db.close()
        logger.info("Test completed successfully")

    except Exception as e:
        logger.error(f"Test error: {e}")


if __name__ == "__main__":
    # Run test if executed directly
    test_trend_analytics()
