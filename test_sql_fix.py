#!/usr/bin/env python3
"""
Test script to verify the SQL type mismatch fix in monitoring.py
"""

import json
import psycopg2
from datetime import datetime, timedelta
import sys

def test_sql_fix():
    """Test the fixed SQL query that was causing type mismatch errors"""
    
    print("=" * 60)
    print("Testing SQL Type Mismatch Fix")
    print("=" * 60)
    
    try:
        # Load proxy config
        with open('proxy_config.json') as f:
            config = json.load(f)

        db_config = config['db']
        print(f"✓ Loaded database config")
        print(f"  User: {db_config['user']}")
        print(f"  Database: {db_config['name']}")
        
        # Test connection
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['name'],
            user=db_config['user'],
            password=db_config['password']
        )
        print("✓ Database connection successful")
        
        cursor = conn.cursor()
        
        # Test the exact query from monitoring.py that was causing the error
        proxy_id = 'test_proxy_123'
        time_limit = datetime.now() - timedelta(hours=24)
        
        print(f"\n🔍 Testing query with:")
        print(f"  proxy_id: {proxy_id} (type: {type(proxy_id)})")
        print(f"  time_limit: {time_limit} (type: {type(time_limit)})")
        
        # This is the FIXED query (removed ::text casting)
        print("\n📝 Executing FIXED query (without ::text casting):")
        cursor.execute('''
            SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = TRUE THEN 1 ELSE 0 END) as successful_requests,
                SUM(CASE WHEN success = FALSE THEN 1 ELSE 0 END) as failed_requests,
                AVG(response_time) as avg_response_time,
                MIN(response_time) as min_response_time,
                MAX(response_time) as max_response_time
            FROM proxy_metrics
            WHERE proxy_id = %s AND timestamp > %s
        ''', (str(proxy_id), time_limit))
        
        result = cursor.fetchone()
        print("✅ SUCCESS: SQL query executed without type mismatch error!")
        print(f"📊 Query result: {result}")
        
        # Test the monitoring module directly
        print("\n🔧 Testing monitoring module integration:")
        sys.path.append('.')
        from monitoring import Monitor
        
        monitor = Monitor()
        print("✓ Monitor instance created successfully")
        
        stats = monitor.get_proxy_stats('test_proxy_456')
        print("✅ get_proxy_stats method executed successfully!")
        print(f"📈 Stats result: {stats}")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED - SQL FIX IS WORKING!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("❌ TESTS FAILED")
        print("=" * 60)
        
        return False

if __name__ == "__main__":
    success = test_sql_fix()
    sys.exit(0 if success else 1)
