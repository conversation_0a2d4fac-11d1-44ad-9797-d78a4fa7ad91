#!/usr/bin/env python3
"""
Vector Store Manager for trend-crawler

This module handles vector embeddings and PostgreSQL with pgvector operations.
It provides functionality for:
1. Connecting to PostgreSQL with pgvector extension
2. Generating embeddings from text content
3. Storing embeddings and related data
4. Performing similarity searches and trend discovery
"""

import os
import logging
import json
import time
from typing import Dict, List, Tuple, Optional, Any, Union, Set
import numpy as np
import psycopg2
from psycopg2 import pool
from psycopg2.extras import execute_values
from sentence_transformers import SentenceTransformer
import torch
from datetime import datetime
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default model settings
DEFAULT_MODEL_NAME = "all-MiniLM-L6-v2"  # 384-dimensional embeddings
DEFAULT_MODEL_VERSION = "minilm_l6_v2"  # Version tag

class VectorStoreManager:
    """
    Manages vector embeddings and PostgreSQL with pgvector operations.
    """
    
    def __init__(self, 
                 db_host: str = None,
                 db_port: str = None, 
                 db_name: str = None, 
                 db_user: str = None, 
                 db_password: str = None,
                 min_pool_size: int = 1,
                 max_pool_size: int = 10,
                 model_name: str = DEFAULT_MODEL_NAME,
                 model_version: str = DEFAULT_MODEL_VERSION):
        """
        Initialize the Vector Store Manager.

        Args:
            db_host: PostgreSQL database host
            db_port: PostgreSQL database port
            db_name: PostgreSQL database name
            db_user: PostgreSQL database username
            db_password: PostgreSQL database password
            min_pool_size: Minimum database connection pool size
            max_pool_size: Maximum database connection pool size
            model_name: Sentence transformer model name
            model_version: Model version tag
        """
        # Load configuration from environment variables if not provided
        self.db_host = db_host or os.getenv('DB_HOST', 'localhost')
        self.db_port = db_port or os.getenv('DB_PORT', '5432')
        self.db_name = db_name or os.getenv('DB_NAME', 'trend_crawler')
        self.db_user = db_user or os.getenv('DB_USER', 'postgres')
        self.db_password = db_password or os.getenv('DB_PASSWORD', 'postgres')
        
        self.model_name = model_name
        self.model_version = model_version
        
        # Initialize connection pool
        try:
            self.conn_pool = psycopg2.pool.SimpleConnectionPool(
                min_pool_size,
                max_pool_size,
                host=self.db_host,
                port=self.db_port,
                dbname=self.db_name,
                user=self.db_user,
                password=self.db_password
            )
            logger.info(f"PostgreSQL connection pool initialized with {max_pool_size} connections")
        except Exception as e:
            logger.error(f"Failed to initialize database connection pool: {e}")
            self.conn_pool = None
            raise
        
        # Initialize embedding model
        try:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            logger.info(f"Using device: {self.device}")
            
            self.embedding_model = SentenceTransformer(self.model_name, device=self.device)
            self.embedding_dim = self.embedding_model.get_sentence_embedding_dimension()
            logger.info(f"Initialized {self.model_name} with dimension {self.embedding_dim}")
            
            # Verify that the model dimension matches what's expected in the database schema
            if self.model_name == DEFAULT_MODEL_NAME and self.embedding_dim != 384:
                logger.warning(f"Model dimension {self.embedding_dim} doesn't match expected dimension 384")
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            self.embedding_model = None
            raise
    
    def __del__(self):
        """Clean up resources when the object is destroyed."""
        if hasattr(self, 'conn_pool') and self.conn_pool:
            self.conn_pool.closeall()
            logger.info("Closed all database connections")
    
    def get_connection(self):
        """Get a connection from the pool."""
        if not self.conn_pool:
            raise ValueError("Database connection pool not initialized")
        return self.conn_pool.getconn()
    
    def release_connection(self, conn):
        """Return a connection to the pool."""
        if self.conn_pool and conn:
            self.conn_pool.putconn(conn)
    
    def setup_schema(self):
        """
        Set up the PostgreSQL schema with pgvector extension.
        """
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                # Read the SQL schema file
                schema_path = os.path.join(os.path.dirname(__file__), 'pgvector_schema.sql')
                with open(schema_path, 'r') as f:
                    schema_sql = f.read()
                
                # Execute the SQL script
                cur.execute(schema_sql)
                conn.commit()
            logger.info("Successfully set up pgvector schema")
            return True
        except Exception as e:
            logger.error(f"Error setting up pgvector schema: {e}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                self.release_connection(conn)
    
    def generate_embedding(self, text: str) -> np.ndarray:
        """
        Generate an embedding vector for the given text.
        
        Args:
            text: The text to embed
            
        Returns:
            The embedding vector as a numpy array
        """
        if not self.embedding_model:
            raise ValueError("Embedding model not initialized")
        
        # Handle empty or None text
        if not text or not text.strip():
            logger.warning("Empty text provided for embedding")
            return np.zeros(self.embedding_dim)
        
        # Handle very long texts - implement a chunking strategy
        words = text.split()
        if len(words) > 500:  # Rough estimate for token limit
            logger.info(f"Long text detected ({len(words)} words), using chunking strategy")
            return self._embed_long_text(text)
        
        # Standard embedding for normal length text
        try:
            with torch.no_grad():
                embedding = self.embedding_model.encode([text])[0]
            return embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    def _embed_long_text(self, text: str) -> np.ndarray:
        """
        Handle embedding of long texts by chunking.
        
        Args:
            text: Long text to embed
            
        Returns:
            The embedding vector as a numpy array
        """
        # Simple chunking by splitting text into sentences or paragraphs
        chunks = self._split_into_chunks(text)
        
        if not chunks:
            logger.warning("No valid chunks extracted from text")
            return np.zeros(self.embedding_dim)
        
        # Embed each chunk
        try:
            with torch.no_grad():
                chunk_embeddings = self.embedding_model.encode(chunks)
            
            # Average the chunk embeddings
            avg_embedding = np.mean(chunk_embeddings, axis=0)
            return avg_embedding
        except Exception as e:
            logger.error(f"Error embedding long text: {e}")
            raise
    
    def _split_into_chunks(self, text: str, max_chunk_length: int = 256) -> List[str]:
        """
        Split long text into chunks, preferably at sentence or paragraph boundaries.
        
        Args:
            text: Text to split
            max_chunk_length: Maximum number of words per chunk
            
        Returns:
            List of text chunks
        """
        # Try to split by paragraphs first
        paragraphs = text.split('\n\n')
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for para in paragraphs:
            para_words = para.split()
            para_length = len(para_words)
            
            if current_length + para_length <= max_chunk_length:
                # Add to current chunk
                current_chunk.append(para)
                current_length += para_length
            elif para_length > max_chunk_length:
                # Paragraph itself is too long, need to split it
                if current_chunk:
                    # Save the current chunk first
                    chunks.append(' '.join(current_chunk))
                    current_chunk = []
                    current_length = 0
                
                # Split paragraph into smaller pieces
                words = para.split()
                for i in range(0, len(words), max_chunk_length):
                    chunk = ' '.join(words[i:i + max_chunk_length])
                    chunks.append(chunk)
            else:
                # Start a new chunk with this paragraph
                if current_chunk:
                    chunks.append(' '.join(current_chunk))
                current_chunk = [para]
                current_length = para_length
        
        # Don't forget the last chunk
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        return chunks
    
    def store_content(self, 
                     source_platform: str,
                     source_item_id: str,
                     content_text: str,
                     url: str = None,
                     full_content: Dict = None,
                     author_id: str = None,
                     author_username: str = None,
                     published_at: datetime = None,
                     language: str = 'en',
                     keywords: List[str] = None,
                     sentiment_score: float = None,
                     sentiment_label: str = None,
                     engagement_metrics: Dict = None,
                     embedding: np.ndarray = None) -> int:
        """
        Store content with its embedding in the database.
        
        Args:
            source_platform: Source platform name (e.g. 'twitter', 'reddit')
            source_item_id: Original ID from the source
            content_text: The text content to embed
            url: URL of the content (optional)
            full_content: Complete raw response/data
            author_id: Author ID
            author_username: Author username
            published_at: Content publication timestamp
            language: Content language code
            keywords: List of keywords or hashtags
            sentiment_score: Sentiment score (-1.0 to 1.0)
            sentiment_label: Sentiment label (positive, negative, neutral)
            engagement_metrics: Dictionary of engagement metrics
            embedding: Pre-computed embedding vector (optional)
            
        Returns:
            ID of the inserted record
        """
        if not embedding and content_text:
            # Generate embedding if not provided
            embedding = self.generate_embedding(content_text)
        
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                # Convert data to appropriate format
                if full_content and isinstance(full_content, dict):
                    full_content_json = json.dumps(full_content)
                else:
                    full_content_json = None
                    
                if engagement_metrics and isinstance(engagement_metrics, dict):
                    engagement_metrics_json = json.dumps(engagement_metrics)
                else:
                    engagement_metrics_json = None
                
                # Set status based on embedding availability
                status = 'embedding_complete' if embedding is not None else 'pending_embedding'
                
                query = """
                INSERT INTO crawled_content (
                    source_platform, source_item_id, content_text, url, full_content,
                    author_id, author_username, published_at, scraped_at, language,
                    embedding, engagement_metrics, keywords, sentiment_score, sentiment_label,
                    status, model_version, embedding_generated_at
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, NOW(), %s,
                    %s, %s, %s, %s, %s,
                    %s, %s, %s
                )
                ON CONFLICT (source_platform, source_item_id) DO UPDATE SET
                    content_text = EXCLUDED.content_text,
                    url = EXCLUDED.url,
                    full_content = EXCLUDED.full_content,
                    author_id = EXCLUDED.author_id,
                    author_username = EXCLUDED.author_username,
                    published_at = EXCLUDED.published_at,
                    language = EXCLUDED.language,
                    embedding = EXCLUDED.embedding,
                    engagement_metrics = EXCLUDED.engagement_metrics,
                    keywords = EXCLUDED.keywords,
                    sentiment_score = EXCLUDED.sentiment_score,
                    sentiment_label = EXCLUDED.sentiment_label,
                    status = EXCLUDED.status,
                    model_version = EXCLUDED.model_version,
                    embedding_generated_at = EXCLUDED.embedding_generated_at,
                    scraped_at = NOW()
                RETURNING id;
                """
                
                # Execute the query with parameters
                cur.execute(query, (
                    source_platform, source_item_id, content_text, url, full_content_json,
                    author_id, author_username, published_at, language,
                    embedding.tolist() if embedding is not None else None, 
                    engagement_metrics_json,
                    keywords, sentiment_score, sentiment_label,
                    status, self.model_version if status == 'embedding_complete' else None,
                    datetime.now() if status == 'embedding_complete' else None
                ))
                
                content_id = cur.fetchone()[0]
                conn.commit()
                
                return content_id
                
        except Exception as e:
            logger.error(f"Error storing content: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                self.release_connection(conn)
    
    def batch_store_content(self, items: List[Dict]) -> List[int]:
        """
        Store multiple content items with their embeddings in batch.
        
        Args:
            items: List of dictionaries with content data
            
        Returns:
            List of inserted record IDs
        """
        conn = None
        content_ids = []
        
        try:
            conn = self.get_connection()
            
            # First, prepare the texts for bulk embedding
            texts_to_embed = []
            items_without_embeddings = []
            
            for item in items:
                if 'embedding' not in item or item['embedding'] is None:
                    if 'content_text' in item and item['content_text']:
                        texts_to_embed.append(item['content_text'])
                        items_without_embeddings.append(item)
            
            # Generate embeddings in bulk if needed
            if texts_to_embed:
                logger.info(f"Generating embeddings for {len(texts_to_embed)} items in batch")
                embeddings = self.embedding_model.encode(texts_to_embed)
                
                # Assign embeddings back to items
                for idx, embedding in enumerate(embeddings):
                    items_without_embeddings[idx]['embedding'] = embedding
            
            # Now store all items with their embeddings
            with conn.cursor() as cur:
                for item in items:
                    content_id = self.store_content(
                        conn=conn,
                        cursor=cur,
                        **item
                    )
                    content_ids.append(content_id)
                
                conn.commit()
            
            return content_ids
            
        except Exception as e:
            logger.error(f"Error batch storing content: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                self.release_connection(conn)
    
    def find_similar_content(self, 
                            query_text: str = None,
                            query_embedding: np.ndarray = None,
                            filter_params: Dict = None,
                            limit: int = 10,
                            similarity_threshold: float = 0.7) -> List[Dict]:
        """
        Find content similar to the query text or embedding.
        
        Args:
            query_text: Text to find similar content for (will be embedded)
            query_embedding: Pre-computed embedding vector
            filter_params: Dictionary of filter parameters (e.g. source_platform, language)
            limit: Maximum number of results to return
            similarity_threshold: Minimum similarity score (0-1)
            
        Returns:
            List of similar content items with similarity scores
        """
        if query_text is None and query_embedding is None:
            raise ValueError("Either query_text or query_embedding must be provided")
        
        if query_embedding is None:
            query_embedding = self.generate_embedding(query_text)
        
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                # Construct the WHERE clause based on filter parameters
                where_clauses = []
                params = [query_embedding.tolist()]  # First parameter is always the query embedding
                
                if filter_params:
                    for key, value in filter_params.items():
                        if key == 'source_platform':
                            where_clauses.append(f"source_platform = %s")
                            params.append(value)
                        elif key == 'language':
                            where_clauses.append(f"language = %s")
                            params.append(value)
                        elif key == 'time_range':
                            where_clauses.append(f"published_at >= NOW() - %s::INTERVAL")
                            params.append(value)  # e.g. '1 day', '1 week'
                        elif key == 'keywords':
                            where_clauses.append(f"keywords && %s")
                            params.append(value if isinstance(value, list) else [value])
                
                # Always filter for records with embeddings
                where_clauses.append("embedding IS NOT NULL")
                where_clauses.append("status = 'embedding_complete'")
                
                where_clause = " AND ".join(where_clauses) if where_clauses else "TRUE"
                
                # Construct the final query with cosine similarity
                query = f"""
                SELECT
                    id,
                    source_platform,
                    source_item_id,
                    content_text,
                    url,
                    author_username,
                    published_at,
                    sentiment_score,
                    sentiment_label,
                    1 - (embedding <=> %s) AS similarity
                FROM crawled_content
                WHERE {where_clause}
                AND 1 - (embedding <=> %s) > %s  -- Cosine similarity threshold
                ORDER BY similarity DESC
                LIMIT %s;
                """
                
                # Add parameters for similarity threshold and limit
                params.append(query_embedding.tolist())  # Same query embedding for the threshold
                params.append(similarity_threshold)
                params.append(limit)
                
                # Execute the query
                cur.execute(query, params)
                results = cur.fetchall()
                
                # Convert to list of dictionaries
                columns = [desc[0] for desc in cur.description]
                similar_items = []
                for row in results:
                    item = dict(zip(columns, row))
                    similar_items.append(item)
                
                return similar_items
                
        except Exception as e:
            logger.error(f"Error finding similar content: {e}")
            raise
        finally:
            if conn:
                self.release_connection(conn)
    
    def find_or_create_trend(self, content_ids: List[int], name: str = None, description: str = None) -> Dict:
        """
        Find an existing trend that matches the content, or create a new one.
        
        Args:
            content_ids: IDs of content items in this trend
            name: Optional name for the trend
            description: Optional description
            
        Returns:
            Trend information dictionary
        """
        if not content_ids:
            raise ValueError("At least one content ID must be provided")
        
        conn = None
        try:
            conn = self.get_connection()
            
            # First, get embeddings for the content items
            embeddings = []
            with conn.cursor() as cur:
                query = "SELECT embedding FROM crawled_content WHERE id = ANY(%s) AND embedding IS NOT NULL"
                cur.execute(query, (content_ids,))
                for row in cur.fetchall():
                    embeddings.append(np.array(row[0]))
            
            if not embeddings:
                raise ValueError("No valid embeddings found for the provided content IDs")
            
            # Compute a centroid (average) embedding for the trend
            trend_embedding = np.mean(embeddings, axis=0)
            
            # Normalize the trend embedding (for cosine similarity)
            trend_embedding_norm = trend_embedding / np.linalg.norm(trend_embedding)
            
            # Try to find an existing trend that's similar enough
            similar_trend = None
            with conn.cursor() as cur:
                query = """
                SELECT
                    id,
                    name,
                    description,
                    first_seen_at,
                    last_updated_at,
                    status,
                    1 - (trend_embedding <=> %s) AS similarity
                FROM trends
                WHERE status IN ('emerging', 'growing', 'peaking')
                AND 1 - (trend_embedding <=> %s) > 0.85  -- High threshold for trend matching
                ORDER BY similarity DESC
                LIMIT 1;
                """
                cur.execute(query, (trend_embedding_norm.tolist(), trend_embedding_norm.tolist()))
                result = cur.fetchone()
                
                if result:
                    trend_id, trend_name, trend_desc, first_seen, last_updated, status, similarity = result
                    similar_trend = {
                        'id': trend_id,
                        'name': trend_name,
                        'description': trend_desc,
                        'first_seen_at': first_seen,
                        'last_updated_at': last_updated,
                        'status': status,
                        'similarity': similarity,
                        'is_new': False
                    }
            
            if similar_trend:
                # Update the existing trend
                with conn.cursor() as cur:
                    # Update the trend's last_updated_at timestamp
                    query = """
                    UPDATE trends
                    SET last_updated_at = NOW(),
                        trend_embedding = (trend_embedding + %s) / 2  -- Average with new embedding
                    WHERE id = %s
                    RETURNING id;
                    """
                    cur.execute(query, (trend_embedding_norm.tolist(), similar_trend['id']))
                    trend_id = cur.fetchone()[0]
                    
                    # Link the new content items to this trend
                    self._link_content_to_trend(conn, cur, trend_id, content_ids, trend_embedding_norm)
                    
                    conn.commit()
                    
                return similar_trend
            else:
                # Create a new trend
                if not name:
                    # Auto-generate a name if not provided
                    name = f"Trend-{uuid.uuid4().hex[:8]}"
                
                with conn.cursor() as cur:
                    # Get the earliest content item for first_seen_at
                    cur.execute(
                        "SELECT MIN(published_at) FROM crawled_content WHERE id = ANY(%s)",
                        (content_ids,)
                    )
                    first_seen_at = cur.fetchone()[0] or datetime.now()
                    
                    # Create the trend
                    query = """
                    INSERT INTO trends (
                        name, description, trend_embedding, 
                        first_seen_item_id, first_seen_at, last_updated_at
                    ) VALUES (%s, %s, %s, %s, %s, NOW())
                    RETURNING id;
                    """
                    cur.execute(query, (
                        name, 
                        description, 
                        trend_embedding_norm.tolist(),
                        content_ids[0],  # First item as representative
                        first_seen_at
                    ))
                    trend_id = cur.fetchone()[0]
                    
                    # Link all content items to this trend
                    self._link_content_to_trend(conn, cur, trend_id, content_ids, trend_embedding_norm)
                    
                    conn.commit()
                    
                    return {
                        'id': trend_id,
                        'name': name,
                        'description': description,
                        'first_seen_at': first_seen_at,
                        'last_updated_at': datetime.now(),
                        'status': 'emerging',
                        'similarity': 1.0,  # Perfect match as it's a new trend
                        'is_new': True
                    }
                
        except Exception as e:
            logger.error(f"Error finding or creating trend: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                self.release_connection(conn)
    
    def _link_content_to_trend(self, conn, cur, trend_id: int, content_ids: List[int], trend_embedding: np.ndarray):
        """
        Link content items to a trend, calculating similarity scores.
        
        Args:
            conn: Database connection
            cur: Database cursor
            trend_id: ID of the trend
            content_ids: List of content IDs
            trend_embedding: Normalized trend embedding
        """
        # Get embeddings for all content items
        cur.execute(
            "SELECT id, embedding FROM crawled_content WHERE id = ANY(%s) AND embedding IS NOT NULL",
            (content_ids,)
        )
        item_embeddings = cur.fetchall()
        
        # Prepare values for batch insert
        values = []
        for item_id, embedding in item_embeddings:
            # Calculate cosine similarity
            item_embedding = np.array(embedding)
            norm_item = np.linalg.norm(item_embedding)
            if norm_item > 0:  # Avoid division by zero
                item_embedding_norm = item_embedding / norm_item
                similarity = float(np.dot(trend_embedding, item_embedding_norm))
            else:
                similarity = 0.0
                
            values.append((trend_id, item_id, similarity))
        
        # Batch insert with ON CONFLICT DO NOTHING
        execute_values(cur, """
            INSERT INTO trend_content_map (trend_id, content_id, similarity_to_trend)
            VALUES %s
            ON CONFLICT (trend_id, content_id) DO NOTHING
        """, values)
    
    def discover_potential_trends(self, 
                                min_cluster_size: int = 3, 
                                time_window: str = '1 week',
                                min_similarity: float = 0.75) -> List[Dict]:
        """
        Discover potential new trends by clustering recent content.
        
        Args:
            min_cluster_size: Minimum number of items to form a trend
            time_window: Time window for recent content (e.g. '1 day', '1 week')
            min_similarity: Minimum similarity for items in the same cluster
            
        Returns:
            List of potential trend clusters
        """
        conn = None
        try:
            conn = self.get_connection()
            with conn.cursor() as cur:
                # Get recent content that's not yet assigned to any trend
                query = """
                SELECT cc.id, cc.content_text, cc.embedding
                FROM crawled_content cc
                LEFT JOIN trend_content_map tcm ON cc.id = tcm.content_id
                WHERE cc.scraped_at >= NOW() - %s::INTERVAL
                AND cc.embedding IS NOT NULL
                AND cc.status = 'embedding_complete'
                AND tcm.trend_id IS NULL
                LIMIT 1000;  -- Protect against processing too many at once
                """
                cur.execute(query, (time_window,))
                results = cur.fetchall()
                
                if not results:
                    logger.info("No unclassified recent content found")
                    return []
                
                # Extract data
                content_ids = []
                content_texts = []
                embeddings = []
                
                for row in results:
                    content_ids.append(row[0])
                    content_texts.append(row[1])
                    embeddings.append(np.array(row[2]))
                
                # Convert to numpy array for processing
                embeddings_array = np.array(embeddings)
                
                # Generate clusters using a simple algorithm (in production, you might use DBSCAN/HDBSCAN)
                clusters = self._simple_clustering(embeddings_array, content_ids, content_texts, 
                                                min_similarity, min_cluster_size)
                
                logger.info(f"Discovered {len(clusters)} potential trends")
                return clusters
                
        except Exception as e:
            logger.error(f"Error discovering potential trends: {e}")
            raise
        finally:
            if conn:
                self.release_connection(conn)
    
    def _simple_clustering(self, 
                          embeddings: np.ndarray,
                          content_ids: List[int],
                          content_texts: List[str],
                          min_similarity: float,
                          min_cluster_size: int) -> List[Dict]:
        """
        Simple clustering algorithm based on cosine similarity.
        
        Args:
            embeddings: Array of embedding vectors
            content_ids: List of content IDs
            content_texts: List of content texts
            min_similarity: Minimum similarity threshold
            min_cluster_size: Minimum cluster size
            
        Returns:
            List of clusters, each with item IDs and representative text
        """
        clusters = []
        remaining_indices = set(range(len(embeddings)))
        
        # Normalize embeddings for cosine similarity
        normalized_embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        while remaining_indices:
            # Pick a random seed point
            seed_idx = list(remaining_indices)[0]
            seed_embedding = normalized_embeddings[seed_idx]
            
            # Find all points similar to the seed
            similarities = np.dot(normalized_embeddings, seed_embedding)
            similar_indices = set(i for i in remaining_indices 
                                if similarities[i] >= min_similarity)
            
            # If we have enough similar items, form a cluster
            if len(similar_indices) >= min_cluster_size:
                cluster_ids = [content_ids[i] for i in similar_indices]
                cluster_texts = [content_texts[i] for i in similar_indices]
                
                # Compute centroid embedding for the cluster
                cluster_embeddings = np.array([embeddings[i] for i in similar_indices])
                centroid = np.mean(cluster_embeddings, axis=0)
                
                # Use the most central item as representative
                similarities_to_centroid = np.dot(cluster_embeddings, centroid) / (
                    np.linalg.norm(cluster_embeddings, axis=1) * np.linalg.norm(centroid))
                most_central_idx = np.argmax(similarities_to_centroid)
                
                clusters.append({
                    'content_ids': cluster_ids,
                    'representative_text': cluster_texts[most_central_idx],
                    'size': len(cluster_ids),
                    'avg_similarity': float(np.mean(similarities_to_centroid))
                })
                
                # Remove these indices from consideration
                remaining_indices -= similar_indices
            else:
                # Remove just the seed point if cluster is too small
                remaining_indices.remove(seed_idx)
        
        return clusters

# Example usage
if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize the vector store manager
    try:
        vector_store = VectorStoreManager()
        
        # Set up the schema
        vector_store.setup_schema()
        
        # Test storing a single item
        content_id = vector_store.store_content(
            source_platform="test",
            source_item_id="test123",
            content_text="This is a test of the vector store manager with pgvector.",
            url="http://example.com/test",
            author_username="tester"
        )
        print(f"Stored test content with ID: {content_id}")
        
        # Test finding similar content
        similar = vector_store.find_similar_content(
            query_text="Testing pgvector integration",
            limit=5
        )
        print(f"Found {len(similar)} similar items")
        
    except Exception as e:
        logger.error(f"Error in example usage: {e}")
