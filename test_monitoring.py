#!/usr/bin/env python3
"""
Tests for the Monitoring module.
"""

import os
import json
import time
import psycopg2
import threading
import pytest
from unittest.mock import patch, MagicMock

import monitoring
from monitoring import (
    Monitor,
    DatabaseManager,
    AlertManager,
    monitor,
    monitor_scraper
)


@pytest.fixture
def test_db_path():
    """Return a PostgreSQL connection string for testing."""
    # Use a test database
    db_path = {
        "host": "localhost",
        "database": "trend_crawler_test",
        "user": "postgres",
        "password": "postgres"
    }
    yield db_path


@pytest.fixture
def test_config(test_db_path):
    """Create a test configuration."""
    return {
        "db": {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        },
        "prometheus": {
            "enabled": False,
            "port": 9090
        },
        "alert_thresholds": {
            "error_rate": 20,
            "captcha_rate": 10,
            "blocked_rate": 15,
            "latency": 10
        },
        "alert_channels": {
            "email": {
                "enabled": False,
                "smtp_server": "smtp.example.com",
                "smtp_port": 587,
                "use_tls": True,
                "username": "<EMAIL>",
                "password": "test_password",
                "from_address": "<EMAIL>",
                "recipients": ["<EMAIL>"]
            },
            "slack": {
                "enabled": False,
                "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz"
            },
            "log": {
                "enabled": True
            }
        },
        "monitoring_interval": 1,
        "proxy_health_check_interval": 3
    }


@pytest.fixture
def mock_monitor(test_config):
    """Create a test monitor instance."""
    with patch('monitoring.CONFIG', test_config):
        # Create a new monitor instance for testing
        test_monitor = Monitor()

        # Yield the monitor for testing
        yield test_monitor

        # Cleanup
        test_monitor.stop_monitoring()


class TestDatabaseManager:
    """Tests for the DatabaseManager class."""

    def test_init_postgres(self, test_db_path):
        """Test PostgreSQL initialization."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        assert db_manager.initialized is True
        assert db_manager.connection is not None

        # Verify tables were created
        conn = psycopg2.connect(
            host=test_db_path["host"],
            database=test_db_path["database"],
            user=test_db_path["user"],
            password=test_db_path["password"]
        )
        cursor = conn.cursor()

        cursor.execute("""
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public'
        """)
        tables = [row[0] for row in cursor.fetchall()]

        assert "requests" in tables
        assert "proxy_metrics" in tables
        assert "alerts" in tables

        conn.close()

    def test_store_request_metrics(self, test_db_path):
        """Test storing request metrics."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        # Store a request metric
        metrics = {
            "scraper_name": "test_scraper",
            "url": "https://example.com",
            "status_code": 200,
            "success": True,
            "response_time": 1.234,
            "proxy_id": "test_proxy",
            "captcha_detected": False,
            "error_message": None
        }

        db_manager.store_request_metrics(metrics)

        # Verify the metric was stored
        conn = psycopg2.connect(
            host=test_db_path["host"],
            database=test_db_path["database"],
            user=test_db_path["user"],
            password=test_db_path["password"]
        )
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM requests")
        row = cursor.fetchone()

        assert row is not None
        assert row[1] == "test_scraper"  # scraper_name
        assert row[2] == "https://example.com"  # url
        assert row[4] == 200  # status_code
        assert bool(row[5]) is True  # success
        assert abs(row[6] - 1.234) < 0.001  # response_time

        conn.close()

    def test_store_proxy_metrics(self, test_db_path):
        """Test storing proxy metrics."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        # Store a proxy metric
        metrics = {
            "proxy_id": "test_proxy",
            "success": True,
            "response_time": 1.234,
            "status_code": 200,
            "error_message": None
        }

        db_manager.store_proxy_metrics(metrics)

        # Verify the metric was stored
        conn = psycopg2.connect(
            host=test_db_path["host"],
            database=test_db_path["database"],
            user=test_db_path["user"],
            password=test_db_path["password"]
        )
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM proxy_metrics")
        row = cursor.fetchone()

        assert row is not None
        assert row[1] == "test_proxy"  # proxy_id
        assert bool(row[3]) is True  # success
        assert abs(row[4] - 1.234) < 0.001  # response_time

        conn.close()

    def test_store_alert(self, test_db_path):
        """Test storing alerts."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        # Store an alert
        db_manager.store_alert("test_alert", "warning", "Test alert message")

        # Verify the alert was stored
        conn = psycopg2.connect(
            host=test_db_path["host"],
            database=test_db_path["database"],
            user=test_db_path["user"],
            password=test_db_path["password"]
        )
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM alerts")
        row = cursor.fetchone()

        assert row is not None
        assert row[2] == "test_alert"  # alert_type
        assert row[3] == "warning"  # severity
        assert row[4] == "Test alert message"  # message

        conn.close()

    def test_get_scraper_stats(self, test_db_path):
        """Test getting scraper statistics."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        # Store some request metrics
        for i in range(10):
            success = i < 7  # 7 successful, 3 failed
            captcha = i >= 8  # 2 CAPTCHAs

            metrics = {
                "scraper_name": "test_scraper",
                "url": f"https://example.com/{i}",
                "status_code": 200 if success else 403,
                "success": success,
                "response_time": 1.0 + i * 0.1,
                "proxy_id": f"test_proxy_{i % 3}",
                "captcha_detected": captcha,
                "error_message": None if success else "Test error"
            }

            db_manager.store_request_metrics(metrics)

        # Get statistics
        stats = db_manager.get_scraper_stats("test_scraper", hours=24)

        # Verify statistics
        assert stats.get('total_requests') == 10
        assert stats.get('successful_requests') == 7
        assert stats.get('failed_requests') == 3
        assert round(stats.get('error_rate')) == 30  # 30%
        assert stats.get('captcha_count') == 2
        assert round(stats.get('captcha_rate')) == 20  # 20%
        assert 1.0 < stats.get('avg_response_time') < 2.0
        assert stats.get('min_response_time') == 1.0
        assert stats.get('max_response_time') == 1.9

    def test_get_proxy_stats(self, test_db_path):
        """Test getting proxy statistics."""
        config = {
            "enabled": True,
            "type": "postgres",
            "host": test_db_path["host"],
            "name": test_db_path["database"],
            "user": test_db_path["user"],
            "password": test_db_path["password"]
        }

        db_manager = DatabaseManager(config)

        # Store some proxy metrics
        for i in range(9):
            proxy_id = f"test_proxy_{i % 3}"
            success = i % 3 != 2  # Make proxy_2 mostly fail

            metrics = {
                "proxy_id": proxy_id,
                "success": success,
                "response_time": 1.0 + i * 0.1,
                "status_code": 200 if success else 500,
                "error_message": None if success else "Test error"
            }

            db_manager.store_proxy_metrics(metrics)

        # Get statistics for a specific proxy
        stats = db_manager.get_proxy_stats("test_proxy_0", hours=24)
        proxy_stats = stats.get("test_proxy_0", {})

        # Verify statistics
        assert proxy_stats.get('total_requests') == 3
        assert proxy_stats.get('successful_requests') == 3
        assert proxy_stats.get('failed_requests') == 0
        assert proxy_stats.get('success_rate') == 100.0

        # Get statistics for all proxies
        all_stats = db_manager.get_proxy_stats(hours=24)

        # Verify all proxies are included
        assert "test_proxy_0" in all_stats
        assert "test_proxy_1" in all_stats
        assert "test_proxy_2" in all_stats

        # Verify problematic proxy stats
        assert all_stats["test_proxy_2"]["success_rate"] == 0  # All failures


class TestAlertManager:
    """Tests for the AlertManager class."""

    def test_init(self, test_config):
        """Test AlertManager initialization."""
        db_manager = MagicMock()
        alert_manager = AlertManager(test_config, db_manager)

        assert alert_manager.config == test_config
        assert alert_manager.db_manager == db_manager
        assert alert_manager.thresholds == test_config["alert_thresholds"]
        assert alert_manager.channels == test_config["alert_channels"]

    def test_send_alert(self, test_config):
        """Test sending alerts."""
        db_manager = MagicMock()
        alert_manager = AlertManager(test_config, db_manager)

        with patch.object(alert_manager, '_send_email_alert') as mock_email, \
             patch.object(alert_manager, '_send_slack_alert') as mock_slack, \
             patch('monitoring.logger') as mock_logger:

            # Send an alert
            alert_manager.send_alert(
                "test_alert", "warning", "Test alert message",
                {"detail1": "value1", "detail2": "value2"}
            )

            # Verify DB storage was called
            db_manager.store_alert.assert_called_once_with(
                "test_alert", "warning", "Test alert message"
            )

            # Verify logging was called
            mock_logger.warning.assert_called_once()

            # Verify email and Slack were not called (disabled in config)
            mock_email.assert_not_called()
            mock_slack.assert_not_called()

            # Now enable email and Slack
            test_config["alert_channels"]["email"]["enabled"] = True
            test_config["alert_channels"]["slack"]["enabled"] = True

            # Reset mocks
            db_manager.store_alert.reset_mock()
            mock_logger.warning.reset_mock()

            # Send another alert
            alert_manager.send_alert(
                "test_alert2", "critical", "Critical alert message"
            )

            # Verify email and Slack were called
            mock_email.assert_called_once()
            mock_slack.assert_called_once()

    def test_alert_cooldown(self, test_config):
        """Test alert cooldown to prevent alert storms."""
        db_manager = MagicMock()
        alert_manager = AlertManager(test_config, db_manager)

        # Set a shorter cooldown for testing
        alert_manager.alert_cooldown = 0.1

        with patch('monitoring.logger') as mock_logger:
            # Send an alert
            alert_manager.send_alert("test_alert", "warning", "Test alert message")

            # Verify DB storage and logging were called
            db_manager.store_alert.assert_called_once()
            mock_logger.warning.assert_called_once()

            # Reset mocks
            db_manager.store_alert.reset_mock()
            mock_logger.warning.reset_mock()

            # Send the same alert immediately
            alert_manager.send_alert("test_alert", "warning", "Test alert message")

            # Verify nothing was called due to cooldown
            db_manager.store_alert.assert_not_called()
            mock_logger.warning.assert_not_called()

            # Wait for cooldown to expire
            time.sleep(0.2)

            # Send the alert again
            alert_manager.send_alert("test_alert", "warning", "Test alert message")

            # Verify calls were made this time
            db_manager.store_alert.assert_called_once()
            mock_logger.warning.assert_called_once()

    def test_check_scraper_thresholds(self, test_config):
        """Test checking scraper metrics against thresholds."""
        db_manager = MagicMock()

        # Configure test data
        scraper_stats = {
            'scraper_name': 'test_scraper',
            'error_rate': 25.0,  # Above threshold (20%)
            'captcha_rate': 15.0,  # Above threshold (10%)
            'avg_response_time': 12.0  # Above threshold (10s)
        }
        db_manager.get_scraper_stats.return_value = scraper_stats

        alert_manager = AlertManager(test_config, db_manager)

        with patch.object(alert_manager, 'send_alert') as mock_send_alert:
            # Check thresholds
            alert_manager.check_scraper_thresholds('test_scraper')

            # Verify alerts were sent for all thresholds
            assert mock_send_alert.call_count == 3

            # Check that the right alert types were sent
            alert_types = [call[0][0] for call in mock_send_alert.call_args_list]
            assert 'high_error_rate' in alert_types
            assert 'high_captcha_rate' in alert_types
            assert 'high_latency' in alert_types


class TestMonitor:
    """Tests for the Monitor class."""

    def test_init(self, test_config):
        """Test Monitor initialization."""
        with patch('monitoring.CONFIG', test_config):
            test_monitor = Monitor()

            assert test_monitor.config == test_config
            assert isinstance(test_monitor.db_manager, DatabaseManager)
            assert isinstance(test_monitor.alert_manager, AlertManager)
            assert test_monitor.scrapers == set()
            assert test_monitor.monitoring_active is False
            assert test_monitor.monitoring_thread is None

    def test_start_stop_monitoring(self, mock_monitor):
        """Test starting and stopping monitoring."""
        assert not mock_monitor.monitoring_active
        assert mock_monitor.monitoring_thread is None

        # Start monitoring
        mock_monitor.start_monitoring()

        assert mock_monitor.monitoring_active
        assert mock_monitor.monitoring_thread is not None
        assert mock_monitor.monitoring_thread.is_alive()

        # Stop monitoring
        mock_monitor.stop_monitoring()

        assert not mock_monitor.monitoring_active
        assert not mock_monitor.monitoring_thread.is_alive()

    def test_register_scraper(self, mock_monitor):
        """Test registering scrapers."""
        assert len(mock_monitor.scrapers) == 0

        # Register a scraper
        mock_monitor.register_scraper("test_scraper")

        assert len(mock_monitor.scrapers) == 1
        assert "test_scraper" in mock_monitor.scrapers

        # Register another scraper
        mock_monitor.register_scraper("another_scraper")

        assert len(mock_monitor.scrapers) == 2
        assert "another_scraper" in mock_monitor.scrapers

        # Register the same scraper again (should not duplicate)
        mock_monitor.register_scraper("test_scraper")

        assert len(mock_monitor.scrapers) == 2

    def test_record_request(self, mock_monitor):
        """Test recording request metrics."""
        with patch.object(mock_monitor.db_manager, 'store_request_metrics') as mock_store:
            # Record a request
            metrics = {
                "url": "https://example.com",
                "success": True,
                "response_time": 1.234,
                "status_code": 200,
                "captcha_detected": False,
                "error_message": None
            }

            mock_monitor.record_request("test_scraper", metrics)

            # Verify scraper was registered
            assert "test_scraper" in mock_monitor.scrapers

            # Verify metrics were stored
            mock_store.assert_called_once()

            # Check that scraper_name was added to metrics
            call_args = mock_store.call_args[0][0]
            assert call_args["scraper_name"] == "test_scraper"

    def test_record_proxy_request(self, mock_monitor):
        """Test recording proxy metrics."""
        with patch.object(mock_monitor.db_manager, 'store_proxy_metrics') as mock_store:
            # Record a proxy request
            metrics = {
                "success": True,
                "response_time": 1.234,
                "status_code": 200,
                "error_message": None
            }

            mock_monitor.record_proxy_request("test_proxy", metrics)

            # Verify metrics were stored
            mock_store.assert_called_once()

            # Check that proxy_id was added to metrics
            call_args = mock_store.call_args[0][0]
            assert call_args["proxy_id"] == "test_proxy"

    def test_get_stats(self, mock_monitor):
        """Test getting statistics."""
        with patch.object(mock_monitor.db_manager, 'get_scraper_stats') as mock_scraper_stats, \
             patch.object(mock_monitor.db_manager, 'get_proxy_stats') as mock_proxy_stats:

            # Configure mock returns
            mock_scraper_stats.return_value = {"scraper_name": "test_scraper", "total_requests": 100}
            mock_proxy_stats.return_value = {"test_proxy": {"total_requests": 50}}

            # Get scraper stats
            stats = mock_monitor.get_scraper_stats("test_scraper", hours=12)

            # Verify correct method was called
            mock_scraper_stats.assert_called_once_with("test_scraper", 12)
            assert stats == {"scraper_name": "test_scraper", "total_requests": 100}

            # Get proxy stats
            stats = mock_monitor.get_proxy_stats("test_proxy", hours=12)

            # Verify correct method was called
            mock_proxy_stats.assert_called_once_with("test_proxy", 12)
            assert stats == {"test_proxy": {"total_requests": 50}}

    def test_set_alert_threshold(self, mock_monitor):
        """Test setting alert thresholds."""
        # Initial thresholds from config
        assert mock_monitor.config["alert_thresholds"]["error_rate"] == 20

        # Set a new threshold
        mock_monitor.set_alert_threshold("error_rate", 30.0)

        # Verify the threshold was updated
        assert mock_monitor.config["alert_thresholds"]["error_rate"] == 30.0

        # Set a new metric
        mock_monitor.set_alert_threshold("new_metric", 15.0)

        # Verify the new metric was added
        assert mock_monitor.config["alert_thresholds"]["new_metric"] == 15.0

        # Enable a channel while setting threshold
        assert mock_monitor.config["alert_channels"]["email"]["enabled"] is False

        mock_monitor.set_alert_threshold("error_rate", 25.0, "email")

        # Verify the channel was enabled
        assert mock_monitor.config["alert_channels"]["email"]["enabled"] is True

    def test_send_alert(self, mock_monitor):
        """Test sending alerts through the monitor."""
        with patch.object(mock_monitor.alert_manager, 'send_alert') as mock_send:
            # Send an alert
            mock_monitor.send_alert("test_alert", "warning", "Test alert message",
                                   {"detail": "value"})

            # Verify alert_manager method was called
            mock_send.assert_called_once_with(
                "test_alert", "warning", "Test alert message", {"detail": "value"}
            )


class TestMonitorDecorator:
    """Tests for the monitor_scraper decorator."""

    def test_monitor_scraper_decorator(self, mock_monitor):
        """Test the monitor_scraper decorator."""
        # Replace the global monitor with our test one
        with patch('monitoring.monitor', mock_monitor):
            # Create a test function with the decorator
            @monitor_scraper("test_scraper")
            def test_function(url):
                if url == "https://example.com/error":
                    raise ValueError("Test error")
                elif url == "https://example.com/captcha":
                    return {"status_code": 200, "captcha_detected": True}
                else:
                    return {"status_code": 200, "success": True}

            # Call with success
            with patch.object(mock_monitor, 'record_request') as mock_record:
                result = test_function("https://example.com")

                # Verify result
                assert result["success"] is True

                # Verify metrics were recorded
                mock_record.assert_called_once()
                metrics = mock_record.call_args[0][1]
                assert metrics["url"] == "https://example.com"
                assert metrics["success"] is True
                assert metrics["status_code"] == 200
                assert metrics["captcha_detected"] is False

            # Call with CAPTCHA detected
            with patch.object(mock_monitor, 'record_request') as mock_record:
                result = test_function("https://example.com/captcha")

                # Verify metrics were recorded with captcha flag
                mock_record.assert_called_once()
                metrics = mock_record.call_args[0][1]
                assert metrics["captcha_detected"] is True

            # Call with error
            with patch.object(mock_monitor, 'record_request') as mock_record:
                try:
                    test_function("https://example.com/error")
                except ValueError:
                    pass

                # Verify error metrics were recorded
                mock_record.assert_called_once()
                metrics = mock_record.call_args[0][1]
                assert metrics["success"] is False
                assert "Test error" in metrics["error_message"]

    def test_decorator_with_proxy(self, mock_monitor):
        """Test the decorator with proxy information."""
        # Replace the global monitor with our test one
        with patch('monitoring.monitor', mock_monitor):
            # Create a test function with the decorator
            @monitor_scraper("test_scraper")
            def test_function(url, proxy=None):
                return {"status_code": 200, "success": True}

            # Call with proxy dict
            with patch.object(mock_monitor, 'record_request') as mock_record:
                proxy = {"id": "test_proxy", "host": "proxy.example.com"}
                test_function("https://example.com", proxy=proxy)

                # Verify proxy ID was added to metrics
                metrics = mock_record.call_args[0][1]
                assert metrics["proxy_id"] == "test_proxy"

            # Call with proxy string
            with patch.object(mock_monitor, 'record_request') as mock_record:
                test_function("https://example.com", proxy="test_proxy_string")

                # Verify proxy ID was added to metrics
                metrics = mock_record.call_args[0][1]
                assert metrics["proxy_id"] == "test_proxy_string"


def test_singleton_instance():
    """Test the singleton monitor instance."""
    # Verify the singleton was initialized
    assert monitor is not None
    assert isinstance(monitor, Monitor)


if __name__ == "__main__":
    pytest.main(["-v", __file__])
