#!/usr/bin/env python3
"""
Resource manager with enhanced error handling and recovery.
"""
import os
import logging
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
import aiohttp
import asyncpg
import nest_asyncio
from contextlib import asynccontextmanager
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ResourceManager:
    """Manages resources and connections with enhanced error handling."""

    def __init__(self):
        """Initialize the resource manager."""
        self.cache = {}
        self.db_pool = None
        self.session = None
        self._setup_asyncio()
        self.last_db_error = None
        self.db_retry_attempts = 0
        self.max_db_retries = 3
        self.db_retry_delay = 5  # seconds
        self.db_recovery_threshold = 300  # 5 minutes

    def _setup_asyncio(self):
        """Set up asyncio event loop handling."""
        try:
            nest_asyncio.apply()
        except Exception as e:
            logger.warning(f"Failed to apply nest_asyncio: {e}")

    async def init_resources(self):
        """Initialize resources asynchronously with enhanced error handling."""
        try:
            # Initialize database pool with retries
            await self._init_db_pool_with_retry()

            # Initialize HTTP session
            if not self.session:
                timeout = aiohttp.ClientTimeout(total=30)
                self.session = aiohttp.ClientSession(timeout=timeout)

            logger.info("Resources initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize resources: {e}")
            await self.cleanup()
            raise

    async def _init_db_pool_with_retry(self):
        """Initialize database connection pool with retry mechanism."""
        if (self.last_db_error and
            datetime.now() - self.last_db_error < timedelta(seconds=self.db_recovery_threshold)):
            # If we had a recent error, wait before retrying
            await asyncio.sleep(self.db_retry_delay)

        try:
            if not self.db_pool:
                self.db_pool = await asyncpg.create_pool(
                    host=os.getenv('DB_HOST', 'localhost'),
                    port=int(os.getenv('DB_PORT', 5432)),
                    database=os.getenv('DB_NAME', 'trend_crawler'),
                    user=os.getenv('DB_USER', 'postgres'),
                    password=os.getenv('DB_PASSWORD', ''),
                    min_size=5,
                    max_size=20,
                    command_timeout=10
                )
                self.db_retry_attempts = 0
                self.last_db_error = None
                logger.info("PostgreSQL database pool initialized successfully")

        except Exception as e:
            self.last_db_error = datetime.now()
            self.db_retry_attempts += 1

            if self.db_retry_attempts >= self.max_db_retries:
                logger.error(f"Database initialization failed after {self.max_db_retries} attempts")
                raise

            retry_delay = self.db_retry_delay * (2 ** (self.db_retry_attempts - 1))
            logger.warning(f"Database initialization attempt {self.db_retry_attempts} failed. Retrying in {retry_delay}s")
            await asyncio.sleep(retry_delay)
            await self._init_db_pool_with_retry()

    @asynccontextmanager
    async def db_connection(self):
        """Get a database connection with enhanced error handling."""
        if not self.db_pool:
            await self._init_db_pool_with_retry()

        try:
            async with self.db_pool.acquire() as conn:
                yield conn
        except asyncpg.PostgresError as e:
            logger.error(f"Database operation failed: {e}")
            # Close and clear the pool for reconnection
            if self.db_pool:
                await self.db_pool.close()
                self.db_pool = None
            raise
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            # Connection is automatically released back to the pool
            pass

    async def execute_query(self, query: str, params: tuple = None) -> Optional[AsyncGenerator]:
        """Execute a database query with retry mechanism."""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                async with self.db_connection() as cur:
                    await cur.execute(query, params)
                    async for row in cur:
                        yield row
                return
            except asyncpg.PostgresError as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise
                await asyncio.sleep(1 * retry_count)
                continue
            except Exception as e:
                logger.error(f"Query execution failed: {e}")
                raise

    async def get_trend_content(self, trend_name: str) -> Optional[Dict[str, Any]]:
        """Get trend content with caching and error handling."""
        cache_key = f"trend_{trend_name}"

        # Check cache first
        if cache_key in self.cache:
            logger.info(f"Cache hit for trend: {trend_name}")
            return self.cache[cache_key]

        try:
            content = await self._fetch_trend_content(trend_name)
            if content:
                self.cache[cache_key] = content
                return content
        except Exception as e:
            await self.handle_error(e, f"Error fetching trend content for {trend_name}")
            return None

    async def _fetch_trend_content(self, trend_name: str) -> Optional[Dict[str, Any]]:
        """Fetch trend content with enhanced error handling."""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)

        try:
            # Search relevant URLs with timeout and retry
            search_url = f"https://api.duckduckgo.com/?q={trend_name}&format=json"
            async with self.session.get(search_url, timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'AbstractText' in data and data['AbstractText']:
                        return {
                            'text': data['AbstractText'],
                            'url': data.get('AbstractURL', ''),
                            'source': 'duckduckgo',
                            'timestamp': datetime.now().isoformat()
                        }

            # Fallback to database cache
            try:
                async with self.db_connection() as cur:
                    await cur.execute(
                        "SELECT content, url FROM trend_cache WHERE name = $1 AND timestamp > $2",
                        (trend_name, datetime.now() - timedelta(days=1))
                    )
                    result = await cur.fetchone()
                    if result:
                        return {
                            'text': result[0],
                            'url': result[1],
                            'source': 'cache',
                            'timestamp': datetime.now().isoformat()
                        }
            except Exception as db_e:
                logger.warning(f"Database cache lookup failed: {db_e}")

            # Return an error state instead of mock data
            logger.error(f"All content retrieval methods failed for {trend_name}")
            return {
                'text': '',
                'url': '',
                'source': 'error',
                'error': 'All content retrieval methods failed',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            await self.handle_error(e, f"Error in _fetch_trend_content for {trend_name}")
            return None

    async def get_metrics(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """Get metrics with error handling and caching."""
        try:
            # Basic metrics with defaults
            metrics = {
                'views': 0,
                'likes': 0,
                'shares': 0,
                'comments': 0
            }

            # Try to get metrics from database
            if content.get('url'):
                try:
                    async with self.db_connection() as cur:
                        await cur.execute(
                            """
                            SELECT views, likes, shares, comments
                            FROM metrics
                            WHERE url = $1 AND timestamp > $2
                            """,
                            (content['url'], datetime.now() - timedelta(hours=24))
                        )
                        result = await cur.fetchone()
                        if result:
                            metrics.update({
                                'views': result[0],
                                'likes': result[1],
                                'shares': result[2],
                                'comments': result[3]
                            })
                except Exception as db_e:
                    logger.error(f"Failed to fetch metrics from database: {db_e}")

            # Add engagement score
            total_engagement = metrics['likes'] + metrics['shares'] + metrics['comments']
            metrics['engagement_score'] = min(1.0, total_engagement / max(metrics['views'], 1))

            return metrics

        except Exception as e:
            await self.handle_error(e, "Error getting metrics")
            return {
                'views': 0,
                'likes': 0,
                'shares': 0,
                'comments': 0,
                'engagement_score': 0.0
            }

    async def handle_error(self, error: Exception, context: str = ""):
        """Enhanced error handling with recovery mechanisms."""
        logger.error(f"{context}: {str(error)}")

        try:
            # Database-specific recovery
            if isinstance(error, asyncpg.PostgresError):
                logger.info("Attempting to recover database connection...")
                if self.db_pool:
                    await self.db_pool.close()
                    self.db_pool = None
                await self._init_db_pool_with_retry()

            # HTTP session recovery
            elif isinstance(error, (aiohttp.ClientError, asyncio.TimeoutError)):
                logger.info("Resetting HTTP session...")
                if self.session:
                    await self.session.close()
                    timeout = aiohttp.ClientTimeout(total=30)
                    self.session = aiohttp.ClientSession(timeout=timeout)

            # Cache cleanup on errors
            elif isinstance(error, (KeyError, ValueError)):
                logger.info("Cleaning up cache due to data error...")
                self.cache.clear()

        except Exception as recovery_error:
            logger.error(f"Error recovery failed: {recovery_error}")

    async def cleanup(self):
        """Enhanced resource cleanup."""
        try:
            if self.session:
                await self.session.close()
                self.session = None

            if self.db_pool:
                self.db_pool.close()
                await self.db_pool.wait_closed()
                self.db_pool = None

            # Clear cache
            self.cache.clear()

            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    async def __aenter__(self):
        """Initialize resources for async context manager."""
        await self.init_resources()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Clean up resources for async context manager."""
        await self.cleanup()

    def __del__(self):
        """Ensure resources are cleaned up."""
        if self.session or self.db_pool:
            asyncio.create_task(self.cleanup())
