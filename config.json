{"pg_vector": {"host": "localhost", "port": 5432, "dbname": "trend_crawler", "user": "postgres", "password": "postgres", "min_pool_size": 2, "max_pool_size": 10}, "nano_llm": {"vocab_size": 32000, "d_model": 64, "nhead": 4, "num_encoder_layers": 2, "dim_feedforward": 128, "dropout": 0.1}, "nano_trend": {"input_dim": 384, "hidden_dim": 128, "output_dim": 1}, "temporal_gan": {"embedding_dim": 384, "hidden_dim": 128, "noise_dim": 32}, "performance": {"use_mixed_precision": true, "use_gradient_checkpointing": true, "memory_limit_mb": 500}, "security": {"tls_min_version": "1.3", "rate_limits": {"compound-beta": {"req_limit": 0.25, "token_limit": 0.0023}, "allam-2-7b": {"req_limit": 0.5, "token_limit": 0.081}}}, "monitoring": {"metrics_port": 9090, "grafana_port": 3000, "collect_interval_seconds": 10}}