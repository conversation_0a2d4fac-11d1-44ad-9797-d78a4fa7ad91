<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication System Test - Trend Crawler</title>
    <link rel="stylesheet" href="static/css/style.css">
    <!-- Test page should also use auth guard -->
    <script src="static/js/auth-guard.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <nav>
                <div class="logo">Trend Crawler - Auth Test</div>
                <ul>
                    <li><a href="/">Dashboard</a></li>
                    <li><a href="/account">Account</a></li>
                    <li><a href="#" onclick="testLogout()" class="logout-btn">Test Logout</a></li>
                </ul>
            </nav>
        </header>
        
        <main>
            <h1>🔐 Authentication System Test</h1>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h2>Authentication Status</h2>
                    <div id="auth-status">
                        <p>Loading authentication status...</p>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h2>Token Information</h2>
                    <div id="token-info">
                        <p>Loading token information...</p>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h2>User Data</h2>
                    <div id="user-data">
                        <p>Loading user data...</p>
                    </div>
                </div>
                
                <div class="dashboard-card">
                    <h2>Authentication Tests</h2>
                    <div class="card-content">
                        <button onclick="testTokenVerification()" class="btn btn-primary">Test Token Verification</button>
                        <button onclick="testApiCall()" class="btn btn-secondary">Test Authenticated API Call</button>
                        <button onclick="testManualLogout()" class="btn btn-danger">Test Manual Logout</button>
                        <div id="test-results" style="margin-top: 1rem;"></div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-card" style="margin-top: 2rem;">
                <h2>Authentication Logs</h2>
                <div id="auth-logs" style="max-height: 300px; overflow-y: auto; background: rgba(0,0,0,0.2); padding: 1rem; border-radius: 0.5rem; font-family: monospace; font-size: 0.9rem;">
                    <div>Authentication system test page loaded...</div>
                </div>
            </div>
        </main>
        
        <footer>
            <p>Trend Crawler Authentication Test - © 2024</p>
        </footer>
    </div>

    <script>
        // Logging function
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('auth-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        // Check authentication status
        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const statusDiv = document.getElementById('auth-status');
            
            if (!token) {
                statusDiv.innerHTML = '<div class="alert alert-error">❌ No authentication token found</div>';
                addLog('No authentication token found', 'error');
                return;
            }
            
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const now = Date.now() / 1000;
                const isExpired = payload.exp < now;
                
                if (isExpired) {
                    statusDiv.innerHTML = '<div class="alert alert-error">❌ Authentication token expired</div>';
                    addLog('Authentication token expired', 'error');
                } else {
                    const expiresIn = Math.round((payload.exp - now) / 60);
                    statusDiv.innerHTML = `<div class="alert alert-success">✅ Authenticated (expires in ${expiresIn} minutes)</div>`;
                    addLog(`Authenticated, expires in ${expiresIn} minutes`, 'success');
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="alert alert-error">❌ Invalid token format</div>';
                addLog('Invalid token format: ' + error.message, 'error');
            }
        }

        // Display token information
        function displayTokenInfo() {
            const token = localStorage.getItem('access_token');
            const tokenDiv = document.getElementById('token-info');
            
            if (!token) {
                tokenDiv.innerHTML = '<p>No token available</p>';
                return;
            }
            
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                tokenDiv.innerHTML = `
                    <div class="token-details">
                        <p><strong>Subject:</strong> ${payload.sub || 'N/A'}</p>
                        <p><strong>Issued:</strong> ${new Date(payload.iat * 1000).toLocaleString()}</p>
                        <p><strong>Expires:</strong> ${new Date(payload.exp * 1000).toLocaleString()}</p>
                        <p><strong>Token Preview:</strong> ${token.substring(0, 50)}...</p>
                    </div>
                `;
                addLog('Token information displayed', 'info');
            } catch (error) {
                tokenDiv.innerHTML = '<p>Error parsing token</p>';
                addLog('Error parsing token: ' + error.message, 'error');
            }
        }

        // Display user data
        function displayUserData() {
            const userData = localStorage.getItem('user_data');
            const userDiv = document.getElementById('user-data');
            
            if (!userData) {
                userDiv.innerHTML = '<p>No user data available</p>';
                return;
            }
            
            try {
                const user = JSON.parse(userData);
                userDiv.innerHTML = `
                    <div class="user-details">
                        <p><strong>Username:</strong> ${user.username || 'N/A'}</p>
                        <p><strong>Email:</strong> ${user.email || 'N/A'}</p>
                        <p><strong>Admin:</strong> ${user.is_admin ? '✅ Yes' : '❌ No'}</p>
                        <p><strong>Active:</strong> ${user.is_active ? '✅ Yes' : '❌ No'}</p>
                    </div>
                `;
                addLog('User data displayed', 'info');
            } catch (error) {
                userDiv.innerHTML = '<p>Error parsing user data</p>';
                addLog('Error parsing user data: ' + error.message, 'error');
            }
        }

        // Test token verification
        async function testTokenVerification() {
            addLog('Testing token verification...', 'info');
            const resultsDiv = document.getElementById('test-results');
            
            try {
                if (window.AuthGuard && window.AuthGuard.verifyToken) {
                    const isValid = await window.AuthGuard.verifyToken();
                    if (isValid) {
                        resultsDiv.innerHTML = '<div class="alert alert-success">✅ Token verification successful</div>';
                        addLog('Token verification successful', 'success');
                    } else {
                        resultsDiv.innerHTML = '<div class="alert alert-error">❌ Token verification failed</div>';
                        addLog('Token verification failed', 'error');
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-error">❌ AuthGuard not available</div>';
                    addLog('AuthGuard not available', 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-error">❌ Error: ${error.message}</div>`;
                addLog('Token verification error: ' + error.message, 'error');
            }
        }

        // Test authenticated API call
        async function testApiCall() {
            addLog('Testing authenticated API call...', 'info');
            const resultsDiv = document.getElementById('test-results');
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                resultsDiv.innerHTML = '<div class="alert alert-error">❌ No token for API call</div>';
                addLog('No token for API call', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = '<div class="alert alert-success">✅ API call successful</div>';
                    addLog('API call successful: ' + JSON.stringify(data), 'success');
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-error">❌ API call failed (${response.status})</div>`;
                    addLog(`API call failed with status ${response.status}`, 'error');
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="alert alert-error">❌ API error: ${error.message}</div>`;
                addLog('API call error: ' + error.message, 'error');
            }
        }

        // Test manual logout
        function testManualLogout() {
            addLog('Testing manual logout...', 'info');
            if (window.AuthGuard && window.AuthGuard.redirectToLogin) {
                window.AuthGuard.redirectToLogin();
            } else {
                localStorage.removeItem('access_token');
                localStorage.removeItem('user_data');
                window.location.href = '/login';
            }
        }

        // Quick logout for testing
        function testLogout() {
            if (confirm('Logout and test authentication redirect?')) {
                testManualLogout();
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Test page DOM loaded', 'info');
            
            // Wait a moment for auth guard to complete
            setTimeout(() => {
                checkAuthStatus();
                displayTokenInfo();
                displayUserData();
                addLog('Authentication system test page ready', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
