#!/usr/bin/env python3
"""
Secure Server for NanoLLM System

This module implements a secure FastAPI server for the NanoLLM system,
providing API endpoints for trend analysis and prediction.
"""

import os
import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import APIKeyHeader
from pydantic import BaseModel, Field
import torch

# Import system components
from nanollm_system import NanoLLMSystem

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="NanoLLM API",
    description="Secure API for NanoLLM System",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

# Load configuration
with open("config.json", 'r') as f:
    config = json.load(f)

# Initialize system
system = NanoLLMSystem(config)

# Request rate limiting
request_counts = {}
RATE_LIMIT = 100  # requests per minute


# Models
class TrendAnalysisRequest(BaseModel):
    trend_id: int = Field(..., description="ID of the trend to analyze")


class TrendPredictionRequest(BaseModel):
    trend_id: int = Field(..., description="ID of the trend to predict")
    days_ahead: int = Field(7, description="Number of days ahead to predict")


class HealthResponse(BaseModel):
    status: str = Field(..., description="Health status")
    version: str = Field(..., description="API version")
    timestamp: str = Field(..., description="Current timestamp")


# Middleware for rate limiting
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    # Get client IP
    client_ip = request.client.host
    
    # Check rate limit
    current_minute = int(time.time() / 60)
    if client_ip not in request_counts:
        request_counts[client_ip] = {}
    
    if current_minute not in request_counts[client_ip]:
        # Clean up old entries
        request_counts[client_ip] = {current_minute: 1}
    else:
        request_counts[client_ip][current_minute] += 1
        
        # Check if rate limit exceeded
        if request_counts[client_ip][current_minute] > RATE_LIMIT:
            return JSONResponse(
                status_code=429,
                content={"detail": "Rate limit exceeded"}
            )
    
    # Process request
    response = await call_next(request)
    return response


# Dependency for API key validation
async def get_api_key(api_key: str = Depends(api_key_header)):
    if api_key is None:
        raise HTTPException(
            status_code=401,
            detail="API key is missing"
        )
    
    # In production, would validate against a database
    valid_api_key = os.environ.get("API_KEY", "test_api_key")
    if api_key != valid_api_key:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    
    return api_key


# Routes
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Check API health."""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }


@app.post("/api/v1/analyze", dependencies=[Depends(get_api_key)])
async def analyze_trend(request: TrendAnalysisRequest):
    """
    Analyze a trend using the NanoLLM system.
    
    Args:
        request: Trend analysis request
        
    Returns:
        Analysis results
    """
    try:
        results = system.analyze_trend(request.trend_id)
        return results
    except Exception as e:
        logger.error(f"Error analyzing trend: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing trend: {str(e)}"
        )


@app.post("/api/v1/predict", dependencies=[Depends(get_api_key)])
async def predict_trend(request: TrendPredictionRequest):
    """
    Predict the evolution of a trend.
    
    Args:
        request: Trend prediction request
        
    Returns:
        Prediction results
    """
    try:
        results = system.predict_trend_evolution(
            request.trend_id,
            days_ahead=request.days_ahead
        )
        return results
    except Exception as e:
        logger.error(f"Error predicting trend: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error predicting trend: {str(e)}"
        )


@app.get("/api/v1/status", dependencies=[Depends(get_api_key)])
async def system_status():
    """
    Get system status.
    
    Returns:
        System status information
    """
    # Get device information
    device_info = {
        "device": str(system.device),
        "cuda_available": torch.cuda.is_available(),
        "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
    }
    
    # Get memory information
    memory_info = {}
    if hasattr(system, 'performance_optimizer') and hasattr(system.performance_optimizer, 'memory_tracker'):
        memory_info = system.performance_optimizer.memory_tracker.check_memory()
    
    # Get teacher status
    teacher_status = {}
    if hasattr(system, 'distillation_gate'):
        teacher_status = system.distillation_gate.get_teacher_status()
    
    return {
        "device": device_info,
        "memory": memory_info,
        "teachers": teacher_status,
        "timestamp": datetime.now().isoformat()
    }


# Shutdown event
@app.on_event("shutdown")
def shutdown_event():
    """Clean up resources on shutdown."""
    system.close()
    logger.info("Server shutting down")


def main():
    """Main entry point."""
    # Get port from environment or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Run server
    uvicorn.run(
        "secure_server:app",
        host="0.0.0.0",
        port=port,
        reload=False,
        workers=1
    )


if __name__ == "__main__":
    main()
