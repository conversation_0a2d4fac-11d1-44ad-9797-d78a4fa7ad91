#!/usr/bin/env python3
"""
Minimal test to check import issues.
"""

import sys
import time
import signal

def timeout_handler(signum, frame):
    print("TIMEOUT: Import took too long!")
    sys.exit(1)

def test_minimal():
    """Test with minimal imports."""
    try:
        print("Testing basic Python imports...")
        import os
        import json
        import random
        print("✅ Basic imports work")
        
        print("Testing requests...")
        import requests
        print("✅ Requests works")
        
        print("Testing BeautifulSoup...")
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup works")
        
        return True
    except Exception as e:
        print(f"❌ Basic import failed: {e}")
        return False

def test_heavy_imports():
    """Test heavy imports that might cause hanging."""
    try:
        print("Testing TensorFlow import...")
        signal.alarm(5)  # 5 second timeout
        import tensorflow as tf
        signal.alarm(0)
        print("✅ TensorFlow imported successfully")
        
        print("Testing Playwright import...")
        signal.alarm(5)
        from playwright.async_api import async_playwright
        signal.alarm(0) 
        print("✅ Playwright imported successfully")
        
        print("Testing PyTorch import...")
        signal.alarm(5)
        import torch
        signal.alarm(0)
        print("✅ PyTorch imported successfully")
        
        return True
    except Exception as e:
        signal.alarm(0)
        print(f"❌ Heavy import failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Minimal Import Test ===")
    
    # Set up timeout handler
    signal.signal(signal.SIGALRM, timeout_handler)
    
    # Test basic imports
    basic_ok = test_minimal()
    
    if basic_ok:
        print("\n=== Testing Heavy Dependencies ===")
        heavy_ok = test_heavy_imports()
        
        if not heavy_ok:
            print("⚠️  Heavy dependencies are causing issues")
    
    print("\n=== Test Complete ===")
