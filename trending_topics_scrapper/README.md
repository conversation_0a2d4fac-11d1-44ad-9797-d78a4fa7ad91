# Content Scraper

A powerful content aggregator that scrapes and combines results from multiple sources including Reddit, Hacker News, TechCrunch, The Verge, Dev.to, Medium, and GitHub. It provides a unified interface to search across all platforms and get the most relevant results sorted by engagement.

## Features

- Multi-source content aggregation
- Concurrent scraping using ThreadPoolExecutor
- Smart text cleaning and formatting
- Engagement-based result ranking
- Beautiful console output with emojis
- Cross-platform compatibility
- Results saved in JSON format
- Modular and extensible architecture

## Supported Sources

- 📱 Reddit - Social news and discussions
- 💻 Hacker News - Tech news and discussions
- 📰 TechCrunch - Tech news articles
- 🌐 The Verge - Tech and culture news
- 👩‍💻 Dev.to - Developer blog posts
- 📝 Medium - Articles and blog posts
- 🐙 GitHub - Code repositories

## Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/content-scraper.git
cd content-scraper
```

2. Create and activate a virtual environment:

```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

## Usage

Run the scraper:

```bash
python scraper.py
```

Follow the interactive prompts to:

1. Enter a search topic
2. Specify the number of results per source
3. Choose a time filter (hour/day/week/month/year/all)

Results will be:

- Displayed in the console with source-specific emojis
- Sorted by engagement score
- Saved in the `results` directory as JSON files

## Project Structure

```
content-scraper/
├── scraper.py           # Main script
├── text_cleaner.py      # Text processing utilities
├── sources/             # Source-specific scrapers
│   ├── __init__.py
│   ├── base_source.py   # Base class for sources
│   ├── reddit_source.py
│   ├── hackernews_source.py
│   ├── techcrunch_source.py
│   ├── verge_source.py
│   ├── devto_source.py
│   ├── medium_source.py
│   └── github_source.py
├── results/             # Saved search results
├── requirements.txt     # Project dependencies
└── README.md           # This file
```

## Dependencies

- requests==2.31.0 - HTTP requests
- beautifulsoup4==4.12.2 - HTML parsing
- feedparser==6.0.10 - RSS feed parsing
- colorama==0.4.6 - Colored console output

## Author

**Waseem Anjum**  
Website: [waseemanjum.com](https://waseemanjum.com)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
