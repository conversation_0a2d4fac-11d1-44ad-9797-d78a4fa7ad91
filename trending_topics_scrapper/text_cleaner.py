import re
from bs4 import BeautifulSoup
import warnings


class TextCleaner:
    """Utility class for cleaning and formatting text content"""

    @staticmethod
    def is_url(text: str) -> bool:
        """Check if the text is a URL"""
        url_pattern = re.compile(
            r"^https?://"  # http:// or https://
            r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|"  # domain...
            r"localhost|"  # localhost...
            r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
            r"(?::\d+)?"  # optional port
            r"(?:/?|[/?]\S+)$",
            re.IGNORECASE,
        )
        return bool(url_pattern.match(text))

    @staticmethod
    def clean_text(
        text: str, clean_markdown: bool = True, clean_html: bool = True
    ) -> str:
        """Clean text by removing markdown, HTML, and normalizing whitespace"""
        if not text or TextCleaner.is_url(text):
            return text

        # Clean HTML if requested
        if clean_html:
            text = TextCleaner.clean_html(text)

        # Clean markdown if requested
        if clean_markdown:
            text = TextCleaner.clean_markdown(text)

        # Normalize whitespace
        return TextCleaner.normalize_whitespace(text)

    @staticmethod
    def clean_html(text: str) -> str:
        """Remove HTML tags and clean HTML content"""
        if not text or TextCleaner.is_url(text):
            return text

        # Suppress all BeautifulSoup warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")

            try:
                # Parse HTML and extract text
                soup = BeautifulSoup(text, "html.parser")

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()

                # Get text content
                text = soup.get_text()

                # Remove HTML entities
                text = re.sub(r"&[a-zA-Z]+;", " ", text)

                return text
            except Exception:
                # If HTML parsing fails, return original text
                return text

    @staticmethod
    def clean_markdown(text: str) -> str:
        """Remove markdown formatting"""
        if not text or TextCleaner.is_url(text):
            return text

        # Remove headers
        text = re.sub(r"^#+\s+", "", text, flags=re.MULTILINE)

        # Remove bold/italic
        text = re.sub(r"\*{1,3}(.*?)\*{1,3}", r"\1", text)
        text = re.sub(r"_{1,3}(.*?)_{1,3}", r"\1", text)

        # Remove inline code
        text = re.sub(r"`{1,3}(.*?)`{1,3}", r"\1", text)

        # Remove code blocks while preserving content
        text = re.sub(r"```[\w]*\n(.*?)\n```", r"\1", text, flags=re.DOTALL)

        # Remove blockquotes
        text = re.sub(r"^\s*>\s+", "", text, flags=re.MULTILINE)

        # Remove horizontal rules
        text = re.sub(r"^\s*[-*_]{3,}\s*$", "", text, flags=re.MULTILINE)

        # Remove list markers
        text = re.sub(r"^\s*[-*+]\s+", "", text, flags=re.MULTILINE)
        text = re.sub(r"^\s*\d+\.\s+", "", text, flags=re.MULTILINE)

        # Remove images but keep alt text
        text = re.sub(r"!\[(.*?)\]\(.*?\)", r"\1", text)

        # Remove links but keep text
        text = re.sub(r"\[(.*?)\]\(.*?\)", r"\1", text)

        return text

    @staticmethod
    def normalize_whitespace(text: str) -> str:
        """Normalize whitespace in text"""
        if not text or TextCleaner.is_url(text):
            return text

        # Replace newlines and tabs with spaces
        text = re.sub(r"[\n\t\r]+", " ", text)

        # Remove multiple spaces
        text = re.sub(r"\s+", " ", text)

        # Strip leading/trailing whitespace
        return text.strip()

    @staticmethod
    def truncate_text(text: str, max_length: int = 1000) -> str:
        """Truncate text to specified length while preserving word boundaries"""
        if not text or len(text) <= max_length or TextCleaner.is_url(text):
            return text

        # Find the last space before max_length
        truncated = text[:max_length]
        last_space = truncated.rfind(" ")

        if last_space > 0:
            truncated = truncated[:last_space]

        return truncated + "..."
