import json
from datetime import datetime
import time
import os
import concurrent.futures
from sources import (
    RedditSource,
    HackerNewsSource,
    TechCrunchSource,
    VergeSource,
    DevToSource,
    MediumSource,
    GitHubSource,
)
from colorama import init, Fore, Style
import platform

# Initialize colorama for cross-platform color support
init()


class Logger:
    """Helper class for pretty console logging"""

    # Emoji mappings (using basic emojis that work across platforms)
    EMOJI = {
        "start": "🚀",
        "search": "🔍",
        "success": "✅",
        "error": "❌",
        "time": "⏱️",
        "save": "💾",
        "stats": "📊",
        "reddit": "📱",
        "hackernews": "💻",
        "techcrunch": "📰",
        "theverge": "🌐",
        "devto": "👩‍💻",
        "medium": "📝",
        "github": "🐙",
        "points": "⭐",
        "comments": "💬",
        "tags": "🏷️",
    }

    @staticmethod
    def timestamp():
        return f"{Fore.BLUE}[{datetime.now().strftime('%H:%M:%S')}]{Style.RESET_ALL}"

    @staticmethod
    def info(msg, emoji=""):
        emoji = Logger.EMOJI.get(emoji, emoji)
        print(f"{Logger.timestamp()} {emoji} {msg}")

    @staticmethod
    def success(msg, emoji="success"):
        emoji = Logger.EMOJI.get(emoji, emoji)
        print(f"{Logger.timestamp()} {emoji} {Fore.GREEN}{msg}{Style.RESET_ALL}")

    @staticmethod
    def error(msg, emoji="error"):
        emoji = Logger.EMOJI.get(emoji, emoji)
        print(f"{Logger.timestamp()} {emoji} {Fore.RED}{msg}{Style.RESET_ALL}")


class ContentScraper:
    def __init__(self):
        # Get the absolute path to the results directory
        self.results_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "results"
        )
        # Create the results directory if it doesn't exist
        os.makedirs(self.results_dir, exist_ok=True)

        # Initialize all sources
        self.sources = {
            "reddit": RedditSource(),
            "hackernews": HackerNewsSource(),
            "techcrunch": TechCrunchSource(),
            "theverge": VergeSource(),
            "devto": DevToSource(),
            "medium": MediumSource(),
            "github": GitHubSource(),
        }

    def search_all(
        self, topic: str, num_results: int = 10, time_filter: str = "month"
    ) -> list:
        """Search all sources concurrently and return results"""
        start_time = time.time()
        Logger.info(f"Starting search for '{topic}'", "start")

        with concurrent.futures.ThreadPoolExecutor(
            max_workers=len(self.sources)
        ) as executor:
            future_to_source = {
                executor.submit(
                    source.search, topic, num_results, time_filter=time_filter
                ): name
                for name, source in self.sources.items()
            }

            all_results = []
            for future in concurrent.futures.as_completed(future_to_source):
                source_name = future_to_source[future]
                try:
                    results = future.result()
                    all_results.extend(results)
                    Logger.success(
                        f"{source_name}: Found {len(results)} results", source_name
                    )
                except Exception as e:
                    Logger.error(f"Error with {source_name}: {str(e)}")

        # Sort all results by engagement score
        all_results.sort(key=lambda x: x["engagement"]["score"], reverse=True)

        # Save combined results
        filename = self.save_results(topic, all_results)

        # Log completion statistics
        elapsed_time = time.time() - start_time
        Logger.info(f"Search completed in {elapsed_time:.2f} seconds", "time")
        Logger.success(f"Total results: {len(all_results)}")
        Logger.info(f"Results saved to: {os.path.basename(filename)}", "save")

        return all_results

    def save_results(self, topic: str, results: list) -> str:
        """Save results to a JSON file"""
        # Ensure the results directory exists
        os.makedirs(self.results_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(
            self.results_dir, f"results_{topic.replace(' ', '_')}_{timestamp}.json"
        )

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "topic": topic,
                    "timestamp": datetime.now().isoformat(),
                    "total_results": len(results),
                    "results": results,
                },
                f,
                indent=2,
                ensure_ascii=False,
            )

        return filename


def main():
    print(f"\n{Fore.CYAN}{'='*20} Content Scraper {'='*20}{Style.RESET_ALL}")
    scraper = ContentScraper()

    while True:
        print(f"\n{Fore.YELLOW}{'='*50}{Style.RESET_ALL}")
        topic = input(
            f"\n{Logger.EMOJI['search']} Enter a topic to search (or 'quit' to exit): "
        )
        if topic.lower() == "quit":
            break

        num_results = input(
            f"{Logger.EMOJI['search']} How many results per source? (default: 10): "
        )
        num_results = int(num_results) if num_results.isdigit() else 10

        time_filter = input(
            f"{Logger.EMOJI['time']} Time filter (hour/day/week/month/year/all) [default: month]: "
        )
        if time_filter not in ["hour", "day", "week", "month", "year", "all"]:
            time_filter = "month"

        results = scraper.search_all(topic, num_results, time_filter)


if __name__ == "__main__":
    main()
