import feedparser
from datetime import datetime
from typing import List, Dict, Any
from .base_source import BaseSource
from text_cleaner import TextCleaner


class VergeSource(BaseSource):
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.theverge.com"

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[str, Any]]:
        """Search The Verge articles"""
        search_url = f"{self.base_url}/rss/index.xml"

        try:
            feed = feedparser.parse(search_url)
            results = []

            # Filter entries that contain the topic
            matching_entries = [
                entry
                for entry in feed.entries
                if topic.lower() in entry.title.lower()
                or topic.lower() in entry.get("summary", "").lower()
            ]

            for entry in matching_entries[:num_results]:
                # Parse the date
                published = datetime(*entry.published_parsed[:6]).isoformat()

                # Clean the content and title
                content = TextCleaner.clean_text(entry.get("summary", ""))
                title = TextCleaner.clean_text(entry.title)

                # Extract tags from content or categories
                tags = []
                if hasattr(entry, "tags"):
                    tags.extend(tag.term for tag in entry.tags)
                elif hasattr(entry, "categories"):
                    tags.extend(entry.categories)

                result = {
                    "source": "theverge",
                    "title": title,
                    "author": entry.get("author", "The Verge"),
                    "url": entry.link,
                    "created_at": published,
                    "engagement": {
                        "score": 0,  # The Verge doesn't provide engagement metrics
                        "comments": 0,
                    },
                    "metadata": {
                        "tags": tags,
                        "category": entry.get("category", ""),
                    },
                    "content": content,
                }
                results.append(result)

            return results

        except Exception as e:
            print(f"The Verge error: {str(e)}")
            return []
