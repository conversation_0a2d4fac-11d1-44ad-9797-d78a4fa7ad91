from typing import List, Dict, Any
from .base_source import BaseSource
from text_cleaner import TextCleaner


class DevToSource(BaseSource):
    def __init__(self):
        super().__init__()
        self.base_url = "https://dev.to/api"

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[str, Any]]:
        """Search Dev.to articles"""
        search_url = f"{self.base_url}/articles"
        params = {
            "tag": topic.lower(),
            "per_page": num_results,
            "top": 1,  # Get top articles
        }

        try:
            response = self.session.get(search_url, headers=self.headers, params=params)
            response.raise_for_status()
            articles = response.json()

            results = []
            for article in articles[:num_results]:
                # Clean text content
                title = TextCleaner.clean_text(article.get("title", ""))
                content = TextCleaner.clean_text(article.get("description", ""))
                author = TextCleaner.clean_text(article.get("user", {}).get("name", ""))

                # Clean tags
                tags = [
                    TextCleaner.clean_text(tag, clean_markdown=False, clean_html=True)
                    for tag in article.get("tag_list", [])
                ]

                result = {
                    "source": "devto",
                    "title": title,
                    "author": author,
                    "url": article.get("url"),
                    "created_at": article.get("published_at"),
                    "engagement": {
                        "score": article.get("positive_reactions_count", 0),
                        "comments": article.get("comments_count", 0),
                    },
                    "metadata": {
                        "tags": tags,
                        "reading_time": article.get("reading_time_minutes"),
                    },
                    "content": TextCleaner.truncate_text(content, 1500),
                }
                results.append(result)

            return results

        except Exception as e:
            print(f"Dev.to error: {str(e)}")
            return []
