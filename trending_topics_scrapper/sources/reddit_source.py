from datetime import datetime
from urllib.parse import quote_plus
from typing import List, Dict, Any
from .base_source import BaseSource
from text_cleaner import TextCleaner


class RedditSource(BaseSource):
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.reddit.com"

    def search(
        self, topic: str, num_results: int = 10, time_filter: str = "month"
    ) -> List[Dict[str, Any]]:
        """Search Reddit for topics"""
        encoded_topic = quote_plus(topic)
        search_url = f"{self.base_url}/search.json?q={encoded_topic}&sort=top&t={time_filter}&limit={num_results}"

        try:
            response = self.session.get(search_url, headers=self.headers)
            response.raise_for_status()
            data = response.json()

            results = []
            for post in data["data"]["children"]:
                post_data = post["data"]

                # Clean the content and title
                content = TextCleaner.clean_text(
                    post_data.get("selftext") or post_data.get("url", "")
                )
                title = TextCleaner.clean_text(post_data.get("title", ""))

                result = {
                    "source": "reddit",
                    "title": title,
                    "author": post_data.get("author"),
                    "url": f"https://reddit.com{post_data.get('permalink')}",
                    "created_at": datetime.fromtimestamp(
                        post_data.get("created_utc")
                    ).isoformat(),
                    "engagement": {
                        "score": post_data.get("score", 0),
                        "comments": post_data.get("num_comments", 0),
                        "upvote_ratio": post_data.get("upvote_ratio", 0),
                    },
                    "metadata": {
                        "subreddit": post_data.get("subreddit"),
                        "is_video": post_data.get("is_video", False),
                    },
                    "content": content,
                }
                results.append(result)
            return results

        except Exception as e:
            print(f"Reddit error: {str(e)}")
            return []
