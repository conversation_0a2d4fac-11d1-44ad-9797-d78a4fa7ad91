from abc import ABC, abstractmethod
import requests
from typing import List, Dict, Any


class BaseSource(ABC):
    def __init__(self):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        self.session = requests.Session()

    @abstractmethod
    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Search for a topic and return results in the unified format:
        {
            "source": str,
            "title": str,
            "author": str,
            "url": str,
            "created_at": str,
            "engagement": {
                "score": int,
                "comments": int,
                ...
            },
            "metadata": dict,
            "content": str
        }
        """
        pass
