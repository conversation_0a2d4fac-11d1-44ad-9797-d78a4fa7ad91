from typing import List, Dict, Any
from .base_source import BaseSource


class HackerNewsSource(BaseSource):
    def __init__(self):
        super().__init__()
        self.base_url = "https://hn.algolia.com/api/v1"

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[str, Any]]:
        """Search HackerNews for topics"""
        search_url = f"{self.base_url}/search"
        params = {
            "query": topic,
            "tags": "story",
            "numericFilters": "points>1",
            "hitsPerPage": num_results,
        }

        try:
            response = self.session.get(search_url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            for hit in data.get("hits", []):
                result = {
                    "source": "hackernews",
                    "title": hit.get("title"),
                    "author": hit.get("author"),
                    "url": hit.get("url")
                    or f"https://news.ycombinator.com/item?id={hit.get('objectID')}",
                    "created_at": hit.get("created_at"),
                    "engagement": {
                        "score": hit.get("points", 0),
                        "comments": hit.get("num_comments", 0),
                    },
                    "metadata": {"object_id": hit.get("objectID")},
                    "content": hit.get("story_text", ""),
                }
                results.append(result)
            return results

        except Exception as e:
            print(f"HackerNews error: {str(e)}")
            return []
