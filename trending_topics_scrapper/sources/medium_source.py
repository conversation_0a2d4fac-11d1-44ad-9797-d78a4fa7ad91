from typing import List, Dict, Any
from datetime import datetime
import feedparser
from .base_source import BaseSource
from text_cleaner import TextCleaner


class MediumSource(BaseSource):
    """Source class for scraping Medium articles"""

    def __init__(self):
        super().__init__()
        self.base_url = "https://medium.com/feed/tag/{}"

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[Any, Any]]:
        """Search Medium for articles related to the topic"""
        try:
            # Construct search URL using the tag/topic
            search_url = self.base_url.format(topic.replace(" ", "-").lower())

            # Parse the RSS feed
            feed = feedparser.parse(search_url)

            results = []
            for entry in feed.entries[:num_results]:
                # Extract and clean content
                content = entry.get("content", [{"value": ""}])[0]["value"]
                content = TextCleaner.clean_text(content)

                # Extract tags if available
                tags = []
                if "tags" in entry:
                    tags = [tag.term for tag in entry.tags]

                # Calculate engagement score based on reading time
                # Medium provides a rough estimate of reading time in minutes
                reading_time = int(
                    content.count(" ") / 200
                )  # Approximate words per minute
                engagement_score = min(reading_time * 10, 100)  # Cap at 100

                # Parse the publication date using feedparser's built-in parsed date
                pub_date = datetime(*entry.published_parsed[:6]).isoformat()

                result = {
                    "title": TextCleaner.clean_text(entry.title),
                    "url": entry.link,
                    "author": entry.get("author", "Unknown"),
                    "date": pub_date,
                    "content": (
                        content[:1000] + "..." if len(content) > 1000 else content
                    ),
                    "source": "medium",
                    "engagement": {
                        "score": engagement_score,
                        "reading_time": reading_time,
                    },
                    "tags": tags,
                }
                results.append(result)

            return results

        except Exception as e:
            print(f"Medium error: {str(e)}")
            return []
