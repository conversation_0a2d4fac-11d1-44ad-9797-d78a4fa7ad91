from typing import List, Dict, Any
from datetime import datetime
import requests
from .base_source import BaseSource
from text_cleaner import TextCleaner


class GitHubSource(BaseSource):
    """Source class for scraping trending GitHub repositories"""

    def __init__(self):
        super().__init__()
        self.base_url = "https://api.github.com/search/repositories"
        self.headers.update({"Accept": "application/vnd.github.v3+json"})

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[Any, Any]]:
        """Search GitHub for repositories related to the topic"""
        try:
            # Construct query parameters
            params = {
                "q": topic,
                "sort": "stars",
                "order": "desc",
                "per_page": num_results,
            }

            # Make the API request
            response = self.session.get(self.base_url, params=params)
            response.raise_for_status()
            data = response.json()

            results = []
            for repo in data.get("items", [])[:num_results]:
                # Calculate engagement score based on stars and forks
                stars = repo.get("stargazers_count", 0)
                forks = repo.get("forks_count", 0)
                engagement_score = min((stars + forks * 2) / 100, 100)  # Cap at 100

                # Extract topics/tags
                topics = repo.get("topics", [])

                # Clean and prepare the description
                description = repo.get("description", "")
                if description:
                    description = TextCleaner.clean_text(description)

                result = {
                    "title": TextCleaner.clean_text(repo["name"]),
                    "url": repo["html_url"],
                    "author": repo["owner"]["login"],
                    "date": datetime.strptime(
                        repo["created_at"], "%Y-%m-%dT%H:%M:%SZ"
                    ).isoformat(),
                    "content": description,
                    "source": "github",
                    "engagement": {
                        "score": engagement_score,
                        "stars": stars,
                        "forks": forks,
                        "watchers": repo.get("watchers_count", 0),
                    },
                    "tags": topics,
                    "language": repo.get("language", "Unknown"),
                }
                results.append(result)

            return results

        except Exception as e:
            raise Exception(f"Error fetching GitHub repositories: {str(e)}")
