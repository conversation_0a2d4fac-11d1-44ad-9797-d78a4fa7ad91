import feedparser
from datetime import datetime
from typing import List, Dict, Any
from .base_source import BaseSource
from text_cleaner import TextCleaner


class TechCrunchSource(BaseSource):
    def __init__(self):
        super().__init__()
        self.base_url = "https://techcrunch.com"

    def search(
        self, topic: str, num_results: int = 10, **kwargs
    ) -> List[Dict[str, Any]]:
        """Search TechCrunch articles"""
        search_url = f"{self.base_url}/search/{topic}/feed"

        try:
            feed = feedparser.parse(search_url)
            results = []

            for entry in feed.entries[:num_results]:
                # Parse the date
                published = datetime(*entry.published_parsed[:6]).isoformat()

                # Clean the content and title
                content = TextCleaner.clean_text(entry.get("summary", ""))
                title = TextCleaner.clean_text(entry.title)

                # Extract and clean tags
                tags = []
                if hasattr(entry, "tags"):
                    tags = [
                        TextCleaner.clean_text(
                            tag.term, clean_markdown=False, clean_html=True
                        )
                        for tag in entry.get("tags", [])
                    ]

                result = {
                    "source": "techcrunch",
                    "title": title,
                    "author": TextCleaner.clean_text(entry.get("author", "TechCrunch")),
                    "url": entry.link,
                    "created_at": published,
                    "engagement": {
                        "score": 0,  # TechCrunch doesn't provide engagement metrics
                        "comments": 0,
                    },
                    "metadata": {
                        "tags": tags,
                        "category": TextCleaner.clean_text(entry.get("category", "")),
                    },
                    "content": content,
                }
                results.append(result)

            return results

        except Exception as e:
            print(f"TechCrunch error: {str(e)}")
            return []
