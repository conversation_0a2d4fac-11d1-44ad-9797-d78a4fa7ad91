#!/bin/bash
# <PERSON><PERSON>t to reset PostgreSQL password and set up the database

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "PostgreSQL is not installed. Please install it first."
    exit 1
fi

# Reset password for postgres user
echo "Resetting PostgreSQL password..."
sudo -u postgres psql -c "ALTER USER postgres WITH PASSWORD 'postgres';" || {
    echo "Failed to reset PostgreSQL password. Make sure PostgreSQL is running and you have sudo privileges."
    exit 1
}

# Create the database if it doesn't exist
echo "Creating database if not exists..."
sudo -u postgres psql -c "SELECT 1 FROM pg_database WHERE datname = 'scraper_metrics'" | grep -q 1 || {
    sudo -u postgres psql -c "CREATE DATABASE scraper_metrics;"
    echo "Database 'scraper_metrics' created."
}

echo "PostgreSQL setup completed successfully."
echo "Username: postgres"
echo "Password: postgres"
echo "Database: scraper_metrics"

# Running the database fix script
echo "Running database fix script..."
cd "$(dirname "$0")"
python db_fix.py

# Running proxy schema fix script
echo "Running proxy schema fix script..."
python fix_proxy_schema.py

# Running CAPTCHA API fix script
echo "Running CAPTCHA API fix script..."
python fix_captcha_api.py

echo "Setup complete!"
