#!/usr/bin/env python3
"""
Integration test for the captcha solver with fixed TensorFlow lazy loading.
"""

import sys
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_captcha_solver_integration():
    """Test the main captcha solver integration."""
    logger.info("Testing main captcha solver integration...")
    
    try:
        # Import and initialize the main captcha solver
        start_time = time.time()
        from captcha_solver import CaptchaSolver
        import_elapsed = time.time() - start_time
        
        logger.info(f"✅ CaptchaSolver imported successfully in {import_elapsed:.2f}s")
        
        # Initialize solver
        init_start = time.time()
        solver = CaptchaSolver()
        init_elapsed = time.time() - init_start
        
        logger.info(f"✅ CaptchaSolver initialized successfully in {init_elapsed:.2f}s")
        
        # Check available solvers
        logger.info("Available solver capabilities:")
        
        # Check ML solvers availability
        try:
            from ml_captcha_solvers import PYTORCH_AVAILABLE, TENSORFLOW_AVAILABLE
            logger.info(f"  - PyTorch ML solver: {PYTORCH_AVAILABLE}")
            logger.info(f"  - TensorFlow ML solver: {TENSORFLOW_AVAILABLE}")
        except ImportError:
            logger.info("  - ML solvers package: Not available")
        
        # Check other capabilities
        try:
            from captcha_solver import TWOCAPTCHA_AVAILABLE, OCR_AVAILABLE, ML_SOLVERS_AVAILABLE
            logger.info(f"  - 2Captcha service: {TWOCAPTCHA_AVAILABLE}")
            logger.info(f"  - OCR capabilities: {OCR_AVAILABLE}")
            logger.info(f"  - ML solvers overall: {ML_SOLVERS_AVAILABLE}")
        except ImportError:
            logger.info("  - Could not check all capabilities")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ml_solver_fallback():
    """Test ML solver fallback behavior."""
    logger.info("Testing ML solver fallback behavior...")
    
    try:
        # Test direct ML solver imports
        from ml_captcha_solvers import PYTORCH_AVAILABLE, TENSORFLOW_AVAILABLE
        
        logger.info(f"ML Solver Status:")
        logger.info(f"  - PyTorch: {PYTORCH_AVAILABLE}")
        logger.info(f"  - TensorFlow: {TENSORFLOW_AVAILABLE}")
        
        if PYTORCH_AVAILABLE:
            try:
                from ml_captcha_solvers import PyTorchCaptchaSolver
                pytorch_solver = PyTorchCaptchaSolver()
                logger.info("✅ PyTorch solver initialized successfully")
            except Exception as e:
                logger.error(f"❌ PyTorch solver initialization failed: {e}")
        
        if TENSORFLOW_AVAILABLE:
            try:
                from ml_captcha_solvers import TensorFlowCaptchaSolver
                tf_solver = TensorFlowCaptchaSolver()
                logger.info("✅ TensorFlow solver initialized successfully")
            except Exception as e:
                logger.error(f"❌ TensorFlow solver initialization failed: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ML solver fallback test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run integration tests."""
    logger.info("Starting captcha solver integration tests...")
    
    tests = [
        ("Main Captcha Solver Integration", test_captcha_solver_integration),
        ("ML Solver Fallback", test_ml_solver_fallback),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        start_time = time.time()
        success = test_func()
        elapsed = time.time() - start_time
        
        results[test_name] = {
            'success': success,
            'elapsed': elapsed
        }
        
        if success:
            logger.info(f"✅ {test_name} PASSED ({elapsed:.2f}s)")
        else:
            logger.error(f"❌ {test_name} FAILED ({elapsed:.2f}s)")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("INTEGRATION TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result['success'] else "FAILED"
        logger.info(f"{test_name}: {status} ({result['elapsed']:.2f}s)")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All integration tests passed! The TensorFlow hanging issue has been resolved.")
        return 0
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. System may still be functional with available solvers.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
