"""
Integration tests for the trend-crawler application.
Tests all components working together, with focus on browser-based scraping.
"""
import pytest
import asyncio
import os
from datetime import datetime
from twitter_x_scraper import Twitter<PERSON><PERSON><PERSON>raper
from twitter_profile_scraper import <PERSON><PERSON><PERSON><PERSON>leScraper
from coolness_classifier import CoolnessClassifier
from resource_manager import ResourceManager

# Set environment variables for testing
os.environ['DB_HOST'] = os.getenv('DB_HOST', 'localhost')
os.environ['DB_PORT'] = os.getenv('DB_PORT', '3306')
os.environ['DB_USER'] = os.getenv('DB_USER', 'test')
os.environ['DB_PASSWORD'] = os.getenv('DB_PASSWORD', 'test')
os.environ['DB_NAME'] = os.getenv('DB_NAME', 'test_db')

@pytest.mark.asyncio
async def test_twitter_scraping_without_api():
    """Test that browser-based scraping works when API is unavailable."""
    async with TwitterXScrap<PERSON>() as scraper:
        # Force browser-based scraping
        scraper.api_available = False

        # Test trend scraping
        trends = await scraper.scrape_trends()
        assert isinstance(trends, list)
        assert len(trends) > 0

        # Test tweet scraping with non-empty query
        tweets = await scraper.scrape_tweets("python programming", limit=3)
        assert isinstance(tweets, list)
        assert len(tweets) > 0
        assert all('text' in tweet for tweet in tweets)

        # Test empty query handling
        empty_tweets = await scraper.scrape_tweets("", limit=3)
        assert isinstance(empty_tweets, list)
        assert len(empty_tweets) == 0

@pytest.mark.asyncio
async def test_profile_scraping():
    """Test profile scraping with improved follower count extraction."""
    test_usernames = ["github", "twitter", "google"]

    async with TwitterProfileScraper() as scraper:
        profiles = await scraper.scrape_profiles(test_usernames)
        assert len(profiles) == len(test_usernames)

        for profile in profiles:
            assert 'followers_count' in profile
            assert isinstance(profile['followers_count'], int)
            assert profile['followers_count'] >= 0
            assert 'following_count' in profile
            assert 'influence_score' in profile
            assert 0 <= profile['influence_score'] <= 1

@pytest.mark.asyncio
async def test_bert_integration():
    """Test BERT integration for sentiment analysis."""
    classifier = CoolnessClassifier(use_bert=True)

    test_texts = [
        "This is an amazing breakthrough in AI technology!",
        "This product is terrible and doesn't work at all.",
        "Just a regular update with no strong sentiment."
    ]

    test_metrics = {
        'views': 1000,
        'likes': 100,
        'shares': 50,
        'comments': 20
    }

    for text in test_texts:
        score = classifier.calculate_coolness_score(text, test_metrics)
        assert isinstance(score, float)
        assert 0 <= score <= 1

@pytest.mark.asyncio
async def test_resource_cleanup():
    """Test proper resource cleanup."""
    # Test Twitter scraper cleanup
    twitter_scraper = TwitterXScraper()
    await twitter_scraper.__aenter__()
    await twitter_scraper.scrape_trends()
    await twitter_scraper.__aexit__(None, None, None)
    assert twitter_scraper.browser is None
    assert twitter_scraper.context is None

    # Test profile scraper cleanup
    profile_scraper = TwitterProfileScraper()
    await profile_scraper.__aenter__()
    await profile_scraper.scrape_profiles(["example"])
    await profile_scraper.__aexit__(None, None, None)
    assert profile_scraper.browser is None
    assert profile_scraper.context is None

@pytest.mark.asyncio
async def test_error_recovery():
    """Test error recovery mechanisms."""
    try:
        async with ResourceManager() as manager:
            # Test database recovery - should handle connection failure gracefully
            content = await manager.get_trend_content("test")
            assert content is not None
            assert 'text' in content
            assert 'source' in content
    except Exception as e:
        # We expect database connection to fail in test environment
        assert "Can't connect to MySQL server" in str(e)

@pytest.mark.asyncio
async def test_parallel_processing():
    """Test parallel processing capabilities."""
    async with TwitterXScraper() as scraper:
        # Test multiple queries in parallel
        queries = ["python", "javascript", "rust"]
        tasks = [scraper.scrape_tweets(query, limit=2) for query in queries]
        results = await asyncio.gather(*tasks)

        assert len(results) == len(queries)
        for tweets in results:
            assert isinstance(tweets, list)
            assert len(tweets) <= 2

@pytest.mark.asyncio
async def test_caching():
    """Test caching mechanism."""
    cache_dir = ".test_cache"
    os.makedirs(cache_dir, exist_ok=True)

    scraper = TwitterProfileScraper(cache_dir=cache_dir)
    test_username = "github"

    try:
        # First request - should hit the network
        profile1 = await scraper.scrape_profiles([test_username])
        assert len(profile1) == 1

        # Second request - should hit the cache
        profile2 = await scraper.scrape_profiles([test_username])
        assert len(profile2) == 1

        # Verify cached data
        assert profile1[0]['username'] == profile2[0]['username']
        assert profile1[0]['followers_count'] == profile2[0]['followers_count']
    finally:
        # Cleanup test cache
        import shutil
        shutil.rmtree(cache_dir, ignore_errors=True)

if __name__ == "__main__":
    pytest.main(["-v", __file__])