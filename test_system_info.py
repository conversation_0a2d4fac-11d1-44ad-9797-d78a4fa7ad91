from fastapi.testclient import TestClient
from unittest.mock import patch
# Assuming your FastAPI app instance is named 'app' in 'web_dashboard.py'
# Adjust the import if your app instance or file name is different.
from web_dashboard import app # Make sure 'app' is importable

client = TestClient(app)

EXPECTED_KEYS = [
    "hostname", "cpu_usage", "memory_usage", "disk_usage",
    "os_info", "kernel", "cpu_info", "python_version",
    "uptime", "load_avg"
]

def test_get_system_info_psutil_available():
    """
    Test the /api/v1/system/info endpoint when psutil is considered available.
    This test relies on psutil actually being available in the test environment
    or being well-mocked if specific psutil return values were needed.
    """
    response = client.get("/api/v1/system/info")
    assert response.status_code == 200
    data = response.json()

    for key in EXPECTED_KEYS:
        assert key in data, f"Expected key '{key}' not found in response."

    # python_version should always be available via platform module
    assert data["python_version"] != "N/A", "python_version should not be 'N/A'."
    # hostname is also from platform module
    assert data["hostname"] != "N/A", "hostname should not be 'N/A'."
    
    # If psutil is truly available and working, these fields should not be "N/A"
    # However, in some CI environments, psutil calls might fail or return unusual values.
    # This test primarily checks the structure and that platform data is present.
    # A more advanced test would mock each psutil call to return predictable values.
    # For now, we'll just check that the psutil dependent keys are not "N/A (psutil not available)"
    # which would indicate PSUTIL_AVAILABLE was false.
    psutil_dependent_keys = ["cpu_usage", "memory_usage", "disk_usage", "uptime", "load_avg"]
    for key in psutil_dependent_keys:
        assert "N/A (psutil not available)" not in data[key], f"Key '{key}' indicates psutil was unavailable, but this test assumes it is."
        # We can't be certain they won't be "N/A (Error)" if psutil itself has an issue.

def test_get_system_info_psutil_unavailable():
    """
    Test the /api/v1/system/info endpoint when psutil and monitoring are explicitly
    mocked as unavailable to isolate the psutil-specific fallback logic.
    """
    with patch("web_dashboard.MONITORING_AVAILABLE", False):
        with patch("web_dashboard.PSUTIL_AVAILABLE", False):
            response = client.get("/api/v1/system/info")
            assert response.status_code == 200
            data = response.json()

            for key in EXPECTED_KEYS:
                assert key in data, f"Expected key '{key}' not found in response."

            # For psutil-dependent fields, expect "N/A"
            psutil_dependent_keys = ["cpu_usage", "memory_usage", "disk_usage", "uptime", "load_avg"]
            for key in psutil_dependent_keys:
                assert data[key] == "N/A", f"Key '{key}' should be 'N/A' when PSUTIL_AVAILABLE and MONITORING_AVAILABLE are False, but got '{data[key]}'."

            # Platform-dependent fields should still attempt to return actual data
            assert data["python_version"] != "N/A", "python_version should not be 'N/A'."
            assert data["hostname"] is not None 
            assert data["os_info"] is not None
            assert data["kernel"] is not None
            assert data["cpu_info"] is not None

# Example of how to run with pytest if this file is executed directly (optional)
if __name__ == "__main__":
    import pytest
    pytest.main([__file__])
