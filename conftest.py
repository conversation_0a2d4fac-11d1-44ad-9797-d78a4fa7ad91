"""
Pytest configuration for async tests.
"""
import pytest
import pytest_asyncio
from typing import List

@pytest_asyncio.fixture
async def sample_texts():
    """Provide sample texts for testing."""
    return [
        "Revolutionary new AI technology transforms data analysis",
        "Breaking: Major breakthrough in quantum computing",
        "Startup launches innovative blockchain solution",
        "Scientists discover breakthrough in renewable energy",
        "New machine learning model achieves record accuracy"
    ]

# Mock data fixtures removed

@pytest_asyncio.fixture
async def profiles():
    """Provide test profile names."""
    return [
        "github",
        "twitter",
        "google",
        "microsoft",
        "openai"
    ]

@pytest_asyncio.fixture
async def trend():
    """Provide test trend name."""
    return "artificial_intelligence"