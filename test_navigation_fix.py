#!/usr/bin/env python3
"""
Test script to verify the navigation click interception fix
Tests that navigation links properly handle authentication
"""

import requests
import json
import time
from urllib.parse import urljoin

BASE_URL = "http://localhost:8000"

def test_login():
    """Test login and get authentication token"""
    print("🔐 Testing login...")
    
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code == 200:
        data = response.json()
        token = data.get("access_token")
        print(f"✅ Login successful, token: {token[:20]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def test_protected_routes_with_auth(token):
    """Test protected routes with valid authentication"""
    print("\n🔒 Testing protected routes with authentication...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    }
    
    protected_routes = [
        "/",
        "/dashboard", 
        "/scrapers",
        "/proxies",
        "/captchas",
        "/alerts",
        "/system",
        "/account",
        "/user-management"
    ]
    
    results = {}
    
    for route in protected_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}", headers=headers)
            status = response.status_code
            results[route] = status
            
            if status == 200:
                print(f"✅ {route} -> {status} (OK)")
            elif status == 307:
                print(f"✅ {route} -> {status} (Redirect - expected for root)")
            else:
                print(f"⚠️  {route} -> {status}")
                
        except Exception as e:
            print(f"❌ {route} -> ERROR: {e}")
            results[route] = "ERROR"
    
    return results

def test_protected_routes_without_auth():
    """Test protected routes without authentication (should return 401)"""
    print("\n🚫 Testing protected routes without authentication...")
    
    headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    }
    
    protected_routes = [
        "/dashboard", 
        "/scrapers",
        "/proxies", 
        "/captchas",
        "/alerts",
        "/system",
        "/account",
        "/user-management"
    ]
    
    results = {}
    
    for route in protected_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}", headers=headers, allow_redirects=False)
            status = response.status_code
            results[route] = status
            
            if status == 401:
                print(f"✅ {route} -> {status} (Unauthorized - expected)")
            else:
                print(f"⚠️  {route} -> {status} (unexpected)")
                
        except Exception as e:
            print(f"❌ {route} -> ERROR: {e}")
            results[route] = "ERROR"
    
    return results

def test_public_routes():
    """Test public routes (should be accessible without authentication)"""
    print("\n🌍 Testing public routes...")
    
    public_routes = [
        "/login"
    ]
    
    results = {}
    
    for route in public_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}")
            status = response.status_code
            results[route] = status
            
            if status == 200:
                print(f"✅ {route} -> {status} (OK)")
            else:
                print(f"⚠️  {route} -> {status}")
                
        except Exception as e:
            print(f"❌ {route} -> ERROR: {e}")
            results[route] = "ERROR"
    
    return results

def test_disabled_routes():
    """Test routes that should be disabled (like registration)"""
    print("\n🚷 Testing disabled routes...")
    
    disabled_routes = [
        "/register"
    ]
    
    results = {}
    
    for route in disabled_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}")
            status = response.status_code
            results[route] = status
            
            if status == 404:
                print(f"✅ {route} -> {status} (Not Found - expected for disabled route)")
            else:
                print(f"⚠️  {route} -> {status} (unexpected)")
                
        except Exception as e:
            print(f"❌ {route} -> ERROR: {e}")
            results[route] = "ERROR"
    
    return results

def test_auth_flow():
    """Test the authentication flow that the JavaScript should handle"""
    print("\n🔄 Testing authentication flow...")
    
    # First, try to access a protected route without auth
    response = requests.get(f"{BASE_URL}/scrapers", allow_redirects=False)
    if response.status_code == 401:
        print("✅ Unauthenticated request returns 401 (good)")
    else:
        print(f"⚠️  Unauthenticated request returns {response.status_code} (expected 401)")
    
    # Login and get token
    token = test_login()
    if not token:
        print("❌ Cannot test authenticated flow - login failed")
        return
    
    # Try the same route with authentication
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/scrapers", headers=headers)
    if response.status_code == 200:
        print("✅ Authenticated request returns 200 (good)")
    else:
        print(f"⚠️  Authenticated request returns {response.status_code} (expected 200)")

def main():
    """Run all tests"""
    print("🧪 Testing Navigation Fix for Trend Crawler Private Mode")
    print("=" * 60)
    
    # Test server is running
    try:
        response = requests.get(f"{BASE_URL}/login", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not responding properly: {response.status_code}")
            return
        print("✅ Server is running and responding")
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Run tests
    token = test_login()
    
    if token:
        test_protected_routes_with_auth(token)
    
    test_protected_routes_without_auth()
    test_public_routes()
    test_disabled_routes()
    test_auth_flow()
    
    print("\n" + "=" * 60)
    print("🎯 Navigation Fix Test Summary:")
    print("• The JavaScript auth-guard.js now intercepts navigation clicks")
    print("• When clicking nav links, it checks authentication first")
    print("• If 401/403 response, it redirects to login instead of showing error")
    print("• This should fix the issue where tabs showed 401 without redirect")
    print("\n✨ Test the fix by:")
    print("1. Open http://localhost:8000/login in browser")
    print("2. Login with admin/admin")
    print("3. Click on navigation tabs (scrapers, alerts, etc.)")
    print("4. They should load properly or redirect to login if session expired")

if __name__ == "__main__":
    main()
