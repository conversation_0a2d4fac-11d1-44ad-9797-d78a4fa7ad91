<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication - Trend Crawler</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .status-indicator {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: var(--radius-md);
            text-align: center;
            font-weight: 500;
        }
        .status-authenticated {
            background: var(--success-gradient);
            color: white;
        }
        .status-unauthenticated {
            background: var(--error-gradient);
            color: white;
        }
        .test-button {
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition-normal);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <h1>Authentication Test Page</h1>
            
            <div id="auth-status" class="status-indicator">
                Checking authentication status...
            </div>
            
            <div id="token-info">
                <h3>Token Information</h3>
                <p><strong>Token exists:</strong> <span id="token-exists">Checking...</span></p>
                <p><strong>Token value:</strong> <span id="token-value">Checking...</span></p>
                <p><strong>Token expired:</strong> <span id="token-expired">Checking...</span></p>
            </div>
            
            <div id="server-verification">
                <h3>Server Verification</h3>
                <p><strong>Server response:</strong> <span id="server-status">Not checked</span></p>
                <button class="test-button" onclick="verifyWithServer()">Verify with Server</button>
            </div>
            
            <div id="test-actions">
                <h3>Test Actions</h3>
                <button class="test-button" onclick="testApiCall()">Test API Call</button>
                <button class="test-button" onclick="clearToken()">Clear Token</button>
                <button class="test-button" onclick="goToLogin()">Go to Login</button>
                <button class="test-button" onclick="goToDashboard()">Go to Dashboard</button>
            </div>
            
            <div id="console-output">
                <h3>Console Output</h3>
                <div id="console-log" style="background: var(--bg-tertiary); padding: 1rem; border-radius: var(--radius-md); font-family: monospace; max-height: 300px; overflow-y: auto;">
                </div>
            </div>
        </div>
    </div>

    <script src="static/js/dashboard.js"></script>
    <script>
        // Override console.log to show output on page
        const originalConsoleLog = console.log;
        const consoleLogElement = document.getElementById('console-log');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg).join(' ');
            consoleLogElement.innerHTML += `<div style="margin-bottom: 0.5rem; color: var(--text-secondary);">${new Date().toLocaleTimeString()}: ${message}</div>`;
            consoleLogElement.scrollTop = consoleLogElement.scrollHeight;
        };

        function updateAuthStatus() {
            const isAuth = AuthUtils.isAuthenticated();
            const statusElement = document.getElementById('auth-status');
            
            if (isAuth) {
                statusElement.className = 'status-indicator status-authenticated';
                statusElement.textContent = '✓ User is authenticated';
            } else {
                statusElement.className = 'status-indicator status-unauthenticated';
                statusElement.textContent = '✗ User is not authenticated';
            }
            
            // Update token info
            const token = AuthUtils.getToken();
            document.getElementById('token-exists').textContent = token ? 'Yes' : 'No';
            document.getElementById('token-value').textContent = token ? token.substring(0, 50) + '...' : 'None';
            document.getElementById('token-expired').textContent = token ? (AuthUtils.isTokenExpired(token) ? 'Yes' : 'No') : 'N/A';
            
            console.log('Authentication status updated:', { isAuth, hasToken: !!token });
        }

        async function verifyWithServer() {
            console.log('Starting server verification...');
            const isValid = await AuthUtils.verifyToken();
            document.getElementById('server-status').textContent = isValid ? 'Valid' : 'Invalid/Failed';
            console.log('Server verification result:', isValid);
            updateAuthStatus();
        }

        async function testApiCall() {
            console.log('Testing API call...');
            try {
                const response = await authenticatedFetch('/api/v1/auth/me');
                if (response) {
                    const data = await response.json();
                    console.log('API call successful:', data);
                } else {
                    console.log('API call failed - no response (likely redirected to login)');
                }
            } catch (error) {
                console.log('API call error:', error.message);
            }
            updateAuthStatus();
        }

        function clearToken() {
            console.log('Clearing token...');
            localStorage.removeItem('access_token');
            updateAuthStatus();
        }

        function goToLogin() {
            console.log('Redirecting to login...');
            window.location.href = '/login';
        }

        function goToDashboard() {
            console.log('Redirecting to dashboard...');
            window.location.href = '/dashboard';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            updateAuthStatus();
        });
    </script>
</body>
</html>
