#!/usr/bin/env python3
"""
Docker Deployment Configuration for trend-crawler

This module handles Docker setup for the nano neural network architecture.
"""

import os
import logging
import json
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DockerManager:
    """
    Manages Docker deployment for the trend-crawler application.
    
    Handles Dockerfile generation, Docker Compose setup, and container
    orchestration for secure deployment.
    """
    
    def __init__(self, base_dir: str):
        """
        Initialize Docker manager.
        
        Args:
            base_dir: Base directory for Docker files
        """
        self.base_dir = base_dir
        self.dockerfile_path = os.path.join(base_dir, "Dockerfile")
        self.docker_compose_path = os.path.join(base_dir, "docker-compose.yml")
        self.env_file_path = os.path.join(base_dir, ".env")
        self.scripts_dir = os.path.join(base_dir, "scripts")
        
        # Ensure scripts directory exists
        os.makedirs(self.scripts_dir, exist_ok=True)
        
    def generate_dockerfile(self, use_gpu: bool = False) -> str:
        """
        Generate Dockerfile content.
        
        Args:
            use_gpu: Whether to include GPU support
            
        Returns:
            Dockerfile content
        """
        python_version = "3.10"
        base_image = f"python:{python_version}-slim"
        
        # For GPU support, use pytorch image
        if use_gpu:
            base_image = f"pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime"
        
        dockerfile_content = f"""# Dockerfile for Secure Nano Neural Network
FROM {base_image}

# Setup working directory
WORKDIR /app

# Add non-root user for better security
RUN groupadd -g 1000 appuser && \\
    useradd -u 1000 -g appuser -s /bin/bash -m appuser

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \\
    PYTHONUNBUFFERED=1 \\
    PIP_NO_CACHE_DIR=1 \\
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \\
    build-essential \\
    libpq-dev \\
    gcc \\
    g++ \\
    git \\
    curl \\
    && apt-get clean \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt /app/

# Install Python dependencies
RUN pip install --upgrade pip && \\
    pip install -r requirements.txt

# Copy application code
COPY . /app/

# Set proper ownership
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose necessary ports
EXPOSE 8000

# Create volume for data persistence
VOLUME /app/data

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \\
    CMD curl -f http://localhost:8000/health || exit 1

# Set entrypoint and command
ENTRYPOINT ["python"]
CMD ["app.py"]
"""
        
        # Write Dockerfile
        with open(self.dockerfile_path, "w") as f:
            f.write(dockerfile_content)
            
        logger.info(f"Generated Dockerfile: {self.dockerfile_path}")
        return dockerfile_content
    
    def generate_docker_compose(self, 
                               services: Dict[str, Dict[str, Any]] = None) -> str:
        """
        Generate Docker Compose file.
        
        Args:
            services: Additional services to include
            
        Returns:
            Docker Compose content
        """
        # Default services
        default_services = {
            "app": {
                "build": ".",
                "restart": "unless-stopped",
                "ports": ["8000:8000"],
                "volumes": ["./:/app", "./data:/app/data"],
                "env_file": [".env"],
                "depends_on": ["db"],
                "networks": ["app-network"]
            },
            "db": {
                "image": "postgres:15-alpine",
                "restart": "unless-stopped",
                "environment": {
                    "POSTGRES_USER": "${POSTGRES_USER:-postgres}",
                    "POSTGRES_PASSWORD": "${POSTGRES_PASSWORD:-postgres}",
                    "POSTGRES_DB": "${POSTGRES_DB:-trend_crawler}"
                },
                "ports": ["5432:5432"],
                "volumes": ["postgres_data:/var/lib/postgresql/data"],
                "networks": ["app-network"]
            }
        }
        
        # Merge with provided services
        if services:
            for name, config in services.items():
                if name in default_services:
                    default_services[name].update(config)
                else:
                    default_services[name] = config
        
        # Generate Docker Compose content
        compose_content = """version: '3.8'

services:
"""
        
        # Add each service
        for name, config in default_services.items():
            compose_content += f"  {name}:\n"
            
            for key, value in config.items():
                if isinstance(value, str):
                    compose_content += f"    {key}: {value}\n"
                elif isinstance(value, list):
                    compose_content += f"    {key}:\n"
                    for item in value:
                        compose_content += f"      - {item}\n"
                elif isinstance(value, dict):
                    compose_content += f"    {key}:\n"
                    for k, v in value.items():
                        compose_content += f"      {k}: {v}\n"
                else:
                    compose_content += f"    {key}: {value}\n"
        
        # Add networks and volumes
        compose_content += """
networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
"""
        
        # Write Docker Compose file
        with open(self.docker_compose_path, "w") as f:
            f.write(compose_content)
            
        logger.info(f"Generated Docker Compose file: {self.docker_compose_path}")
        return compose_content
    
    def generate_env_file(self, env_vars: Dict[str, str] = None) -> str:
        """
        Generate environment variables file.
        
        Args:
            env_vars: Environment variables
            
        Returns:
            Environment file content
        """
        # Default environment variables
        default_env = {
            "POSTGRES_USER": "postgres",
            "POSTGRES_PASSWORD": "postgres",
            "POSTGRES_DB": "trend_crawler",
            "DB_HOST": "db",
            "DB_PORT": "5432",
            "DB_NAME": "trend_crawler",
            "DB_USER": "postgres",
            "DB_PASSWORD": "postgres",
            "LOG_LEVEL": "INFO",
            "MODEL_VERSION": "1.0.0"
        }
        
        # Merge with provided variables
        if env_vars:
            default_env.update(env_vars)
            
        # Generate environment file content
        env_content = ""
        for key, value in default_env.items():
            env_content += f"{key}={value}\n"
            
        # Write environment file
        with open(self.env_file_path, "w") as f:
            f.write(env_content)
            
        logger.info(f"Generated environment file: {self.env_file_path}")
        return env_content
    
    def generate_startup_script(self) -> str:
        """
        Generate startup script for Docker.
        
        Returns:
            Startup script content
        """
        script_path = os.path.join(self.scripts_dir, "startup.sh")
        
        script_content = """#!/bin/bash
# Startup script for trend-crawler application

set -e

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL..."
while ! pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER; do
  sleep 1
done
echo "PostgreSQL is ready"

# Run database migrations
echo "Running database setup..."
python setup_db.py

# Start application
echo "Starting application..."
exec python app.py
"""
        
        # Write script file
        with open(script_path, "w") as f:
            f.write(script_content)
            
        # Make executable
        os.chmod(script_path, 0o755)
        
        logger.info(f"Generated startup script: {script_path}")
        return script_content
    
    def generate_requirements(self) -> str:
        """
        Generate requirements file.
        
        Returns:
            Requirements file content
        """
        requirements_path = os.path.join(self.base_dir, "requirements.txt")
        
        requirements_content = """# Requirements for trend-crawler
numpy==1.23.5
torch==2.0.1
sentence-transformers==2.2.2
scikit-learn==1.2.2
psycopg2-binary==2.9.6
cryptography==39.0.1
hdbscan==0.8.29
fastapi==0.95.1
uvicorn==0.22.0
python-dotenv==1.0.0
pydantic==1.10.8
pytest==7.3.1
httpx==0.24.0
pandas==2.0.1
"""
        
        # Write requirements file
        with open(requirements_path, "w") as f:
            f.write(requirements_content)
            
        logger.info(f"Generated requirements file: {requirements_path}")
        return requirements_content
    
    def generate_all(self, use_gpu: bool = False) -> Dict[str, str]:
        """
        Generate all Docker files.
        
        Args:
            use_gpu: Whether to include GPU support
            
        Returns:
            Dictionary with file paths and contents
        """
        results = {}
        
        # Generate files
        results["dockerfile"] = self.generate_dockerfile(use_gpu)
        results["docker_compose"] = self.generate_docker_compose()
        results["env_file"] = self.generate_env_file()
        results["startup_script"] = self.generate_startup_script()
        results["requirements"] = self.generate_requirements()
        
        return results


class MonitoringDashboard:
    """
    Generates a monitoring dashboard for the trend-crawler application.
    
    Uses Prometheus and Grafana for metrics collection and visualization.
    """
    
    def __init__(self, base_dir: str):
        """
        Initialize monitoring dashboard.
        
        Args:
            base_dir: Base directory for dashboard files
        """
        self.base_dir = base_dir
        self.dashboard_dir = os.path.join(base_dir, "monitoring")
        
        # Ensure dashboard directory exists
        os.makedirs(self.dashboard_dir, exist_ok=True)
        
    def generate_prometheus_config(self) -> str:
        """
        Generate Prometheus configuration.
        
        Returns:
            Prometheus configuration content
        """
        config_path = os.path.join(self.dashboard_dir, "prometheus.yml")
        
        config_content = """global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'trend_crawler'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/metrics'
    
  - job_name: 'node_exporter'
    static_configs:
      - targets: ['node-exporter:9100']
"""
        
        # Write configuration file
        with open(config_path, "w") as f:
            f.write(config_content)
            
        logger.info(f"Generated Prometheus configuration: {config_path}")
        return config_content
    
    def generate_grafana_dashboard(self) -> str:
        """
        Generate Grafana dashboard configuration.
        
        Returns:
            Grafana dashboard content
        """
        dashboard_path = os.path.join(self.dashboard_dir, "dashboard.json")
        
        dashboard = {
            "annotations": {
                "list": [
                    {
                        "builtIn": 1,
                        "datasource": "-- Grafana --",
                        "enable": True,
                        "hide": True,
                        "iconColor": "rgba(0, 211, 255, 1)",
                        "name": "Annotations & Alerts",
                        "type": "dashboard"
                    }
                ]
            },
            "editable": True,
            "gnetId": None,
            "graphTooltip": 0,
            "id": 1,
            "links": [],
            "panels": [
                {
                    "aliasColors": {},
                    "bars": False,
                    "dashLength": 10,
                    "dashes": False,
                    "datasource": "Prometheus",
                    "fieldConfig": {
                        "defaults": {
                            "custom": {},
                            "unit": "short"
                        },
                        "overrides": []
                    },
                    "fill": 1,
                    "fillGradient": 0,
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 0,
                        "y": 0
                    },
                    "hiddenSeries": False,
                    "id": 2,
                    "legend": {
                        "avg": False,
                        "current": False,
                        "max": False,
                        "min": False,
                        "show": True,
                        "total": False,
                        "values": False
                    },
                    "lines": True,
                    "linewidth": 1,
                    "nullPointMode": "null",
                    "options": {
                        "dataLinks": []
                    },
                    "percentage": False,
                    "pointradius": 2,
                    "points": False,
                    "renderer": "flot",
                    "seriesOverrides": [],
                    "spaceLength": 10,
                    "stack": False,
                    "steppedLine": False,
                    "targets": [
                        {
                            "expr": "rate(http_requests_total[5m])",
                            "interval": "",
                            "legendFormat": "{{method}} {{path}}",
                            "refId": "A"
                        }
                    ],
                    "thresholds": [],
                    "timeRegions": [],
                    "title": "HTTP Request Rate",
                    "tooltip": {
                        "shared": True,
                        "sort": 0,
                        "value_type": "individual"
                    },
                    "type": "graph",
                    "xaxis": {
                        "buckets": None,
                        "mode": "time",
                        "name": None,
                        "show": True,
                        "values": []
                    },
                    "yaxes": [
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        },
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        }
                    ],
                    "yaxis": {
                        "align": False,
                        "alignLevel": None
                    }
                },
                {
                    "aliasColors": {},
                    "bars": False,
                    "dashLength": 10,
                    "dashes": False,
                    "datasource": "Prometheus",
                    "fieldConfig": {
                        "defaults": {
                            "custom": {},
                            "unit": "percentunit"
                        },
                        "overrides": []
                    },
                    "fill": 1,
                    "fillGradient": 0,
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 12,
                        "y": 0
                    },
                    "hiddenSeries": False,
                    "id": 4,
                    "legend": {
                        "avg": False,
                        "current": False,
                        "max": False,
                        "min": False,
                        "show": True,
                        "total": False,
                        "values": False
                    },
                    "lines": True,
                    "linewidth": 1,
                    "nullPointMode": "null",
                    "options": {
                        "dataLinks": []
                    },
                    "percentage": False,
                    "pointradius": 2,
                    "points": False,
                    "renderer": "flot",
                    "seriesOverrides": [],
                    "spaceLength": 10,
                    "stack": False,
                    "steppedLine": False,
                    "targets": [
                        {
                            "expr": "rate(process_cpu_seconds_total[5m])",
                            "interval": "",
                            "legendFormat": "CPU",
                            "refId": "A"
                        }
                    ],
                    "thresholds": [],
                    "timeRegions": [],
                    "title": "CPU Usage",
                    "tooltip": {
                        "shared": True,
                        "sort": 0,
                        "value_type": "individual"
                    },
                    "type": "graph",
                    "xaxis": {
                        "buckets": None,
                        "mode": "time",
                        "name": None,
                        "show": True,
                        "values": []
                    },
                    "yaxes": [
                        {
                            "format": "percentunit",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        },
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        }
                    ],
                    "yaxis": {
                        "align": False,
                        "alignLevel": None
                    }
                },
                {
                    "aliasColors": {},
                    "bars": False,
                    "dashLength": 10,
                    "dashes": False,
                    "datasource": "Prometheus",
                    "fieldConfig": {
                        "defaults": {
                            "custom": {},
                            "unit": "bytes"
                        },
                        "overrides": []
                    },
                    "fill": 1,
                    "fillGradient": 0,
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 0,
                        "y": 8
                    },
                    "hiddenSeries": False,
                    "id": 6,
                    "legend": {
                        "avg": False,
                        "current": False,
                        "max": False,
                        "min": False,
                        "show": True,
                        "total": False,
                        "values": False
                    },
                    "lines": True,
                    "linewidth": 1,
                    "nullPointMode": "null",
                    "options": {
                        "dataLinks": []
                    },
                    "percentage": False,
                    "pointradius": 2,
                    "points": False,
                    "renderer": "flot",
                    "seriesOverrides": [],
                    "spaceLength": 10,
                    "stack": False,
                    "steppedLine": False,
                    "targets": [
                        {
                            "expr": "process_resident_memory_bytes",
                            "interval": "",
                            "legendFormat": "Memory",
                            "refId": "A"
                        }
                    ],
                    "thresholds": [
                        {
                            "colorMode": "critical",
                            "fill": True,
                            "line": True,
                            "op": "gt",
                            "value": 524288000,
                            "yaxis": "left"
                        }
                    ],
                    "timeRegions": [],
                    "title": "Memory Usage",
                    "tooltip": {
                        "shared": True,
                        "sort": 0,
                        "value_type": "individual"
                    },
                    "type": "graph",
                    "xaxis": {
                        "buckets": None,
                        "mode": "time",
                        "name": None,
                        "show": True,
                        "values": []
                    },
                    "yaxes": [
                        {
                            "format": "bytes",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        },
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        }
                    ],
                    "yaxis": {
                        "align": False,
                        "alignLevel": None
                    }
                },
                {
                    "aliasColors": {},
                    "bars": False,
                    "dashLength": 10,
                    "dashes": False,
                    "datasource": "Prometheus",
                    "fieldConfig": {
                        "defaults": {
                            "custom": {},
                            "unit": "short"
                        },
                        "overrides": []
                    },
                    "fill": 1,
                    "fillGradient": 0,
                    "gridPos": {
                        "h": 8,
                        "w": 12,
                        "x": 12,
                        "y": 8
                    },
                    "hiddenSeries": False,
                    "id": 8,
                    "legend": {
                        "avg": False,
                        "current": False,
                        "max": False,
                        "min": False,
                        "show": True,
                        "total": False,
                        "values": False
                    },
                    "lines": True,
                    "linewidth": 1,
                    "nullPointMode": "null",
                    "options": {
                        "dataLinks": []
                    },
                    "percentage": False,
                    "pointradius": 2,
                    "points": False,
                    "renderer": "flot",
                    "seriesOverrides": [],
                    "spaceLength": 10,
                    "stack": False,
                    "steppedLine": False,
                    "targets": [
                        {
                            "expr": "trends_processed_total",
                            "interval": "",
                            "legendFormat": "Trends",
                            "refId": "A"
                        }
                    ],
                    "thresholds": [],
                    "timeRegions": [],
                    "title": "Trends Processed",
                    "tooltip": {
                        "shared": True,
                        "sort": 0,
                        "value_type": "individual"
                    },
                    "type": "graph",
                    "xaxis": {
                        "buckets": None,
                        "mode": "time",
                        "name": None,
                        "show": True,
                        "values": []
                    },
                    "yaxes": [
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        },
                        {
                            "format": "short",
                            "label": None,
                            "logBase": 1,
                            "max": None,
                            "min": None,
                            "show": True
                        }
                    ],
                    "yaxis": {
                        "align": False,
                        "alignLevel": None
                    }
                }
            ],
            "refresh": "10s",
            "schemaVersion": 25,
            "style": "dark",
            "tags": [],
            "templating": {
                "list": []
            },
            "time": {
                "from": "now-1h",
                "to": "now"
            },
            "timepicker": {
                "refresh_intervals": [
                    "5s",
                    "10s",
                    "30s",
                    "1m",
                    "5m",
                    "15m",
                    "30m",
                    "1h",
                    "2h",
                    "1d"
                ]
            },
            "timezone": "",
            "title": "Trend Crawler Dashboard",
            "uid": "trend-crawler-dashboard",
            "version": 1
        }
        
        # Write dashboard file
        with open(dashboard_path, "w") as f:
            json.dump(dashboard, f, indent=2)
            
        logger.info(f"Generated Grafana dashboard: {dashboard_path}")
        return json.dumps(dashboard, indent=2)
    
    def generate_docker_compose(self) -> str:
        """
        Generate Docker Compose file for monitoring.
        
        Returns:
            Docker Compose content
        """
        compose_path = os.path.join(self.dashboard_dir, "docker-compose-monitoring.yml")
        
        compose_content = """version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    networks:
      - app-network

  grafana:
    image: grafana/grafana:9.5.3
    container_name: grafana
    restart: unless-stopped
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/dashboard.json:/etc/grafana/provisioning/dashboards/dashboard.json
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    networks:
      - app-network

  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - app-network

networks:
  app-network:
    external: true

volumes:
  prometheus_data:
  grafana_data:
"""
        
        # Write Docker Compose file
        with open(compose_path, "w") as f:
            f.write(compose_content)
            
        logger.info(f"Generated Docker Compose for monitoring: {compose_path}")
        return compose_content
    
    def generate_all(self) -> Dict[str, str]:
        """
        Generate all monitoring files.
        
        Returns:
            Dictionary with file paths and contents
        """
        results = {}
        
        # Generate files
        results["prometheus_config"] = self.generate_prometheus_config()
        results["grafana_dashboard"] = self.generate_grafana_dashboard()
        results["docker_compose"] = self.generate_docker_compose()
        
        return results


if __name__ == "__main__":
    # Get base directory from environment or use current directory
    base_dir = os.environ.get("APP_DIR", os.getcwd())
    
    # Generate Docker files
    docker_manager = DockerManager(base_dir)
    docker_files = docker_manager.generate_all(use_gpu=False)
    
    # Generate monitoring files
    monitoring_dashboard = MonitoringDashboard(base_dir)
    monitoring_files = monitoring_dashboard.generate_all()
    
    print(f"Generated Docker and monitoring files in {base_dir}")
