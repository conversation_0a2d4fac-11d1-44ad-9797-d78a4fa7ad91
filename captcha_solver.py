#!/usr/bin/env python3
"""
CAPTCHA Solver Module for Web Scrapers

This module provides functionality to solve various types of CAPTCHAs
encountered during web scraping, including:
- reCAPTCHA v2
- reCAPTCHA v3
- hCaptcha
- Image CAPTCHAs
- Text CAPTCHAs
- Cloudflare Turnstile

The module supports multiple CAPTCHA solving services:
- 2Captcha
- Anti-Captcha
- CapSolver
- Local ML-based solvers (PyTorch and TensorFlow)

Advanced features:
- JavaScript-based CAPTCHA detection for dynamic content
- Feedback loop for continuous improvement
- Integration with monitoring system
- Performance metrics and analytics
"""

import os
import time
import base64
import logging
import traceback
import json
import enum
import re
from typing import Dict, Any, Optional, Union, Tuple, List
from pathlib import Path
from functools import wraps
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import optional dependencies
try:
    from twocaptcha import TwoCaptcha
    TWOCAPTCHA_AVAILABLE = True
    logger.info("2Captcha module available")
except ImportError:
    TWOCAPTCHA_AVAILABLE = False
    logger.warning("2Captcha module not available. Install with 'pip install 2captcha-python'")

try:
    from anticaptchaofficial.recaptchav2proxyless import recaptchaV2Proxyless
    from anticaptchaofficial.hcaptchaproxyless import hCaptchaProxyless
    from anticaptchaofficial.imagecaptcha import imagecaptcha
    ANTICAPTCHA_AVAILABLE = True
    logger.info("AntiCaptcha module available")
except ImportError:
    ANTICAPTCHA_AVAILABLE = False
    logger.warning("AntiCaptcha module not available. Install with 'pip install anticaptchaofficial'")

try:
    import capsolver
    CAPSOLVER_AVAILABLE = True
    logger.info("CapSolver module available")
except ImportError:
    CAPSOLVER_AVAILABLE = False
    logger.warning("CapSolver module not available. Install with 'pip install capsolver'")

try:
    import cv2
    import numpy as np
    from PIL import Image
    try:
        import pytesseract
        OCR_AVAILABLE = True
        logger.info("OCR dependencies available for local CAPTCHA solving")
    except ImportError:
        OCR_AVAILABLE = False
        logger.warning("Pytesseract not available, some local CAPTCHA solving will be disabled")
except ImportError:
    OCR_AVAILABLE = False
    logger.warning("OpenCV/PIL not available, local CAPTCHA solving will be disabled")

# Try to import ML-based solvers (availability flags only, no classes)
try:
    from ml_captcha_solvers import (
        PYTORCH_AVAILABLE,
        TENSORFLOW_AVAILABLE
    )
    ML_SOLVERS_AVAILABLE = PYTORCH_AVAILABLE or TENSORFLOW_AVAILABLE
    if ML_SOLVERS_AVAILABLE:
        logger.info("ML-based CAPTCHA solvers available")
    else:
        logger.warning("ML solvers available but dependencies not available")
except ImportError:
    ML_SOLVERS_AVAILABLE = False
    PYTORCH_AVAILABLE = False
    TENSORFLOW_AVAILABLE = False
    logger.warning("ML-based CAPTCHA solvers not available. Check package")


# Helper functions for dynamic ML solver imports
def _get_pytorch_solver():
    """Dynamically import PyTorch solver only when needed."""
    try:
        from ml_captcha_solvers import PyTorchCaptchaSolver
        return PyTorchCaptchaSolver
    except ImportError:
        logger.error("Failed to import PyTorchCaptchaSolver")
        return None


def _get_tensorflow_solver():
    """Dynamically import TensorFlow solver only when needed."""
    try:
        from ml_captcha_solvers import TensorFlowCaptchaSolver
        return TensorFlowCaptchaSolver
    except ImportError:
        logger.error("Failed to import TensorFlowCaptchaSolver")
        return None

# Advanced CAPTCHA detection 
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
    logger.info("BeautifulSoup available for HTML parsing")
except ImportError:
    BS4_AVAILABLE = False
    logger.warning("BeautifulSoup not available, some detection features will be limited")

try:
    import selenium
    from selenium import webdriver
    SELENIUM_AVAILABLE = True
    logger.info("Selenium available for JavaScript-based detection")
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.warning("Selenium not available, JavaScript-based detection will be disabled")

# Load configuration
def load_config():
    """Load CAPTCHA solver configuration from file or environment."""
    config = {}

    # Try loading from file
    try:
        with open('proxy_config.json', 'r') as f:
            file_config = json.load(f)
            if 'captcha' in file_config:
                config = file_config['captcha']
                logger.info("Loaded CAPTCHA config from proxy_config.json")
    except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
        logger.warning(f"Could not load CAPTCHA config from file: {e}")

    # Override/set with environment variables
    if 'TWOCAPTCHA_API_KEY' in os.environ:
        config['twocaptcha_key'] = os.environ['TWOCAPTCHA_API_KEY']

    if 'ANTICAPTCHA_API_KEY' in os.environ:
        config['anticaptcha_key'] = os.environ['ANTICAPTCHA_API_KEY']

    if 'CAPSOLVER_API_KEY' in os.environ:
        config['capsolver_key'] = os.environ['CAPSOLVER_API_KEY']

    return config

# Global config
CONFIG = load_config()

# Feedback database (in-memory for now, should be replaced with proper database)
FEEDBACK_DB = {}

# Detection heuristics stats
DETECTION_STATS = {
    'html_pattern_matching': {'detections': 0, 'successes': 0, 'failures': 0},
    'js_execution_analysis': {'detections': 0, 'successes': 0, 'failures': 0},
    'visual_element_detection': {'detections': 0, 'successes': 0, 'failures': 0},
    'network_request_profiling': {'detections': 0, 'successes': 0, 'failures': 0},
    'dynamic_iframe_detection': {'detections': 0, 'successes': 0, 'failures': 0}
}

class CaptchaServiceUnavailable(Exception):
    """Exception raised when a CAPTCHA solving service is not available."""
    pass

class CaptchaSolvingError(Exception):
    """Exception raised when a CAPTCHA cannot be solved."""
    pass

class CaptchaType(enum.Enum):
    """Enum for CAPTCHA types."""
    NONE = "none"
    RECAPTCHA_V2 = "recaptcha_v2"
    RECAPTCHA_V2_INVISIBLE = "recaptcha_v2_invisible"
    RECAPTCHA_V3 = "recaptcha_v3"
    HCAPTCHA = "hcaptcha"
    IMAGE = "image"
    TEXT = "text"
    TURNSTILE = "turnstile"
    DYNAMIC_JS = "dynamic_js"  # New type for JavaScript-loaded CAPTCHAs


class SolverResult:
    """Result of a CAPTCHA solving attempt."""

    def __init__(self, solution: str = None, success: bool = False,
                 service: str = None, captcha_type: CaptchaType = None,
                 error: str = None, elapsed_time: float = 0.0,
                 detection_method: str = None):
        """
        Initialize the result.

        Args:
            solution: The solution string
            success: Whether the solving was successful
            service: The service used to solve the CAPTCHA
            captcha_type: The type of CAPTCHA
            error: Error message if solving failed
            elapsed_time: Time taken to solve the CAPTCHA
            detection_method: Method used to detect the CAPTCHA
        """
        self.solution = solution
        self.success = success
        self.service = service
        self.captcha_type = captcha_type
        self.error = error
        self.elapsed_time = elapsed_time
        self.detection_method = detection_method
        self.timestamp = datetime.now().isoformat()
        self.id = f"{int(time.time())}_{hash(str(self.timestamp))}"


class CaptchaError(Exception):
    """Base exception for CAPTCHA solving errors."""
    pass


class CaptchaTimeoutError(CaptchaError):
    """Exception raised when a CAPTCHA solving times out."""
    pass


class CaptchaFeedback:
    """Class for storing and processing CAPTCHA solution feedback."""
    
    def __init__(self, captcha_id: str, correct_solution: str = None,
                feedback_type: str = None, comments: str = None):
        """Initialize the feedback object."""
        self.captcha_id = captcha_id
        self.correct_solution = correct_solution
        self.feedback_type = feedback_type
        self.comments = comments
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the feedback object to a dictionary."""
        return {
            'captcha_id': self.captcha_id,
            'correct_solution': self.correct_solution,
            'feedback_type': self.feedback_type,
            'comments': self.comments,
            'timestamp': self.timestamp
        }


class AdvancedCaptchaDetector:
    """Advanced CAPTCHA detection using multiple heuristics."""
    
    def __init__(self):
        """Initialize the detector."""
        self.selenium_driver = None
        self._initialize_selenium()
    
    def _initialize_selenium(self):
        """Initialize Selenium WebDriver if available."""
        if not SELENIUM_AVAILABLE:
            self.selenium_driver = None
            return

        try:
            # First try webdriver-manager for automatic driver management
            try:
                from selenium.webdriver.chrome.service import Service
                from webdriver_manager.chrome import ChromeDriverManager
                
                options = webdriver.ChromeOptions()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                
                # Force download latest ChromeDriver version
                service = Service(ChromeDriverManager().install())
                self.selenium_driver = webdriver.Chrome(service=service, options=options)
                logger.info("Selenium WebDriver initialized with webdriver-manager")
                return
            except Exception as e_wdm:
                logger.warning(f"Failed to initialize with webdriver-manager: {e_wdm}")

            # Try undetected-chromedriver as fallback
            try:
                import undetected_chromedriver as uc
                # Use undetected-chromedriver with minimal options to avoid conflicts
                options = uc.ChromeOptions()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                
                # Disable WebDriver detection
                self.selenium_driver = uc.Chrome(options=options, headless=True, use_subprocess=False)
                logger.info("Selenium WebDriver initialized with undetected-chromedriver")
                return
            except Exception as e_uc:
                logger.warning(f"Failed to initialize undetected-chromedriver: {e_uc}")

            # Try chromedriver-autoinstaller as second fallback
            try:
                import chromedriver_autoinstaller
                # Force install latest compatible ChromeDriver version
                driver_path = chromedriver_autoinstaller.install(cwd=True)
                
                options = webdriver.ChromeOptions()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-blink-features=AutomationControlled')
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                
                from selenium.webdriver.chrome.service import Service
                service = Service(driver_path)
                self.selenium_driver = webdriver.Chrome(service=service, options=options)
                logger.info(f"Selenium WebDriver initialized with chromedriver-autoinstaller at {driver_path}")
                return
            except Exception as e_ca:
                logger.warning(f"Failed to initialize with chromedriver-autoinstaller: {e_ca}")

            # Try chromedriver-py as third fallback
            try:
                from chromedriver_py import binary_path
                options = webdriver.ChromeOptions()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                
                self.selenium_driver = webdriver.Chrome(executable_path=binary_path, options=options)
                logger.info("Selenium WebDriver initialized with chromedriver-py")
                return
            except Exception as e_cp:
                logger.warning(f"Failed to initialize with chromedriver-py: {e_cp}")

            # Final fallback - system Chrome
            try:
                options = webdriver.ChromeOptions()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                
                self.selenium_driver = webdriver.Chrome(options=options)
                logger.info("Selenium WebDriver initialized using system Chrome")
                return
            except Exception as e_sys:
                logger.warning(f"Failed to initialize system Chrome: {e_sys}")

            # If all methods fail, disable selenium functionality
            logger.warning("All ChromeDriver initialization methods failed. Selenium-based CAPTCHA detection disabled.")
            self.selenium_driver = None

        except Exception as e:
            logger.error(f"Unexpected error during Selenium initialization: {e}")
            self.selenium_driver = None
    
    def detect_with_html_patterns(self, html_content: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Detect CAPTCHA using HTML pattern matching.
        
        Args:
            html_content: HTML content of the page
            
        Returns:
            A tuple of (captcha_type, site_key) or (None, None) if no CAPTCHA is detected
        """
        DETECTION_STATS['html_pattern_matching']['detections'] += 1
        
        # Check for reCAPTCHA v2
        recaptcha_match = re.search(r'class=["\']g-recaptcha["\'][^>]*data-sitekey=["\']([^"\']+)["\']', html_content)
        if recaptcha_match:
            DETECTION_STATS['html_pattern_matching']['successes'] += 1
            return 'recaptcha_v2', recaptcha_match.group(1)

        # Check for invisible reCAPTCHA
        invisible_match = re.search(r'data-sitekey=["\']([^"\']+)["\'][^>]*data-callback=', html_content)
        if invisible_match:
            DETECTION_STATS['html_pattern_matching']['successes'] += 1
            return 'recaptcha_v2_invisible', invisible_match.group(1)

        # Check for hCaptcha
        hcaptcha_match = re.search(r'class=["\']h-captcha["\'][^>]*data-sitekey=["\']([^"\']+)["\']', html_content)
        if hcaptcha_match:
            DETECTION_STATS['html_pattern_matching']['successes'] += 1
            return 'hcaptcha', hcaptcha_match.group(1)

        # Check for Turnstile
        turnstile_match = re.search(r'class=["\']cf-turnstile["\'][^>]*data-sitekey=["\']([^"\']+)["\']', html_content)
        if turnstile_match:
            DETECTION_STATS['html_pattern_matching']['successes'] += 1
            return 'turnstile', turnstile_match.group(1)
        
        # More advanced HTML parsing with BeautifulSoup if available
        if BS4_AVAILABLE:
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Find reCAPTCHA scripts
                recaptcha_scripts = soup.find_all('script', src=re.compile(r'recaptcha|google\.com/recaptcha'))
                if recaptcha_scripts:
                    # Try to find site key in script or nearby elements
                    # This is a more robust method than regex alone
                    divs = soup.find_all('div', {'data-sitekey': True})
                    if divs:
                        DETECTION_STATS['html_pattern_matching']['successes'] += 1
                        return 'recaptcha_v2', divs[0]['data-sitekey']
                
                # Find hCaptcha scripts
                hcaptcha_scripts = soup.find_all('script', src=re.compile(r'hcaptcha\.com'))
                if hcaptcha_scripts:
                    divs = soup.find_all('div', {'data-sitekey': True})
                    if divs:
                        DETECTION_STATS['html_pattern_matching']['successes'] += 1
                        return 'hcaptcha', divs[0]['data-sitekey']
            
            except Exception as e:
                logger.error(f"Error during BeautifulSoup parsing: {e}")
        
        DETECTION_STATS['html_pattern_matching']['failures'] += 1
        return None, None
    
    def detect_with_javascript_execution(self, url: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Detect dynamically loaded CAPTCHAs using JavaScript execution.
        
        Args:
            url: URL of the page to analyze
            
        Returns:
            A tuple of (captcha_type, site_key) or (None, None) if no CAPTCHA is detected
        """
        if not SELENIUM_AVAILABLE or not self.selenium_driver:
            logger.warning("Selenium not available for JavaScript-based detection")
            return None, None
        
        DETECTION_STATS['js_execution_analysis']['detections'] += 1
        
        try:
            self.selenium_driver.get(url)
            time.sleep(3)  # Wait for JavaScript to execute
            
            # Look for CAPTCHA frames or specific elements
            page_source = self.selenium_driver.page_source
            
            # Check for reCAPTCHA iframes
            recaptcha_frames = self.selenium_driver.find_elements_by_css_selector('iframe[src*="recaptcha"]')
            if recaptcha_frames:
                # Try to extract site key from iframe src
                for frame in recaptcha_frames:
                    src = frame.get_attribute('src')
                    site_key_match = re.search(r'k=([^&]+)', src)
                    if site_key_match:
                        site_key = site_key_match.group(1)
                        DETECTION_STATS['js_execution_analysis']['successes'] += 1
                        return 'recaptcha_v2', site_key
            
            # Check for hCaptcha iframes
            hcaptcha_frames = self.selenium_driver.find_elements_by_css_selector('iframe[src*="hcaptcha"]')
            if hcaptcha_frames:
                for frame in hcaptcha_frames:
                    src = frame.get_attribute('src')
                    site_key_match = re.search(r'sitekey=([^&]+)', src)
                    if site_key_match:
                        site_key = site_key_match.group(1)
                        DETECTION_STATS['js_execution_analysis']['successes'] += 1
                        return 'hcaptcha', site_key
            
            # Check if "I'm not a robot" checkbox is present (generic detection)
            if 'g-recaptcha' in page_source or 'h-captcha' in page_source:
                # Use HTML pattern matching as fallback to get site key
                captcha_type, site_key = self.detect_with_html_patterns(page_source)
                if captcha_type and site_key:
                    DETECTION_STATS['js_execution_analysis']['successes'] += 1
                    return captcha_type, site_key
        
        except Exception as e:
            logger.error(f"Error during JavaScript execution analysis: {e}")
        
        DETECTION_STATS['js_execution_analysis']['failures'] += 1
        return None, None
    
    def detect_with_network_profiling(self, url: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Detect CAPTCHAs by monitoring network requests.
        
        Args:
            url: URL of the page to analyze
            
        Returns:
            A tuple of (captcha_type, site_key) or (None, None) if no CAPTCHA is detected
        """
        if not SELENIUM_AVAILABLE or not self.selenium_driver:
            logger.warning("Selenium not available for network profiling")
            return None, None
        
        DETECTION_STATS['network_request_profiling']['detections'] += 1
        
        try:
            # TODO: Use Selenium with CDP (Chrome DevTools Protocol) to capture network requests
            # For now, using a simpler approach based on page source analysis
            self.selenium_driver.get(url)
            time.sleep(2)  # Wait for requests to occur
            
            # Check the page source for known CAPTCHA API endpoints
            page_source = self.selenium_driver.page_source
            
            if 'www.google.com/recaptcha/api.js' in page_source:
                # Use HTML pattern matching to get site key
                captcha_type, site_key = self.detect_with_html_patterns(page_source)
                if captcha_type and site_key:
                    DETECTION_STATS['network_request_profiling']['successes'] += 1
                    return captcha_type, site_key
            
            if 'js.hcaptcha.com' in page_source:
                # Use HTML pattern matching to get site key
                captcha_type, site_key = self.detect_with_html_patterns(page_source)
                if captcha_type and site_key:
                    DETECTION_STATS['network_request_profiling']['successes'] += 1
                    return captcha_type, site_key
            
            if 'challenges.cloudflare.com' in page_source:
                # Use HTML pattern matching to get site key
                captcha_type, site_key = self.detect_with_html_patterns(page_source)
                if captcha_type and site_key:
                    DETECTION_STATS['network_request_profiling']['successes'] += 1
                    return captcha_type, site_key
            
        except Exception as e:
            logger.error(f"Error during network request profiling: {e}")
        
        DETECTION_STATS['network_request_profiling']['failures'] += 1
        return None, None
    
    def detect_captcha(self, html_content: str = None, url: str = None) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Detect CAPTCHA using multiple heuristics.
        
        Args:
            html_content: HTML content of the page
            url: URL of the page (required for JavaScript-based detection)
            
        Returns:
            A tuple of (captcha_type, site_key, detection_method) or (None, None, None) if no CAPTCHA is detected
        """
        if html_content:
            # Try HTML pattern matching first (fastest method)
            captcha_type, site_key = self.detect_with_html_patterns(html_content)
            if captcha_type and site_key:
                return captcha_type, site_key, 'html_pattern_matching'
        
        if url:
            # Try JavaScript execution analysis if HTML pattern matching failed
            captcha_type, site_key = self.detect_with_javascript_execution(url)
            if captcha_type and site_key:
                return captcha_type, site_key, 'js_execution_analysis'
            
            # Try network request profiling as a last resort
            captcha_type, site_key = self.detect_with_network_profiling(url)
            if captcha_type and site_key:
                return captcha_type, site_key, 'network_request_profiling'
        
        return None, None, None
    
    def cleanup(self):
        """Clean up resources."""
        if self.selenium_driver:
            try:
                self.selenium_driver.quit()
            except Exception as e:
                logger.error(f"Error cleaning up Selenium driver: {e}")


class CaptchaSolver:
    """Main class for solving CAPTCHAs."""

    def __init__(self, api_key: Optional[str] = None, service: str = 'auto',
                 ml_model_path: Optional[str] = None, use_local_ml: bool = True):
        """
        Initialize the CAPTCHA solver.

        Args:
            api_key: API key for the CAPTCHA solving service
            service: Service to use ('2captcha', 'anticaptcha', 'capsolver', 'pytorch', 'tensorflow', or 'auto')
            ml_model_path: Path to ML model for local solving
            use_local_ml: Whether to use local ML-based solvers when possible
        """
        self.config = CONFIG
        self.service = service.lower()
        self.api_key = api_key
        self.ml_model_path = ml_model_path
        self.use_local_ml = use_local_ml

        # Solution cache
        self.solution_cache = {}
        
        # Results history for feedback loop
        self.results_history = []
        self.max_history_size = 1000  # Keep last 1000 results

        # Initialize advanced CAPTCHA detector
        self.advanced_detector = AdvancedCaptchaDetector()

        # Initialize solvers dictionary
        self.solvers = {
            '2captcha': {'enabled': False, 'priority': 1, 'service': None},
            'anticaptcha': {'enabled': False, 'priority': 2, 'service': None},
            'capsolver': {'enabled': False, 'priority': 3, 'service': None},
            'pytorch': {'enabled': False, 'priority': 0, 'service': None},  # Local solvers have higher priority
            'tensorflow': {'enabled': False, 'priority': 0, 'service': None}
        }

        # Update priorities from config
        if 'service_priority' in self.config:
            for service_name, priority in self.config['service_priority'].items():
                if service_name in self.solvers:
                    self.solvers[service_name]['priority'] = priority

        # Initialize 2captcha if available
        if TWOCAPTCHA_AVAILABLE and (self.service in ['2captcha', 'auto']):
            key = self.api_key
            if not key and 'twocaptcha_key' in self.config:
                key = self.config['twocaptcha_key']

            if key:
                try:
                    self.solvers['2captcha']['service'] = TwoCaptcha(key)
                    self.solvers['2captcha']['enabled'] = True
                    logger.info("2Captcha solver initialized")
                except Exception as e:
                    logger.error(f"Error initializing 2Captcha: {e}")

        # Initialize anticaptcha if available
        if ANTICAPTCHA_AVAILABLE and (self.service in ['anticaptcha', 'auto']):
            key = self.api_key
            if not key and 'anticaptcha_key' in self.config:
                key = self.config['anticaptcha_key']

            if key:
                try:
                    self.solvers['anticaptcha']['service'] = key
                    self.solvers['anticaptcha']['enabled'] = True
                    logger.info("AntiCaptcha solver initialized")
                except Exception as e:
                    logger.error(f"Error initializing AntiCaptcha: {e}")

        # Initialize capsolver if available
        if CAPSOLVER_AVAILABLE and (self.service in ['capsolver', 'auto']):
            key = self.api_key
            if not key and 'capsolver_key' in self.config:
                key = self.config['capsolver_key']

            if key:
                try:
                    capsolver.api_key = key
                    self.solvers['capsolver']['service'] = key
                    self.solvers['capsolver']['enabled'] = True
                    logger.info("CapSolver initialized")
                except Exception as e:
                    logger.error(f"Error initializing CapSolver: {e}")

        # Initialize ML-based solvers if available and requested
        if use_local_ml and ML_SOLVERS_AVAILABLE:
            # Initialize PyTorch solver with dynamic import
            if PYTORCH_AVAILABLE and (self.service in ['pytorch', 'auto']):
                try:
                    pytorch_solver_class = _get_pytorch_solver()
                    if pytorch_solver_class:
                        self.solvers['pytorch']['service'] = pytorch_solver_class(model_path=ml_model_path)
                        self.solvers['pytorch']['enabled'] = True
                        logger.info("PyTorch CAPTCHA solver initialized")
                    else:
                        logger.error("Failed to load PyTorch solver class")
                except Exception as e:
                    logger.error(f"Error initializing PyTorch solver: {e}")

            # Initialize TensorFlow solver (lazy initialization to prevent hanging)
            if TENSORFLOW_AVAILABLE and (self.service in ['tensorflow', 'auto']):
                try:
                    # Store the class and model path for lazy initialization
                    tensorflow_solver_class = _get_tensorflow_solver()
                    if tensorflow_solver_class:
                        self.solvers['tensorflow']['service'] = {
                            'class': tensorflow_solver_class,
                            'model_path': ml_model_path,
                            'instance': None
                        }
                        self.solvers['tensorflow']['enabled'] = True
                        logger.info("TensorFlow CAPTCHA solver prepared (lazy initialization)")
                    else:
                        logger.error("Failed to load TensorFlow solver class")
                except Exception as e:
                    logger.error(f"Error preparing TensorFlow solver: {e}")

        # Metrics
        self.metrics = {
            "total_attempts": 0,
            "successful_solutions": 0,
            "failed_solutions": 0,
            "avg_solution_time": 0.0,
            "by_service": {}
        }

        # Check if any solvers are enabled
        if not any(solver['enabled'] for solver in self.solvers.values()):
            logger.warning("No CAPTCHA solving services are available or properly configured")

    def solve_recaptcha_v2(self, site_key: str, page_url: str, invisible: bool = False, proxy: Optional[Dict[str, str]] = None) -> Optional[str]:
        """
        Solve a reCAPTCHA v2 challenge.

        Args:
            site_key: The site key for the reCAPTCHA
            page_url: The URL of the page containing the reCAPTCHA
            invisible: Whether this is an invisible reCAPTCHA
            proxy: Optional proxy configuration

        Returns:
            The reCAPTCHA solution token or None if failed
        """
        self.metrics['total_attempts'] += 1
        start_time = time.time()

        # Check cache first (site_key + page_url as key)
        cache_key = f"recaptcha_v2:{site_key}:{page_url}"
        if cache_key in self.solution_cache:
            if time.time() - self.solution_cache[cache_key]['timestamp'] < 110:  # Valid for less than 2 minutes
                logger.info("Using cached reCAPTCHA solution")
                return self.solution_cache[cache_key]['solution']

        # Get sorted list of solvers by priority
        sorted_solvers = sorted(
            [name for name, info in self.solvers.items() if info['enabled']],
            key=lambda x: self.solvers[x]['priority']
        )

        if not sorted_solvers:
            logger.error("No CAPTCHA solvers available")
            self.metrics['failed_solutions'] += 1
            return None

        solution = None
        error_messages = []

        for solver_name in sorted_solvers:
            solver_info = self.solvers[solver_name]

            if solver_name not in self.metrics['by_service']:
                self.metrics['by_service'][solver_name] = {
                    'attempts': 0,
                    'successes': 0,
                    'failures': 0,
                    'avg_time': 0
                }

            self.metrics['by_service'][solver_name]['attempts'] += 1
            solver_start_time = time.time()

            try:
                logger.info(f"Attempting to solve reCAPTCHA with {solver_name}")

                if solver_name == '2captcha':
                    solver = solver_info['service']
                    # Prepare parameters
                    params = {
                        'sitekey': site_key,
                        'url': page_url,
                        'invisible': 1 if invisible else 0
                    }

                    if proxy:
                        if 'username' in proxy and 'password' in proxy:
                            proxy_str = f"{proxy['protocol']}://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}"
                        else:
                            proxy_str = f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"
                        params['proxy'] = proxy_str
                        params['proxytype'] = proxy['protocol']

                    result = solver.recaptcha(**params)
                    if result and 'code' in result:
                        solution = result['code']

                elif solver_name == 'anticaptcha':
                    api_key = solver_info['service']
                    solver = recaptchaV2Proxyless()
                    solver.set_verbose(1)
                    solver.set_key(api_key)
                    solver.set_website_url(page_url)
                    solver.set_website_key(site_key)
                    solver.set_is_invisible(invisible)

                    solution = solver.solve_and_return_solution()
                    if not solution or solution == 'ERROR_ZERO_BALANCE':
                        error = solution if solution else "Unknown error"
                        error_messages.append(f"{solver_name}: {error}")
                        solution = None

                elif solver_name == 'capsolver':
                    api_key = solver_info['service']
                    capsolver.api_key = api_key

                    task_type = "ReCaptchaV2TaskProxyLess"
                    task_params = {
                        "websiteURL": page_url,
                        "websiteKey": site_key,
                        "isInvisible": invisible
                    }

                    if proxy:
                        task_type = "ReCaptchaV2Task"
                        task_params["proxy"] = (
                            f"{proxy['protocol']}://"
                            f"{proxy.get('username', '')}:{proxy.get('password', '')}@"
                            f"{proxy['host']}:{proxy['port']}"
                        )

                    try:
                        result = capsolver.solve({
                            "type": task_type,
                            **task_params
                        })

                        if result and 'solution' in result and 'gRecaptchaResponse' in result['solution']:
                            solution = result['solution']['gRecaptchaResponse']
                    except Exception as caps_err:
                        error_messages.append(f"CapSolver: {str(caps_err)}")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error with {solver_name} solver: {error_msg}")
                error_messages.append(f"{solver_name}: {error_msg}")
                traceback.print_exc()

            solver_elapsed = time.time() - solver_start_time
            service_metrics = self.metrics['by_service'][solver_name]

            # Update metrics for this service
            if solution:
                service_metrics['successes'] += 1
                current_avg = service_metrics['avg_time']
                total_success = service_metrics['successes']
                service_metrics['avg_time'] = (current_avg * (total_success - 1) + solver_elapsed) / total_success

                # Log success and break the loop
                logger.info(f"Successfully solved reCAPTCHA with {solver_name} in {solver_elapsed:.2f} seconds")
                break
            else:
                service_metrics['failures'] += 1

        elapsed = time.time() - start_time

        if solution:
            # Cache the solution
            self.solution_cache[cache_key] = {
                'solution': solution,
                'timestamp': time.time()
            }

            # Clean old cache entries
            self._clean_solution_cache()

            # Update overall metrics
            self.metrics['successful_solutions'] += 1
            total_success = self.metrics['successful_solutions']
            current_avg = self.metrics['avg_solution_time']
            self.metrics['avg_solution_time'] = (current_avg * (total_success - 1) + elapsed) / total_success

            return solution
        else:
            self.metrics['failed_solutions'] += 1
            error_summary = ', '.join(error_messages)
            logger.error(f"Failed to solve reCAPTCHA after trying {len(sorted_solvers)} services. Errors: {error_summary}")
            return None

    def solve_hcaptcha(self, site_key: str, page_url: str, proxy: Optional[Dict[str, str]] = None) -> Optional[str]:
        """
        Solve an hCaptcha challenge.

        Args:
            site_key: The site key for the hCaptcha
            page_url: The URL of the page containing the hCaptcha
            proxy: Optional proxy configuration

        Returns:
            The hCaptcha solution token or None if failed
        """
        self.metrics['total_attempts'] += 1
        start_time = time.time()

        # Check cache first
        cache_key = f"hcaptcha:{site_key}:{page_url}"
        if cache_key in self.solution_cache:
            if time.time() - self.solution_cache[cache_key]['timestamp'] < 110:  # Valid for less than 2 minutes
                logger.info("Using cached hCaptcha solution")
                return self.solution_cache[cache_key]['solution']

        # Get sorted list of solvers by priority
        sorted_solvers = sorted(
            [name for name, info in self.solvers.items() if info['enabled']],
            key=lambda x: self.solvers[x]['priority']
        )

        if not sorted_solvers:
            logger.error("No CAPTCHA solvers available")
            self.metrics['failed_solutions'] += 1
            return None

        solution = None
        error_messages = []

        for solver_name in sorted_solvers:
            solver_info = self.solvers[solver_name]

            if solver_name not in self.metrics['by_service']:
                self.metrics['by_service'][solver_name] = {
                    'attempts': 0,
                    'successes': 0,
                    'failures': 0,
                    'avg_time': 0
                }

            self.metrics['by_service'][solver_name]['attempts'] += 1
            solver_start_time = time.time()

            try:
                logger.info(f"Attempting to solve hCaptcha with {solver_name}")

                if solver_name == '2captcha':
                    solver = solver_info['service']
                    params = {
                        'sitekey': site_key,
                        'url': page_url
                    }

                    if proxy:
                        if 'username' in proxy and 'password' in proxy:
                            proxy_str = f"{proxy['protocol']}://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}"
                        else:
                            proxy_str = f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"
                        params['proxy'] = proxy_str
                        params['proxytype'] = proxy['protocol']

                    result = solver.hcaptcha(**params)
                    if result and 'code' in result:
                        solution = result['code']

                elif solver_name == 'anticaptcha':
                    api_key = solver_info['service']
                    solver = hCaptchaProxyless()
                    solver.set_verbose(1)
                    solver.set_key(api_key)
                    solver.set_website_url(page_url)
                    solver.set_website_key(site_key)

                    solution = solver.solve_and_return_solution()
                    if not solution or solution == 'ERROR_ZERO_BALANCE':
                        error = solution if solution else "Unknown error"
                        error_messages.append(f"{solver_name}: {error}")
                        solution = None

                elif solver_name == 'capsolver':
                    api_key = solver_info['service']
                    capsolver.api_key = api_key

                    task_type = "HCaptchaTaskProxyLess"
                    task_params = {
                        "websiteURL": page_url,
                        "websiteKey": site_key
                    }

                    if proxy:
                        task_type = "HCaptchaTask"
                        task_params["proxy"] = (
                            f"{proxy['protocol']}://"
                            f"{proxy.get('username', '')}:{proxy.get('password', '')}@"
                            f"{proxy['host']}:{proxy['port']}"
                        )

                    try:
                        result = capsolver.solve({
                            "type": task_type,
                            **task_params
                        })

                        if result and 'solution' in result and 'gRecaptchaResponse' in result['solution']:
                            solution = result['solution']['gRecaptchaResponse']
                    except Exception as caps_err:
                        error_messages.append(f"CapSolver: {str(caps_err)}")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error with {solver_name} solver: {error_msg}")
                error_messages.append(f"{solver_name}: {error_msg}")
                traceback.print_exc()

            solver_elapsed = time.time() - solver_start_time
            service_metrics = self.metrics['by_service'][solver_name]

            # Update metrics for this service
            if solution:
                service_metrics['successes'] += 1
                current_avg = service_metrics['avg_time']
                total_success = service_metrics['successes']
                service_metrics['avg_time'] = (current_avg * (total_success - 1) + solver_elapsed) / total_success

                # Log success and break the loop
                logger.info(f"Successfully solved hCaptcha with {solver_name} in {solver_elapsed:.2f} seconds")
                break
            else:
                service_metrics['failures'] += 1

        elapsed = time.time() - start_time

        if solution:
            # Cache the solution
            self.solution_cache[cache_key] = {
                'solution': solution,
                'timestamp': time.time()
            }

            # Clean old cache entries
            self._clean_solution_cache()

            # Update overall metrics
            self.metrics['successful_solutions'] += 1
            total_success = self.metrics['successful_solutions']
            current_avg = self.metrics['avg_solution_time']
            self.metrics['avg_solution_time'] = (current_avg * (total_success - 1) + elapsed) / total_success

            return solution
        else:
            self.metrics['failed_solutions'] += 1
            error_summary = ', '.join(error_messages)
            logger.error(f"Failed to solve hCaptcha after trying {len(sorted_solvers)} services. Errors: {error_summary}")
            return None

    def solve_image_captcha(self, image_path: str = None, image_data: bytes = None, hint: str = None) -> Optional[str]:
        """
        Solve a standard image CAPTCHA.

        Args:
            image_path: Path to the CAPTCHA image file
            image_data: Raw image data (alternative to image_path)
            hint: Optional hint about the CAPTCHA format

        Returns:
            The CAPTCHA solution string or None if failed
        """
        if not image_path and not image_data:
            logger.error("Either image_path or image_data must be provided")
            return None

        # Load image data if path is provided
        if image_path and not image_data:
            try:
                with open(image_path, 'rb') as f:
                    image_data = f.read()
            except Exception as e:
                logger.error(f"Failed to read image file: {e}")
                return None

        self.metrics['total_attempts'] += 1
        start_time = time.time()

        # Get sorted list of solvers by priority
        sorted_solvers = sorted(
            [name for name, info in self.solvers.items() if info['enabled']],
            key=lambda x: self.solvers[x]['priority']
        )

        if not sorted_solvers:
            logger.error("No CAPTCHA solvers available")
            self.metrics['failed_solutions'] += 1
            return None

        solution = None
        error_messages = []

        for solver_name in sorted_solvers:
            solver_info = self.solvers[solver_name]

            if solver_name not in self.metrics['by_service']:
                self.metrics['by_service'][solver_name] = {
                    'attempts': 0,
                    'successes': 0,
                    'failures': 0,
                    'avg_time': 0
                }

            self.metrics['by_service'][solver_name]['attempts'] += 1
            solver_start_time = time.time()

            try:
                logger.info(f"Attempting to solve image CAPTCHA with {solver_name}")

                # Try ML-based solvers first
                if solver_name == 'pytorch':
                    # Create a temporary file for the image
                    temp_dir = Path('temp')
                    temp_dir.mkdir(exist_ok=True)
                    temp_file = temp_dir / f"captcha_pytorch_{int(time.time())}.png"
                    with open(temp_file, 'wb') as f:
                        f.write(image_data)

                    # Use PyTorch solver
                    pytorch_solver = solver_info['service']
                    solution = pytorch_solver.predict(str(temp_file))

                    # Clean up
                    if os.path.exists(temp_file):
                        os.remove(temp_file)

                elif solver_name == 'tensorflow':
                    # Create a temporary file for the image
                    temp_dir = Path('temp')
                    temp_dir.mkdir(exist_ok=True)
                    temp_file = temp_dir / f"captcha_tf_{int(time.time())}.png"
                    with open(temp_file, 'wb') as f:
                        f.write(image_data)

                    # Get TensorFlow solver with lazy initialization
                    tensorflow_service = solver_info['service']
                    if isinstance(tensorflow_service, dict):
                        # Lazy initialization - create instance if not already created
                        if tensorflow_service['instance'] is None:
                            logger.info("Initializing TensorFlow solver on first use...")
                            tensorflow_service['instance'] = tensorflow_service['class'](
                                model_path=tensorflow_service['model_path']
                            )
                        tensorflow_solver = tensorflow_service['instance']
                    else:
                        # Direct instance (fallback)
                        tensorflow_solver = tensorflow_service
                    
                    solution = tensorflow_solver.predict(str(temp_file))

                    # Clean up
                    if os.path.exists(temp_file):
                        os.remove(temp_file)

                # External API-based solvers
                elif solver_name == '2captcha':
                    solver = solver_info['service']
                    # Convert image data to base64 if needed
                    result = solver.normal(image_data, phrase=0, caseSensitive=1, hint=hint or '')
                    if result and 'code' in result:
                        solution = result['code']

                elif solver_name == 'anticaptcha':
                    api_key = solver_info['service']
                    solver = imagecaptcha()
                    solver.set_verbose(1)
                    solver.set_key(api_key)

                    # Save image data to temporary file if we only have raw data
                    if image_path is None:
                        temp_dir = Path('temp')
                        temp_dir.mkdir(exist_ok=True)
                        temp_file = temp_dir / f"captcha_{int(time.time())}.png"
                        with open(temp_file, 'wb') as f:
                            f.write(image_data)
                        image_path = str(temp_file)

                    solution = solver.solve_and_return_solution(image_path)
                    if not solution or solution == 'ERROR_ZERO_BALANCE':
                        error = solution if solution else "Unknown error"
                        error_messages.append(f"{solver_name}: {error}")
                        solution = None

                elif solver_name == 'capsolver':
                    api_key = solver_info['service']
                    capsolver.api_key = api_key

                    # Convert image to base64
                    base64_image = base64.b64encode(image_data).decode('utf-8')

                    try:
                        result = capsolver.solve({
                            "type": "ImageToTextTask",
                            "body": base64_image,
                            "recognizingThreshold": 95
                        })

                        if result and 'solution' in result and 'text' in result['solution']:
                            solution = result['solution']['text']
                    except Exception as caps_err:
                        error_messages.append(f"CapSolver: {str(caps_err)}")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error with {solver_name} solver: {error_msg}")
                error_messages.append(f"{solver_name}: {error_msg}")
                traceback.print_exc()

            solver_elapsed = time.time() - solver_start_time
            service_metrics = self.metrics['by_service'][solver_name]

            # Update metrics for this service
            if solution:
                service_metrics['successes'] += 1
                current_avg = service_metrics['avg_time']
                total_success = service_metrics['successes']
                service_metrics['avg_time'] = (current_avg * (total_success - 1) + solver_elapsed) / total_success

                # Log success and break the loop
                logger.info(f"Successfully solved image CAPTCHA with {solver_name} in {solver_elapsed:.2f} seconds")
                break
            else:
                service_metrics['failures'] += 1

        elapsed = time.time() - start_time

        if solution:
            # Update overall metrics
            self.metrics['successful_solutions'] += 1
            total_success = self.metrics['successful_solutions']
            current_avg = self.metrics['avg_solution_time']
            self.metrics['avg_solution_time'] = (current_avg * (total_success - 1) + elapsed) / total_success

            return solution
        else:
            self.metrics['failed_solutions'] += 1
            error_summary = ', '.join(error_messages)
            logger.error(f"Failed to solve image CAPTCHA after trying {len(sorted_solvers)} services. Errors: {error_summary}")
            return None

    def solve_turnstile(self, site_key: str, page_url: str, proxy: Optional[Dict[str, str]] = None) -> Optional[str]:
        """
        Solve a Cloudflare Turnstile challenge.

        Args:
            site_key: The site key for the Turnstile
            page_url: The URL of the page containing the Turnstile
            proxy: Optional proxy configuration

        Returns:
            The Turnstile solution token or None if failed
        """
        self.metrics['total_attempts'] += 1
        start_time = time.time()

        # Check cache first
        cache_key = f"turnstile:{site_key}:{page_url}"
        if cache_key in self.solution_cache:
            if time.time() - self.solution_cache[cache_key]['timestamp'] < 110:  # Valid for less than 2 minutes
                logger.info("Using cached Turnstile solution")
                return self.solution_cache[cache_key]['solution']

        # Get sorted list of solvers by priority
        sorted_solvers = sorted(
            [name for name, info in self.solvers.items() if info['enabled']],
            key=lambda x: self.solvers[x]['priority']
        )

        if not sorted_solvers:
            logger.error("No CAPTCHA solvers available")
            self.metrics['failed_solutions'] += 1
            return None

        solution = None
        error_messages = []

        for solver_name in sorted_solvers:
            solver_info = self.solvers[solver_name]

            if solver_name not in self.metrics['by_service']:
                self.metrics['by_service'][solver_name] = {
                    'attempts': 0,
                    'successes': 0,
                    'failures': 0,
                    'avg_time': 0
                }

            self.metrics['by_service'][solver_name]['attempts'] += 1
            solver_start_time = time.time()

            try:
                logger.info(f"Attempting to solve Turnstile with {solver_name}")

                if solver_name == '2captcha':
                    solver = solver_info['service']
                    # Turnstile support is limited in older versions
                    if hasattr(solver, 'turnstile'):
                        params = {
                            'sitekey': site_key,
                            'url': page_url
                        }

                        if proxy:
                            if 'username' in proxy and 'password' in proxy:
                                proxy_str = f"{proxy['protocol']}://{proxy['username']}:{proxy['password']}@{proxy['host']}:{proxy['port']}"
                            else:
                                proxy_str = f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"
                            params['proxy'] = proxy_str
                            params['proxytype'] = proxy['protocol']

                        result = solver.turnstile(**params)
                        if result and 'code' in result:
                            solution = result['code']
                    else:
                        logger.warning("2Captcha version doesn't support Turnstile")

                elif solver_name == 'anticaptcha':
                    # Anti-Captcha handles Turnstile using the same module as reCAPTCHA
                    api_key = solver_info['service']
                    solver = recaptchaV2Proxyless()
                    solver.set_verbose(1)
                    solver.set_key(api_key)
                    solver.set_website_url(page_url)
                    solver.set_website_key(site_key)
                    solver.set_is_invisible(True)  # Turnstile is typically invisible

                    solution = solver.solve_and_return_solution()
                    if not solution or solution == 'ERROR_ZERO_BALANCE':
                        error = solution if solution else "Unknown error"
                        error_messages.append(f"{solver_name}: {error}")
                        solution = None

                elif solver_name == 'capsolver':
                    api_key = solver_info['service']
                    capsolver.api_key = api_key

                    task_type = "TurnstileTaskProxyLess"
                    task_params = {
                        "websiteURL": page_url,
                        "websiteKey": site_key
                    }

                    if proxy:
                        task_type = "TurnstileTask"
                        task_params["proxy"] = (
                            f"{proxy['protocol']}://"
                            f"{proxy.get('username', '')}:{proxy.get('password', '')}@"
                            f"{proxy['host']}:{proxy['port']}"
                        )

                    try:
                        result = capsolver.solve({
                            "type": task_type,
                            **task_params
                        })

                        if result and 'solution' in result and 'token' in result['solution']:
                            solution = result['solution']['token']
                    except Exception as caps_err:
                        error_messages.append(f"CapSolver: {str(caps_err)}")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error with {solver_name} solver: {error_msg}")
                error_messages.append(f"{solver_name}: {error_msg}")
                traceback.print_exc()

            solver_elapsed = time.time() - solver_start_time
            service_metrics = self.metrics['by_service'][solver_name]

            # Update metrics for this service
            if solution:
                service_metrics['successes'] += 1
                current_avg = service_metrics['avg_time']
                total_success = service_metrics['successes']
                service_metrics['avg_time'] = (current_avg * (total_success - 1) + solver_elapsed) / total_success

                # Log success and break the loop
                logger.info(f"Successfully solved Turnstile with {solver_name} in {solver_elapsed:.2f} seconds")
                break
            else:
                service_metrics['failures'] += 1

        elapsed = time.time() - start_time

        if solution:
            # Cache the solution
            self.solution_cache[cache_key] = {
                'solution': solution,
                'timestamp': time.time()
            }

            # Clean old cache entries
            self._clean_solution_cache()

            # Update overall metrics
            self.metrics['successful_solutions'] += 1
            total_success = self.metrics['successful_solutions']
            current_avg = self.metrics['avg_solution_time']
            self.metrics['avg_solution_time'] = (current_avg * (total_success - 1) + elapsed) / total_success

            return solution
        else:
            self.metrics['failed_solutions'] += 1
            error_summary = ', '.join(error_messages)
            logger.error(f"Failed to solve Turnstile after trying {len(sorted_solvers)} services. Errors: {error_summary}")
            return None

    def detect_captcha_type(self, html_content: str, url: str = None) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Detect the type of CAPTCHA and extract the site key from HTML content.
        Uses multiple detection methods including advanced JavaScript-based detection.

        Args:
            html_content: The HTML content of the page
            url: The URL of the page (optional, required for JavaScript detection)

        Returns:
            A tuple of (captcha_type, site_key, detection_method) or (None, None, None) if no CAPTCHA is detected
        """
        return self.advanced_detector.detect_captcha(html_content, url)

    def detect_and_solve(self, html_content: str, page_url: str, proxy: Optional[Dict[str, str]] = None) -> Optional[Dict[str, Any]]:
        """
        Detect the CAPTCHA type in the HTML content and solve it.

        Args:
            html_content: The HTML content of the page
            page_url: The URL of the page
            proxy: Optional proxy configuration

        Returns:
            A dictionary with 'solution' and 'type' keys, or None if no solution
        """
        captcha_type, site_key, detection_method = self.detect_captcha_type(html_content, page_url)

        if not captcha_type or not site_key:
            logger.info("No CAPTCHA detected in the HTML content")
            return None

        logger.info(f"Detected {captcha_type} with site key {site_key} using {detection_method}")

        solution = None

        if captcha_type == 'recaptcha_v2':
            solution = self.solve_recaptcha_v2(site_key, page_url, invisible=False, proxy=proxy)
        elif captcha_type == 'recaptcha_v2_invisible':
            solution = self.solve_recaptcha_v2(site_key, page_url, invisible=True, proxy=proxy)
        elif captcha_type == 'hcaptcha':
            solution = self.solve_hcaptcha(site_key, page_url, proxy=proxy)
        elif captcha_type == 'turnstile':
            solution = self.solve_turnstile(site_key, page_url, proxy=proxy)
        
        # Create result object
        result = SolverResult(
            solution=solution,
            success=solution is not None,
            service=self.service if solution else None,
            captcha_type=captcha_type,
            error=None if solution else "Failed to solve CAPTCHA",
            detection_method=detection_method
        )
        
        # Add to history for feedback loop
        self._add_result_to_history(result)

        if solution:
            return {
                'solution': solution,
                'type': captcha_type,
                'site_key': site_key,
                'detection_method': detection_method,
                'result_id': result.id
            }
        else:
            return None
    
    def _add_result_to_history(self, result: SolverResult):
        """Add a result to the history for the feedback loop."""
        self.results_history.append(result)
        if len(self.results_history) > self.max_history_size:
            self.results_history.pop(0)  # Remove oldest result

    def submit_feedback(self, captcha_id: str, correct_solution: str = None,
                        feedback_type: str = None, comments: str = None) -> bool:
        """
        Submit feedback on a CAPTCHA solution to improve accuracy over time.
        
        Args:
            captcha_id: ID of the CAPTCHA result
            correct_solution: The correct solution (if the original was wrong)
            feedback_type: Type of feedback (incorrect_solution, false_positive, false_negative, other)
            comments: Additional comments
            
        Returns:
            True if feedback was successfully processed, False otherwise
        """
        # Check if the captcha_id exists in history
        result = None
        for r in self.results_history:
            if r.id == captcha_id:
                result = r
                break
        
        if not result:
            logger.warning(f"Cannot find CAPTCHA result with ID {captcha_id} in history")
            return False
        
        # Create feedback object
        feedback = CaptchaFeedback(
            captcha_id=captcha_id,
            correct_solution=correct_solution,
            feedback_type=feedback_type,
            comments=comments
        )
        
        # Store feedback
        FEEDBACK_DB[captcha_id] = feedback.to_dict()
        
        # Process feedback to improve detection
        self._process_feedback(result, feedback)
        
        return True
    
    def _process_feedback(self, result: SolverResult, feedback: CaptchaFeedback):
        """
        Process feedback to improve CAPTCHA detection and solving.
        
        Args:
            result: The original CAPTCHA result
            feedback: The feedback provided
        """
        if feedback.feedback_type == 'incorrect_solution':
            # The solution was wrong - this could help train the ML models
            logger.info(f"Incorrect solution feedback received for {result.captcha_type}. Will use for model improvement.")
            
            # If we have detection method info, update the stats
            if result.detection_method and result.detection_method in DETECTION_STATS:
                DETECTION_STATS[result.detection_method]['failures'] += 1
        
        elif feedback.feedback_type == 'false_positive':
            # Something was detected as CAPTCHA but wasn't
            logger.info(f"False positive detection reported for {result.detection_method}")
            
            # Update detection stats
            if result.detection_method and result.detection_method in DETECTION_STATS:
                DETECTION_STATS[result.detection_method]['failures'] += 1
        
        elif feedback.feedback_type == 'false_negative':
            # A CAPTCHA was missed
            logger.info("False negative reported - CAPTCHA was missed")
            
            # This is harder to attribute to a specific detection method
            # Could log the URL for future analysis
        
        # Log all feedback for manual review
        logger.info(f"CAPTCHA feedback received: {feedback.to_dict()}")

    def get_detection_stats(self) -> Dict[str, Any]:
        """Get current detection statistics."""
        stats = {}
        for method, data in DETECTION_STATS.items():
            if data['detections'] > 0:
                success_rate = (data['successes'] / data['detections']) * 100
                stats[method] = {
                    'detections': data['detections'],
                    'successes': data['successes'],
                    'failures': data['failures'],
                    'success_rate': success_rate
                }
            else:
                stats[method] = {
                    'detections': 0,
                    'successes': 0,
                    'failures': 0,
                    'success_rate': 0
                }
        return stats
    
    def get_feedback_history(self) -> Dict[str, Any]:
        """Get all feedback history."""
        return FEEDBACK_DB
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        metrics = self.metrics.copy()
        metrics['detection_stats'] = self.get_detection_stats()
        return metrics

    def cleanup(self):
        """Clean up resources."""
        self.advanced_detector.cleanup()

    def _clean_solution_cache(self) -> None:
        """Clean expired solutions from the cache."""
        now = time.time()
        expired_keys = [key for key, value in self.solution_cache.items() if now - value['timestamp'] > 120]

        for key in expired_keys:
            del self.solution_cache[key]

# Create a singleton instance
captcha_solver = CaptchaSolver()










#!/usr/bin/env python3
"""
CAPTCHA solving module for the anti-scraping framework.

This module provides integrations with various CAPTCHA solving services
and utilities for handling different types of CAPTCHAs.
"""

import os
import time
import json
import base64
import logging
import asyncio
from typing import Dict, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod
from io import BytesIO
import requests

# Optional imports for different CAPTCHA solvers
try:
    from anticaptchaofficial.recaptchav2proxyless import recaptchaV2Proxyless
    from anticaptchaofficial.recaptchav3proxyless import recaptchaV3Proxyless
    from anticaptchaofficial.imagecaptcha import imagecaptcha
    ANTICAPTCHA_AVAILABLE = True
except ImportError:
    ANTICAPTCHA_AVAILABLE = False

try:
    from twocaptcha import TwoCaptcha
    TWOCAPTCHA_AVAILABLE = True
except ImportError:
    TWOCAPTCHA_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)
file_handler = logging.FileHandler('logs/captcha_solver.log')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)


class CaptchaSolverError(Exception):
    """Exception raised for errors in the CAPTCHA solver."""
    pass


class CaptchaSolver(ABC):
    """Abstract base class for CAPTCHA solvers."""
    
    @abstractmethod
    async def solve_recaptcha_v2(self, site_key: str, page_url: str) -> str:
        """
        Solve a reCAPTCHA v2 challenge.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        pass
    
    @abstractmethod
    async def solve_recaptcha_v3(self, site_key: str, page_url: str, action: str = "verify") -> str:
        """
        Solve a reCAPTCHA v3 challenge.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            action: The action value
            
        Returns:
            The solution token
        """
        pass
    
    @abstractmethod
    async def solve_image_captcha(self, image_data: Union[str, bytes]) -> str:
        """
        Solve an image CAPTCHA.
        
        Args:
            image_data: The CAPTCHA image as bytes or base64 string
            
        Returns:
            The solution text
        """
        pass
    
    @abstractmethod
    async def solve_hcaptcha(self, site_key: str, page_url: str) -> str:
        """
        Solve an hCaptcha challenge.
        
        Args:
            site_key: The hCaptcha site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        pass
    
    @abstractmethod
    async def get_balance(self) -> float:
        """
        Get the current balance of the CAPTCHA solving service account.
        
        Returns:
            The account balance
        """
        pass


class AntiCaptchaSolver(CaptchaSolver):
    """CAPTCHA solver using Anti-Captcha service."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Anti-Captcha solver.
        
        Args:
            api_key: Anti-Captcha API key (defaults to environment variable)
        """
        if not ANTICAPTCHA_AVAILABLE:
            raise ImportError(
                "anticaptchaofficial package is not installed. "
                "Install it with: pip install anticaptchaofficial"
            )
        
        self.api_key = api_key or os.environ.get("ANTI_CAPTCHA_API_KEY")
        if not self.api_key:
            raise CaptchaSolverError(
                "Anti-Captcha API key not provided. Set the ANTI_CAPTCHA_API_KEY "
                "environment variable or pass it to the constructor."
            )
        
        logger.info("AntiCaptchaSolver initialized")
    
    async def solve_recaptcha_v2(self, site_key: str, page_url: str) -> str:
        """
        Solve a reCAPTCHA v2 challenge using Anti-Captcha.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving reCAPTCHA v2 on {page_url}")
            
            # Anti-Captcha's library is synchronous, run in a thread
            def solve():
                solver = recaptchaV2Proxyless()
                solver.set_verbose(1)
                solver.set_key(self.api_key)
                solver.set_website_url(page_url)
                solver.set_website_key(site_key)
                
                return solver.solve_and_return_solution()
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            token = await loop.run_in_executor(None, solve)
            
            if not token:
                raise CaptchaSolverError("Failed to solve reCAPTCHA v2")
            
            logger.info("reCAPTCHA v2 solved successfully")
            return token
            
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v2: {e}")
            raise CaptchaSolverError(f"Failed to solve reCAPTCHA v2: {str(e)}")
    
    async def solve_recaptcha_v3(self, site_key: str, page_url: str, action: str = "verify") -> str:
        """
        Solve a reCAPTCHA v3 challenge using Anti-Captcha.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            action: The action value
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving reCAPTCHA v3 on {page_url}")
            
            # Anti-Captcha's library is synchronous, run in a thread
            def solve():
                solver = recaptchaV3Proxyless()
                solver.set_verbose(1)
                solver.set_key(self.api_key)
                solver.set_website_url(page_url)
                solver.set_website_key(site_key)
                solver.set_page_action(action)
                solver.set_min_score(0.7)
                
                return solver.solve_and_return_solution()
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            token = await loop.run_in_executor(None, solve)
            
            if not token:
                raise CaptchaSolverError("Failed to solve reCAPTCHA v3")
            
            logger.info("reCAPTCHA v3 solved successfully")
            return token
            
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v3: {e}")
            raise CaptchaSolverError(f"Failed to solve reCAPTCHA v3: {str(e)}")
    
    async def solve_image_captcha(self, image_data: Union[str, bytes]) -> str:
        """
        Solve an image CAPTCHA using Anti-Captcha.
        
        Args:
            image_data: The CAPTCHA image as bytes or base64 string
            
        Returns:
            The solution text
        """
        try:
            logger.info("Solving image CAPTCHA")
            
            # Convert base64 string to bytes if necessary
            if isinstance(image_data, str) and image_data.startswith("data:image"):
                image_data = base64.b64decode(image_data.split(",")[1])
            elif isinstance(image_data, str):
                image_data = base64.b64decode(image_data)
            
            # Save image to temporary file
            temp_path = "temp_captcha.png"
            with open(temp_path, "wb") as f:
                f.write(image_data)
            
            # Anti-Captcha's library is synchronous, run in a thread
            def solve():
                solver = imagecaptcha()
                solver.set_verbose(1)
                solver.set_key(self.api_key)
                return solver.solve_and_return_solution(temp_path)
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            solution = await loop.run_in_executor(None, solve)
            
            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            if not solution:
                raise CaptchaSolverError("Failed to solve image CAPTCHA")
            
            logger.info("Image CAPTCHA solved successfully")
            return solution
            
        except Exception as e:
            logger.error(f"Error solving image CAPTCHA: {e}")
            raise CaptchaSolverError(f"Failed to solve image CAPTCHA: {str(e)}")
    
    async def solve_hcaptcha(self, site_key: str, page_url: str) -> str:
        """
        Solve an hCaptcha challenge using Anti-Captcha.
        
        Args:
            site_key: The hCaptcha site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving hCaptcha on {page_url}")
            
            # Make a direct API call since anticaptchaofficial doesn't have a direct hCaptcha method
            data = {
                "clientKey": self.api_key,
                "task": {
                    "type": "HCaptchaTaskProxyless",
                    "websiteURL": page_url,
                    "websiteKey": site_key
                }
            }
            
            # Create task
            response = requests.post("https://api.anti-captcha.com/createTask", json=data)
            task = response.json()
            
            if task.get("errorId"):
                raise CaptchaSolverError(f"Error creating task: {task.get('errorDescription')}")
            
            task_id = task.get("taskId")
            
            # Wait for solution
            max_attempts = 30
            for attempt in range(max_attempts):
                await asyncio.sleep(5)  # Wait between checks
                
                status_data = {
                    "clientKey": self.api_key,
                    "taskId": task_id
                }
                
                response = requests.post("https://api.anti-captcha.com/getTaskResult", json=status_data)
                result = response.json()
                
                if result.get("status") == "ready":
                    solution = result.get("solution", {}).get("gRecaptchaResponse")
                    if solution:
                        logger.info("hCaptcha solved successfully")
                        return solution
                    else:
                        raise CaptchaSolverError("No solution in response")
            
            raise CaptchaSolverError("Timed out waiting for hCaptcha solution")
            
        except Exception as e:
            logger.error(f"Error solving hCaptcha: {e}")
            raise CaptchaSolverError(f"Failed to solve hCaptcha: {str(e)}")
    
    async def get_balance(self) -> float:
        """
        Get the current balance of the Anti-Captcha account.
        
        Returns:
            The account balance
        """
        try:
            response = requests.post("https://api.anti-captcha.com/getBalance", json={
                "clientKey": self.api_key
            })
            result = response.json()
            
            if result.get("errorId"):
                raise CaptchaSolverError(f"Error checking balance: {result.get('errorDescription')}")
            
            return result.get("balance", 0.0)
            
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            raise CaptchaSolverError(f"Failed to get balance: {str(e)}")


class TwoCaptchaSolver(CaptchaSolver):
    """CAPTCHA solver using 2Captcha service."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the 2Captcha solver.
        
        Args:
            api_key: 2Captcha API key (defaults to environment variable)
        """
        if not TWOCAPTCHA_AVAILABLE:
            raise ImportError(
                "2captcha-python package is not installed. "
                "Install it with: pip install 2captcha-python"
            )
        
        self.api_key = api_key or os.environ.get("TWOCAPTCHA_API_KEY")
        if not self.api_key:
            raise CaptchaSolverError(
                "2Captcha API key not provided. Set the TWOCAPTCHA_API_KEY "
                "environment variable or pass it to the constructor."
            )
        
        self.solver = TwoCaptcha(self.api_key)
        logger.info("TwoCaptchaSolver initialized")
    
    async def solve_recaptcha_v2(self, site_key: str, page_url: str) -> str:
        """
        Solve a reCAPTCHA v2 challenge using 2Captcha.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving reCAPTCHA v2 on {page_url}")
            
            # 2Captcha's library is synchronous, run in a thread
            def solve():
                return self.solver.recaptcha(
                    sitekey=site_key,
                    url=page_url
                )
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, solve)
            
            token = result.get("code")
            if not token:
                raise CaptchaSolverError("Failed to solve reCAPTCHA v2")
            
            logger.info("reCAPTCHA v2 solved successfully")
            return token
            
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v2: {e}")
            raise CaptchaSolverError(f"Failed to solve reCAPTCHA v2: {str(e)}")
    
    async def solve_recaptcha_v3(self, site_key: str, page_url: str, action: str = "verify") -> str:
        """
        Solve a reCAPTCHA v3 challenge using 2Captcha.
        
        Args:
            site_key: The reCAPTCHA site key
            page_url: The URL of the page with the CAPTCHA
            action: The action value
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving reCAPTCHA v3 on {page_url}")
            
            # 2Captcha's library is synchronous, run in a thread
            def solve():
                return self.solver.recaptcha(
                    sitekey=site_key,
                    url=page_url,
                    version="v3",
                    action=action,
                    score=0.7
                )
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, solve)
            
            token = result.get("code")
            if not token:
                raise CaptchaSolverError("Failed to solve reCAPTCHA v3")
            
            logger.info("reCAPTCHA v3 solved successfully")
            return token
            
        except Exception as e:
            logger.error(f"Error solving reCAPTCHA v3: {e}")
            raise CaptchaSolverError(f"Failed to solve reCAPTCHA v3: {str(e)}")
    
    async def solve_image_captcha(self, image_data: Union[str, bytes]) -> str:
        """
        Solve an image CAPTCHA using 2Captcha.
        
        Args:
            image_data: The CAPTCHA image as bytes or base64 string
            
        Returns:
            The solution text
        """
        try:
            logger.info("Solving image CAPTCHA")
            
            # Convert base64 string to bytes if necessary
            if isinstance(image_data, str) and image_data.startswith("data:image"):
                image_data = base64.b64decode(image_data.split(",")[1])
            elif isinstance(image_data, str):
                image_data = base64.b64decode(image_data)
            
            # Save image to temporary file
            temp_path = "temp_captcha.png"
            with open(temp_path, "wb") as f:
                f.write(image_data)
            
            # 2Captcha's library is synchronous, run in a thread
            def solve():
                return self.solver.normal(temp_path)
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, solve)
            
            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            solution = result.get("code")
            if not solution:
                raise CaptchaSolverError("Failed to solve image CAPTCHA")
            
            logger.info("Image CAPTCHA solved successfully")
            return solution
            
        except Exception as e:
            logger.error(f"Error solving image CAPTCHA: {e}")
            raise CaptchaSolverError(f"Failed to solve image CAPTCHA: {str(e)}")
    
    async def solve_hcaptcha(self, site_key: str, page_url: str) -> str:
        """
        Solve an hCaptcha challenge using 2Captcha.
        
        Args:
            site_key: The hCaptcha site key
            page_url: The URL of the page with the CAPTCHA
            
        Returns:
            The solution token
        """
        try:
            logger.info(f"Solving hCaptcha on {page_url}")
            
            # 2Captcha's library is synchronous, run in a thread
            def solve():
                return self.solver.hcaptcha(
                    sitekey=site_key,
                    url=page_url
                )
            
            # Run the synchronous solver in a thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, solve)
            
            token = result.get("code")
            if not token:
                raise CaptchaSolverError("Failed to solve hCaptcha")
            
            logger.info("hCaptcha solved successfully")
            return token
            
        except Exception as e:
            logger.error(f"Error solving hCaptcha: {e}")
            raise CaptchaSolverError(f"Failed to solve hCaptcha: {str(e)}")
    
    async def get_balance(self) -> float:
        """
        Get the current balance of the 2Captcha account.
        
        Returns:
            The account balance
        """
        try:
            # 2Captcha's library is synchronous, run in a thread
            def get_balance():
                return self.solver.balance()
            
            # Run in a thread pool
            loop = asyncio.get_event_loop()
            balance = await loop.run_in_executor(None, get_balance)
            
            return float(balance)
            
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            raise CaptchaSolverError(f"Failed to get balance: {str(e)}")


class CaptchaSolverFactory:
    """Factory class for creating CAPTCHA solvers."""
    
    @staticmethod
    def create_solver(service: str = "auto", api_key: Optional[str] = None) -> CaptchaSolver:
        """
        Create a CAPTCHA solver based on the specified service.
        
        Args:
            service: The CAPTCHA solving service to use ("anticaptcha", "2captcha", or "auto")
            api_key: Optional API key (defaults to environment variable)
            
        Returns:
            A CaptchaSolver instance
        """
        # If auto, try to determine which service to use based on available API keys
        if service == "auto":
            if api_key:
                # If API key is provided, try AntiCaptcha first
                if ANTICAPTCHA_AVAILABLE:
                    return AntiCaptchaSolver(api_key)
                elif TWOCAPTCHA_AVAILABLE:
                    return TwoCaptchaSolver(api_key)
            else:
                # If no API key provided, check environment variables
                if ANTICAPTCHA_AVAILABLE and os.environ.get("ANTI_CAPTCHA_API_KEY"):
                    return AntiCaptchaSolver()
                elif TWOCAPTCHA_AVAILABLE and os.environ.get("TWOCAPTCHA_API_KEY"):
                    return TwoCaptchaSolver()
        
        # Explicit service selection
        if service == "anticaptcha":
            if not ANTICAPTCHA_AVAILABLE:
                raise ImportError(
                    "anticaptchaofficial package is not installed. "
                    "Install it with: pip install anticaptchaofficial"
                )
            return AntiCaptchaSolver(api_key)
        elif service == "2captcha":
            if not TWOCAPTCHA_AVAILABLE:
                raise ImportError(
                    "2captcha-python package is not installed. "
                    "Install it with: pip install 2captcha-python"
                )
            return TwoCaptchaSolver(api_key)
        
        raise ValueError(f"Unknown CAPTCHA solving service: {service}")


# Helper function to extract reCAPTCHA site key from HTML
def extract_recaptcha_site_key(html: str) -> Optional[str]:
    """
    Extract reCAPTCHA site key from HTML content.
    
    Args:
        html: HTML content containing reCAPTCHA
        
    Returns:
        reCAPTCHA site key if found, None otherwise
    """
    import re
    
    # Regular expressions to match common reCAPTCHA patterns
    patterns = [
        r'data-sitekey="([^"]+)"',
        r'data-site-key="([^"]+)"',
        r'sitekey="([^"]+)"',
        r'sitekey:\s*[\'"]([^\'"]+)[\'"]',
        r'site-key:\s*[\'"]([^\'"]+)[\'"]'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, html)
        if match:
            return match.group(1)
    
    return None


# Helper function to extract hCaptcha site key from HTML
def extract_hcaptcha_site_key(html: str) -> Optional[str]:
    """
    Extract hCaptcha site key from HTML content.
    
    Args:
        html: HTML content containing hCaptcha
        
    Returns:
        hCaptcha site key if found, None otherwise
    """
    import re
    
    # Regular expressions to match common hCaptcha patterns
    patterns = [
        r'data-sitekey="([^"]+)"',
        r'data-site-key="([^"]+)"',
        r'sitekey="([^"]+)"',
        r'sitekey:\s*[\'"]([^\'"]+)[\'"]',
        r'site-key:\s*[\'"]([^\'"]+)[\'"]'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, html)
        if match and ('h-captcha' in html or 'hcaptcha' in html):
            return match.group(1)
    
    return None


# Function to detect CAPTCHA type from HTML
def detect_captcha_type(html: str) -> Tuple[str, Optional[str]]:
    """
    Detect the type of CAPTCHA present in the HTML.
    
    Args:
        html: HTML content to analyze
        
    Returns:
        Tuple of (captcha_type, site_key)
        captcha_type can be "recaptcha_v2", "recaptcha_v3", "hcaptcha", "image", or "unknown"
    """
    html_lower = html.lower()
    
    # Check for reCAPTCHA v3
    if 'recaptcha/api.js?render=' in html_lower:
        site_key = extract_recaptcha_site_key(html)
        return "recaptcha_v3", site_key
    
    # Check for reCAPTCHA v2
    elif 'g-recaptcha' in html_lower or 'google.com/recaptcha' in html_lower:
        site_key = extract_recaptcha_site_key(html)
        return "recaptcha_v2", site_key
    
    # Check for hCaptcha
    elif 'hcaptcha.com/1/api.js' in html_lower or 'h-captcha' in html_lower:
        site_key = extract_hcaptcha_site_key(html)
        return "hcaptcha", site_key
    
    # Check for image CAPTCHA (more complex, just a basic check)
    elif 'captcha' in html_lower and ('image' in html_lower or 'img' in html_lower):
        return "image", None
    
    return "unknown", None


# Helper function to solve CAPTCHA from browser page
async def solve_captcha_from_page(page, service: str = "auto", api_key: Optional[str] = None) -> Optional[str]:
    """
    Detect and solve CAPTCHA on the current page.
    
    Args:
        page: Playwright or Puppeteer page object
        service: CAPTCHA solving service to use
        api_key: Optional API key for the service
        
    Returns:
        CAPTCHA solution token if successful, None if no CAPTCHA detected
    """
    try:
        # Get page content and URL
        content = await page.content()
        url = page.url
        
        # Detect CAPTCHA type
        captcha_type, site_key = detect_captcha_type(content)
        
        if captcha_type == "unknown" or not site_key:
            logger.info("No CAPTCHA detected or couldn't extract site key")
            return None
        
        # Create solver
        solver = CaptchaSolverFactory.create_solver(service, api_key)
        
        # Solve based on type
        if captcha_type == "recaptcha_v2":
            token = await solver.solve_recaptcha_v2(site_key, url)
        elif captcha_type == "recaptcha_v3":
            token = await solver.solve_recaptcha_v3(site_key, url)
        elif captcha_type == "hcaptcha":
            token = await solver.solve_hcaptcha(site_key, url)
        elif captcha_type == "image":
            # Image CAPTCHA requires additional steps to extract the image
            logger.warning("Image CAPTCHA detected but need special handling")
            return None
        else:
            return None
        
        # Inject token into page
        if captcha_type.startswith("recaptcha"):
            await page.evaluate(f"""
                document.querySelector('textarea[name="g-recaptcha-response"]').innerHTML = '{token}';
                document.querySelector('textarea#g-recaptcha-response').innerHTML = '{token}';
                window.grecaptcha = window.grecaptcha || {{}};
                window.grecaptcha.getResponse = function() {{ return '{token}'; }};
            """)
        elif captcha_type == "hcaptcha":
            await page.evaluate(f"""
                document.querySelector('textarea[name="h-captcha-response"]').innerHTML = '{token}';
                window.hcaptcha = window.hcaptcha || {{}};
                window.hcaptcha.getResponse = function() {{ return '{token}'; }};
            """)
        
        return token
        
    except Exception as e:
        logger.error(f"Error solving CAPTCHA from page: {e}")
        return None


if __name__ == "__main__":
    # Example usage
    async def main():
        try:
            # Create a solver using environment variables
            solver = CaptchaSolverFactory.create_solver()
            
            # Check balance
            balance = await solver.get_balance()
            print(f"Current balance: ${balance:.2f}")
            
        except Exception as e:
            print(f"Error: {e}")
    
    # Run example
    asyncio.run(main())