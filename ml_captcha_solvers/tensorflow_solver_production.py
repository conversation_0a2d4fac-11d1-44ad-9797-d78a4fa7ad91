"""
Production-Optimized TensorFlow CAPTCHA Solver

Revolutionary approach that treats human-like timing as an ANTI-BOT FEATURE:
1. CPU-first optimization with intelligent GPU acceleration
2. Human behavioral simulation for anti-bot protection
3. Adaptive resource management for production environments
4. Integration-ready for nano-neural-network and micro-LLM systems

Key Features:
- 🎯 Human-like timing HELPS bypass anti-bot detection
- ⚡ CPU optimization without sacrificing accuracy
- 🔧 Intelligent hardware adaptation
- 📊 Behavioral analytics and pattern recognition
- ✅ Model validation and auto-download
- 🩺 Comprehensive health checks
- 🚀 Performance benchmarking tools
"""

import os
import time
import random
import io
import logging
import hashlib
import urllib.request
from typing import Optional, Any, Dict, List, Union
from collections import deque
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

# CAPTCHA constants
IMAGE_WIDTH = 150
IMAGE_HEIGHT = 50
CHAR_SET = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

# Human behavior simulation (ANTI-BOT FEATURE)
HUMAN_TIME_RANGE = (0.8, 2.5)  # Realistic response times
OPTIMAL_TIME = 1.2             # Sweet spot for human-like behavior
THINKING_VARIANCE = 0.3        # Natural human variation
FATIGUE_THRESHOLD = 10         # When to simulate fatigue

# Production optimization
CPU_THREADS = os.cpu_count() or 4
MAX_MEMORY_MB = 512
HEALTH_CHECK_INTERVAL = 300
BENCHMARK_INTERVAL = 3600  # 1 hour

# Model download configuration
MODEL_URLS = {
    'captcha_v1': {
        'url': ('https://github.com/tensorflow/models/releases/download/'
                'v1.0/captcha_model.tar.gz'),
        'sha256': 'abc123...',  # Placeholder SHA256
        'filename': 'captcha_model.tar.gz'
    }
}

# Lazy imports for production optimization
tf = None
np = None


def _lazy_import():
    """Lazy import heavy dependencies only when needed."""
    global tf, np  # noqa: PLW0603
    if tf is None:
        try:
            import tensorflow as tf_module
            tf = tf_module
            # Configure TensorFlow for production
            tf.config.threading.set_inter_op_parallelism_threads(
                CPU_THREADS)
            tf.config.threading.set_intra_op_parallelism_threads(
                CPU_THREADS)
            logger.info("TensorFlow %s imported and configured for "
                       "production", tf.__version__)
        except ImportError as e:
            logger.error("TensorFlow not available: %s", e)
            raise ImportError(
                "TensorFlow is required for production solver") from e
    
    if np is None:
        try:
            import numpy as np_module
            np = np_module
        except ImportError as e:
            logger.error("NumPy not available: %s", e)
            raise ImportError(
                "NumPy is required for production solver") from e
    
    return tf, np


@dataclass
class BehaviorMetrics:
    """Behavioral analytics for human-like timing."""
    response_times: List[float]
    fatigue_level: float
    distraction_events: int
    learning_curve: float
    consistency_score: float
    last_update: float


@dataclass
class PerformanceMetrics:
    """System performance monitoring."""
    avg_response_time: float
    success_rate: float
    memory_usage: float
    cpu_usage: float
    error_count: int
    last_benchmark: float


class HumanBehaviorSimulator:
    """
    Advanced human behavior simulation for anti-bot protection.
    
    This is our SECRET WEAPON - making the solver appear human
    actually HELPS it bypass detection systems!
    """
    
    def __init__(self):
        self.solve_history = deque(maxlen=50)
        self.session_start = time.time()
        self.solve_count = 0
        self.metrics = BehaviorMetrics(
            response_times=[],
            fatigue_level=0.0,
            distraction_events=0,
            learning_curve=1.0,
            consistency_score=1.0,
            last_update=time.time()
        )
    
    def calculate_response_time(self, complexity: float = 0.5) -> float:
        """Calculate human-like response time based on CAPTCHA complexity."""
        self.solve_count += 1
        
        # Base time increases with complexity (humans take longer for hard CAPTCHAs)
        base_time = HUMAN_TIME_RANGE[0] + (
            complexity * (HUMAN_TIME_RANGE[1] - HUMAN_TIME_RANGE[0])
        )
        
        # Add natural human variance (no robot-like consistency)
        variance = random.gauss(0, THINKING_VARIANCE * 0.3)
        response_time = base_time + variance
        
        # Simulate fatigue (humans slow down over time)
        if self.solve_count > FATIGUE_THRESHOLD:
            fatigue_factor = 1 + ((self.solve_count - FATIGUE_THRESHOLD) * 0.02)
            response_time *= min(fatigue_factor, 1.3)  # Max 30% slowdown
        
        # Occasional "distraction" spikes (very human!)
        if random.random() < 0.05:  # 5% chance
            response_time *= random.uniform(1.5, 2.0)
            self.metrics.distraction_events += 1
        
        # Learning curve (humans get slightly faster with practice)
        learning_factor = max(0.8, 1.0 - (self.solve_count * 0.005))
        response_time *= learning_factor
        
        # Ensure we stay within human ranges
        response_time = max(HUMAN_TIME_RANGE[0], 
                          min(response_time, HUMAN_TIME_RANGE[1] * 1.5))
        
        # Update metrics
        self.metrics.response_times.append(response_time)
        if len(self.metrics.response_times) > 100:
            self.metrics.response_times.pop(0)
        
        self.solve_history.append({
            'time': time.time(),
            'response_time': response_time,
            'complexity': complexity
        })
        
        return response_time
    
    def get_human_score(self) -> float:
        """Calculate how human-like the current behavior appears (0-1)."""
        if not self.metrics.response_times:
            return 1.0
        
        # Perfect timing patterns are suspicious - some variance is good!
        avg_time = sum(self.metrics.response_times) / len(self.metrics.response_times)
        timing_score = 1.0 if HUMAN_TIME_RANGE[0] <= avg_time <= HUMAN_TIME_RANGE[1] else 0.5
        
        # Variance is human (robots are too consistent)
        if len(self.metrics.response_times) > 5:
            variance = sum((t - avg_time) ** 2 for t in self.metrics.response_times[-5:]) / 5
            variance_score = min(1.0, variance / (THINKING_VARIANCE ** 2))
        else:
            variance_score = 1.0
        
        # Factor in distraction events (very human!)
        distraction_bonus = min(0.2, self.metrics.distraction_events * 0.05)
        
        return min(1.0, (timing_score * 0.6) + (variance_score * 0.3) + (0.1 + distraction_bonus))
    
    def should_pause(self) -> bool:
        """Determine if a human-like pause is needed."""
        # Occasional longer pauses are very human
        if self.solve_count > 0 and self.solve_count % 7 == 0:
            return random.random() < 0.3  # 30% chance every 7 solves
        return False
    
    def get_pause_duration(self) -> float:
        """Get a human-like pause duration."""
        return random.uniform(2.0, 5.0)  # Short break


class ProductionResourceManager:
    """
    Intelligent resource management for production environments.
    Optimizes for reliability and efficiency over raw speed.
    """
    
    def __init__(self):
        self.optimization_strategy = "balanced"  # cpu_optimized, gpu_accelerated, balanced
        self.hardware_profile = self._detect_hardware()
        self.resource_limits = self._calculate_limits()
        self._tf = None
    
    def _detect_hardware(self) -> Dict[str, Any]:
        """Detect available hardware for optimization."""
        try:
            # Memory detection
            try:
                import psutil
                memory_gb = psutil.virtual_memory().total / (1024**3)
            except ImportError:
                # Fallback without psutil
                memory_gb = 8.0  # Conservative default
            
            # GPU detection (lazy TensorFlow import)
            gpu_available = False
            gpu_memory = 0.0
            try:
                tf, _ = _lazy_import()
                if tf.config.list_physical_devices('GPU'):
                    gpu_available = True
                    try:
                        gpu_info = tf.config.experimental.get_memory_info('GPU:0')
                        gpu_memory = gpu_info['total'] / (1024**3)
                    except Exception:
                        gpu_memory = 4.0  # Default assumption
            except Exception:
                pass
            
            return {
                'cpu_count': os.cpu_count() or 4,
                'memory_gb': memory_gb,
                'gpu_available': gpu_available,
                'gpu_memory_gb': gpu_memory,
                'is_cloud': 'CLOUD_PROVIDER' in os.environ,
                'is_container': os.path.exists('/.dockerenv')
            }
        except Exception as e:
            logger.warning(f"Hardware detection failed: {e}")
            return {
                'cpu_count': 4,
                'memory_gb': 8.0,
                'gpu_available': False,
                'gpu_memory_gb': 0.0,
                'is_cloud': False,
                'is_container': False
            }
    
    def _calculate_limits(self) -> Dict[str, Any]:
        """Calculate optimal resource limits."""
        hardware = self.hardware_profile
        
        # Memory allocation (leave some for system)
        tf_memory_limit = min(
            hardware['memory_gb'] * 0.7,  # Use max 70% of system memory
            MAX_MEMORY_MB / 1024.0
        )
        
        # Thread allocation
        thread_count = min(hardware['cpu_count'], CPU_THREADS)
        
        return {
            'tf_memory_gb': tf_memory_limit,
            'thread_count': thread_count,
            'gpu_memory_growth': True,  # Allow gradual GPU memory allocation
            'mixed_precision': hardware['gpu_available']  # Use mixed precision if GPU available
        }
    
    def configure_tensorflow(self) -> bool:
        """Configure TensorFlow for optimal production performance."""
        try:
            tf, _ = _lazy_import()
            self._tf = tf
            
            # Thread configuration
            tf.config.threading.set_inter_op_parallelism_threads(
                self.resource_limits['thread_count']
            )
            tf.config.threading.set_intra_op_parallelism_threads(
                self.resource_limits['thread_count']
            )
            
            # Memory configuration
            if self.hardware_profile['gpu_available']:
                gpus = tf.config.experimental.list_physical_devices('GPU')
                if gpus:
                    try:
                        for gpu in gpus:
                            if self.resource_limits['gpu_memory_growth']:
                                tf.config.experimental.set_memory_growth(gpu, True)
                            else:
                                # Set memory limit
                                memory_limit = int(self.hardware_profile['gpu_memory_gb'] * 1024 * 0.8)
                                tf.config.experimental.set_memory_limit(gpu, memory_limit)
                    except RuntimeError as e:
                        logger.warning(f"GPU configuration failed: {e}")
            
            # Enable mixed precision if available
            if self.resource_limits['mixed_precision']:
                try:
                    policy = tf.keras.mixed_precision.Policy('mixed_float16')
                    tf.keras.mixed_precision.set_global_policy(policy)
                    logger.info("Mixed precision enabled for better performance")
                except Exception as e:
                    logger.warning(f"Mixed precision setup failed: {e}")
            
            logger.info(f"TensorFlow configured for {self.optimization_strategy} optimization")
            return True
            
        except Exception as e:
            logger.error(f"TensorFlow configuration failed: {e}")
            return False
    
    def get_optimal_device(self) -> str:
        """Get the optimal device for current workload."""
        if self.optimization_strategy == "cpu_optimized":
            return "/CPU:0"
        elif self.optimization_strategy == "gpu_accelerated" and self.hardware_profile['gpu_available']:
            return "/GPU:0"
        else:  # balanced
            return "/GPU:0" if self.hardware_profile['gpu_available'] else "/CPU:0"
    
    def is_ready(self) -> bool:
        """Check if resource manager is ready for use."""
        return self._tf is not None


class ModelManager:
    """
    Handles model loading, validation, and automatic downloads.
    Ensures model integrity and availability for production.
    """
    
    def __init__(self, model_dir: str = "models"):
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)
        self.cached_models = {}
        self._validation_cache = {}
    
    def validate_model(self, model_path: str, expected_sha256: str = None) -> bool:
        """Validate model integrity using SHA256 checksum."""
        try:
            if not os.path.exists(model_path):
                return False
            
            # Use cached validation if available
            cache_key = f"{model_path}:{os.path.getmtime(model_path)}"
            if cache_key in self._validation_cache:
                return self._validation_cache[cache_key]
            
            if expected_sha256:
                actual_sha256 = self._calculate_file_hash(model_path)
                is_valid = actual_sha256 == expected_sha256
                if not is_valid:
                    logger.warning(f"Model validation failed. Expected: {expected_sha256}, Got: {actual_sha256}")
            else:
                # Basic existence and readability check
                is_valid = os.path.isfile(model_path) and os.access(model_path, os.R_OK)
            
            self._validation_cache[cache_key] = is_valid
            return is_valid
            
        except Exception as e:
            logger.error(f"Model validation error: {e}")
            return False
    
    def _calculate_file_hash(self, filepath: str) -> str:
        """Calculate SHA256 hash of a file."""
        sha256_hash = hashlib.sha256()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def download_model(self, model_name: str, force_download: bool = False) -> bool:
        """Download a model if not available locally."""
        if model_name not in MODEL_URLS:
            logger.error(f"Unknown model: {model_name}")
            return False
        
        model_config = MODEL_URLS[model_name]
        local_path = self.model_dir / model_config['filename']
        
        # Check if already exists and valid
        if not force_download and self.validate_model(str(local_path), model_config.get('sha256')):
            logger.info(f"Model {model_name} already available and valid")
            return True
        
        try:
            logger.info(f"Downloading model {model_name}...")
            urllib.request.urlretrieve(model_config['url'], local_path)
            
            # Validate downloaded model
            if self.validate_model(str(local_path), model_config.get('sha256')):
                logger.info(f"Model {model_name} downloaded and validated successfully")
                return True
            else:
                logger.error(f"Downloaded model {model_name} failed validation")
                if local_path.exists():
                    local_path.unlink()  # Remove invalid file
                return False
                
        except Exception as e:
            logger.error(f"Model download failed: {e}")
            return False
    
    def get_model_path(self, model_name: str) -> Optional[str]:
        """Get the local path to a model, downloading if necessary."""
        if model_name not in MODEL_URLS:
            return None
        
        model_config = MODEL_URLS[model_name]
        local_path = self.model_dir / model_config['filename']
        
        if self.validate_model(str(local_path), model_config.get('sha256')):
            return str(local_path)
        
        # Try to download
        if self.download_model(model_name):
            return str(local_path)
        
        return None
    
    def list_available_models(self) -> List[str]:
        """List all available models (local and downloadable)."""
        available = []
        for model_name, config in MODEL_URLS.items():
            local_path = self.model_dir / config['filename']
            if self.validate_model(str(local_path), config.get('sha256')):
                available.append(f"{model_name} (local)")
            else:
                available.append(f"{model_name} (downloadable)")
        return available


class PerformanceBenchmark:
    """
    Comprehensive performance monitoring and benchmarking.
    Tracks system health and optimization opportunities.
    """
    
    def __init__(self):
        self.metrics = PerformanceMetrics(
            avg_response_time=0.0,
            success_rate=0.0,
            memory_usage=0.0,
            cpu_usage=0.0,
            error_count=0,
            last_benchmark=time.time()
        )
        self.benchmark_history = deque(maxlen=100)
        self.last_health_check = 0
    
    def record_solve_attempt(self, response_time: float, success: bool, error: str = None):
        """Record a CAPTCHA solve attempt for benchmarking."""
        try:
            # Update running metrics
            if hasattr(self, '_solve_times'):
                self._solve_times.append(response_time)
                self._solve_results.append(success)
            else:
                self._solve_times = deque([response_time], maxlen=100)
                self._solve_results = deque([success], maxlen=100)
            
            if error:
                self.metrics.error_count += 1
                logger.warning(f"Solve error recorded: {error}")
            
            # Update derived metrics
            self._update_metrics()
            
        except Exception as e:
            logger.error(f"Benchmark recording error: {e}")
    
    def _update_metrics(self):
        """Update calculated metrics from recorded data."""
        try:
            if hasattr(self, '_solve_times') and self._solve_times:
                self.metrics.avg_response_time = sum(self._solve_times) / len(self._solve_times)
            
            if hasattr(self, '_solve_results') and self._solve_results:
                success_count = sum(1 for result in self._solve_results if result)
                self.metrics.success_rate = success_count / len(self._solve_results)
            
            # Update system resource usage
            self._update_system_metrics()
            
        except Exception as e:
            logger.error(f"Metrics update error: {e}")
    
    def _update_system_metrics(self):
        """Update system resource usage metrics."""
        try:
            import psutil
            self.metrics.memory_usage = psutil.virtual_memory().percent
            self.metrics.cpu_usage = psutil.cpu_percent(interval=None)
        except ImportError:
            # Fallback if psutil not available
            self.metrics.memory_usage = 0.0
            self.metrics.cpu_usage = 0.0
        except Exception as e:
            logger.error(f"System metrics update error: {e}")
    
    def run_benchmark(self, solver_instance) -> Dict[str, Any]:
        """Run comprehensive benchmark on solver instance."""
        benchmark_start = time.time()
        results = {
            'timestamp': benchmark_start,
            'tests': {},
            'overall_score': 0.0,
            'recommendations': []
        }
        
        try:
            # Test 1: Response time consistency
            response_times = []
            for _ in range(5):
                start_time = time.time()
                # Simulate a simple solve (would use real test image in production)
                time.sleep(random.uniform(0.1, 0.3))  # Simulate processing
                response_times.append(time.time() - start_time)
            
            avg_response = sum(response_times) / len(response_times)
            response_variance = sum((t - avg_response) ** 2 for t in response_times) / len(response_times)
            
            results['tests']['response_time'] = {
                'avg_time': avg_response,
                'variance': response_variance,
                'score': max(0, 100 - (avg_response * 50))  # Lower time = higher score
            }
            
            # Test 2: Memory efficiency
            try:
                import psutil
                process = psutil.Process()
                memory_usage = process.memory_info().rss / 1024 / 1024  # MB
                memory_score = max(0, 100 - (memory_usage / 10))  # Penalty for high memory
                
                results['tests']['memory_efficiency'] = {
                    'memory_mb': memory_usage,
                    'score': memory_score
                }
            except ImportError:
                results['tests']['memory_efficiency'] = {
                    'memory_mb': 0,
                    'score': 50  # Neutral score if can't measure
                }
            
            # Test 3: Human behavior simulation quality
            if hasattr(solver_instance, 'behavior_sim') and solver_instance.behavior_sim:
                human_score = solver_instance.behavior_sim.get_human_score()
                results['tests']['human_behavior'] = {
                    'human_score': human_score,
                    'score': human_score * 100
                }
            
            # Calculate overall score
            test_scores = [test_data['score'] for test_data in results['tests'].values()]
            results['overall_score'] = sum(test_scores) / len(test_scores) if test_scores else 0
            
            # Generate recommendations
            if results['overall_score'] < 70:
                results['recommendations'].append("Consider optimizing solver configuration")
            if avg_response > 2.0:
                results['recommendations'].append("Response times may be too slow")
            
            # Store benchmark
            self.benchmark_history.append(results)
            self.metrics.last_benchmark = benchmark_start
            
            logger.info(f"Benchmark completed. Overall score: {results['overall_score']:.1f}")
            
        except Exception as e:
            logger.error(f"Benchmark failed: {e}")
            results['error'] = str(e)
        
        return results
    
    def get_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report."""
        current_time = time.time()
        
        return {
            'timestamp': current_time,
            'uptime': current_time - (self.metrics.last_benchmark or current_time),
            'performance': asdict(self.metrics),
            'recent_benchmarks': list(self.benchmark_history)[-5:],
            'status': self._determine_health_status(),
            'alerts': self._check_for_alerts()
        }
    
    def _determine_health_status(self) -> str:
        """Determine overall health status."""
        if self.metrics.success_rate > 0.9 and self.metrics.avg_response_time < 2.0:
            return "excellent"
        elif self.metrics.success_rate > 0.8 and self.metrics.avg_response_time < 3.0:
            return "good"
        elif self.metrics.success_rate > 0.7:
            return "fair"
        else:
            return "poor"
    
    def _check_for_alerts(self) -> List[str]:
        """Check for performance alerts."""
        alerts = []
        
        if self.metrics.success_rate < 0.8:
            alerts.append("Low success rate detected")
        if self.metrics.avg_response_time > 3.0:
            alerts.append("High response times detected")
        if self.metrics.error_count > 10:
            alerts.append("High error count detected")
        if self.metrics.memory_usage > 90:
            alerts.append("High memory usage detected")
        if self.metrics.cpu_usage > 90:
            alerts.append("High CPU usage detected")
        
        return alerts


class ProductionTensorFlowSolver:
    """
    Production-ready TensorFlow CAPTCHA solver with revolutionary human behavior simulation.
    
    Key Innovation: Human-like timing is treated as an ANTI-BOT FEATURE rather than a limitation.
    This makes the solver appear more human and actually HELPS bypass detection systems.
    """
    
    def __init__(self, model_path: str = None, behavior_simulation: bool = True):
        """
        Initialize the production TensorFlow solver.
        
        Args:
            model_path: Path to TensorFlow model (auto-download if None)
            behavior_simulation: Enable human behavior simulation (recommended)
        """
        # Core components
        self.behavior_sim = HumanBehaviorSimulator() if behavior_simulation else None
        self.resource_mgr = ProductionResourceManager()
        self.model_mgr = ModelManager()
        self.benchmark = PerformanceBenchmark()
        
        # Model state
        self.model_path = model_path
        self.model = None
        self.graph = None
        self.session = None
        self._model_loaded = False
        self._last_health_check = 0
        
        # Lazy imports
        self._tf = None
        self._np = None
        self._pil = None
        
        # Initialize
        self._initialize()
    
    def _initialize(self):
        """Initialize the solver components."""
        try:
            # Setup resource management
            if not self.resource_mgr.configure_tensorflow():
                logger.warning("TensorFlow configuration failed, using defaults")
            
            # Load model
            if self.model_path is None:
                self.model_path = self.model_mgr.get_model_path('captcha_v1')
            
            if self.model_path:
                self._load_model()
            else:
                logger.warning("No model available, solver will attempt auto-download on first use")
            
            logger.info("Production TensorFlow solver initialized successfully")
            
        except Exception as e:
            logger.error(f"Solver initialization failed: {e}")
            raise
    
    def _ensure_imports(self):
        """Ensure all required modules are imported."""
        if self._tf is None:
            self._tf, self._np = _lazy_import()
        
        if self._pil is None:
            try:
                from PIL import Image
                self._pil = Image
            except ImportError:
                logger.error("PIL/Pillow not available")
                raise ImportError("PIL/Pillow is required for image processing")
    
    def _load_model(self) -> bool:
        """Load TensorFlow model with proper error handling."""
        try:
            self._ensure_imports()
            
            if not os.path.exists(self.model_path):
                logger.error(f"Model file not found: {self.model_path}")
                return False
            
            # Use appropriate device
            device = self.resource_mgr.get_optimal_device()
            logger.info(f"Loading model on device: {device}")
            
            with self._tf.device(device):
                # Load saved model
                self.model = self._tf.saved_model.load(self.model_path)
                self._model_loaded = True
                
                logger.info(f"Model loaded successfully from {self.model_path}")
                return True
                
        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            self._model_loaded = False
            return False
    
    def _preprocess_image(self, image_data: Union[str, bytes, Any]) -> Any:
        """Preprocess image for model input."""
        try:
            self._ensure_imports()
            
            # Handle different input types
            if isinstance(image_data, str):
                # File path
                img = self._pil.open(image_data)
            elif isinstance(image_data, bytes):
                # Raw image bytes
                import io
                img = self._pil.open(io.BytesIO(image_data))
            else:
                # NumPy array
                img_array = image_data
                if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                    # Convert RGB to grayscale
                    img_array = self._np.mean(img_array, axis=2)
                return img_array.astype(self._np.float32)
            
            # Resize to expected dimensions
            img = img.resize((IMAGE_WIDTH, IMAGE_HEIGHT))
            
            # Convert to grayscale if needed
            if img.mode != 'L':
                img = img.convert('L')
            
            # Convert to numpy array and normalize
            img_array = self._np.array(img, dtype=self._np.float32) / 255.0
            
            # Add batch dimension if needed
            if len(img_array.shape) == 2:
                img_array = self._np.expand_dims(img_array, axis=0)
            if len(img_array.shape) == 3:
                img_array = self._np.expand_dims(img_array, axis=-1)
            
            # Normalize to [-0.5, 0.5] for better model performance
            img_array = img_array - 0.5
            
            return img_array
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            raise
    
    def _estimate_complexity(self, image_path: str) -> float:
        """Estimate CAPTCHA complexity for human behavior simulation."""
        try:
            self._ensure_imports()
            
            # Simple complexity estimation based on image properties
            img = self._pil.open(image_path)
            img_array = self._np.array(img)
            
            # Calculate variance (higher variance = more complex)
            variance = self._np.var(img_array)
            
            # Normalize to 0-1 range (typical variance ranges)
            complexity = min(1.0, variance / 10000.0)
            
            return complexity
            
        except Exception:
            # Default to medium complexity if estimation fails
            return 0.5
    
    def solve_captcha(self, image_data: Union[str, bytes, Any]) -> Dict[str, Any]:
        """
        Solve CAPTCHA with human behavior simulation.
        
        Args:
            image_data: Image as file path, bytes, or numpy array
            
        Returns:
            Dict containing solution, confidence, timing, and metadata
        """
        solve_start = time.time()
        result = {
            'success': False,
            'solution': '',
            'confidence': 0.0,
            'response_time': 0.0,
            'human_score': 0.0,
            'metadata': {}
        }
        
        try:
            # Ensure model is loaded
            if not self._model_loaded:
                if not self._attempt_model_recovery():
                    raise RuntimeError("Model not available and recovery failed")
            
            # Estimate complexity for behavior simulation
            complexity = 0.5
            if isinstance(image_data, str):
                complexity = self._estimate_complexity(image_data)
            
            # Calculate human-like response time (ANTI-BOT FEATURE!)
            if self.behavior_sim:
                target_response_time = self.behavior_sim.calculate_response_time(complexity)
                result['metadata']['target_response_time'] = target_response_time
            else:
                target_response_time = 1.0
            
            # Preprocess image
            processed_image = self._preprocess_image(image_data)
            
            # Run inference
            predictions = self.model(processed_image)
            
            # Decode predictions
            solution = self._decode_predictions(predictions)
            confidence = self._calculate_confidence(predictions)
            
            # Simulate human-like timing
            actual_processing_time = time.time() - solve_start
            if self.behavior_sim and actual_processing_time < target_response_time:
                pause_time = target_response_time - actual_processing_time
                time.sleep(pause_time)
            
            # Final timing
            total_time = time.time() - solve_start
            
            # Compile results
            result.update({
                'success': True,
                'solution': solution,
                'confidence': confidence,
                'response_time': total_time,
                'human_score': self.behavior_sim.get_human_score() if self.behavior_sim else 1.0,
                'metadata': {
                    'complexity': complexity,
                    'processing_time': actual_processing_time,
                    'target_response_time': target_response_time,
                    'device': self.resource_mgr.get_optimal_device(),
                    'model_loaded': self._model_loaded
                }
            })
            
            # Record for benchmarking
            self.benchmark.record_solve_attempt(total_time, True)
            
            # Occasional human-like pauses
            if self.behavior_sim and self.behavior_sim.should_pause():
                pause_duration = self.behavior_sim.get_pause_duration()
                logger.debug(f"Human-like pause: {pause_duration:.2f}s")
                time.sleep(pause_duration)
            
        except Exception as e:
            error_msg = str(e)
            result['error'] = error_msg
            result['response_time'] = time.time() - solve_start
            
            # Record failed attempt
            self.benchmark.record_solve_attempt(result['response_time'], False, error_msg)
            
            logger.error(f"CAPTCHA solve failed: {error_msg}")
        
        return result
    
    def _decode_predictions(self, predictions) -> str:
        """Decode model predictions to text."""
        try:
            self._ensure_imports()
            
            solution = ""
            
            # Handle different prediction formats
            if hasattr(predictions, 'numpy'):
                predictions = predictions.numpy()
            
            # Assume predictions are character probabilities
            for char_probs in predictions[0]:  # First batch item
                if self._np.max(char_probs) > 1.0:
                    # Apply softmax if values are logits
                    char_probs = self._softmax(char_probs)
                
                predicted_idx = self._np.argmax(char_probs)
                if predicted_idx < len(CHAR_SET):
                    solution += CHAR_SET[predicted_idx]
            
            return solution
            
        except Exception as e:
            logger.error(f"Prediction decoding failed: {e}")
            return ""
    
    def _softmax(self, x):
        """Apply softmax to convert logits to probabilities."""
        exp_x = self._np.exp(x - self._np.max(x))
        return exp_x / self._np.sum(exp_x)
    
    def _calculate_confidence(self, predictions) -> float:
        """Calculate confidence score from predictions."""
        try:
            self._ensure_imports()
            
            if hasattr(predictions, 'numpy'):
                predictions = predictions.numpy()
            
            # Calculate average maximum probability across characters
            confidences = []
            for char_probs in predictions[0]:
                if self._np.max(char_probs) > 1.0:
                    char_probs = self._softmax(char_probs)
                confidences.append(self._np.max(char_probs))
            
            return float(self._np.mean(confidences)) if confidences else 0.0
            
        except Exception:
            return 0.0
    
    def _attempt_model_recovery(self) -> bool:
        """Attempt to recover from model loading failures."""
        try:
            logger.info("Attempting model recovery...")
            
            # Try to download model
            if self.model_mgr.download_model('captcha_v1'):
                self.model_path = self.model_mgr.get_model_path('captcha_v1')
                return self._load_model()
            
            return False
            
        except Exception as e:
            logger.error(f"Model recovery failed: {e}")
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status."""
        current_time = time.time()
        
        # Throttle health checks
        if current_time - self._last_health_check < HEALTH_CHECK_INTERVAL:
            return {'status': 'cached', 'last_check': self._last_health_check}
        
        self._last_health_check = current_time
        
        health = {
            'timestamp': current_time,
            'overall_status': 'healthy',
            'components': {},
            'performance': {},
            'recommendations': []
        }
        
        try:
            # Component health
            health['components'] = {
                'model_loaded': self._model_loaded,
                'resource_manager': self.resource_mgr.is_ready(),
                'behavior_simulator': self.behavior_sim is not None,
                'dependencies_ok': self._check_dependencies()
            }
            
            # Performance metrics
            health['performance'] = self.benchmark.get_health_report()
            
            # Overall status determination
            if not all(health['components'].values()):
                health['overall_status'] = 'degraded'
                if not health['components']['model_loaded']:
                    health['recommendations'].append("Model loading required")
                if not health['components']['dependencies_ok']:
                    health['recommendations'].append("Check dependencies installation")
            
            # Resource usage alerts
            if health['performance']['performance']['memory_usage'] > 90:
                health['overall_status'] = 'warning'
                health['recommendations'].append("High memory usage detected")
            
        except Exception as e:
            health['overall_status'] = 'error'
            health['error'] = str(e)
            logger.error(f"Health check failed: {e}")
        
        return health
    
    def _check_dependencies(self) -> bool:
        """Check if all required dependencies are available."""
        try:
            self._ensure_imports()
            return True
        except Exception:
            return False
    
    def run_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive performance benchmark."""
        return self.benchmark.run_benchmark(self)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get detailed performance report."""
        return {
            'health': self.get_health_status(),
            'benchmark': self.benchmark.get_health_report(),
            'behavior_metrics': asdict(self.behavior_sim.metrics) if self.behavior_sim else None,
            'resource_profile': self.resource_mgr.hardware_profile,
            'optimization_strategy': self.resource_mgr.optimization_strategy
        }


# Backward compatibility functions
def create_production_solver(model_path: str = None, **kwargs) -> ProductionTensorFlowSolver:
    """Create a production-ready TensorFlow solver instance."""
    return ProductionTensorFlowSolver(model_path=model_path, **kwargs)


def solve_captcha_with_timing(image_data, model_path: str = None) -> Dict[str, Any]:
    """
    Solve CAPTCHA with human-like timing simulation.
    
    This is the main entry point for the revolutionary timing approach.
    """
    solver = ProductionTensorFlowSolver(model_path=model_path)
    return solver.solve_captcha(image_data)


# Configuration presets
OPTIMIZATION_PRESETS = {
    'cpu_optimized': {
        'optimization_strategy': 'cpu_optimized',
        'behavior_simulation': True,
        'gpu_acceleration': False
    },
    'gpu_accelerated': {
        'optimization_strategy': 'gpu_accelerated', 
        'behavior_simulation': True,
        'gpu_acceleration': True
    },
    'stealth_mode': {
        'optimization_strategy': 'balanced',
        'behavior_simulation': True,
        'human_timing_emphasis': 'high'
    },
    'production_balanced': {
        'optimization_strategy': 'balanced',
        'behavior_simulation': True,
        'health_monitoring': True
    }
}


def configure_solver_preset(preset_name: str) -> ProductionTensorFlowSolver:
    """Configure solver with predefined optimization preset."""
    if preset_name not in OPTIMIZATION_PRESETS:
        raise ValueError(f"Unknown preset: {preset_name}")
    
    config = OPTIMIZATION_PRESETS[preset_name]
    solver = ProductionTensorFlowSolver()
    
    # Apply configuration
    solver.resource_mgr.optimization_strategy = config['optimization_strategy']
    
    if not config.get('behavior_simulation', True):
        solver.behavior_sim = None
    
    return solver


# Export main classes and functions
__all__ = [
    'ProductionTensorFlowSolver',
    'HumanBehaviorSimulator', 
    'ProductionResourceManager',
    'ModelManager',
    'PerformanceBenchmark',
    'create_production_solver',
    'solve_captcha_with_timing',
    'configure_solver_preset',
    'OPTIMIZATION_PRESETS'
]


if __name__ == "__main__":
    # Demonstration of the revolutionary human timing approach
    print("🎯 Production TensorFlow CAPTCHA Solver with Human Behavior Simulation")
    print("=" * 70)
    
    try:
        # Create solver instance
        solver = create_production_solver()
        
        # Display system information
        health = solver.get_health_status()
        print(f"System Status: {health['overall_status']}")
        print(f"Model Loaded: {health['components']['model_loaded']}")
        print(f"Hardware Profile: {solver.resource_mgr.hardware_profile}")
        
        # Demonstrate human behavior scoring
        if solver.behavior_sim:
            print(f"\nHuman Behavior Score: {solver.behavior_sim.get_human_score():.2f}/1.0")
            print("📊 Higher scores indicate more human-like behavior patterns")
        
        print("\n✅ Production solver ready for deployment!")
        print("🚀 Use solve_captcha(image_data) to process CAPTCHAs with human-like timing")
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        print("💡 Ensure TensorFlow and dependencies are installed")


