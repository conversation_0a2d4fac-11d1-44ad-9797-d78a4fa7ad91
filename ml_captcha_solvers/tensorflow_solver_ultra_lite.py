#!/usr/bin/env python3
"""
Ultra-lightweight TensorFlow captcha solver with maximum import isolation.

This module provides a TensorFlow-based captcha solver that completely
isolates ALL imports to prevent hanging issues.
"""

import os
import sys
import logging
from typing import Optional, Union, Any

# Configure logging
logger = logging.getLogger(__name__)

# Constants
IMAGE_WIDTH = 150
IMAGE_HEIGHT = 50

class TensorFlowCaptchaSolverUltraLite:
    """
    Ultra-lightweight TensorFlow-based captcha solver with maximum isolation.
    
    This solver uses dynamic imports for ALL dependencies to avoid
    hanging issues during package initialization.
    """
    
    def __init__(self, model_path: Optional[str] = None):
        """
        Initialize the TensorFlow captcha solver.
        
        Args:
            model_path: Path to the trained model file
        """
        self.model_path = model_path or self._get_default_model_path()
        
        # State tracking
        self._model_loaded = False
        self._tf_module = None
        self._np_module = None
        self._pil_module = None
        
        # TensorFlow objects (loaded lazily)
        self.graph = None
        self.sess = None
        self.images = None
        self.result = None
        
        logger.info("TensorFlow captcha solver initialized (ultra-lite)")
    
    def _get_default_model_path(self) -> str:
        """Get the default model path."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return os.path.join(current_dir, 'models', 'captcha.pb')
    
    def _ensure_numpy(self):
        """Ensure numpy is imported."""
        if self._np_module is None:
            try:
                import importlib
                self._np_module = importlib.import_module('numpy')
                logger.debug("Numpy imported successfully")
            except Exception as e:
                logger.error(f"Failed to import numpy: {e}")
                raise ImportError("numpy is required for image processing")
        return self._np_module
    
    def _ensure_pil(self):
        """Ensure PIL is imported."""
        if self._pil_module is None:
            try:
                import importlib
                pil_image = importlib.import_module('PIL.Image')
                self._pil_module = pil_image
                logger.debug("PIL imported successfully")
            except Exception as e:
                logger.error(f"Failed to import PIL: {e}")
                raise ImportError("PIL is required for image processing")
        return self._pil_module
    
    def _ensure_tensorflow(self):
        """Ensure TensorFlow is imported."""
        if self._tf_module is None:
            try:
                import importlib
                logger.info("Attempting to import TensorFlow...")
                self._tf_module = importlib.import_module('tensorflow')
                logger.info("TensorFlow imported successfully")
            except ImportError:
                logger.error("TensorFlow is not installed")
                raise ImportError("TensorFlow is required for TensorFlow captcha solver")
            except Exception as e:
                logger.error(f"Error importing TensorFlow: {e}")
                raise RuntimeError(f"Failed to import TensorFlow: {e}")
        return self._tf_module
    
    def _ensure_model_loaded(self) -> bool:
        """Ensure the TensorFlow model is loaded."""
        if self._model_loaded:
            return True
        
        try:
            # Ensure TensorFlow is imported
            tf = self._ensure_tensorflow()
            
            # Check if model file exists
            if not os.path.exists(self.model_path):
                logger.error(f"Model file not found: {self.model_path}")
                return False
            
            # Create TensorFlow graph and session
            self.graph = tf.Graph()
            
            with self.graph.as_default():
                # Load the model
                graph_def = tf.GraphDef()
                with open(self.model_path, 'rb') as f:
                    graph_def.ParseFromString(f.read())
                tf.import_graph_def(graph_def, name='')
                
                # Get input/output tensors
                self.images = self.graph.get_tensor_by_name('input:0')
                self.result = self.graph.get_tensor_by_name('output:0')
            
            # Create session
            config = tf.ConfigProto()
            config.gpu_options.allow_growth = True
            self.sess = tf.Session(graph=self.graph, config=config)
            
            self._model_loaded = True
            logger.info("TensorFlow model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading TensorFlow model: {e}")
            return False
    
    def _preprocess_image_lite(self, image_path: str) -> Optional[Any]:
        """
        Lightweight image preprocessing using dynamic imports.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Preprocessed image array or None if failed
        """
        try:
            # Ensure dependencies
            np = self._ensure_numpy()
            Image = self._ensure_pil()
            
            # Load and preprocess image
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize image
                img_resized = img.resize((IMAGE_WIDTH, IMAGE_HEIGHT))
                
                # Convert to grayscale
                img_gray = img_resized.convert('L')
                
                # Convert to numpy array
                img_array = np.array(img_gray)
                
                # Normalize to [0, 1]
                img_normalized = img_array.astype(np.float32) / 255.0
                
                # Flatten the image
                img_flattened = img_normalized.flatten()
                
                logger.debug(f"Preprocessed image shape: {img_flattened.shape}")
                return img_flattened
                
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            return None
    
    def _one_hot_to_text(self, one_hot_array) -> str:
        """Convert one-hot encoded array to text."""
        # This would need to be implemented based on your specific model
        # For now, return a placeholder
        return "CAPTCHA"
    
    def predict(self, image_path: str) -> Optional[str]:
        """
        Predict captcha from image file.
        
        Args:
            image_path: Path to the captcha image
            
        Returns:
            Predicted captcha text or None if failed
        """
        try:
            # Ensure model is loaded
            if not self._ensure_model_loaded():
                logger.error("Failed to load TensorFlow model")
                return None
            
            # Preprocess image
            input_img = self._preprocess_image_lite(image_path)
            if input_img is None:
                logger.error("Failed to preprocess image")
                return None
            
            # Ensure numpy for array operations
            np = self._ensure_numpy()
            input_img = np.expand_dims(input_img, axis=0)
            
            # Run prediction
            with self.graph.as_default():
                recog_result = self.sess.run(
                    self.result, 
                    feed_dict={self.images: input_img}
                )
            
            # Convert to text
            text = self._one_hot_to_text(recog_result[0])
            logger.info(f"Predicted captcha: {text}")
            return text
            
        except Exception as e:
            logger.error(f"Error predicting captcha: {e}")
            return None
    
    def predict_from_array(self, image_array) -> Optional[str]:
        """
        Predict captcha from numpy array.
        
        Args:
            image_array: Image as numpy array
            
        Returns:
            Predicted captcha text or None if failed
        """
        try:
            # Ensure model is loaded
            if not self._ensure_model_loaded():
                logger.error("Failed to load TensorFlow model")
                return None
            
            # Ensure numpy
            np = self._ensure_numpy()
            
            # Validate array shape
            expected_shape = (IMAGE_HEIGHT * IMAGE_WIDTH,)
            if image_array.shape != expected_shape:
                logger.error(
                    f"Invalid image array shape: {image_array.shape}, "
                    f"expected: {expected_shape}"
                )
                return None
            
            # Add batch dimension
            input_img = np.expand_dims(image_array, axis=0)
            
            # Run prediction
            with self.graph.as_default():
                recog_result = self.sess.run(
                    self.result, 
                    feed_dict={self.images: input_img}
                )
            
            # Convert to text
            text = self._one_hot_to_text(recog_result[0])
            logger.info(f"Predicted captcha from array: {text}")
            return text
            
        except Exception as e:
            logger.error(f"Error predicting captcha from array: {e}")
            return None
    
    def cleanup(self):
        """Clean up TensorFlow resources."""
        try:
            if self.sess is not None:
                self.sess.close()
                self.sess = None
                logger.info("TensorFlow session closed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Alias for backward compatibility
TensorFlowCaptchaSolver = TensorFlowCaptchaSolverUltraLite
