#!/usr/bin/env python3
"""
Lightweight image preprocessing utilities for captcha solving.

This module provides functions to preprocess captcha images using only PIL
to avoid OpenCV dependency issues.
"""

import os
import numpy as np
from PIL import Image, ImageFilter, ImageOps
import logging
from typing import Tuple, Optional, Union, List

# Configure logging
logger = logging.getLogger(__name__)

def resize_image(image: Union[np.ndarray, Image.Image], width: int, height: int) -> np.ndarray:
    """
    Resize an image to the specified dimensions using PIL.
    
    Args:
        image: Input image (numpy array or PIL Image)
        width: Target width
        height: Target height
        
    Returns:
        Resized image as numpy array
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(image.astype('uint8'), 'RGB')
        else:
            image = Image.fromarray(image.astype('uint8'), 'L')
    
    return np.array(image.resize((width, height)))

def convert_to_grayscale(image: Union[np.ndarray, Image.Image]) -> np.ndarray:
    """
    Convert an image to grayscale using PIL.
    
    Args:
        image: Input image (numpy array or PIL Image)
        
    Returns:
        Grayscale image as numpy array
    """
    if isinstance(image, np.ndarray):
        # Convert numpy array to PIL Image
        if len(image.shape) == 3:
            image = Image.fromarray(image.astype('uint8'), 'RGB')
        else:
            return image  # Already grayscale
    
    return np.array(image.convert('L'))

def normalize_image(image: np.ndarray) -> np.ndarray:
    """
    Normalize image pixel values to [0, 1] range.
    
    Args:
        image: Input image as numpy array
        
    Returns:
        Normalized image
    """
    return image.astype(np.float32) / 255.0

def threshold_image(image: np.ndarray, threshold: int = 128) -> np.ndarray:
    """
    Apply binary thresholding to an image.
    
    Args:
        image: Input grayscale image
        threshold: Threshold value (0-255)
        
    Returns:
        Binary image
    """
    return (image > threshold).astype(np.uint8) * 255

def denoise_image(image: Union[np.ndarray, Image.Image]) -> np.ndarray:
    """
    Apply denoising to an image using PIL filters.
    
    Args:
        image: Input image
        
    Returns:
        Denoised image as numpy array
    """
    if isinstance(image, np.ndarray):
        # Convert to PIL Image
        if len(image.shape) == 3:
            pil_image = Image.fromarray(image.astype('uint8'), 'RGB')
        else:
            pil_image = Image.fromarray(image.astype('uint8'), 'L')
    else:
        pil_image = image
    
    # Apply median filter for denoising
    denoised = pil_image.filter(ImageFilter.MedianFilter(size=3))
    return np.array(denoised)

def preprocess_for_tensorflow(image_path: str, width: int = 150, height: int = 50) -> np.ndarray:
    """
    Preprocess an image for the TensorFlow captcha solver using PIL only.
    
    Args:
        image_path: Path to the image file
        width: Target width (default: 150)
        height: Target height (default: 50)
        
    Returns:
        Preprocessed image as flattened numpy array
    """
    try:
        # Load image using PIL
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize image
            img_resized = img.resize((width, height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale
            img_gray = img_resized.convert('L')
            
            # Convert to numpy array
            img_array = np.array(img_gray)
            
            # Normalize to [0, 1]
            img_normalized = img_array.astype(np.float32) / 255.0
            
            # Flatten the image
            img_flattened = img_normalized.flatten()
            
            logger.debug(f"Preprocessed image shape: {img_flattened.shape}")
            return img_flattened
            
    except Exception as e:
        logger.error(f"Error preprocessing image for TensorFlow: {e}")
        return None

def preprocess_for_pytorch(image_path: str, width: int = 150, height: int = 50) -> np.ndarray:
    """
    Preprocess an image for the PyTorch captcha solver using PIL only.
    
    Args:
        image_path: Path to the image file
        width: Target width (default: 150)  
        height: Target height (default: 50)
        
    Returns:
        Preprocessed image as numpy array with shape (1, height, width)
    """
    try:
        # Load image using PIL
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize image
            img_resized = img.resize((width, height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale
            img_gray = img_resized.convert('L')
            
            # Convert to numpy array
            img_array = np.array(img_gray)
            
            # Normalize to [0, 1]
            img_normalized = img_array.astype(np.float32) / 255.0
            
            # Add channel dimension for PyTorch (C, H, W)
            img_tensor = np.expand_dims(img_normalized, axis=0)
            
            logger.debug(f"Preprocessed image shape: {img_tensor.shape}")
            return img_tensor
            
    except Exception as e:
        logger.error(f"Error preprocessing image for PyTorch: {e}")
        return None

def preprocess_image_data(image_data: bytes, model_type: str = 'tensorflow') -> np.ndarray:
    """
    Preprocess image data (bytes) for the specified model type.
    
    Args:
        image_data: Raw image data as bytes
        model_type: Type of model ('tensorflow' or 'pytorch')
        
    Returns:
        Preprocessed image as numpy array
    """
    try:
        # Create PIL Image from bytes
        from io import BytesIO
        img = Image.open(BytesIO(image_data))
        
        # Save temporarily and use existing preprocessing functions
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            img.save(temp_file.name)
            temp_path = temp_file.name
        
        try:
            if model_type.lower() == 'tensorflow':
                result = preprocess_for_tensorflow(temp_path)
            elif model_type.lower() == 'pytorch':
                result = preprocess_for_pytorch(temp_path)
            else:
                raise ValueError(f"Unsupported model type: {model_type}")
            
            return result
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                
    except Exception as e:
        logger.error(f"Error preprocessing image data: {e}")
        return None
