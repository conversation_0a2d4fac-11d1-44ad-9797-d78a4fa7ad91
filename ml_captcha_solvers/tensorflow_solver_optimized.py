#!/usr/bin/env python3
"""
TensorFlow-based captcha solver with complete lazy loading isolation.

This module provides a TensorFlow-based captcha solver that completely
isolates TensorFlow imports to prevent initialization hanging.
"""

import os
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from PIL import Image
import importlib
import sys

# Configure logging
logger = logging.getLogger(__name__)

# Constants
IMAGE_WIDTH = 150
IMAGE_HEIGHT = 50
CHARS_NUM = 6  # Number of characters in captcha
CLASSES_NUM = 10  # Number of classes (digits 0-9)
CHAR_SET = '0123456789'  # Character set for decoding


class TensorFlowCaptchaSolverOptimized:
    """TensorFlow-based captcha solver with complete lazy loading."""

    def __init__(self, model_path: str = None, chars_num: int = CHARS_NUM, 
                 classes_num: int = CLASSES_NUM):
        """
        Initialize the solver.

        Args:
            model_path: Path to the model checkpoint directory
            chars_num: Number of characters in captcha
            classes_num: Number of character classes
        """
        self.chars_num = chars_num
        self.classes_num = classes_num
        self.model_path = model_path
        
        # Initialize model components to None (lazy loading)
        self.graph = None
        self.sess = None
        self.images = None
        self.logits = None
        self.result = None
        self.saver = None
        self._model_loaded = False
        self._tf_module = None

    def _ensure_tensorflow(self):
        """Completely isolated TensorFlow import."""
        if self._tf_module is None:
            try:
                logger.info("Attempting to import TensorFlow...")
                # Use importlib to avoid any import-time side effects
                self._tf_module = importlib.import_module('tensorflow')
                logger.info(f"TensorFlow {self._tf_module.__version__} imported successfully")
            except ImportError as e:
                logger.error(f"TensorFlow not available: {e}")
                raise ImportError("TensorFlow is required for TensorFlow captcha solver")
            except Exception as e:
                logger.error(f"Error importing TensorFlow: {e}")
                raise RuntimeError(f"Failed to import TensorFlow: {e}")
        return self._tf_module

    def _ensure_model_loaded(self):
        """Ensure TensorFlow is loaded and model is created (lazy loading)."""
        if self._model_loaded:
            return True
            
        try:
            # Ensure TensorFlow is available
            tf = self._ensure_tensorflow()
            
            # Create the model
            self._create_model()
            
            # Load model if path is provided
            if self.model_path and os.path.exists(self.model_path):
                self._load_model()
            else:
                logger.warning(f"Model path not provided or does not exist: {self.model_path}")
            
            self._model_loaded = True
            return True
        except Exception as e:
            logger.error(f"Error initializing TensorFlow model: {e}")
            return False

    def _create_model(self) -> None:
        """Create the TensorFlow model."""
        try:
            tf = self._tf_module
            # Check TensorFlow version
            tf_version = tf.__version__
            logger.info(f"Using TensorFlow version: {tf_version}")

            # TensorFlow 2.x compatibility
            if tf_version.startswith('2'):
                # Use TF2 with eager execution disabled for compatibility
                tf.compat.v1.disable_eager_execution()

                # Create a new graph
                self.graph = tf.compat.v1.Graph()
                with self.graph.as_default():
                    # Input placeholder
                    self.images = tf.compat.v1.placeholder(
                        tf.float32, 
                        [None, IMAGE_HEIGHT * IMAGE_WIDTH], 
                        name='images'
                    )

                    # Create the model
                    self.logits = self._inference(self.images, keep_prob=1.0)

                    # Output operation
                    self.result = self._output(self.logits)

                    # Create a saver
                    self.saver = tf.compat.v1.train.Saver()

                    # Create a session
                    self.sess = tf.compat.v1.Session(graph=self.graph)
            else:
                # TensorFlow 1.x code path
                # Create a new graph
                self.graph = tf.Graph()
                with self.graph.as_default():
                    # Input placeholder
                    self.images = tf.placeholder(
                        tf.float32, 
                        [None, IMAGE_HEIGHT * IMAGE_WIDTH], 
                        name='images'
                    )

                    # Create the model
                    self.logits = self._inference(self.images, keep_prob=1.0)

                    # Output operation
                    self.result = self._output(self.logits)

                    # Create a saver
                    self.saver = tf.train.Saver()

                    # Create a session
                    self.sess = tf.Session(graph=self.graph)
        except Exception as e:
            logger.error(f"Error creating TensorFlow model: {e}")
            raise

    def _load_model(self) -> None:
        """Load model from checkpoint."""
        try:
            tf = self._tf_module
            with self.graph.as_default():
                # Check TensorFlow version
                tf_version = tf.__version__

                # TensorFlow 2.x compatibility
                if tf_version.startswith('2'):
                    # Use TF2 checkpoint loading
                    checkpoint = tf.compat.v1.train.latest_checkpoint(self.model_path)
                    if checkpoint:
                        self.saver.restore(self.sess, checkpoint)
                        logger.info(f"Model loaded from {checkpoint}")
                    else:
                        logger.warning(f"No checkpoint found in {self.model_path}")
                else:
                    # TensorFlow 1.x code path
                    checkpoint = tf.train.latest_checkpoint(self.model_path)
                    if checkpoint:
                        self.saver.restore(self.sess, checkpoint)
                        logger.info(f"Model loaded from {checkpoint}")
                    else:
                        logger.warning(f"No checkpoint found in {self.model_path}")
        except Exception as e:
            logger.error(f"Error loading TensorFlow model: {e}")
            raise

    def _inference(self, images, keep_prob):
        """Create the model inference graph."""
        tf = self._tf_module
        tf_version = tf.__version__

        # Use appropriate variable_scope based on TF version
        if tf_version.startswith('2'):
            variable_scope = tf.compat.v1.variable_scope
        else:
            variable_scope = tf.variable_scope

        images = tf.reshape(images, [-1, IMAGE_HEIGHT, IMAGE_WIDTH, 1])

        # Conv1
        with variable_scope('conv1'):
            kernel = self._weight_variable('weights', shape=[3, 3, 1, 64])
            biases = self._bias_variable('biases', [64])
            pre_activation = tf.nn.bias_add(self._conv2d(images, kernel), biases)
            conv1 = tf.nn.relu(pre_activation, name='conv1')

        # Pool1
        pool1 = self._max_pool_2x2(conv1, name='pool1')

        # Conv2
        with variable_scope('conv2'):
            kernel = self._weight_variable('weights', shape=[3, 3, 64, 64])
            biases = self._bias_variable('biases', [64])
            pre_activation = tf.nn.bias_add(self._conv2d(pool1, kernel), biases)
            conv2 = tf.nn.relu(pre_activation, name='conv2')

        # Pool2
        pool2 = self._max_pool_2x2(conv2, name='pool2')

        # Conv3
        with variable_scope('conv3'):
            kernel = self._weight_variable('weights', shape=[3, 3, 64, 64])
            biases = self._bias_variable('biases', [64])
            pre_activation = tf.nn.bias_add(self._conv2d(pool2, kernel), biases)
            conv3 = tf.nn.relu(pre_activation, name='conv3')

        # Pool3
        pool3 = self._max_pool_2x2(conv3, name='pool3')

        # Fully connected layer
        with variable_scope('local1'):
            # Get batch size safely for both TF1 and TF2
            if tf_version.startswith('2'):
                batch_size = tf.shape(images)[0]
            else:
                batch_size = tf.shape(images)[0]

            reshape = tf.reshape(pool3, [batch_size, -1])

            # Calculate dimension safely
            if tf_version.startswith('2'):
                pool3_shape = pool3.get_shape().as_list()
                h = pool3_shape[1] if pool3_shape[1] is not None else IMAGE_HEIGHT // 8
                w = pool3_shape[2] if pool3_shape[2] is not None else IMAGE_WIDTH // 8
                c = pool3_shape[3] if pool3_shape[3] is not None else 64
                dim = h * w * c
            else:
                dim = reshape.get_shape()[1].value

            weights = self._weight_variable('weights', shape=[dim, 1024])
            biases = self._bias_variable('biases', [1024])
            local1 = tf.nn.relu(tf.matmul(reshape, weights) + biases, name='local1')

        # Dropout
        if tf_version.startswith('2'):
            local1_drop = tf.nn.dropout(local1, rate=1-keep_prob)
        else:
            local1_drop = tf.nn.dropout(local1, keep_prob)

        # Output layer
        with variable_scope('softmax_linear'):
            weights = self._weight_variable(
                'weights', 
                shape=[1024, self.chars_num * self.classes_num]
            )
            biases = self._bias_variable('biases', [self.chars_num * self.classes_num])
            softmax_linear = tf.add(
                tf.matmul(local1_drop, weights), 
                biases, 
                name='softmax_linear'
            )

        return tf.reshape(softmax_linear, [-1, self.chars_num, self.classes_num])

    def _output(self, logits):
        """Create the output operation."""
        tf = self._tf_module
        return tf.argmax(logits, 2)

    def _conv2d(self, value, weight):
        """Create a 2D convolution operation."""
        tf = self._tf_module
        return tf.nn.conv2d(value, weight, strides=[1, 1, 1, 1], padding='SAME')

    def _max_pool_2x2(self, value, name):
        """Create a 2x2 max pooling operation."""
        tf = self._tf_module
        tf_version = tf.__version__

        if tf_version.startswith('2'):
            return tf.nn.max_pool2d(
                value, 
                ksize=[1, 2, 2, 1], 
                strides=[1, 2, 2, 1],
                padding='SAME', 
                name=name
            )
        else:
            return tf.nn.max_pool(
                value, 
                ksize=[1, 2, 2, 1], 
                strides=[1, 2, 2, 1],
                padding='SAME', 
                name=name
            )

    def _weight_variable(self, name, shape):
        """Create a weight variable."""
        tf = self._tf_module
        tf_version = tf.__version__

        with tf.device('/cpu:0'):
            if tf_version.startswith('2'):
                initializer = tf.compat.v1.truncated_normal_initializer(stddev=0.1)
                var = tf.compat.v1.get_variable(
                    name, 
                    shape, 
                    initializer=initializer, 
                    dtype=tf.float32
                )
            else:
                initializer = tf.truncated_normal_initializer(stddev=0.1)
                var = tf.get_variable(
                    name, 
                    shape, 
                    initializer=initializer, 
                    dtype=tf.float32
                )
        return var

    def _bias_variable(self, name, shape):
        """Create a bias variable."""
        tf = self._tf_module
        tf_version = tf.__version__

        with tf.device('/cpu:0'):
            if tf_version.startswith('2'):
                initializer = tf.compat.v1.constant_initializer(0.1)
                var = tf.compat.v1.get_variable(
                    name, 
                    shape, 
                    initializer=initializer, 
                    dtype=tf.float32
                )
            else:
                initializer = tf.constant_initializer(0.1)
                var = tf.get_variable(
                    name, 
                    shape, 
                    initializer=initializer, 
                    dtype=tf.float32
                )
        return var

    def predict(self, image_path: str) -> str:
        """
        Predict captcha from image.

        Args:
            image_path: Path to the image file

        Returns:
            Predicted captcha text
        """
        try:
            # Ensure model is loaded
            if not self._ensure_model_loaded():
                logger.error("Failed to load TensorFlow model")
                return None
                
            # Lazy import preprocessing function using importlib
            try:
                preprocessing_module = importlib.import_module(
                    '.preprocessing', package='ml_captcha_solvers'
                )
                preprocess_for_tensorflow = getattr(
                    preprocessing_module, 'preprocess_for_tensorflow'
                )
            except Exception as e:
                logger.error(f"Failed to import preprocessing function: {e}")
                return None
                
            # Preprocess image
            input_img = preprocess_for_tensorflow(image_path)
            if input_img is None:
                logger.error("Failed to preprocess image")
                return None
                
            input_img = np.expand_dims(input_img, axis=0)

            # Run prediction
            with self.graph.as_default():
                recog_result = self.sess.run(
                    self.result, 
                    feed_dict={self.images: input_img}
                )

            # Convert to text
            text = self._one_hot_to_text(recog_result[0])

            return text
        except Exception as e:
            logger.error(f"Error predicting captcha: {e}")
            return None

    def predict_from_array(self, image_array: np.ndarray) -> str:
        """
        Predict captcha from numpy array.

        Args:
            image_array: Image as numpy array

        Returns:
            Predicted captcha text
        """
        try:
            # Ensure model is loaded
            if not self._ensure_model_loaded():
                logger.error("Failed to load TensorFlow model")
                return None
                
            # Ensure the array is in the right shape
            expected_shape = (IMAGE_HEIGHT * IMAGE_WIDTH,)
            if image_array.shape != expected_shape:
                logger.error(
                    f"Invalid image array shape: {image_array.shape}, "
                    f"expected: {expected_shape}"
                )
                return None

            # Add batch dimension
            input_img = np.expand_dims(image_array, axis=0)

            # Run prediction
            with self.graph.as_default():
                recog_result = self.sess.run(
                    self.result, 
                    feed_dict={self.images: input_img}
                )

            # Convert to text
            text = self._one_hot_to_text(recog_result[0])

            return text
        except Exception as e:
            logger.error(f"Error predicting captcha from array: {e}")
            return None

    def _one_hot_to_text(self, one_hot: np.ndarray) -> str:
        """
        Convert one-hot encoded array to text.

        Args:
            one_hot: One-hot encoded array

        Returns:
            Text representation
        """
        return ''.join([CHAR_SET[i] for i in one_hot])
        
    def cleanup(self):
        """Clean up TensorFlow resources."""
        try:
            if self.sess is not None:
                self.sess.close()
                logger.info("TensorFlow session closed")
        except Exception as e:
            logger.error(f"Error cleaning up TensorFlow resources: {e}")


# For backward compatibility
TensorFlowCaptchaSolver = TensorFlowCaptchaSolverOptimized
