#!/usr/bin/env python3
"""
Evaluation script for ML-based CAPTCHA solvers.

This script provides functionality to evaluate ML-based CAPTCHA solvers
using PyTorch or TensorFlow.
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Evaluate ML-based CAPTCHA solvers')
    parser.add_argument('--data-dir', type=str, required=True, 
                        help='Directory containing evaluation data')
    parser.add_argument('--model-path', type=str, required=True,
                        help='Path to trained model')
    parser.add_argument('--model-type', type=str, choices=['pytorch', 'tensorflow'], 
                        required=True, help='Type of model to evaluate')
    parser.add_argument('--char-set', type=str, default='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                        help='Character set for CAPTCHA')
    parser.add_argument('--captcha-length', type=int, default=4,
                        help='Length of CAPTCHA')
    parser.add_argument('--image-width', type=int, default=160,
                        help='Width of CAPTCHA image')
    parser.add_argument('--image-height', type=int, default=60,
                        help='Height of CAPTCHA image')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--gpu', action='store_true', help='Use GPU for evaluation')
    parser.add_argument('--output-file', type=str, help='Path to save evaluation results')
    return parser.parse_args()

def evaluate_pytorch_model(args):
    """
    Evaluate a PyTorch-based CAPTCHA solver.
    
    Args:
        args: Command line arguments
        
    Returns:
        Dictionary with evaluation results
    """
    try:
        import torch
        import torch.nn as nn
        from torch.utils.data import DataLoader, Dataset
        import torchvision.transforms as transforms
        from PIL import Image
        import numpy as np
        
        from .pytorch_solver import CaptchaResNetModel
        
        logger.info("Evaluating PyTorch model...")
        
        # Check if GPU is available
        device = torch.device('cuda' if args.gpu and torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {device}")
        
        # Define dataset
        class CaptchaDataset(Dataset):
            def __init__(self, data_dir, char_set, transform=None):
                self.data_dir = Path(data_dir)
                self.char_set = char_set
                self.transform = transform
                self.samples = list(self.data_dir.glob('*.png')) + list(self.data_dir.glob('*.jpg'))
                
            def __len__(self):
                return len(self.samples)
            
            def __getitem__(self, idx):
                img_path = self.samples[idx]
                # Extract label from filename (assuming filename format: label_*.png)
                label = img_path.stem.split('_')[0]
                
                # Convert label to tensor
                label_tensor = torch.tensor([self.char_set.find(c) for c in label])
                
                # Load and transform image
                img = Image.open(img_path).convert('RGB')
                if self.transform:
                    img = self.transform(img)
                
                return img, label_tensor, str(img_path)
        
        # Define transforms
        transform = transforms.Compose([
            transforms.Resize((args.image_height, args.image_width)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        # Create dataset and data loader
        dataset = CaptchaDataset(args.data_dir, args.char_set, transform=transform)
        data_loader = DataLoader(dataset, batch_size=args.batch_size)
        
        # Create model
        model = CaptchaResNetModel(char_len=args.captcha_length, class_num=len(args.char_set))
        model.load_state_dict(torch.load(args.model_path, map_location=device))
        model = model.to(device)
        model.eval()
        
        # Evaluation
        correct = 0
        total = 0
        results = []
        start_time = time.time()
        
        with torch.no_grad():
            for images, labels, img_paths in data_loader:
                images, labels = images.to(device), labels.to(device)
                
                outputs = model(images)
                
                # Get predictions
                _, predicted = torch.max(outputs, 2)
                
                # Calculate accuracy
                batch_correct = (predicted == labels).all(dim=1)
                correct += batch_correct.sum().item()
                total += labels.size(0)
                
                # Save results
                for i in range(len(images)):
                    label = ''.join([args.char_set[idx] for idx in labels[i].cpu().numpy()])
                    pred = ''.join([args.char_set[idx] for idx in predicted[i].cpu().numpy()])
                    is_correct = batch_correct[i].item()
                    
                    results.append({
                        'image_path': img_paths[i],
                        'label': label,
                        'prediction': pred,
                        'correct': is_correct
                    })
        
        elapsed_time = time.time() - start_time
        
        # Calculate metrics
        accuracy = correct / total if total > 0 else 0
        avg_time_per_image = elapsed_time / total if total > 0 else 0
        
        # Print results
        logger.info(f"Accuracy: {accuracy:.4f} ({correct}/{total})")
        logger.info(f"Average time per image: {avg_time_per_image:.4f} seconds")
        
        # Save results if requested
        if args.output_file:
            import json
            with open(args.output_file, 'w') as f:
                json.dump({
                    'accuracy': accuracy,
                    'correct': correct,
                    'total': total,
                    'avg_time_per_image': avg_time_per_image,
                    'results': results
                }, f, indent=2)
            logger.info(f"Results saved to {args.output_file}")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'avg_time_per_image': avg_time_per_image,
            'results': results
        }
    
    except ImportError as e:
        logger.error(f"Error importing PyTorch dependencies: {e}")
        logger.error("Please install PyTorch: pip install torch torchvision")
        return None

def evaluate_tensorflow_model(args):
    """
    Evaluate a TensorFlow-based CAPTCHA solver.
    
    Args:
        args: Command line arguments
        
    Returns:
        Dictionary with evaluation results
    """
    try:
        import tensorflow as tf
        import numpy as np
        from PIL import Image
        import glob
        
        logger.info("Evaluating TensorFlow model...")
        
        # Check if GPU is available
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if args.gpu and gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                logger.info(f"Using GPU: {gpus}")
            except RuntimeError as e:
                logger.error(f"Error setting up GPU: {e}")
        else:
            logger.info("Using CPU")
        
        # Load model
        model = tf.keras.models.load_model(args.model_path)
        
        # Get image files
        image_files = glob.glob(os.path.join(args.data_dir, '*.png')) + glob.glob(os.path.join(args.data_dir, '*.jpg'))
        
        # Evaluation
        correct = 0
        total = 0
        results = []
        start_time = time.time()
        
        for img_path in image_files:
            # Extract label from filename (assuming filename format: label_*.png)
            label = os.path.basename(img_path).split('_')[0]
            
            # Skip if label length doesn't match
            if len(label) != args.captcha_length:
                continue
            
            # Load and preprocess image
            img = Image.open(img_path).convert('L')  # Convert to grayscale
            img = img.resize((args.image_width, args.image_height))
            img_array = np.array(img, dtype=np.float32) / 255.0 - 0.5  # Normalize to [-0.5, 0.5]
            img_array = img_array.flatten()
            
            # Make prediction
            pred = model.predict(np.array([img_array]), verbose=0)
            pred = np.argmax(pred, axis=2)[0]
            
            # Convert prediction to string
            pred_str = ''.join([args.char_set[idx] for idx in pred])
            
            # Check if prediction is correct
            is_correct = (pred_str == label)
            if is_correct:
                correct += 1
            total += 1
            
            # Save result
            results.append({
                'image_path': img_path,
                'label': label,
                'prediction': pred_str,
                'correct': is_correct
            })
        
        elapsed_time = time.time() - start_time
        
        # Calculate metrics
        accuracy = correct / total if total > 0 else 0
        avg_time_per_image = elapsed_time / total if total > 0 else 0
        
        # Print results
        logger.info(f"Accuracy: {accuracy:.4f} ({correct}/{total})")
        logger.info(f"Average time per image: {avg_time_per_image:.4f} seconds")
        
        # Save results if requested
        if args.output_file:
            import json
            with open(args.output_file, 'w') as f:
                json.dump({
                    'accuracy': accuracy,
                    'correct': correct,
                    'total': total,
                    'avg_time_per_image': avg_time_per_image,
                    'results': results
                }, f, indent=2)
            logger.info(f"Results saved to {args.output_file}")
        
        return {
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'avg_time_per_image': avg_time_per_image,
            'results': results
        }
    
    except ImportError as e:
        logger.error(f"Error importing TensorFlow dependencies: {e}")
        logger.error("Please install TensorFlow: pip install tensorflow")
        return None

def main():
    """Main function."""
    args = parse_args()
    
    # Check if model exists
    if not os.path.exists(args.model_path):
        logger.error(f"Model not found: {args.model_path}")
        return 1
    
    # Check if data directory exists
    if not os.path.exists(args.data_dir):
        logger.error(f"Data directory not found: {args.data_dir}")
        return 1
    
    # Evaluate model
    if args.model_type == 'pytorch':
        results = evaluate_pytorch_model(args)
    else:
        results = evaluate_tensorflow_model(args)
    
    if results:
        logger.info("Evaluation completed successfully.")
        return 0
    else:
        logger.error("Evaluation failed.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
