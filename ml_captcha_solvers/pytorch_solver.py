#!/usr/bin/env python3
"""
PyTorch-based captcha solver adapted from PyCAPTCHA.

This module provides a PyTorch-based captcha solver that can be used
for local captcha solving without external services.
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple, Union

from .preprocessing import preprocess_for_pytorch

# Configure logging
logger = logging.getLogger(__name__)

# Constants
CHAR_LEN = 4  # Default captcha length
CLASS_NUM = 62  # 10 digits + 26 lowercase + 26 uppercase
HEIGHT = 60
WIDTH = 160

# Character set
CHAR_SET = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

def vec_to_str(vec: torch.Tensor) -> str:
    """
    Convert a vector of indices to a string.
    
    Args:
        vec: Vector of character indices
        
    Returns:
        String representation
    """
    return ''.join([CHAR_SET[i] for i in vec])

def lst_to_str(lst: torch.Tensor) -> str:
    """
    Convert a list of indices to a string.
    
    Args:
        lst: List of character indices
        
    Returns:
        String representation
    """
    return ''.join([CHAR_SET[i] for i in lst.cpu().numpy()])

def str_to_vec(s: str) -> torch.Tensor:
    """
    Convert a string to a vector of indices.
    
    Args:
        s: Input string
        
    Returns:
        Vector of character indices
    """
    return torch.tensor([CHAR_SET.find(c) for c in s])

class CaptchaResNetModel(nn.Module):
    """ResNet-based model for captcha recognition."""
    
    def __init__(self, char_len: int = CHAR_LEN, class_num: int = CLASS_NUM):
        """
        Initialize the model.
        
        Args:
            char_len: Length of captcha
            class_num: Number of character classes
        """
        super(CaptchaResNetModel, self).__init__()
        self.char_len = char_len
        self.class_num = class_num
        
        # Use ResNet18 as base model
        self.resnet = models.resnet18(weights=None)
        
        # Modify the final fully connected layer
        self.resnet.fc = nn.Linear(512, char_len * class_num)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.
        
        Args:
            x: Input tensor
            
        Returns:
            Output tensor
        """
        x = self.resnet(x)
        x = x.view(x.size(0), self.char_len, self.class_num)
        return x

class PyTorchCaptchaSolver:
    """PyTorch-based captcha solver."""
    
    def __init__(self, model_path: str = None, char_len: int = CHAR_LEN, class_num: int = CLASS_NUM, device: str = None):
        """
        Initialize the solver.
        
        Args:
            model_path: Path to the model checkpoint
            char_len: Length of captcha
            class_num: Number of character classes
            device: Device to use ('cuda' or 'cpu')
        """
        self.char_len = char_len
        self.class_num = class_num
        
        # Determine device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        logger.info(f"Using device: {self.device}")
        
        # Create model
        self.model = CaptchaResNetModel(char_len, class_num)
        
        # Load model if path is provided
        if model_path and os.path.exists(model_path):
            self._load_model(model_path)
        else:
            logger.warning(f"Model path not provided or does not exist: {model_path}")
        
        # Set up transforms
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    
    def _load_model(self, model_path: str) -> None:
        """
        Load model from checkpoint.
        
        Args:
            model_path: Path to the model checkpoint
        """
        try:
            # Check if the model is a PyTorch Lightning checkpoint
            if 'ckpt' in model_path:
                # Try to load as a PyTorch Lightning checkpoint
                try:
                    import pytorch_lightning as pl
                    self.model = pl.LightningModule.load_from_checkpoint(
                        model_path, 
                        model=CaptchaResNetModel(self.char_len, self.class_num)
                    )
                except (ImportError, Exception) as e:
                    logger.error(f"Error loading PyTorch Lightning model: {e}")
                    # Fall back to loading state dict directly
                    state_dict = torch.load(model_path, map_location=self.device)
                    if 'state_dict' in state_dict:
                        state_dict = state_dict['state_dict']
                    self.model.load_state_dict(state_dict)
            else:
                # Load as a regular PyTorch model
                state_dict = torch.load(model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
            
            logger.info(f"Model loaded from {model_path}")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def predict(self, image_path: str) -> str:
        """
        Predict captcha from image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Predicted captcha text
        """
        try:
            # Set model to evaluation mode
            self.model.eval()
            
            # Load and preprocess image
            img = Image.open(image_path)
            img = self.transform(img)
            img = img.unsqueeze(0).to(self.device)
            
            # Make prediction
            with torch.no_grad():
                output = self.model(img)
                output = output.permute(1, 0, 2)
                pred = output.argmax(dim=2)
                
                # Convert to string
                result = lst_to_str(pred)
                
                return result
        except Exception as e:
            logger.error(f"Error predicting captcha: {e}")
            return None
    
    def predict_from_array(self, image_array: np.ndarray) -> str:
        """
        Predict captcha from numpy array.
        
        Args:
            image_array: Image as numpy array
            
        Returns:
            Predicted captcha text
        """
        try:
            # Set model to evaluation mode
            self.model.eval()
            
            # Convert to tensor
            img = torch.from_numpy(image_array).float()
            if img.dim() == 3:
                img = img.unsqueeze(0)
            img = img.to(self.device)
            
            # Make prediction
            with torch.no_grad():
                output = self.model(img)
                output = output.permute(1, 0, 2)
                pred = output.argmax(dim=2)
                
                # Convert to string
                result = lst_to_str(pred)
                
                return result
        except Exception as e:
            logger.error(f"Error predicting captcha from array: {e}")
            return None
