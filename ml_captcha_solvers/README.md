# ML-based CAPTCHA Solvers

This package provides machine learning-based CAPTCHA solvers that can be used for local CAPTCHA solving without relying on external services. It integrates with the main `captcha_solver.py` module to provide a unified interface for solving CAPTCHAs.

## Features

- **PyTorch-based solver**: Uses a ResNet-based model for CAPTCHA recognition
- **TensorFlow-based solver**: Uses a CNN-based model for CAPTCHA recognition
- **Preprocessing utilities**: Image preprocessing for different CAPTCHA types
- **Unified interface**: Integrates with the main `captcha_solver.py` module

## Requirements

- Python 3.6+
- PyTorch (optional, for PyTorch-based solver)
- TensorFlow (optional, for TensorFlow-based solver)
- OpenCV
- NumPy
- PIL (Pillow)

## Installation

The package is included with the trend-crawler project. To use it, make sure you have the required dependencies installed:

```bash
# For PyTorch-based solver
pip install torch torchvision

# For TensorFlow-based solver
pip install tensorflow

# Common dependencies
pip install opencv-python numpy pillow
```

## Usage

### Basic Usage

```python
from captcha_solver import CaptchaSolver

# Initialize solver with ML-based solvers
solver = CaptchaSolver(use_local_ml=True)

# Solve image CAPTCHA
solution = solver.solve_image_captcha(image_path="path/to/captcha.png")
print(f"Solution: {solution}")
```

### Specifying Model Path

```python
from captcha_solver import CaptchaSolver

# Initialize solver with specific model path
solver = CaptchaSolver(
    ml_model_path="path/to/model.pth",  # or .ckpt for PyTorch, or directory for TensorFlow
    use_local_ml=True
)

# Solve image CAPTCHA
solution = solver.solve_image_captcha(image_path="path/to/captcha.png")
print(f"Solution: {solution}")
```

### Specifying Solver Type

```python
from captcha_solver import CaptchaSolver

# Initialize with specific solver type
solver = CaptchaSolver(service="pytorch")  # or "tensorflow"

# Solve image CAPTCHA
solution = solver.solve_image_captcha(image_path="path/to/captcha.png")
print(f"Solution: {solution}")
```

### Fallback to External Services

```python
from captcha_solver import CaptchaSolver

# Initialize with ML-based solvers and fallback to external service
solver = CaptchaSolver(
    api_key="your-api-key",
    service="auto",  # Try all available solvers
    use_local_ml=True
)

# Solve image CAPTCHA
solution = solver.solve_image_captcha(image_path="path/to/captcha.png")
print(f"Solution: {solution}")
```

## Testing

You can test the ML-based solvers using the provided test script:

```bash
python test_ml_captcha_solver.py --image path/to/captcha.png --solver auto
```

To download sample CAPTCHA images for testing:

```bash
python test_ml_captcha_solver.py --download-samples --solver auto
```

## Models

### PyTorch Model

The PyTorch-based solver uses a ResNet-18 model adapted for CAPTCHA recognition. The model is trained to recognize alphanumeric characters (0-9, a-z, A-Z) in CAPTCHA images.

### TensorFlow Model

The TensorFlow-based solver uses a CNN model with multiple convolutional and pooling layers, followed by fully connected layers. The model is trained to recognize numeric characters (0-9) in CAPTCHA images.

## Training Your Own Models

To train your own models, you can use the code from the original repositories:

- PyTorch: [PyCAPTCHA - captcha solver](https://github.com/example/pycaptcha)
- TensorFlow: [tensorflow-captcha-solver](https://github.com/example/tensorflow-captcha-solver)

## Integration with External Services

The ML-based solvers are integrated with the main `captcha_solver.py` module, which also supports external CAPTCHA solving services like 2Captcha, AntiCaptcha, and CapSolver. The module will try to use the ML-based solvers first (if available and enabled) before falling back to external services.

## License

This package is part of the trend-crawler project and is subject to the same license terms.
