#!/usr/bin/env python3
"""
Training script for ML-based CAPTCHA solvers.

This script provides functionality to train ML-based CAPTCHA solvers
using PyTorch or TensorFlow.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train ML-based CAPTCHA solvers')
    parser.add_argument('--data-dir', type=str, required=True, 
                        help='Directory containing training data')
    parser.add_argument('--output-dir', type=str, default='models',
                        help='Directory to save trained models')
    parser.add_argument('--model-type', type=str, choices=['pytorch', 'tensorflow'], 
                        default='pytorch', help='Type of model to train')
    parser.add_argument('--epochs', type=int, default=10,
                        help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate for training')
    parser.add_argument('--char-set', type=str, default='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                        help='Character set for CAPTCHA')
    parser.add_argument('--captcha-length', type=int, default=4,
                        help='Length of CAPTCHA')
    parser.add_argument('--image-width', type=int, default=160,
                        help='Width of CAPTCHA image')
    parser.add_argument('--image-height', type=int, default=60,
                        help='Height of CAPTCHA image')
    parser.add_argument('--validation-split', type=float, default=0.2,
                        help='Fraction of data to use for validation')
    parser.add_argument('--resume', type=str, help='Path to model to resume training from')
    parser.add_argument('--gpu', action='store_true', help='Use GPU for training')
    return parser.parse_args()

def train_pytorch_model(args):
    """
    Train a PyTorch-based CAPTCHA solver.
    
    Args:
        args: Command line arguments
    """
    try:
        import torch
        import torch.nn as nn
        import torch.optim as optim
        from torch.utils.data import DataLoader, Dataset, random_split
        import torchvision.transforms as transforms
        from PIL import Image
        import numpy as np
        
        from .pytorch_solver import CaptchaResNetModel
        
        logger.info("Training PyTorch model...")
        
        # Check if GPU is available
        device = torch.device('cuda' if args.gpu and torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {device}")
        
        # Define dataset
        class CaptchaDataset(Dataset):
            def __init__(self, data_dir, char_set, transform=None):
                self.data_dir = Path(data_dir)
                self.char_set = char_set
                self.transform = transform
                self.samples = list(self.data_dir.glob('*.png')) + list(self.data_dir.glob('*.jpg'))
                
            def __len__(self):
                return len(self.samples)
            
            def __getitem__(self, idx):
                img_path = self.samples[idx]
                # Extract label from filename (assuming filename format: label_*.png)
                label = img_path.stem.split('_')[0]
                
                # Convert label to tensor
                label_tensor = torch.tensor([self.char_set.find(c) for c in label])
                
                # Load and transform image
                img = Image.open(img_path).convert('RGB')
                if self.transform:
                    img = self.transform(img)
                
                return img, label_tensor
        
        # Define transforms
        transform = transforms.Compose([
            transforms.Resize((args.image_height, args.image_width)),
            transforms.ToTensor(),
            transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
        
        # Create dataset and data loaders
        dataset = CaptchaDataset(args.data_dir, args.char_set, transform=transform)
        
        # Split dataset
        val_size = int(args.validation_split * len(dataset))
        train_size = len(dataset) - val_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=args.batch_size)
        
        # Create model
        model = CaptchaResNetModel(char_len=args.captcha_length, class_num=len(args.char_set))
        model = model.to(device)
        
        # Resume training if requested
        if args.resume:
            logger.info(f"Resuming from {args.resume}")
            model.load_state_dict(torch.load(args.resume, map_location=device))
        
        # Define loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
        
        # Training loop
        for epoch in range(args.epochs):
            model.train()
            train_loss = 0.0
            correct = 0
            total = 0
            
            for images, labels in train_loader:
                images, labels = images.to(device), labels.to(device)
                
                optimizer.zero_grad()
                outputs = model(images)
                
                # Reshape outputs for loss calculation
                outputs = outputs.permute(0, 2, 1)  # (batch, class, char_len)
                
                # Calculate loss
                loss = 0
                for i in range(args.captcha_length):
                    loss += criterion(outputs[:, :, i], labels[:, i])
                
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                # Calculate accuracy
                _, predicted = torch.max(outputs.permute(0, 2, 1), 2)
                correct += (predicted == labels).all(dim=1).sum().item()
                total += labels.size(0)
            
            # Validation
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0
            
            with torch.no_grad():
                for images, labels in val_loader:
                    images, labels = images.to(device), labels.to(device)
                    
                    outputs = model(images)
                    outputs = outputs.permute(0, 2, 1)
                    
                    # Calculate loss
                    loss = 0
                    for i in range(args.captcha_length):
                        loss += criterion(outputs[:, :, i], labels[:, i])
                    
                    val_loss += loss.item()
                    
                    # Calculate accuracy
                    _, predicted = torch.max(outputs.permute(0, 2, 1), 2)
                    val_correct += (predicted == labels).all(dim=1).sum().item()
                    val_total += labels.size(0)
            
            # Print statistics
            logger.info(f"Epoch {epoch+1}/{args.epochs}, "
                        f"Train Loss: {train_loss/len(train_loader):.4f}, "
                        f"Train Acc: {100*correct/total:.2f}%, "
                        f"Val Loss: {val_loss/len(val_loader):.4f}, "
                        f"Val Acc: {100*val_correct/val_total:.2f}%")
        
        # Save model
        os.makedirs(args.output_dir, exist_ok=True)
        output_path = os.path.join(args.output_dir, f"pytorch_captcha_model.pth")
        torch.save(model.state_dict(), output_path)
        logger.info(f"Model saved to {output_path}")
        
        return output_path
    
    except ImportError as e:
        logger.error(f"Error importing PyTorch dependencies: {e}")
        logger.error("Please install PyTorch: pip install torch torchvision")
        return None

def train_tensorflow_model(args):
    """
    Train a TensorFlow-based CAPTCHA solver.
    
    Args:
        args: Command line arguments
    """
    try:
        import tensorflow as tf
        import numpy as np
        from PIL import Image
        import glob
        
        logger.info("Training TensorFlow model...")
        
        # Check if GPU is available
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if args.gpu and gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                logger.info(f"Using GPU: {gpus}")
            except RuntimeError as e:
                logger.error(f"Error setting up GPU: {e}")
        else:
            logger.info("Using CPU")
        
        # Define data loading function
        def load_data(data_dir, char_set, image_width, image_height, captcha_length):
            image_files = glob.glob(os.path.join(data_dir, '*.png')) + glob.glob(os.path.join(data_dir, '*.jpg'))
            X = []
            y = []
            
            for img_path in image_files:
                # Extract label from filename (assuming filename format: label_*.png)
                label = os.path.basename(img_path).split('_')[0]
                
                # Skip if label length doesn't match
                if len(label) != captcha_length:
                    continue
                
                # Load and preprocess image
                img = Image.open(img_path).convert('L')  # Convert to grayscale
                img = img.resize((image_width, image_height))
                img_array = np.array(img, dtype=np.float32) / 255.0 - 0.5  # Normalize to [-0.5, 0.5]
                X.append(img_array.flatten())
                
                # Convert label to one-hot encoding
                label_array = np.zeros((captcha_length, len(char_set)))
                for i, char in enumerate(label):
                    label_array[i, char_set.find(char)] = 1
                y.append(label_array)
            
            return np.array(X), np.array(y)
        
        # Load data
        X, y = load_data(args.data_dir, args.char_set, args.image_width, args.image_height, args.captcha_length)
        
        # Split data
        indices = np.random.permutation(len(X))
        val_size = int(args.validation_split * len(X))
        train_indices = indices[val_size:]
        val_indices = indices[:val_size]
        
        X_train, y_train = X[train_indices], y[train_indices]
        X_val, y_val = X[val_indices], y[val_indices]
        
        # Define model
        model = tf.keras.Sequential([
            tf.keras.layers.Reshape((args.image_height, args.image_width, 1), 
                                    input_shape=(args.image_height * args.image_width,)),
            tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
            tf.keras.layers.MaxPooling2D((2, 2)),
            tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            tf.keras.layers.MaxPooling2D((2, 2)),
            tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            tf.keras.layers.MaxPooling2D((2, 2)),
            tf.keras.layers.Flatten(),
            tf.keras.layers.Dense(1024, activation='relu'),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(args.captcha_length * len(args.char_set)),
            tf.keras.layers.Reshape((args.captcha_length, len(args.char_set)))
        ])
        
        # Resume training if requested
        if args.resume:
            logger.info(f"Resuming from {args.resume}")
            model.load_weights(args.resume)
        
        # Compile model
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=args.learning_rate),
            loss=tf.keras.losses.CategoricalCrossentropy(from_logits=True),
            metrics=['accuracy']
        )
        
        # Train model
        history = model.fit(
            X_train, y_train,
            epochs=args.epochs,
            batch_size=args.batch_size,
            validation_data=(X_val, y_val),
            callbacks=[
                tf.keras.callbacks.EarlyStopping(patience=5, restore_best_weights=True)
            ]
        )
        
        # Save model
        os.makedirs(args.output_dir, exist_ok=True)
        output_path = os.path.join(args.output_dir, "tensorflow_captcha_model")
        model.save(output_path)
        logger.info(f"Model saved to {output_path}")
        
        return output_path
    
    except ImportError as e:
        logger.error(f"Error importing TensorFlow dependencies: {e}")
        logger.error("Please install TensorFlow: pip install tensorflow")
        return None

def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Train model
    if args.model_type == 'pytorch':
        output_path = train_pytorch_model(args)
    else:
        output_path = train_tensorflow_model(args)
    
    if output_path:
        logger.info(f"Training completed successfully. Model saved to {output_path}")
        return 0
    else:
        logger.error("Training failed.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
