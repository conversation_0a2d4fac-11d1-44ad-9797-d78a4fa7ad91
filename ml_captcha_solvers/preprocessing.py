#!/usr/bin/env python3
"""
Image preprocessing utilities for captcha solving.

This module provides functions to preprocess captcha images for different
ML-based solvers.
"""

import os
import cv2
import numpy as np
from PIL import Image
import logging
from typing import Tuple, Optional, Union, List

# Configure logging
logger = logging.getLogger(__name__)

def resize_image(image: Union[np.ndarray, Image.Image], width: int, height: int) -> np.ndarray:
    """
    Resize an image to the specified dimensions.
    
    Args:
        image: Input image (numpy array or PIL Image)
        width: Target width
        height: Target height
        
    Returns:
        Resized image as numpy array
    """
    if isinstance(image, Image.Image):
        return np.array(image.resize((width, height)))
    else:
        return cv2.resize(image, (width, height))

def convert_to_grayscale(image: Union[np.ndarray, Image.Image]) -> np.ndarray:
    """
    Convert an image to grayscale.
    
    Args:
        image: Input image (numpy array or PIL Image)
        
    Returns:
        Grayscale image as numpy array
    """
    if isinstance(image, Image.Image):
        return np.array(image.convert('L'))
    else:
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        return image

def normalize_image(image: np.ndarray, mean: float = 0.5, std: float = 0.5) -> np.ndarray:
    """
    Normalize image pixel values.
    
    Args:
        image: Input image as numpy array
        mean: Mean for normalization
        std: Standard deviation for normalization
        
    Returns:
        Normalized image
    """
    if image.dtype != np.float32:
        image = image.astype(np.float32)
    
    # Normalize to [0, 1]
    if image.max() > 1.0:
        image = image / 255.0
    
    # Normalize with mean and std
    image = (image - mean) / std
    
    return image

def threshold_image(image: np.ndarray, threshold: int = 127) -> np.ndarray:
    """
    Apply binary thresholding to an image.
    
    Args:
        image: Input grayscale image
        threshold: Threshold value
        
    Returns:
        Binary image
    """
    if len(image.shape) > 2:
        image = convert_to_grayscale(image)
    
    _, binary = cv2.threshold(image, threshold, 255, cv2.THRESH_BINARY)
    return binary

def denoise_image(image: np.ndarray) -> np.ndarray:
    """
    Remove noise from an image.
    
    Args:
        image: Input image
        
    Returns:
        Denoised image
    """
    if len(image.shape) > 2:
        # Color image
        return cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
    else:
        # Grayscale image
        return cv2.fastNlMeansDenoising(image, None, 10, 7, 21)

def preprocess_for_tensorflow(image_path: str, width: int = 150, height: int = 50) -> np.ndarray:
    """
    Preprocess an image for the TensorFlow captcha solver.
    
    Args:
        image_path: Path to the image file
        width: Target width
        height: Target height
        
    Returns:
        Preprocessed image as numpy array
    """
    try:
        # Load image
        image = Image.open(image_path)
        
        # Convert to grayscale
        image_gray = image.convert('L')
        
        # Resize
        image_resize = image_gray.resize((width, height))
        
        # Convert to numpy array
        input_img = np.array(image_resize, dtype=np.float32)
        
        # Normalize and flatten
        input_img = np.multiply(input_img.flatten(), 1./255) - 0.5
        
        return input_img
    except Exception as e:
        logger.error(f"Error preprocessing image for TensorFlow: {e}")
        return None

def preprocess_for_pytorch(image_path: str, width: int = 160, height: int = 60) -> np.ndarray:
    """
    Preprocess an image for the PyTorch captcha solver.
    
    Args:
        image_path: Path to the image file
        width: Target width
        height: Target height
        
    Returns:
        Preprocessed image as numpy array
    """
    try:
        # Load image
        image = Image.open(image_path)
        
        # Resize
        image = image.resize((width, height))
        
        # Convert to numpy array
        image_np = np.array(image)
        
        # Normalize
        image_np = image_np.astype(np.float32) / 255.0
        
        # Transpose to (channels, height, width) for PyTorch
        if len(image_np.shape) == 3:
            image_np = image_np.transpose((2, 0, 1))
        else:
            # If grayscale, add channel dimension
            image_np = np.expand_dims(image_np, axis=0)
        
        # Normalize with ImageNet mean and std
        mean = np.array([0.485, 0.456, 0.406]).reshape(-1, 1, 1)
        std = np.array([0.229, 0.224, 0.225]).reshape(-1, 1, 1)
        
        if image_np.shape[0] == 3:
            image_np = (image_np - mean) / std
        else:
            # For grayscale, use the first channel's mean and std
            image_np = (image_np - mean[0]) / std[0]
        
        return image_np
    except Exception as e:
        logger.error(f"Error preprocessing image for PyTorch: {e}")
        return None

def preprocess_image_data(image_data: bytes, model_type: str = 'tensorflow') -> np.ndarray:
    """
    Preprocess image data for the specified model type.
    
    Args:
        image_data: Raw image data
        model_type: Type of model ('tensorflow' or 'pytorch')
        
    Returns:
        Preprocessed image as numpy array
    """
    try:
        # Create a temporary file
        temp_path = "temp_captcha_preprocess.png"
        with open(temp_path, "wb") as f:
            f.write(image_data)
        
        # Preprocess based on model type
        if model_type.lower() == 'tensorflow':
            result = preprocess_for_tensorflow(temp_path)
        elif model_type.lower() == 'pytorch':
            result = preprocess_for_pytorch(temp_path)
        else:
            logger.error(f"Unknown model type: {model_type}")
            result = None
        
        # Clean up
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
        return result
    except Exception as e:
        logger.error(f"Error preprocessing image data: {e}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return None
