#!/usr/bin/env python3
"""
ML-based captcha solvers package.

This package provides machine learning-based captcha solvers that can be used
for local captcha solving without external services.
"""

import logging
from typing import Dict, Any, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)

# Import solvers if dependencies are available
try:
    from .pytorch_solver import PyTorchCaptchaSolver
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    logger.warning("PyTorch solver not available. Install PyTorch to use it.")

# TensorFlow solver import hierarchy: Production -> Enhanced -> Ultra-lite
try:
    # First choice: Production-optimized solver with human behavior simulation
    from .tensorflow_solver_production import (
        ProductionTensorFlowSolver as TensorFlowCaptchaSolver,
        ProductionTensorFlowSolver,
        HumanBehaviorSimulator,
        ProductionResourceManager
    )
    # For backward compatibility
    EnhancedTensorFlowCaptchaSolver = ProductionTensorFlowSolver
    TENSORFLOW_AVAILABLE = True
    TENSORFLOW_MODE = 'production'
    logger.info(
        "TensorFlow solver available (production version with human behavior)"
    )
except ImportError:
    try:
        # Second choice: Enhanced TensorFlow solver
        from .tensorflow_solver_enhanced import (
            EnhancedTensorFlowCaptchaSolver as TensorFlowCaptchaSolver,
            EnhancedTensorFlowCaptchaSolver
        )
        ProductionTensorFlowSolver = None
        HumanBehaviorSimulator = None
        ProductionResourceManager = None
        TENSORFLOW_AVAILABLE = True
        TENSORFLOW_MODE = 'enhanced'
        logger.info("TensorFlow solver available (enhanced version)")
    except ImportError:
        try:
            # Fallback: Ultra-lite solver
            from .tensorflow_solver_ultra_lite import (
                TensorFlowCaptchaSolverUltraLite as TensorFlowCaptchaSolver
            )
            # For backward compatibility
            EnhancedTensorFlowCaptchaSolver = TensorFlowCaptchaSolver
            ProductionTensorFlowSolver = None
            HumanBehaviorSimulator = None
            ProductionResourceManager = None
            TENSORFLOW_AVAILABLE = True
            TENSORFLOW_MODE = 'ultra_lite'
            logger.info("TensorFlow solver available (ultra-lite fallback)")
        except ImportError:
            TENSORFLOW_AVAILABLE = False
            TENSORFLOW_MODE = None
            EnhancedTensorFlowCaptchaSolver = None
            ProductionTensorFlowSolver = None
            HumanBehaviorSimulator = None
            ProductionResourceManager = None
            logger.warning(
                "TensorFlow solver not available. Install TensorFlow to use it."
            )

# Preprocessing utilities are available via dynamic import
# to avoid dependency issues during package initialization

__all__ = [
    'PyTorchCaptchaSolver',
    'TensorFlowCaptchaSolver',
    'EnhancedTensorFlowCaptchaSolver',
    'ProductionTensorFlowSolver',
    'HumanBehaviorSimulator', 
    'ProductionResourceManager',
    'PYTORCH_AVAILABLE',
    'TENSORFLOW_AVAILABLE',
    'TENSORFLOW_MODE',
    'get_best_solver',
    'get_solver_info',
    'create_solver'
]


def get_best_solver() -> Optional[type]:
    """
    Get the best available solver for production use.
    
    Returns the most capable solver available, preferring production features.
    """
    if TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production':
        return ProductionTensorFlowSolver
    elif TENSORFLOW_AVAILABLE:
        return TensorFlowCaptchaSolver
    elif PYTORCH_AVAILABLE:
        return PyTorchCaptchaSolver
    else:
        return None


def get_solver_info() -> Dict[str, Any]:
    """Get information about available solvers and their capabilities."""
    return {
        'tensorflow': {
            'available': TENSORFLOW_AVAILABLE,
            'mode': TENSORFLOW_MODE,
            'features': {
                'production_optimized': TENSORFLOW_MODE == 'production',
                'human_behavior_sim': TENSORFLOW_MODE == 'production',
                'gpu_acceleration': TENSORFLOW_AVAILABLE,
                'cpu_optimization': TENSORFLOW_MODE in ['production', 'enhanced'],
                'resource_management': TENSORFLOW_MODE == 'production'
            }
        },
        'pytorch': {
            'available': PYTORCH_AVAILABLE,
            'features': {
                'gpu_acceleration': PYTORCH_AVAILABLE,
                'dynamic_models': PYTORCH_AVAILABLE
            }
        },
        'recommended_solver': (
            get_best_solver().__name__ if get_best_solver() else None
        )
    }


def create_solver(**kwargs) -> Optional[Any]:
    """Create the best available solver with given parameters."""
    solver_class = get_best_solver()
    if solver_class:
        return solver_class(**kwargs)
    else:
        raise ImportError(
            "No ML captcha solvers available. Install TensorFlow or PyTorch."
        )
