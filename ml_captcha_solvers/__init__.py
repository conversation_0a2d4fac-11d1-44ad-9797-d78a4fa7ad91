#!/usr/bin/env python3
"""
ML-based captcha solvers package.

This package provides machine learning-based captcha solvers that can be used
for local captcha solving without external services.
"""

import logging
from typing import Dict, Any, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)

# Import solvers if dependencies are available
try:
    from .pytorch_solver import PyTorchCaptchaSolver
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    logger.warning("PyTorch solver not available. Install PyTorch to use it.")

try:
    from .tensorflow_solver import TensorFlowCaptchaSolver
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow solver not available. Install TensorFlow to use it.")

# Import preprocessing utilities
from .preprocessing import (
    resize_image,
    convert_to_grayscale,
    normalize_image,
    threshold_image,
    denoise_image,
    preprocess_for_tensorflow,
    preprocess_for_pytorch,
    preprocess_image_data
)

__all__ = [
    'PyTorchCaptchaSolver',
    'TensorFlowCaptchaSolver',
    'resize_image',
    'convert_to_grayscale',
    'normalize_image',
    'threshold_image',
    'denoise_image',
    'preprocess_for_tensorflow',
    'preprocess_for_pytorch',
    'preprocess_image_data',
    'PYTORCH_AVAILABLE',
    'TENSORFLOW_AVAILABLE'
]
