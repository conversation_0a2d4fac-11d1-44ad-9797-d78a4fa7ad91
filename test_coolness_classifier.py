#!/usr/bin/env python3
"""
Tests for coolness classifier.
"""
import argparse
import logging
import sys
from typing import List, Dict, Any
import pytest
import pytest_asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import dependencies
try:
    from coolness_classifier import CoolnessClassifier, COOLNESS_LEVELS
    CLASSIFIER_AVAILABLE = True
except ImportError:
    CLASSIFIER_AVAILABLE = False
    logger.warning("CoolnessClassifier not available. Install required dependencies.")


@pytest.mark.asyncio
async def test_classifier(sample_texts: List[str], mock_metrics: Dict[str, int],
                         mock_twitter_data: Dict[str, Any]):
    """Test coolness classifier functionality."""
    classifier = CoolnessClassifier(use_bert=True)

    # Test sentiment analysis
    for text in sample_texts:
        sentiment_score = classifier.analyze_sentiment_bert(text)
        assert -1 <= sentiment_score <= 1, "Sentiment score should be between -1 and 1"

    # Test engagement score calculation
    engagement_score = classifier.calculate_engagement_score(mock_metrics)
    assert 0 <= engagement_score <= 1, "Engagement score should be between 0 and 1"

    # Test overall coolness score
    coolness_score = classifier.calculate_coolness_score(sample_texts[0], mock_metrics)
    assert 0 <= coolness_score <= 1, "Coolness score should be between 0 and 1"

@pytest.mark.asyncio
async def test_feedback_training():
    """Test feedback-based training functionality."""
    classifier = CoolnessClassifier(use_bert=True)
    test_text = "Revolutionary new AI technology transforms data analysis"
    
    # Initial score
    initial_score = classifier.calculate_coolness_score(test_text)
    assert 0 <= initial_score <= 1, "Initial score should be between 0 and 1"


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the BERT-based coolness classification system")
    parser.add_argument("--texts", nargs="+", help="Texts to classify")
    parser.add_argument("--verbose", action="store_true", help="Print verbose output")
    parser.add_argument("--test-feedback", action="store_true", help="Test feedback training")
    
    args = parser.parse_args()
    
    if args.test_feedback:
        test_feedback_training()
    elif args.texts:
        test_classifier(args.texts, args.verbose)
    else:
        # Default test texts
        default_texts = [
            "Revolutionary new AI technology that will change the world with unprecedented capabilities.",
            "Breaking news about current events that might be interesting to some people.",
            "Mundane article about everyday topics with no excitement or novelty."
        ]
        test_classifier(default_texts, args.verbose)


if __name__ == "__main__":
    main()
