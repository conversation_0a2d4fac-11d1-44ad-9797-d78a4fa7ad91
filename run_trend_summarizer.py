#!/usr/bin/env python3
"""
Initialize and run the trend summarization system.
This script sets up the integration between PostgreSQL/pgvector and the NanoLLM
to implement the innovative applications described in the QA LLM requirements.
"""

import os
import logging
import numpy as np
import torch
import argparse
import json
from datetime import datetime

# Import our custom modules
from nano_neural_network import NanoLLM, NeuralOptimizer, ActiveMemory
from secure_services import SecureDistillationSystem
from vector_store_manager import VectorStoreManager
from trend_summarizer import TrendSummarizer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

def initialize_components(config_path=None):
    """
    Initialize all required components based on configuration.
    
    Args:
        config_path: Path to configuration file (optional)
    
    Returns:
        Dictionary with initialized components
    """
    # Load configuration
    config = {}
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    
    # Get database connection parameters from config or environment
    db_config = config.get('database', {})
    db_host = db_config.get('host') or os.getenv('DB_HOST', 'localhost')
    db_port = db_config.get('port') or os.getenv('DB_PORT', '5432')
    db_name = db_config.get('name') or os.getenv('DB_NAME', 'trend_crawler')
    db_user = db_config.get('user') or os.getenv('DB_USER', 'postgres')
    db_password = db_config.get('password') or os.getenv('DB_PASSWORD', 'postgres')
    
    # Initialize NanoLLM
    model_config = config.get('nano_llm', {})
    vocab_size = model_config.get('vocab_size', 32000)
    d_model = model_config.get('d_model', 64)
    nhead = model_config.get('nhead', 4)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    nano_llm = NanoLLM(
        vocab_size=vocab_size,
        d_model=d_model,
        nhead=nhead
    ).to(device)
    
    # Check model size
    model_size = sum(p.numel() for p in nano_llm.parameters()) * 4 / 1024 / 1024  # Size in MB
    logger.info(f"NanoLLM size: {model_size:.2f} MB")
    
    # Initialize optimizer and memory
    optimizer_config = config.get('optimizer', {})
    learning_rate = optimizer_config.get('learning_rate', 1e-3)
    
    optimizer = NeuralOptimizer(
        nano_llm.parameters(),
        lr=learning_rate,
        privatize_gradients_flag=True  # Enable differential privacy
    )
    
    # Initialize active memory
    memory_config = config.get('active_memory', {})
    capacity = memory_config.get('capacity', 10000)
    
    active_memory = ActiveMemory(capacity=capacity)
    
    # Initialize secure distillation system
    distillation_system = SecureDistillationSystem(
        nano_llm_instance=nano_llm,
        neural_optimizer_instance=optimizer,
        active_memory_instance=active_memory
    )
    
    # Initialize vector store manager
    vector_store_manager = VectorStoreManager(
        db_host=db_host,
        db_port=db_port,
        db_name=db_name,
        db_user=db_user,
        db_password=db_password
    )
    
    # Initialize trend summarizer
    trend_summarizer = TrendSummarizer(
        secure_distiller=distillation_system,
        vector_store_manager=vector_store_manager,
        db_host=db_host,
        db_port=db_port,
        db_name=db_name,
        db_user=db_user,
        db_password=db_password
    )
    
    return {
        'nano_llm': nano_llm,
        'optimizer': optimizer,
        'active_memory': active_memory,
        'distillation_system': distillation_system,
        'vector_store_manager': vector_store_manager,
        'trend_summarizer': trend_summarizer
    }

def process_trend(trend_id, components):
    """
    Process a specific trend with all the innovative applications.
    
    Args:
        trend_id: ID of the trend to process
        components: Dictionary of initialized components
    
    Returns:
        Dictionary with processing results
    """
    trend_summarizer = components['trend_summarizer']
    
    results = {}
    
    # 1. Generate summary
    logger.info(f"Generating summary for trend {trend_id}")
    summary = trend_summarizer.summarize_trend(trend_id, top_k=10)
    results['summary'] = summary
    
    # 2. Analyze semantic velocity
    logger.info(f"Analyzing semantic velocity for trend {trend_id}")
    velocity = trend_summarizer.analyze_semantic_velocity(trend_id)
    results['velocity'] = velocity
    
    # 3. Detect semantic shifts
    logger.info(f"Detecting semantic shifts for trend {trend_id}")
    shifts = trend_summarizer.detect_semantic_shifts(trend_id)
    results['shifts'] = shifts
    
    # 4. Predict significance
    logger.info(f"Predicting significance for trend {trend_id}")
    significance = trend_summarizer.predict_trend_significance(trend_id)
    results['significance'] = significance
    
    return results

def process_all_trends(components, max_trends=50):
    """
    Process all active trends.
    
    Args:
        components: Dictionary of initialized components
        max_trends: Maximum number of trends to process
    
    Returns:
        Processing statistics
    """
    trend_summarizer = components['trend_summarizer']
    return trend_summarizer.process_all_trends(max_trends=max_trends)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Trend Summarization System")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--trend-id", type=int, help="Process a specific trend ID")
    parser.add_argument("--all", action="store_true", help="Process all trends")
    parser.add_argument("--max-trends", type=int, default=50, help="Maximum number of trends to process")
    
    args = parser.parse_args()
    
    # Initialize components
    logger.info("Initializing components")
    components = initialize_components(args.config)
    
    try:
        if args.trend_id:
            # Process a specific trend
            results = process_trend(args.trend_id, components)
            logger.info(f"Results for trend {args.trend_id}: {json.dumps(results, indent=2)}")
        
        elif args.all:
            # Process all trends
            stats = process_all_trends(components, max_trends=args.max_trends)
            logger.info(f"Processing stats: {json.dumps(stats, indent=2)}")
        
        else:
            # Default: find a single trend to process
            import psycopg2
            db_config = {
                'host': components['vector_store_manager'].db_host,
                'port': components['vector_store_manager'].db_port,
                'dbname': components['vector_store_manager'].db_name,
                'user': components['vector_store_manager'].db_user,
                'password': components['vector_store_manager'].db_password
            }
            
            conn = psycopg2.connect(**db_config)
            with conn.cursor() as cur:
                cur.execute("""
                SELECT id FROM trends
                WHERE status != 'archived'
                ORDER BY RANDOM()
                LIMIT 1
                """)
                
                row = cur.fetchone()
                if row:
                    trend_id = row[0]
                    logger.info(f"Selected random trend ID: {trend_id}")
                    results = process_trend(trend_id, components)
                    logger.info(f"Results: {json.dumps(results, indent=2)}")
                else:
                    logger.warning("No active trends found in the database")
            
            conn.close()
    
    finally:
        # Clean up
        if 'trend_summarizer' in components:
            components['trend_summarizer'].close()
        
        if 'vector_store_manager' in components:
            components['vector_store_manager'].conn_pool = None

if __name__ == "__main__":
    main()
