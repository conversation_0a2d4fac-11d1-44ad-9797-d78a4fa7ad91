# Trend Crawler

An advanced web scraping and data collection system for trend analysis, featuring Twitter/X scraping capabilities, CAPTCHA solving, and anti-detection measures.

## Setup

1. Install Anaconda or Miniconda if you don't have it already
2. Run the setup script to create and configure the environment:

```bash
./setup.sh
```

This script will:
- Create the `trend-crawler` Anaconda environment
- Install all required dependencies
- Set up the `.env` file with your credentials
- Configure proxy settings
- Create necessary directories

## Using the Application

Use the run script to activate the environment and start different components of the application:

```bash
# To run the Twitter/X scraper:
./run_scraper.sh twitter

# To start the web dashboard:
./run_scraper.sh dashboard

# To run the trend crawler:
./run_scraper.sh trends
```

## Important Files

- `.env`: Contains environment variables and credentials
- `proxy_config.json`: Contains proxy configuration
- `twitter_x_scraper.py`: Twitter/X scraping module
- `trend_crawler.py`: Main trend analysis module
- `web_dashboard.py`: Web dashboard for monitoring and visualization

## Environment Variables

The following environment variables should be configured in the `.env` file:

- `TWITTER_USERNAME` and `TWITTER_PASSWORD`: Twitter login credentials
- `PROXY_SERVER`: Full proxy URL for scraping
- `BRIGHTDATA_*`: Bright Data proxy configuration
- `HEADLESS_MODE`: Whether to run browser in headless mode
- `REQUEST_TIMEOUT`: Timeout for HTTP requests
- `MAX_RETRIES`: Maximum number of retry attempts
- `USER_AGENT_ROTATION`: Whether to rotate user agents

## Proxy Configuration

This application uses Bright Data proxies for scraping. The configuration is stored in `proxy_config.json`.

## Working on the Code

Always use the `trend-crawler` Anaconda environment when working on this code:

```bash
conda activate trend-crawler
```

## Notes

- Twitter login credentials are required for better scraping results
- Proxies are essential for avoiding IP blocks
- The web dashboard provides useful metrics and monitoring
