#!/usr/bin/env python3
"""
Database Hooks for Real-time Trend Analysis Updates

This module implements the database hooks and monitoring system for real-time
updates to the trend analysis features using the pgvector extension.
"""

import os
import sys
import logging
import time
import json
import signal
import threading
import psycopg2
from psycopg2.extras import DictCursor
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import nano neural network if available
try:
    from nano_neural_network import NanoNetworkManager
    NANO_NETWORK_AVAILABLE = True
except ImportError:
    NANO_NETWORK_AVAILABLE = False
    logger.warning("Nano Neural Network module not available")

# Import trend analytics if available
try:
    from trend_analytics import TrendAnalytics
    TREND_ANALYTICS_AVAILABLE = True
except ImportError:
    TREND_ANALYTICS_AVAILABLE = False
    logger.warning("TrendAnalytics module not available")


class RealtimeHookManager:
    """
    Manages real-time database hooks and updates for trend analysis.
    
    This class monitors for new content, trend changes, and updates
    predictive models in real-time.
    """
    
    def __init__(self, 
                db_host: str = None,
                db_port: str = None,
                db_name: str = None,
                db_user: str = None,
                db_password: str = None,
                polling_interval: int = 60):
        """
        Initialize the real-time hook manager.
        
        Args:
            db_host: Database host
            db_port: Database port
            db_name: Database name
            db_user: Database user
            db_password: Database password
            polling_interval: Polling interval in seconds
        """
        # Load configuration from environment variables if not provided
        self.db_host = db_host or os.getenv('DB_HOST', 'localhost')
        self.db_port = db_port or os.getenv('DB_PORT', '5432')
        self.db_name = db_name or os.getenv('DB_NAME', 'trend_crawler')
        self.db_user = db_user or os.getenv('DB_USER', 'postgres')
        self.db_password = db_password or os.getenv('DB_PASSWORD', 'postgres')
        
        # Set polling interval
        self.polling_interval = polling_interval
        
        # Initialize models
        self.nano_manager = None
        self.trend_analytics = None
        
        # Monitoring state
        self.running = False
        self.monitoring_thread = None
        self.last_update_check = datetime.now()
        
        # Connect to database
        self._connect_db()
        
        # Initialize analytics modules if available
        self._init_analytics()
        
        # Ensure database hooks are installed
        self._ensure_hooks()
    
    def _connect_db(self) -> None:
        """Establish database connection."""
        try:
            self.conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                dbname=self.db_name,
                user=self.db_user,
                password=self.db_password
            )
            logger.info(f"Connected to PostgreSQL database {self.db_name}")
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            self.conn = None
    
    def _init_analytics(self) -> None:
        """Initialize analytics modules."""
        if NANO_NETWORK_AVAILABLE:
            try:
                self.nano_manager = NanoNetworkManager(
                    db_host=self.db_host,
                    db_port=self.db_port,
                    db_name=self.db_name,
                    db_user=self.db_user,
                    db_password=self.db_password
                )
                logger.info("Initialized Nano Network Manager")
            except Exception as e:
                logger.error(f"Failed to initialize Nano Network Manager: {e}")
                self.nano_manager = None
        
        if TREND_ANALYTICS_AVAILABLE and self.conn:
            try:
                self.trend_analytics = TrendAnalytics(
                    db=self.conn,
                    llm_service="openai" if os.getenv("OPENAI_API_KEY") else "fallback"
                )
                logger.info("Initialized Trend Analytics")
            except Exception as e:
                logger.error(f"Failed to initialize Trend Analytics: {e}")
                self.trend_analytics = None
    
    def _ensure_hooks(self) -> None:
        """Ensure database hooks are installed."""
        if not self.conn:
            logger.error("No database connection")
            return
        
        try:
            with self.conn.cursor() as cur:
                # Create notification function
                cur.execute("""
                CREATE OR REPLACE FUNCTION notify_trend_update()
                RETURNS TRIGGER AS $$
                BEGIN
                    -- Notify the trend_updates channel with the trend ID
                    PERFORM pg_notify('trend_updates', NEW.id::text);
                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
                """)
                
                # Create notification trigger for content updates
                cur.execute("""
                DROP TRIGGER IF EXISTS trg_notify_content_update ON crawled_content;
                """)
                
                cur.execute("""
                CREATE TRIGGER trg_notify_content_update
                AFTER INSERT OR UPDATE OF embedding
                ON crawled_content
                FOR EACH ROW
                WHEN (NEW.embedding IS NOT NULL)
                EXECUTE FUNCTION notify_trend_update();
                """)
                
                # Create trigger for trend updates
                cur.execute("""
                DROP TRIGGER IF EXISTS trg_notify_trend_update ON trends;
                """)
                
                cur.execute("""
                CREATE TRIGGER trg_notify_trend_update
                AFTER INSERT OR UPDATE
                ON trends
                FOR EACH ROW
                EXECUTE FUNCTION notify_trend_update();
                """)
                
                # Create hook tracking table
                cur.execute("""
                CREATE TABLE IF NOT EXISTS realtime_hook_events (
                    id SERIAL PRIMARY KEY,
                    event_type VARCHAR(50) NOT NULL,
                    entity_id BIGINT NOT NULL,
                    entity_type VARCHAR(50) NOT NULL,
                    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMPTZ,
                    processing_result JSONB
                );
                """)
                
                # Create trend prediction update queue
                cur.execute("""
                CREATE TABLE IF NOT EXISTS trend_prediction_queue (
                    id SERIAL PRIMARY KEY,
                    trend_id INTEGER NOT NULL,
                    trigger_type VARCHAR(50) NOT NULL,
                    trigger_id BIGINT,
                    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                    priority INTEGER DEFAULT 1,
                    scheduled_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE,
                    processed_at TIMESTAMPTZ,
                    result JSONB
                );
                """)
                
                self.conn.commit()
                logger.info("Database hooks installed successfully")
                
        except Exception as e:
            logger.error(f"Failed to install database hooks: {e}")
            self.conn.rollback()
    
    def enqueue_trend_for_prediction(self, 
                                    trend_id: int, 
                                    trigger_type: str = 'manual', 
                                    trigger_id: int = None) -> bool:
        """
        Enqueue a trend for prediction update.
        
        Args:
            trend_id: ID of the trend to update
            trigger_type: Type of trigger ('new_content', 'trend_update', 'manual')
            trigger_id: ID of the triggering entity
            
        Returns:
            True if successful, False otherwise
        """
        if not self.conn:
            logger.error("No database connection")
            return False
        
        try:
            with self.conn.cursor() as cur:
                # Check if trend exists
                cur.execute("SELECT id FROM trends WHERE id = %s", (trend_id,))
                if cur.rowcount == 0:
                    logger.warning(f"Trend {trend_id} does not exist")
                    return False
                
                # Add to prediction queue
                cur.execute("""
                INSERT INTO trend_prediction_queue
                (trend_id, trigger_type, trigger_id, priority)
                VALUES (%s, %s, %s, %s)
                """, (
                    trend_id,
                    trigger_type,
                    trigger_id,
                    2 if trigger_type == 'manual' else 1  # Higher priority for manual requests
                ))
                
                self.conn.commit()
                logger.info(f"Enqueued trend {trend_id} for prediction ({trigger_type})")
                return True
                
        except Exception as e:
            logger.error(f"Failed to enqueue trend for prediction: {e}")
            self.conn.rollback()
            return False
    
    def process_prediction_queue(self, limit: int = 5) -> int:
        """
        Process items in the trend prediction queue.
        
        Args:
            limit: Maximum number of items to process
            
        Returns:
            Number of items processed
        """
        if not self.conn or not self.nano_manager:
            logger.error("No database connection or nano manager")
            return 0
        
        processed = 0
        
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                # Get items from queue
                cur.execute("""
                SELECT id, trend_id, trigger_type, trigger_id
                FROM trend_prediction_queue
                WHERE NOT processed
                ORDER BY priority DESC, created_at
                LIMIT %s
                """, (limit,))
                
                items = cur.fetchall()
                
                for item in items:
                    queue_id = item['id']
                    trend_id = item['trend_id']
                    
                    # Generate prediction
                    prediction = self.nano_manager.predict_trend_evolution(trend_id)
                    
                    if prediction:
                        # Update trend with prediction
                        cur.execute("""
                        UPDATE trends
                        SET metadata = jsonb_set(
                            COALESCE(metadata, '{}'::jsonb),
                            '{nano_predictions}',
                            %s::jsonb,
                            true
                        )
                        WHERE id = %s
                        """, (
                            json.dumps(prediction),
                            trend_id
                        ))
                        
                        # Mark as processed
                        cur.execute("""
                        UPDATE trend_prediction_queue
                        SET processed = TRUE,
                            processed_at = NOW(),
                            result = %s
                        WHERE id = %s
                        """, (
                            json.dumps({"status": "success", "prediction_generated": True}),
                            queue_id
                        ))
                        
                        processed += 1
                        logger.info(f"Processed prediction for trend {trend_id}")
                    else:
                        # Mark as failed
                        cur.execute("""
                        UPDATE trend_prediction_queue
                        SET processed = TRUE,
                            processed_at = NOW(),
                            result = %s
                        WHERE id = %s
                        """, (
                            json.dumps({"status": "failed", "reason": "Failed to generate prediction"}),
                            queue_id
                        ))
                
                self.conn.commit()
                return processed
                
        except Exception as e:
            logger.error(f"Error processing prediction queue: {e}")
            self.conn.rollback()
            return 0
    
    def check_trend_updates(self) -> int:
        """
        Check for trend updates since last check.
        
        Returns:
            Number of trends updated
        """
        if not self.conn:
            logger.error("No database connection")
            return 0
        
        now = datetime.now()
        updated_count = 0
        
        try:
            with self.conn.cursor() as cur:
                # Check for new trend content mappings
                cur.execute("""
                SELECT DISTINCT trend_id
                FROM trend_content_map
                WHERE assigned_at > %s
                """, (self.last_update_check,))
                
                updated_trends = [row[0] for row in cur.fetchall()]
                
                # Enqueue trends for prediction
                for trend_id in updated_trends:
                    if self.enqueue_trend_for_prediction(trend_id, 'new_content'):
                        updated_count += 1
                
                self.last_update_check = now
                
                return updated_count
                
        except Exception as e:
            logger.error(f"Error checking trend updates: {e}")
            return 0
    
    def run_model_training(self) -> bool:
        """
        Run scheduled model training tasks.
        
        Returns:
            True if training was successful, False otherwise
        """
        if not self.nano_manager:
            logger.error("No nano manager available")
            return False
        
        try:
            # Train nano network
            model_path = self.nano_manager.train_nano_network(
                days_back=30,
                batch_size=64,
                epochs=10
            )
            
            if not model_path:
                logger.error("Failed to train nano network")
                return False
            
            # Train temporal GAN for top trends
            if self.conn:
                with self.conn.cursor() as cur:
                    # Get top active trends
                    cur.execute("""
                    SELECT id
                    FROM trends
                    WHERE status IN ('emerging', 'growing', 'peaking')
                    ORDER BY (metadata->'semantic_velocity'->>'prediction_score')::float DESC NULLS LAST
                    LIMIT 3
                    """)
                    
                    top_trends = [row[0] for row in cur.fetchall()]
                    
                    for trend_id in top_trends:
                        logger.info(f"Training temporal GAN for trend {trend_id}")
                        gan_path = self.nano_manager.train_temporal_gan(
                            trend_id=trend_id,
                            batch_size=32,
                            epochs=50
                        )
                        
                        if not gan_path:
                            logger.warning(f"Failed to train temporal GAN for trend {trend_id}")
                        else:
                            logger.info(f"Successfully trained temporal GAN for trend {trend_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in model training: {e}")
            return False
    
    def _monitoring_task(self) -> None:
        """Regular monitoring task to check for updates."""
        try:
            # Check for trend updates
            updates = self.check_trend_updates()
            if updates > 0:
                logger.info(f"Detected {updates} trend updates")
            
            # Process prediction queue
            processed = self.process_prediction_queue()
            if processed > 0:
                logger.info(f"Processed {processed} predictions")
                
        except Exception as e:
            logger.error(f"Error in monitoring task: {e}")
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        logger.info("Starting monitoring loop")
        
        # Schedule tasks
        schedule.every(self.polling_interval).seconds.do(self._monitoring_task)
        schedule.every(12).hours.do(self.run_model_training)
        
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def start_monitoring(self) -> None:
        """Start the monitoring thread."""
        if self.running:
            logger.warning("Monitoring already running")
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        logger.info("Monitoring started")
        
        # Run initial tasks
        self._monitoring_task()
    
    def stop_monitoring(self) -> None:
        """Stop the monitoring thread."""
        if not self.running:
            logger.warning("Monitoring not running")
            return
        
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("Monitoring stopped")
    
    def close(self) -> None:
        """Close connections and clean up."""
        self.stop_monitoring()
        
        if self.nano_manager:
            self.nano_manager.close()
            
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")


def setup_signal_handlers(manager: RealtimeHookManager) -> None:
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(sig, frame):
        logger.info(f"Received signal {sig}, shutting down...")
        manager.stop_monitoring()
        manager.close()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


if __name__ == "__main__":
    try:
        # Get configuration from environment
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'trend_crawler')
        db_user = os.getenv('DB_USER', 'postgres')
        db_password = os.getenv('DB_PASSWORD', 'postgres')
        polling_interval = int(os.getenv('POLLING_INTERVAL', '60'))
        
        # Initialize hook manager
        manager = RealtimeHookManager(
            db_host=db_host,
            db_port=db_port,
            db_name=db_name,
            db_user=db_user,
            db_password=db_password,
            polling_interval=polling_interval
        )
        
        # Set up signal handlers
        setup_signal_handlers(manager)
        
        # Start monitoring
        manager.start_monitoring()
        
        # Keep main thread alive
        while True:
            time.sleep(1)
            
    except Exception as e:
        logger.error(f"Error in main program: {e}")
        sys.exit(1)
