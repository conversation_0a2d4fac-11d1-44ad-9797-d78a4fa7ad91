#!/usr/bin/env python3
"""
Simple test to verify proxy integration with trend_crawler.py
"""

import sys
import os
import logging
from unittest.mock import Mock, patch

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_crawl_website_function():
    """Test that the crawl_website function accepts a proxy_manager parameter."""
    
    print("Testing crawl_website function signature...")
    
    try:
        # Import just the function we need
        from trend_crawler import crawl_website
        
        # Check the function signature
        import inspect
        sig = inspect.signature(crawl_website)
        params = list(sig.parameters.keys())
        
        print(f"crawl_website parameters: {params}")
        
        if 'proxy_manager' in params:
            print("✅ crawl_website function has proxy_manager parameter")
            return True
        else:
            print("❌ crawl_website function missing proxy_manager parameter")
            return False
            
    except Exception as e:
        print(f"❌ Error testing function signature: {e}")
        return False

def test_proxy_manager_import():
    """Test that the proxy manager can be imported."""
    
    print("\nTesting proxy manager import...")
    
    try:
        from proxy_manager import ProxyManager
        print("✅ ProxyManager imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Could not import ProxyManager: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing ProxyManager: {e}")
        return False

def test_proxy_availability_flag():
    """Test that the PROXY_MANAGER_AVAILABLE flag is set correctly."""
    
    print("\nTesting proxy availability flag...")
    
    try:
        from trend_crawler import PROXY_MANAGER_AVAILABLE
        print(f"PROXY_MANAGER_AVAILABLE: {PROXY_MANAGER_AVAILABLE}")
        
        if PROXY_MANAGER_AVAILABLE:
            print("✅ Proxy manager is available")
        else:
            print("ℹ️  Proxy manager is not available (this may be expected)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking proxy availability: {e}")
        return False

def test_mock_crawl_with_proxy():
    """Test crawl_website with a mock proxy manager."""
    
    print("\nTesting crawl_website with mock proxy manager...")
    
    try:
        from trend_crawler import crawl_website
        
        # Create a mock proxy manager
        mock_proxy_manager = Mock()
        mock_proxy_config = {
            'id': 'test_proxy_1',
            'host': '127.0.0.1',
            'port': 8080,
            'username': 'testuser',
            'password': 'testpass'
        }
        mock_proxy_manager.get_proxy.return_value = mock_proxy_config
        mock_proxy_manager.update_proxy_stats = Mock()
        
        # Mock the requests.get call
        with patch('trend_crawler.requests.get') as mock_get:
            # Set up the mock response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '<html><body><h1>Test Content</h1></body></html>'
            mock_get.return_value = mock_response
            
            # Test the crawl_website function with proxy manager
            result = crawl_website('https://example.com', mock_proxy_manager)
            
            # Verify the result
            if result and 'Test Content' in result:
                print("✅ crawl_website returned content with proxy manager")
            else:
                print("❌ crawl_website did not return expected content")
                return False
            
            # Verify that get_proxy was called
            if mock_proxy_manager.get_proxy.called:
                print("✅ Proxy manager get_proxy() was called")
            else:
                print("❌ Proxy manager get_proxy() was not called")
                return False
            
            # Verify that requests.get was called with proxy configuration
            if mock_get.called:
                call_args = mock_get.call_args
                proxies = call_args[1].get('proxies')
                if proxies:
                    print("✅ requests.get was called with proxy configuration")
                    print(f"   Proxy config: {proxies}")
                else:
                    print("❌ requests.get was not called with proxy configuration")
                    return False
            else:
                print("❌ requests.get was not called")
                return False
        
        print("✅ Mock proxy test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Mock proxy test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    
    print("=" * 60)
    print("SIMPLE PROXY INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        test_proxy_manager_import,
        test_proxy_availability_flag,
        test_crawl_website_function,
        test_mock_crawl_with_proxy,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Proxy integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
