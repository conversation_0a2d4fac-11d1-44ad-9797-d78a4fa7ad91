-- Scraping metrics and monitoring schema

-- Table for proxy performance metrics
CREATE TABLE proxy_metrics (
    id SERIAL PRIMARY KEY,
    proxy_id VARCHAR(255) NOT NULL,
    proxy_type VARCHAR(50),
    success BOOLEAN DEFAULT FALSE,
    response_time FLOAT,
    status_code INTEGER,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Keep legacy columns for backward compatibility
    success_count INT DEFAULT 0,
    failure_count INT DEFAULT 0,
    average_response_time FLOAT,
    last_used TIMESTAMP,
    is_blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for CAPTCHA encounters and solutions
CREATE TABLE captcha_metrics (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048),
    captcha_type VARCHAR(50),
    solution_provider VARCHAR(50),
    solution_time FLOAT,
    success BOOLEAN,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for scraping session metrics
CREATE TABLE scraping_sessions (
    id SERIAL PRIMARY KEY,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    total_requests INT,
    successful_requests INT,
    failed_requests INT,
    unique_ips_used INT,
    average_request_time FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for detection events
CREATE TABLE detection_events (
    id SERIAL PRIMARY KEY,
    session_id INT REFERENCES scraping_sessions(id),
    event_type VARCHAR(50),
    url VARCHAR(2048),
    proxy_id VARCHAR(255),
    fingerprint_id VARCHAR(255),
    detection_method VARCHAR(255),
    additional_info JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for fingerprint performance
CREATE TABLE fingerprint_metrics (
    id SERIAL PRIMARY KEY,
    fingerprint_id VARCHAR(255),
    success_count INT DEFAULT 0,
    detection_count INT DEFAULT 0,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for IP rotation history
CREATE TABLE ip_rotation_history (
    id SERIAL PRIMARY KEY,
    old_ip VARCHAR(45),
    new_ip VARCHAR(45),
    rotation_reason VARCHAR(255),
    rotation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for request patterns
CREATE TABLE request_patterns (
    id SERIAL PRIMARY KEY,
    pattern_hash VARCHAR(64),
    request_count INT,
    success_rate FLOAT,
    last_seen TIMESTAMP,
    is_blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for enhanced CAPTCHA metrics
CREATE TABLE captcha_events (
    id SERIAL PRIMARY KEY,
    site_key VARCHAR(255),
    url VARCHAR(2048),
    captcha_type VARCHAR(50) NOT NULL, -- recaptcha, hcaptcha, turnstile, etc.
    solver VARCHAR(50) NOT NULL, -- 2captcha, anticaptcha, capsolver, etc.
    success BOOLEAN NOT NULL,
    solve_time FLOAT, -- in seconds
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_token TEXT -- the actual solution token (truncated if too long)
);

-- Table for WAF detection and bypass attempts
CREATE TABLE waf_bypass_attempts (
    id SERIAL PRIMARY KEY,
    site VARCHAR(255) NOT NULL,
    waf_type VARCHAR(50) NOT NULL, -- cloudflare, akamai, etc.
    headers_used JSONB, -- stored headers used to bypass
    success BOOLEAN NOT NULL DEFAULT FALSE,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for legal honeypot detection
CREATE TABLE legal_honeypots (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    detection_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    honeypot_type VARCHAR(50), -- copyright_trap, data_poisoning, etc.
    content_hash VARCHAR(255), -- hash of content for future reference
    safe_to_scrape BOOLEAN DEFAULT FALSE,
    notes TEXT
);

-- Table for browser fingerprint rotations
CREATE TABLE fingerprint_rotations (
    id SERIAL PRIMARY KEY,
    fingerprint_id VARCHAR(255) NOT NULL,
    user_agent TEXT,
    webgl_vendor VARCHAR(255),
    webgl_renderer VARCHAR(255),
    hardware_metrics JSONB, -- device memory, cores, etc.
    success BOOLEAN DEFAULT TRUE,
    detection_method VARCHAR(255),
    rotation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for TLS fingerprinting avoidance
CREATE TABLE tls_fingerprint_events (
    id SERIAL PRIMARY KEY,
    site VARCHAR(255) NOT NULL,
    browser_type VARCHAR(50), -- chrome, firefox, etc.
    cipher_suite TEXT,
    detected BOOLEAN DEFAULT FALSE,
    bypass_successful BOOLEAN,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for honeypot detection events
CREATE TABLE honeypot_detections (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    trap_type VARCHAR(50) NOT NULL, -- form_field, hidden_link, etc.
    element_selector TEXT, -- CSS selector of detected honeypot
    detection_method VARCHAR(100),
    additional_data JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for tracking successful dynamic layout adaptations
CREATE TABLE dynamic_layout_adaptations (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    original_selectors JSONB, -- original selectors attempted
    fallback_strategy_used VARCHAR(100), -- semantic, AI-based, etc.
    extraction_success BOOLEAN DEFAULT FALSE,
    content_extracted JSONB, -- stats about what was successfully extracted
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for device fingerprinting countermeasures
CREATE TABLE device_fingerprinting_events (
    id SERIAL PRIMARY KEY,
    url VARCHAR(2048) NOT NULL,
    device_type VARCHAR(50), -- desktop, mobile, etc.
    emulated_device VARCHAR(100), -- if specific device was emulated
    spoofed_metrics JSONB, -- which hardware metrics were spoofed
    detected BOOLEAN DEFAULT FALSE, -- whether our countermeasures were detected
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for blocking event tracking
CREATE TABLE blocking_events (
    id SERIAL PRIMARY KEY,
    target_site VARCHAR(255) NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- captcha, ip_ban, browser_fingerprinting, etc.
    proxy_id VARCHAR(255),
    fingerprint_hash VARCHAR(255),
    detection_method VARCHAR(255),
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mitigation_action VARCHAR(255)
);

-- Indexes for performance
CREATE INDEX idx_proxy_metrics_proxy_id ON proxy_metrics(proxy_id);
CREATE INDEX idx_captcha_metrics_url ON captcha_metrics(url);
CREATE INDEX idx_detection_events_session_id ON detection_events(session_id);
CREATE INDEX idx_fingerprint_metrics_fingerprint_id ON fingerprint_metrics(fingerprint_id);
CREATE INDEX idx_request_patterns_pattern_hash ON request_patterns(pattern_hash);
CREATE INDEX idx_captcha_events_url ON captcha_events(url);
CREATE INDEX idx_captcha_events_solver ON captcha_events(solver);
CREATE INDEX idx_waf_bypass_attempts_site ON waf_bypass_attempts(site);
CREATE INDEX idx_waf_bypass_attempts_waf_type ON waf_bypass_attempts(waf_type);
CREATE INDEX idx_legal_honeypots_url ON legal_honeypots(url);
CREATE INDEX idx_honeypot_detections_url ON honeypot_detections(url);
CREATE INDEX idx_tls_fingerprint_events_site ON tls_fingerprint_events(site);
CREATE INDEX idx_fingerprint_rotations_fingerprint_id ON fingerprint_rotations(fingerprint_id);
CREATE INDEX idx_blocking_events_site ON blocking_events(target_site);
CREATE INDEX idx_blocking_events_type ON blocking_events(event_type);

-- Views for monitoring
CREATE VIEW proxy_performance_summary AS
SELECT 
    proxy_id,
    proxy_type,
    success_count,
    failure_count,
    CASE 
        WHEN (success_count + failure_count) > 0 
        THEN (success_count::FLOAT / (success_count + failure_count)) * 100 
        ELSE 0 
    END as success_rate,
    average_response_time,
    last_used
FROM proxy_metrics
WHERE last_used >= NOW() - INTERVAL '24 hours';

CREATE VIEW detection_hotspots AS
SELECT 
    url,
    COUNT(*) as detection_count,
    array_agg(DISTINCT detection_method) as detection_methods,
    MAX(created_at) as last_detected
FROM detection_events
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY url
HAVING COUNT(*) > 5
ORDER BY detection_count DESC;

-- Enhanced views for advanced monitoring
CREATE VIEW captcha_solver_performance AS
SELECT
    solver,
    captcha_type,
    COUNT(*) AS total_attempts,
    SUM(CASE WHEN success THEN 1 ELSE 0 END) AS successful,
    CASE
        WHEN COUNT(*) > 0 THEN 
            (SUM(CASE WHEN success THEN 1 ELSE 0 END)::FLOAT / COUNT(*)) * 100
        ELSE 0
    END AS success_rate,
    AVG(solve_time) AS avg_solve_time
FROM captcha_events
WHERE timestamp >= NOW() - INTERVAL '24 hours'
GROUP BY solver, captcha_type
ORDER BY success_rate DESC;

CREATE VIEW waf_bypass_success_rates AS
SELECT
    site,
    waf_type,
    COUNT(*) AS total_attempts,
    SUM(CASE WHEN success THEN 1 ELSE 0 END) AS successful,
    CASE
        WHEN COUNT(*) > 0 THEN 
            (SUM(CASE WHEN success THEN 1 ELSE 0 END)::FLOAT / COUNT(*)) * 100
        ELSE 0
    END AS success_rate
FROM waf_bypass_attempts
WHERE timestamp >= NOW() - INTERVAL '7 days'
GROUP BY site, waf_type
ORDER BY success_rate DESC;

CREATE VIEW fingerprint_survival_rates AS
SELECT
    fingerprint_id,
    COUNT(*) AS total_uses,
    MAX(rotation_time) - MIN(rotation_time) AS lifetime,
    SUM(CASE WHEN success THEN 0 ELSE 1 END) AS detection_count
FROM fingerprint_rotations
WHERE rotation_time >= NOW() - INTERVAL '30 days'
GROUP BY fingerprint_id
ORDER BY detection_count, lifetime DESC;

-- Functions for monitoring alerts
CREATE OR REPLACE FUNCTION check_proxy_health() RETURNS trigger AS $$
BEGIN
    IF NEW.failure_count - OLD.failure_count >= 5 THEN
        -- Alert logic here (implement with your preferred notification system)
        -- For example, insert into an alerts table
        INSERT INTO system_alerts (
            alert_type,
            message,
            severity
        ) VALUES (
            'proxy_health',
            format('Proxy %s has failed %s times in succession', NEW.proxy_id, NEW.failure_count - OLD.failure_count),
            'high'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER proxy_health_monitor
AFTER UPDATE ON proxy_metrics
FOR EACH ROW
EXECUTE FUNCTION check_proxy_health();

-- New function to monitor fingerprint health
CREATE OR REPLACE FUNCTION check_fingerprint_health() RETURNS trigger AS $$
BEGIN
    IF NEW.detection_count - OLD.detection_count >= 3 THEN
        -- Alert logic for compromised fingerprints
        INSERT INTO system_alerts (
            alert_type,
            message,
            severity
        ) VALUES (
            'fingerprint_compromised',
            format('Fingerprint %s has been detected %s times and should be recycled', 
                   NEW.fingerprint_id, NEW.detection_count),
            'medium'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER fingerprint_health_monitor
AFTER UPDATE ON fingerprint_metrics
FOR EACH ROW
EXECUTE FUNCTION check_fingerprint_health();

-- Function to detect suspicious legal honeypot patterns
CREATE OR REPLACE FUNCTION monitor_legal_honeypots() RETURNS trigger AS $$
BEGIN
    -- Check if we've detected multiple honeypots from the same domain recently
    DECLARE
        honeypot_count INTEGER;
        domain VARCHAR;
    BEGIN
        domain := regexp_replace(NEW.url, '^(?:https?://)?(?:www\.)?([^/]+).*$', '\1');
        
        SELECT COUNT(*) INTO honeypot_count
        FROM legal_honeypots
        WHERE 
            url LIKE '%' || domain || '%' AND
            detection_timestamp >= NOW() - INTERVAL '24 hours';
            
        IF honeypot_count >= 3 THEN
            -- Domain appears to be using multiple honeypot traps
            INSERT INTO system_alerts (
                alert_type,
                message,
                severity
            ) VALUES (
                'legal_honeypot_domain',
                format('Domain %s has deployed %s honeypots. Consider blacklisting domain.', 
                       domain, honeypot_count),
                'high'
            );
        END IF;
    END;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER legal_honeypot_monitor
AFTER INSERT ON legal_honeypots
FOR EACH ROW
EXECUTE FUNCTION monitor_legal_honeypots();