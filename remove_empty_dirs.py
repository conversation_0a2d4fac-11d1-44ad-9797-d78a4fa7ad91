#!/usr/bin/env python3
"""
Script to identify and remove empty directories in the dataSets directory.
"""

import os
import sys

def is_dir_empty(dir_path):
    """Check if a directory is empty."""
    # A directory is empty if it contains no files and no non-empty subdirectories
    for item in os.listdir(dir_path):
        item_path = os.path.join(dir_path, item)
        if os.path.isfile(item_path):
            return False
        if os.path.isdir(item_path) and not is_dir_empty(item_path):
            return False
    return True

def remove_empty_dirs(directory, dry_run=True):
    """
    Recursively remove empty directories.
    If dry_run is True, only print what would be done without actually removing directories.
    """
    removed_count = 0

    # First, get all directories and sort them by depth (descending)
    all_dirs = []
    for root, dirs, _ in os.walk(directory):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            # Calculate depth by counting path separators
            depth = dir_path.count(os.path.sep)
            all_dirs.append((dir_path, depth))

    # Sort by depth in descending order (deepest first)
    all_dirs.sort(key=lambda x: x[1], reverse=True)

    print(f"Found {len(all_dirs)} directories to check")

    # Process directories from deepest to shallowest
    for dir_path, _ in all_dirs:
        if os.path.exists(dir_path) and is_dir_empty(dir_path):
            if dry_run:
                print(f"Would remove empty directory: {dir_path}")
                removed_count += 1
            else:
                try:
                    os.rmdir(dir_path)
                    print(f"Removed empty directory: {dir_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"Error removing directory {dir_path}: {e}")

    return removed_count

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = "/home/<USER>/appsUndCode/dataSets/"

    dry_run = True  # Default to dry run for safety

    if len(sys.argv) > 2 and sys.argv[2].lower() == "--delete":
        dry_run = False

    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)

    print(f"Scanning for empty directories in {directory}...")

    removed_count = remove_empty_dirs(directory, dry_run)

    if dry_run:
        print(f"\nFound {removed_count} empty directories that would be removed.")
        print("This was a dry run. No directories were actually removed.")
        print("To actually remove the directories, run with the --delete flag:")
        print(f"python {sys.argv[0]} {directory} --delete")
    else:
        print(f"\nSuccessfully removed {removed_count} empty directories.")
