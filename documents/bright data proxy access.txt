Access Details
Mode
Immediate access
Host:
brd.superproxy.io

Ports:
33335

Username:
Control your proxy by
modifying the username
brd-customer-hl_caaa2374-zone-residential_proxy1

Passwords:
grh8h9v2fmib

Single string formats:
host:port:username:password

username:password@host:port

host,port,username,password


api token: 5b4f64bce0299cd3a647c175106e9c0e642a8805f39859d27116fdec0eb832a1



⚠ Note: Setup SSL Certificate to enable secure communication and access Bright Data residential network without SSL errors, and enable Cache in all zones:

curl -i --proxy brd.superproxy.io:33335 --proxy-user brd-customer-hl_caaa2374-zone-residential_proxy1:grh8h9v2fmib -k "https://geo.brdtest.com/welcome.txt?product=resi&method=native"



Code Examples:


import urllib.request
import ssl

proxy = 'http://brd-customer-hl_caaa2374-zone-residential_proxy1:<EMAIL>:33335'
url = 'https://geo.brdtest.com/mygeo.json'

opener = urllib.request.build_opener(
    urllib.request.ProxyHandler({'https': proxy, 'http': proxy}),
    urllib.request.HTTPSHandler(context=ssl._create_unverified_context())
)

try:
    print(opener.open(url).read().decode())
except Exception as e:
    print(f"Error: {e}")




import requests

proxy = 'http://brd-customer-hl_caaa2374-zone-residential_proxy1:<EMAIL>:33335'
url = 'https://geo.brdtest.com/mygeo.json'

proxies = {
    'http': proxy,
    'https': proxy,
}

try:
    response = requests.get(url, proxies=proxies, verify=False)
    print(response.text)
except requests.exceptions.RequestException as e:
    print(e)


Proxy
Proxy REST API Playground
Access proxy networks using REST API

POST
/
request

Try it
See instruction on how to create API Token: API Token creation
Authorizations
​
Authorization
stringheaderrequired
Bearer authentication header of the form Bearer <token>, where <token> is your auth token.

Body
application/json
​
zone
stringrequired
Name of the proxy zone which will service your request.

​
url
stringrequired
Target URL for your request.

​
format
enum<string>required
Format for requesting a raw HTML via proxy is raw.

Format for request a JSON response is json.

Available options: raw, json
​
method
stringdefault:GET
Method for requesting an HTML via proxy is GET.

​
country
string
Country code of proxy which request is relayed through.

​
city
string
City name of proxy which request is relayed through, must have country defined.

Supported in DC & ISP for zones pre-configured with cities, and in Residential and mobile networks by default.

​
zip
string
Zip code which request is relayed through, must have country defined.

Supported in Residential networks only.

​
ASN
string
ASN code which request is relayed thru, must have country defined.

Supported in Residential networks only.

​
carrier
string
For mobile network only: carrier code in which request is relayed through.

Supported in mobile networks only.

​
os
string
Operating system of the client to set on proxy.

Supported in mobile networks only.

​
dns
enum<string>
Use local or remote to set if domain name mapping is done locally on client side or on remote proxy peer.

Available options: local, remote
​
session
string
A string relayed by the client to mark a session, all requests with the same session parameter will be relayed to the same proxy peer.

​
ip
string
In Datacenter and ISP zones: Relay the request to a specific proxy identified by the ip.

​
gip
string
In Residential and Mobile zones: Relay the request to a specific proxy group identified by the gip.

​
c_tag
string
Include a unique c_tag flag in their requests. In response, businesses echo back the same tag in the header. This seamless exchange ensures that each response is bound to its corresponding request, eliminating confusion and streamlining data management.

​
direct
boolean
Setting the parameter to true will instruct to process the request by Bright Data super proxy, a datacenter located proxy and not the proxy peer itself.

Response
200

200
application/json
OK
The response is of type string.

Example:
"OK"


import requests

url = "https://api.brightdata.com/request"

payload = {
    "zone": "<string>",
    "url": "<string>",
    "format": "raw",
    "method": "GET",
    "country": "<string>",
    "city": "<string>",
    "zip": "<string>",
    "ASN": "<string>",
    "carrier": "<string>",
    "os": "<string>",
    "dns": "local",
    "session": "<string>",
    "ip": "<string>",
    "gip": "<string>",
    "c_tag": "<string>",
    "direct": True
}
headers = {
    "Authorization": "Bearer <token>",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)




Code Examples
Once you have your proxy credentials, use the following code to send your first request:

import pprint
import requests


host = 'brd.superproxy.io'
port = 33335

username = 'brd-customer-<customer_id>-zone-<zone_name>'
password = '<zone_password>'

proxy_url = f'http://{username}:{password}@{host}:{port}'

proxies = {
    'http': proxy_url,
    'https': proxy_url
}


url = "http://lumtest.com/myip.json"
response = requests.get(url, proxies=proxies)
pprint.pprint(response.json())



The code above uses the residential proxy to send a request to http://lumtest.com/myip.json. It returns your IP information in a JSON format:

{
  "ip": "ALLOCATED_IP",
  "country": "PK",
  "asn": {
    "asnum": 203020,
    "org_name": "HostRoyale Technologies Pvt Ltd"
  },
  "geo": {
    "city": "Islamabad",
    "region": "IS",
    "region_name": "Islamabad",
    "postal_code": "44040",
    "latitude": 33.7233,
    "longitude": 73.0435,
    "tz": "Asia/Karachi",
    "lum_city": "islamabad",
    "lum_region": "is"
  }
}


Now, replace “https://lumtest.com/myip.json” with the website of your choice and …

That’s it!





Proxy integration examples



import urllib.request
import ssl
import urllib.error

proxy = 'http://brd-customer-hl_caaa2374-zone-residential_proxy1:<EMAIL>:33335'
url = 'https://geo.brdtest.com/mygeo.json'

opener = urllib.request.build_opener(
    urllib.request.ProxyHandler({'https': proxy, 'http': proxy}),
    urllib.request.HTTPSHandler(context=ssl._create_unverified_context())
)

try:
    response = opener.open(url)
    print("Response Headers:")
    print('
'.join(f"{k}: {v}" for k, v in response.headers.items()))
    print("
Response Content:")
    print(response.read().decode('utf-8', errors='replace'))

except (urllib.error.HTTPError, urllib.error.URLError) as e:
    error_type = "HTTP Error" if hasattr(e, 'code') else "Connection Error"
    error_details = f"{e.code} {e.reason}" if hasattr(e, 'code') else e.reason
    print(f"{error_type}: {error_details}")

    if hasattr(e, 'headers'):
        print("
Error Response Headers:")
        print('
'.join(f"{k}: {v}" for k, v in e.headers.items()))

        print("
Error Response Content:")
        try:
            print(e.read().decode('utf-8', errors='replace'))
        except:
            print("No content available")





How to Integrate Bright Data With Selenium
Step 0. Prerequisites

Before starting, make sure you have:

1. Python installed: Download the latest version from python.org.

2. Install Selenium: Use pip to install the Selenium library


Copy
pip install selenium
3. Bright Data Proxy details: Obtain your proxy details (host, port, username, password) from the Bright Data dashboard.

4. WebDriver Installed: Obtain the appropriate WebDriver for your browser of choice (e.g., ChromeDriver for Google Chrome).

For easier driver setup, consider using the webdriver-manager package:


pip install webdriver-manager
Step 1. Import Required Libraries

Start by importing the necessary modules:



from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
Step 2. Configure Your Bright Data Proxy

Set up your Bright Data proxy credentials and construct the proxy server URL:



# Bright Data Proxy Configuration
proxy_host = "http://brd.superproxy.io/"
proxy_port = "port"  # Replace with your port number
proxy_username = "username"  # Replace with your Bright Data username
proxy_password = "password"  # Replace with your Bright Data password

# Full Proxy URL
proxy = f"http://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"
Step 3. Set Up Chrome Options

Use the Selenium ChromeOptions to configure the proxy:



# Configure Chrome Options
chrome_options = Options()
chrome_options.add_argument(f"--proxy-server={proxy}")
Step 4: Initialize the WebDriver

Specify the path to your ChromeDriver and initialize the WebDriver with the proxy settings:



# Initialize WebDriver
service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service, options=chrome_options)
Step 5: Test the Proxy

Use Selenium to navigate to a test URL and verify the proxy is working:



    # Navigate to a test site
    print("Connecting to target website...")
    driver.get("http://httpbin.org/ip")

    # Print the page content
    print("Page content:")
    print(driver.page_source)

    # Close the browser
    driver.quit()
Step 6. Verify the Output

When running the script, the IP address used by the proxy will be displayed in the browser or printed to the console. Example output:


{
  "origin": "************"
}
By integrating Bright Data proxies with Selenium, you can automate web interactions securely and efficiently. Whether you’re testing web applications, scraping dynamic content, or navigating geo-restricted websites, Bright Data ensures reliability and privacy for all your automation needs. Start building smarter workflows with Bright Data and Selenium today!





How to Use Bright Data with Scrapy
Integrate Bright Data with Scrapy to enhance your web scraping workflows. This guide provides a step-by-step configuration process to enable secure and anonymous connections for your Scrapy projects.


Expand to get your Bright Data Proxy Access Information

​
What is Scrapy?
Scrapy is a powerful Python-based framework for web scraping and data extraction. Designed for speed and scalability, Scrapy helps developers crawl websites and collect structured data efficiently. By integrating Bright Data proxies into Scrapy, you can enhance your scraping tasks with secure, anonymous, and geo-targeted connections.

​
Why Use Bright Data with Scrapy?
Enhanced Privacy: Mask your IP to stay anonymous while scraping.

Geo-Targeted Data Access: Use Bright Data’s country-specific proxies to gather data from different regions.

Improved Reliability: Reduce the risk of detection or being blocked by distributing requests across Bright Data proxies.

​
How to Set Up and Start a Scrapy Project
Step 0. Prerequisites

Before you begin, ensure you have:

1. Python Installed:

Download and install the latest version from python.org.
2. Scrapy Installed: Run the following command in your terminal to install Scrapy:


Copy
pip install scrapy
3. Bright Data Proxy Credentials:

Log in to your Bright Data dashboard and retrieve your proxy details (Host, Port, Username, and Password).

For region-specific proxies, modify your username using the format your-username-country-XX (e.g., your-username-country-US for a US proxy).

Step 1. Create or Open Your Scrapy Project

1. If you don’t have a Scrapy project, create one by running:


Copy
   scrapy startproject myproject
Replace “myproject” with a name that reflects the purpose of your project, such as “brightdata_test” or “web_scraper”.

2. Navigate to your project folder:


Copy
   cd myproject
Step 2. Generate a Spider

1. Use Scrapy’s command to create a spider:


Copy
 scrapy genspider <spider_name> <target_url>
For example, to scrape httpbin.org/ip, you can run:


Copy
scrapy genspider BrightDataExample http://httpbin.org/ip
2. This generates a basic spider template located in the spiders/ directory of your project. It looks something like this:


Copy
import scrapy

class BrightDataExampleSpider(scrapy.Spider):
  name = "BrightDataExample"
  allowed_domains = ["httpbin.org/ip"]
  start_urls = ["http://httpbin.org/ip"]

  def parse(self, response):
      pass
Step 3. Configure Bright Data Proxies

1. Open the generated spider file in a text editor (spiders/BrightDataExample.py) and update it to include Bright Data proxy settings. Here’s an example:


Copy
import scrapy

class BrightDataExampleSpider(scrapy.Spider):
    name = "BrightDataExample"
    start_urls = ['http://httpbin.org/ip']

    def start_requests(self):
        # Define the Bright Data proxy
        proxy = "http://[USERNAME]:[PASSWORD]@[HOST]:[PORT]"  # Replace with your Bright Data proxy details

        # Use the proxy for all requests
        for url in self.start_urls:
            yield scrapy.Request(url, meta={'proxy': proxy})

    def parse(self, response):
        # Parse and return the IP address
        yield {
            'proxy_ip': response.text
        }
2. Replace [USERNAME], [PASSWORD], [HOST], and [PORT] with your Bright Data credentials. If you need a country-specific proxy, modify the username (e.g., your-username-country-US).

Step 4. Run Your Scrapy Spider

1. Navigate to the project directory in your terminal:


Copy
cd myproject
2. Run the spider:


Copy
scrapy crawl BrightDataExample
3. To save the output to a file, use:


Copy
scrapy crawl BrightDataExample -o output.json
Step 5. Verify the Output

1. If everything is configured correctly, the spider will display the IP address of the Bright Data proxy it’s using. Example output:


Copy
[
    {
        "proxy_ip": "{\n  \"origin\": \"************\"\n}"
    }
]
2. Open the output.json file (if you used the -o flag) to review the scraped data.

With Bright Data proxies integrated into Scrapy, your web scraping tasks become more secure, private, and efficient. Whether you’re collecting geo-specific data, managing high-volume scraping jobs, or avoiding detection, Bright Data provides the stability and anonymity you need. Start scraping smarter with Bright Data and Scrapy today!
