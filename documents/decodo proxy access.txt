username: sp27lq3bep

password: l3Se+1na7Rghy8QhKj





http proxy list:

http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000
http://sp27lq3bep:<EMAIL>:7000




https proxy list:

https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000
https://sp27lq3bep:<EMAIL>:7000








import requests
url = 'https://ip.decodo.com/json'
username = 'sp27lq3bep'
password = 'l3Se+1na7Rghy8QhKj'
proxy = f"http://{username}:{password}@gate.decodo.com:10001"
result = requests.get(url, proxies = {
    'http': proxy,
    'https': proxy
})
print(result.text)
