# Anti-Scraping Framework Documentation

This document provides comprehensive documentation for the anti-scraping framework used in the Trend Crawler project.

## Overview

The anti-scraping framework is designed to help web scrapers evade detection mechanisms employed by modern websites. It provides a suite of tools and techniques to make scraping more reliable and less likely to be blocked.

## Core Components

### ScrapingShield

The `ScrapingShield` class is the main component of the framework. It provides:

- **User Agent Rotation**: Cycles through realistic user agents to avoid fingerprinting
- **Proxy Rotation**: Distributes requests across multiple proxies to avoid IP-based rate limiting
- **TLS Fingerprint Spoofing**: Mimics legitimate browser TLS fingerprints
- **WAF Bypass**: Techniques to bypass Web Application Firewalls
- **Honeypot Detection**: Identifies and avoids honeypot traps
- **CAPTCHA Handling**: Integrates with CAPTCHA solving services

### ProxyManager

The `ProxyManager` class provides advanced proxy management capabilities:

- **Health Checking**: Regularly checks proxy health and connectivity
- **Smart Rotation**: Rotates proxies based on performance metrics
- **Performance Tracking**: Tracks proxy success rates and latency
- **Geographic Distribution**: Manages proxies from different geographic regions
- **Proxy Chaining**: Creates proxy chains for enhanced anonymity

### CaptchaSolver

The `CaptchaSolver` class provides unified CAPTCHA solving capabilities:

- **Multiple Services**: Integrates with 2Captcha, Anti-Captcha, and CapSolver
- **Multiple CAPTCHA Types**: Handles reCAPTCHA v2, hCaptcha, image CAPTCHAs, and Turnstile
- **Solution Caching**: Caches solutions to avoid repeated CAPTCHA solving costs
- **Automatic Detection**: Can detect CAPTCHA type from HTML content
- **Proxy Integration**: Can use proxies for CAPTCHA solving

### Monitoring System

The `ScraperMonitor` class provides:

- **Request Metrics**: Tracks success/failure rates, latencies, and other metrics
- **Proxy Performance**: Monitors proxy reliability and performance
- **Alerting**: Sends alerts when error rates or other metrics exceed thresholds
- **Reporting**: Generates reports on scraping performance

## Installation

Ensure all dependencies are installed:

```bash
pip install -r requirements.txt
```

## Configuration

The framework is configured via the `proxy_config.json` file. Here's an example configuration:

```json
{
  "proxies": [
    {
      "protocol": "http",
      "host": "proxy.example.com",
      "port": 8080,
      "username": "user",
      "password": "pass",
      "id": "proxy1"
    }
  ],
  "max_retries": 3,
  "retry_delay": 2,
  "exponential_backoff": true,
  "rotation_strategy": "weighted_random",
  "health_check_interval": 300,
  "max_consecutive_failures": 10,
  "db": {
    "enabled": true,
    "host": "localhost",
    "name": "scraper_metrics",
    "user": "postgres",
    "password": "password"
  },
  "alert_thresholds": {
    "error_rate": 20,
    "captcha_rate": 10,
    "blocked_rate": 15,
    "latency": 10
  },
  "captcha": {
    "twocaptcha_key": "your_2captcha_api_key",
    "anticaptcha_key": "your_anticaptcha_api_key",
    "capsolver_key": "your_capsolver_api_key",
    "service_priority": {
      "2captcha": 1,
      "anticaptcha": 2,
      "capsolver": 3
    }
  },
  "alert_channels": {
    "email": {
      "enabled": true,
      "smtp_server": "smtp.example.com",
      "smtp_port": 587,
      "use_tls": true,
      "username": "<EMAIL>",
      "password": "password",
      "from_address": "<EMAIL>",
      "recipients": ["<EMAIL>"]
    },
    "slack": {
      "enabled": true,
      "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz"
    },
    "log": {
      "enabled": true
    }
  }
}
```

## Usage

### Basic Usage

```python
from scraping_shield import ScrapingShield
from proxy_manager import proxy_manager
from captcha_solver import captcha_solver

# Initialize the shield
shield = ScrapingShield()

# Make a request with anti-detection measures
async def fetch_url(url):
    response = await shield.make_request(url)
    return response.text

# Get enhanced headers for requests
headers = shield.get_enhanced_headers()

# Get a proxy based on performance criteria
proxy = proxy_manager.get_proxy(country="US", min_success_rate=80.0)
proxy_url = proxy_manager.format_for_requests(proxy)

# Solve a CAPTCHA
solution = captcha_solver.solve_recaptcha_v2(
    site_key="6LdqmVQiAAAAAAObwP1S18Gw_91h5-JCFaEWFEBB",
    page_url="https://example.com/login",
    proxy=proxy
)
```

### Monitoring Usage

```python
from monitoring import monitor, monitor_scraper

# Start the monitoring system
monitor.start_monitoring()

# Decorate a scraper function with monitoring
@monitor_scraper("my_scraper")
def scrape_website(url):
    # Scraping code here
    return {
        "status_code": 200,
        "data": {...}
    }

# Get statistics for a scraper
stats = monitor.get_scraper_stats("my_scraper", hours=24)

# Set up alerts for high error rates
monitor.set_alert_threshold("error_rate", 25.0, "email")
```

## Integration with Scrapers

### TradingView Scraper

The framework is integrated with the TradingView scraper, providing:

- Enhanced proxy rotation
- Better detection evasion
- CAPTCHA solving for login
- Monitoring and alerting

### Twitter/X Scraper

The Twitter/X scraper is enhanced with:

- Anti-detection measures
- CAPTCHA solving
- Rate limit handling
- Proxy rotation based on geography
- Reliable session management

## Advanced Features

### Distributed Scraping

The framework supports distributed scraping architecture:

```python
# Example of distributed scraping setup
from scraping_shield import ScrapingShield
import distributed

# Create a distributed client
client = distributed.Client()

# Create multiple shield instances
shields = [ScrapingShield() for _ in range(5)]

# Submit tasks to the cluster
futures = [client.submit(shields[i % len(shields)].make_request, url)
           for i, url in enumerate(urls)]

# Gather results
results = client.gather(futures)
```

### CAPTCHA Solving

The framework integrates with multiple CAPTCHA solving services:

- 2Captcha
- Anti-Captcha
- CapSolver

Configure your API keys in environment variables:

```bash
export TWOCAPTCHA_API_KEY=your_api_key
export ANTI_CAPTCHA_API_KEY=your_api_key
export CAPSOLVER_API_KEY=your_api_key
```

### Proxy Management

Advanced proxy management features:

```python
# Get the best performing proxies
best_proxies = proxy_manager.get_best_performing_proxies(count=5)

# Set up a proxy chain for enhanced anonymity
proxy_chain = proxy_manager.setup_proxy_chain(target_country="DE")
chain_url = proxy_manager.format_proxy_chain_url(proxy_chain)

# Check health of all proxies
health_results = proxy_manager.check_proxy_health()

# Get statistics for a specific proxy
proxy_stats = proxy_manager.get_proxy_stats("proxy1")
```

## Error Handling

The framework includes robust error handling:

- Automatic retries with exponential backoff
- Detailed error logging
- Specific handling for common scraping errors (timeouts, blocked IPs, etc.)
- Proxy failover on connection failures

```python
from scraping_shield import ScrapingShield, RetryStrategy

shield = ScrapingShield()

# Configure retry strategy
shield.set_retry_strategy(
    RetryStrategy(
        max_retries=5,
        delay=2,
        backoff_factor=2.0,
        retry_on_status_codes=[429, 500, 502, 503, 504],
        retry_on_exceptions=[ConnectionError, TimeoutError]
    )
)
```

## Testing

Comprehensive tests are available in the `test_anti_scraping_comprehensive.py` file. Run them with:

```bash
pytest test_anti_scraping_comprehensive.py -v
```

## Best Practices

When using the anti-scraping framework:

1. **Respect Rate Limits**: Add delays between requests to avoid overwhelming the target
2. **Rotate Proxies**: Use proxy rotation for high-volume scraping
3. **Monitor Error Rates**: Watch for increasing error rates, which may indicate detection
4. **Set Realistic User Agents**: Use up-to-date and realistic user agents
5. **Add Randomization**: Add random delays and behavioral patterns
6. **Handle CAPTCHAs**: Implement proper CAPTCHA handling for targeted sites
7. **Respect Robots.txt**: Consider the `robots.txt` policies of target websites

## Troubleshooting

Common issues and solutions:

1. **High Error Rates**: Check proxy health and rotation
2. **CAPTCHA Frequency**: Your scraping pattern may be too aggressive, add delays
3. **IP Blocking**: Use residential proxies and ensure proper rotation
4. **Database Connection Issues**: Verify PostgreSQL configuration
5. **CAPTCHA Solving Failures**: Check API keys and service availability

## Contributing

When extending the framework:

1. Follow the existing error handling patterns
2. Add tests for new functionality
3. Update this documentation
4. Ensure backward compatibility

## License

This framework is proprietary and confidential. Unauthorized use is prohibited.