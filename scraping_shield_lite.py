#!/usr/bin/env python3
"""
Lightweight version of ScrapingShield for testing and basic anti-scraping.
This version has minimal dependencies and lazy initialization.
"""

import os
import json
import random
import logging
import time
import ssl
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Union
import traceback

# Configure logging
logger = logging.getLogger(__name__)


class ScrapingShieldLite:
    """Lightweight anti-detection system for web scraping."""

    def __init__(self, config_path: str = "proxy_config.json"):
        """Initialize the lightweight scraping shield."""
        self.logger = logging.getLogger(__name__)
        
        try:
            # Load basic configuration
            self.config = self._load_config(config_path)
            
            # Initialize basic attributes without heavy dependencies
            self.proxy_pool = []
            self.current_proxy = None
            self.fingerprint_pool = []
            self.captcha_solver = None
            
            # Set retry parameters
            self.max_retries = self.config.get('max_retries', 3)
            self.retry_delay = self.config.get('retry_delay', 2)
            self.exponential_backoff = self.config.get('exponential_backoff', True)
            
            # Initialize basic user agent pool
            self.user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
            
            logger.info("ScrapingShieldLite initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ScrapingShieldLite: {e}")
            logger.debug(f"Initialization error details: {traceback.format_exc()}")
            # Don't raise - allow fallback initialization
            self.config = {}
            self.proxy_pool = []
            self.user_agents = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36']
            self.max_retries = 3
            self.retry_delay = 2
            self.exponential_backoff = True
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from file."""
        if not os.path.exists(config_path):
            logger.warning(f"Config file {config_path} not found, using defaults")
            return {}
            
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                logger.info(f"Configuration loaded from {config_path}")
                return config
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            return {}
    
    def rotate_user_agent(self) -> str:
        """Get a random user agent string."""
        return random.choice(self.user_agents)
    
    def get_enhanced_headers(self) -> Dict[str, str]:
        """Get enhanced browser headers."""
        return {
            'User-Agent': self.rotate_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    
    def waf_bypass_headers(self, url: str) -> Dict[str, str]:
        """Generate WAF bypass headers."""
        headers = self.get_enhanced_headers()
        
        # Add additional WAF bypass headers
        headers.update({
            'X-Forwarded-For': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'X-Real-IP': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'X-Originating-IP': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'X-Remote-IP': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
            'X-Client-IP': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
        })
        
        return headers
    
    def spoof_tls_fingerprint(self, browser_type: str = "chrome") -> ssl.SSLContext:
        """Create a spoofed TLS fingerprint."""
        context = ssl.create_default_context()
        
        # Basic TLS configuration that mimics browser behavior
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        if browser_type.lower() == "chrome":
            context.set_ciphers(
                'TLS_AES_128_GCM_SHA256:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_256_GCM_SHA384:'
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256'
            )
        elif browser_type.lower() == "firefox":
            context.set_ciphers(
                'TLS_AES_128_GCM_SHA256:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_256_GCM_SHA384:'
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256'
            )
        
        return context
    
    def detect_captcha(self, html_content: str) -> bool:
        """Detect if CAPTCHA is present in HTML content."""
        captcha_indicators = [
            'g-recaptcha',
            'h-captcha',
            'captcha',
            'recaptcha',
            'hcaptcha',
            'data-sitekey',
            'data-site-key',
            'cf-turnstile'
        ]
        
        html_lower = html_content.lower()
        return any(indicator in html_lower for indicator in captcha_indicators)
    
    def detect_honeypot(self, response) -> bool:
        """Basic honeypot detection."""
        try:
            if hasattr(response, 'text'):
                content = response.text.lower()
            elif hasattr(response, 'content'):
                content = response.content.decode('utf-8', errors='ignore').lower()
            else:
                return False
            
            # Simple honeypot indicators
            honeypot_indicators = [
                'display:none',
                'visibility:hidden',
                'opacity:0',
                'bot trap',
                'honeypot',
                'robot check'
            ]
            
            return any(indicator in content for indicator in honeypot_indicators)
            
        except Exception as e:
            logger.debug(f"Error in honeypot detection: {e}")
            return False
    
    def make_request(self, url: str, **kwargs) -> Optional[object]:
        """Make a request with basic anti-detection headers."""
        try:
            import requests
            
            # Set up session with enhanced headers
            session = requests.Session()
            headers = self.get_enhanced_headers()
            
            # Update with any user-provided headers
            if 'headers' in kwargs:
                headers.update(kwargs['headers'])
            kwargs['headers'] = headers
            
            # Make the request
            response = session.get(url, **kwargs)
            return response
            
        except Exception as e:
            logger.error(f"Request failed: {e}")
            return None
    
    def simulate_human_behavior(self):
        """Basic human behavior simulation."""
        # Add a random delay
        delay = random.uniform(1, 3)
        logger.debug(f"Simulating human behavior with {delay:.2f}s delay")
        time.sleep(delay)
    
    def create_enhanced_headers(self) -> Dict[str, str]:
        """Alias for get_enhanced_headers for compatibility."""
        return self.get_enhanced_headers()
    
    def cleanup(self):
        """Clean up resources."""
        logger.debug("ScrapingShieldLite cleanup completed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


# Create helper functions that were causing import issues
def create_enhanced_headers() -> Dict[str, str]:
    """Create enhanced headers using a temporary ScrapingShieldLite instance."""
    try:
        shield = ScrapingShieldLite()
        return shield.get_enhanced_headers()
    except Exception as e:
        logger.error(f"Error creating enhanced headers: {e}")
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive'
        }


def rotate_user_agent() -> str:
    """Get a random user agent string."""
    try:
        shield = ScrapingShieldLite()
        return shield.rotate_user_agent()
    except Exception as e:
        logger.error(f"Error rotating user agent: {e}")
        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'


def detect_captcha_simple(html_content: str) -> bool:
    """Simple CAPTCHA detection."""
    try:
        shield = ScrapingShieldLite()
        return shield.detect_captcha(html_content)
    except Exception as e:
        logger.error(f"Error detecting CAPTCHA: {e}")
        return False


# For backward compatibility, alias the lite version as ScrapingShield
ScrapingShield = ScrapingShieldLite

if __name__ == "__main__":
    # Simple test
    shield = ScrapingShieldLite()
    print("Headers:", shield.get_enhanced_headers())
    print("User-Agent:", shield.rotate_user_agent())
    print("TLS Context:", shield.spoof_tls_fingerprint())
    print("ScrapingShieldLite test completed successfully!")
