#!/usr/bin/env python3
"""
ScrapingShield with lazy imports to avoid hanging on TensorFlow initialization.
"""

import os
import json
import random
import logging
import time
import ssl
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Union
import traceback

# Only import lightweight dependencies at module level
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError, HTTPError, ProxyError, SSLError
from fake_useragent import UserAgent
from bs4 import BeautifulSoup
import nest_asyncio

# Configure logging
logger = logging.getLogger(__name__)


class ScrapingShieldOptimized:
    """Optimized anti-detection system with lazy imports."""

    def __init__(self, config_path: str = "proxy_config.json"):
        """Initialize with minimal dependencies."""
        self.logger = logging.getLogger(__name__)
        
        try:
            # Load basic configuration
            self.config = self._load_config(config_path)
            
            # Initialize basic attributes
            self.proxy_pool = []
            self.current_proxy = None
            self.fingerprint_pool = []
            
            # Lazy import placeholders
            self._captcha_solver = None
            self._tf_loaded = False
            self._playwright_loaded = False
            self._browser = None
            self._page = None
            
            # Set retry parameters
            self.max_retries = self.config.get('max_retries', 3)
            self.retry_delay = self.config.get('retry_delay', 2)
            self.exponential_backoff = self.config.get('exponential_backoff', True)
            
            # Initialize user agent
            try:
                self.ua = UserAgent(fallback='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36')
            except Exception:
                # Fallback if UserAgent fails
                self.ua = None
            
            # Initialize basic user agent pool
            self.user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
            
            logger.info("ScrapingShieldOptimized initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ScrapingShieldOptimized: {e}")
            logger.debug(f"Initialization error details: {traceback.format_exc()}")
            # Fallback initialization
            self.config = {}
            self.proxy_pool = []
            self.user_agents = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36']
            self.max_retries = 3
            self.retry_delay = 2
            self.exponential_backoff = True
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from file."""
        if not os.path.exists(config_path):
            logger.warning(f"Config file {config_path} not found, using defaults")
            return {}
            
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                logger.info(f"Configuration loaded from {config_path}")
                return config
        except (json.JSONDecodeError, IOError) as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            return {}
    
    def _lazy_load_tensorflow(self):
        """Lazy load TensorFlow only when needed."""
        if not self._tf_loaded:
            try:
                logger.info("Loading TensorFlow (this may take a moment)...")
                # Use globals to avoid import hanging
                import importlib
                tf_module = importlib.import_module('tensorflow')
                globals()['tf'] = tf_module
                self._tf_loaded = True
                logger.info("TensorFlow loaded successfully")
                return tf_module
            except Exception as e:
                logger.warning(f"Failed to load TensorFlow: {e}")
                self._tf_loaded = False
                return None
        else:
            return globals().get('tf')
    
    def _lazy_load_playwright(self):
        """Lazy load Playwright only when needed."""
        if not self._playwright_loaded:
            try:
                from playwright.async_api import async_playwright
                self._playwright_module = async_playwright
                self._playwright_loaded = True
                logger.info("Playwright loaded successfully")
                return async_playwright
            except Exception as e:
                logger.warning(f"Failed to load Playwright: {e}")
                self._playwright_loaded = False
                return None
        else:
            return self._playwright_module
    
    def _lazy_load_captcha_solver(self):
        """Lazy load CAPTCHA solver only when needed."""
        if self._captcha_solver is None:
            try:
                if os.getenv('TWOCAPTCHA_API_KEY'):
                    from twocaptcha import TwoCaptcha
                    self._captcha_solver = TwoCaptcha(os.getenv('TWOCAPTCHA_API_KEY'))
                    logger.info("2Captcha solver initialized")
                elif os.getenv('CAPSOLVER_API_KEY'):
                    import capsolver
                    capsolver.api_key = os.getenv('CAPSOLVER_API_KEY')
                    self._captcha_solver = capsolver
                    logger.info("CapSolver initialized")
                else:
                    logger.warning("No CAPTCHA solving service API keys found")
            except Exception as e:
                logger.error(f"Failed to initialize CAPTCHA solver: {e}")
        return self._captcha_solver
    
    def rotate_user_agent(self) -> str:
        """Get a random user agent string."""
        try:
            if self.ua:
                return self.ua.random
            else:
                return random.choice(self.user_agents)
        except Exception:
            return random.choice(self.user_agents)
    
    def get_enhanced_headers(self) -> Dict[str, str]:
        """Get enhanced browser headers."""
        return {
            'User-Agent': self.rotate_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1'
        }
    
    def detect_honeypot(self, response) -> bool:
        """Basic honeypot detection."""
        try:
            if hasattr(response, 'text'):
                content = response.text.lower()
            elif hasattr(response, 'content'):
                content = response.content.decode('utf-8', errors='ignore').lower()
            else:
                return False
            
            # Honeypot indicators
            honeypot_indicators = [
                'display:none',
                'visibility:hidden',
                'opacity:0',
                'bot trap',
                'honeypot',
                'robot check',
                'bot detection',
                'please enable javascript',
                'access denied',
                'blocked',
                'suspicious activity'
            ]
            
            return any(indicator in content for indicator in honeypot_indicators)
            
        except Exception as e:
            logger.debug(f"Error in honeypot detection: {e}")
            return False
    
    def get_session_with_proxy(self) -> requests.Session:
        """Get a requests session with proxy if available."""
        session = requests.Session()
        
        try:
            # Set enhanced headers
            headers = self.get_enhanced_headers()
            session.headers.update(headers)
            
            # Configure proxy if available
            if self.proxy_pool:
                proxy = random.choice(self.proxy_pool)
                proxy_url = f"{proxy['protocol']}://"
                
                if proxy.get('username') and proxy.get('password'):
                    proxy_url += f"{proxy['username']}:{proxy['password']}@"
                
                proxy_url += f"{proxy['host']}:{proxy['port']}"
                
                session.proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }
                logger.debug(f"Using proxy: {proxy['host']}:{proxy['port']}")
            
            return session
            
        except Exception as e:
            logger.warning(f"Error setting up session: {e}")
            return session
    
    async def make_request(self, url: str, method: str = 'get', retries: int = None, **kwargs) -> Optional[requests.Response]:
        """Make a request with anti-detection measures."""
        retries = retries or self.max_retries
        
        for attempt in range(1, retries + 1):
            try:
                logger.debug(f"Request attempt {attempt}/{retries} to {url}")
                
                # Get session with proxy and headers
                session = self.get_session_with_proxy()
                method_func = getattr(session, method.lower())
                
                # Add delay on retry
                if attempt > 1:
                    delay = self.retry_delay
                    if self.exponential_backoff:
                        delay = delay * (2 ** (attempt - 1))
                    delay = delay * random.uniform(0.7, 1.3)  # Add jitter
                    time.sleep(delay)
                
                # Make the request
                response = method_func(url, timeout=30, **kwargs)
                response.raise_for_status()
                
                # Check for honeypot
                if self.detect_honeypot(response):
                    logger.warning(f"Honeypot detected at {url}")
                    continue
                
                logger.debug(f"Request successful: {response.status_code}")
                return response
                
            except Timeout:
                logger.warning(f"Request timeout on attempt {attempt}/{retries}")
            except ConnectionError:
                logger.warning(f"Connection error on attempt {attempt}/{retries}")
            except HTTPError as e:
                status_code = getattr(e.response, 'status_code', None)
                logger.warning(f"HTTP error {status_code} on attempt {attempt}/{retries}")
                
                if status_code in [403, 429]:
                    logger.info("Rotating user agent due to access restrictions")
                    # This will get a new session with new headers on next attempt
            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt}/{retries}: {e}")
        
        logger.error(f"All {retries} request attempts to {url} failed")
        return None
    
    def solve_captcha_if_needed(self, site_key: str, url: str, captcha_type: str = "recaptcha") -> Optional[str]:
        """Solve CAPTCHA only if solver is available."""
        try:
            solver = self._lazy_load_captcha_solver()
            if not solver:
                logger.warning("No CAPTCHA solver available")
                return None
            
            logger.info(f"Attempting to solve {captcha_type} CAPTCHA")
            
            if hasattr(solver, 'recaptcha'):
                # 2Captcha
                result = solver.recaptcha(sitekey=site_key, url=url)
                return result.get('code') if result else None
            elif hasattr(solver, 'solve'):
                # CapSolver
                solution = solver.solve({
                    "type": "ReCaptchaV2TaskProxyLess",
                    "websiteURL": url,
                    "websiteKey": site_key
                })
                return solution.get('gRecaptchaResponse') if solution else None
            
        except Exception as e:
            logger.error(f"CAPTCHA solving failed: {e}")
            return None
    
    async def init_browser_if_needed(self):
        """Initialize browser only when needed."""
        if self._browser is None:
            playwright = self._lazy_load_playwright()
            if not playwright:
                raise RuntimeError("Playwright not available")
            
            playwright_instance = await playwright().start()
            
            browser_options = {
                'args': [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                ],
                'ignoreDefaultArgs': ['--enable-automation'],
                'headless': True
            }
            
            self._browser = await playwright_instance.chromium.launch(**browser_options)
            self._page = await self._browser.new_page()
            
            logger.info("Browser initialized successfully")
        
        return self._browser, self._page
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self._browser:
                # Note: In real usage, you'd want to await this
                logger.info("Browser cleanup - use await browser.close() in async context")
            logger.debug("ScrapingShieldOptimized cleanup completed")
        except Exception as e:
            logger.warning(f"Cleanup error: {e}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


# Alias for backward compatibility
ScrapingShield = ScrapingShieldOptimized

if __name__ == "__main__":
    # Test the optimized version
    shield = ScrapingShieldOptimized()
    print("✅ ScrapingShieldOptimized initialized")
    print("Headers:", shield.get_enhanced_headers())
    print("User-Agent:", shield.rotate_user_agent())
    print("✅ Basic functionality works")
