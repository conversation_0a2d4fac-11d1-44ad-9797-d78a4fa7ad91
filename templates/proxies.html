{% extends "base.html" %}

{% block content %}
<div class="proxy-controls">
    <div class="form-group">
        <label for="proxy-group-filter">Filter by Group:</label>
        <select id="proxy-group-filter" class="form-control">
            <option value="all">All Groups</option>
            <option value="us">US</option>
            <option value="eu">EU</option>
            <option value="asia">Asia</option>
            <option value="residential">Residential</option>
            <option value="datacenter">Datacenter</option>
        </select>
    </div>
    <div class="form-group">
        <label for="proxy-status-filter">Filter by Status:</label>
        <select id="proxy-status-filter" class="form-control">
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="error">Error</option>
            <option value="banned">Banned</option>
        </select>
    </div>
    <div class="form-group">
        <label for="time-range">Time Range:</label>
        <select id="time-range" class="form-control">
            <option value="24">Last 24 Hours</option>
            <option value="48">Last 48 Hours</option>
            <option value="168">Last 7 Days</option>
            <option value="720">Last 30 Days</option>
        </select>
    </div>
    <button id="add-proxy" class="btn">Add Proxy</button>
    <button id="refresh-proxies" class="btn">Refresh Status</button>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Proxy Health Overview</h2>
        <div class="chart-container">
            <canvas id="proxyHealthChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Success Rate by Group</h2>
        <div class="chart-container">
            <canvas id="successRateChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Proxy Response Time</h2>
        <div class="chart-container">
            <canvas id="responseTimeChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Proxy Usage</h2>
        <div class="chart-container">
            <canvas id="usageChart"></canvas>
        </div>
    </div>
</div>

<div class="dashboard-card">
    <h2>Active Proxies</h2>
    <div class="table-container" id="proxyTable">
        <table>
            <thead>
                <tr>
                    <th>IP Address</th>
                    <th>Port</th>
                    <th>Type</th>
                    <th>Group</th>
                    <th>Location</th>
                    <th>Status</th>
                    <th>Success Rate</th>
                    <th>Response Time</th>
                    <th>Last Used</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="proxyTableBody">
                <tr>
                    <td colspan="10" class="loading-cell">Loading proxies...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Add Proxy Modal -->
<div id="addProxyModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Add Proxy</h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="proxy-ip">IP Address:</label>
                <input type="text" id="proxy-ip" class="form-control" placeholder="e.g. ***********">
            </div>
            <div class="form-group">
                <label for="proxy-port">Port:</label>
                <input type="number" id="proxy-port" class="form-control" placeholder="e.g. 8080">
            </div>
            <div class="form-group">
                <label for="proxy-type">Type:</label>
                <select id="proxy-type" class="form-control">
                    <option value="http">HTTP</option>
                    <option value="https">HTTPS</option>
                    <option value="socks4">SOCKS4</option>
                    <option value="socks5">SOCKS5</option>
                </select>
            </div>
            <div class="form-group">
                <label for="proxy-group">Group:</label>
                <select id="proxy-group" class="form-control">
                    <option value="us">US</option>
                    <option value="eu">EU</option>
                    <option value="asia">Asia</option>
                    <option value="residential">Residential</option>
                    <option value="datacenter">Datacenter</option>
                </select>
            </div>
            <div class="form-group">
                <label for="proxy-username">Username (optional):</label>
                <input type="text" id="proxy-username" class="form-control">
            </div>
            <div class="form-group">
                <label for="proxy-password">Password (optional):</label>
                <input type="password" id="proxy-password" class="form-control">
            </div>
            <div class="form-group">
                <label for="proxy-location">Location (optional):</label>
                <input type="text" id="proxy-location" class="form-control" placeholder="e.g. New York, US">
            </div>
        </div>
        <div class="modal-footer">
            <button id="cancel-proxy" class="btn btn-secondary">Cancel</button>
            <button id="save-proxy" class="btn">Save Proxy</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initProxyHealthChart();
    initSuccessRateChart();
    initResponseTimeChart();
    initUsageChart();
    
    // Load proxy table
    loadProxies();
    
    // Add event listeners
    document.getElementById('add-proxy').addEventListener('click', showAddProxyModal);
    document.getElementById('refresh-proxies').addEventListener('click', refreshProxies);
    document.getElementById('proxy-group-filter').addEventListener('change', applyFilters);
    document.getElementById('proxy-status-filter').addEventListener('change', applyFilters);
    document.getElementById('time-range').addEventListener('change', refreshAllData);
    
    // Modal event listeners
    const modal = document.getElementById('addProxyModal');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = document.getElementById('cancel-proxy');
    const saveBtn = document.getElementById('save-proxy');
    
    closeBtn.addEventListener('click', hideAddProxyModal);
    cancelBtn.addEventListener('click', hideAddProxyModal);
    saveBtn.addEventListener('click', saveProxy);
    
    // Close modal if clicked outside
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            hideAddProxyModal();
        }
    });
    
    function showAddProxyModal() {
        modal.style.display = 'block';
    }
    
    function hideAddProxyModal() {
        modal.style.display = 'none';
        // Clear form inputs
        document.getElementById('proxy-ip').value = '';
        document.getElementById('proxy-port').value = '';
        document.getElementById('proxy-username').value = '';
        document.getElementById('proxy-password').value = '';
        document.getElementById('proxy-location').value = '';
    }
    
    function saveProxy() {
        // Get form values
        const ip = document.getElementById('proxy-ip').value;
        const port = document.getElementById('proxy-port').value;
        const type = document.getElementById('proxy-type').value;
        const group = document.getElementById('proxy-group').value;
        const username = document.getElementById('proxy-username').value;
        const password = document.getElementById('proxy-password').value;
        const location = document.getElementById('proxy-location').value;
        
        if (!ip || !port) {
            alert('IP address and port are required');
            return;
        }
        
        // Create proxy data object
        const proxyData = {
            ip,
            port: parseInt(port, 10),
            type,
            group,
            location
        };
        
        if (username) proxyData.username = username;
        if (password) proxyData.password = password;
        
        // Show loading indicator
        const saveBtn = document.getElementById('save-proxy');
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';
        
        // Save proxy to API endpoint
        fetch('/api/v1/proxies', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(proxyData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            alert('Proxy added successfully');
            hideAddProxyModal();
            refreshAllData();
        })
        .catch(error => {
            console.error('Error adding proxy:', error);
            alert(`Error adding proxy: ${error.message}`);
        })
        .finally(() => {
            // Reset button
            saveBtn.disabled = false;
            saveBtn.textContent = 'Save Proxy';
        });
    }
    
    function refreshProxies() {
        const refreshBtn = document.getElementById('refresh-proxies');
        refreshBtn.disabled = true;
        refreshBtn.textContent = 'Refreshing...';
        
        // Refresh all data
        refreshAllData().finally(() => {
            refreshBtn.disabled = false;
            refreshBtn.textContent = 'Refresh Status';
        });
    }
    
    function applyFilters() {
        loadProxies();
    }
    
    function refreshAllData() {
        // Refresh all charts and data
        return Promise.all([
            new Promise(resolve => {
                initProxyHealthChart();
                resolve();
            }),
            new Promise(resolve => {
                initSuccessRateChart();
                resolve();
            }),
            new Promise(resolve => {
                initResponseTimeChart();
                resolve();
            }),
            new Promise(resolve => {
                initUsageChart();
                resolve();
            }),
            new Promise(resolve => {
                loadProxies();
                resolve();
            })
        ]);
    }
    
    function loadProxies() {
        const groupFilter = document.getElementById('proxy-group-filter').value;
        const statusFilter = document.getElementById('proxy-status-filter').value;
        const tableBody = document.getElementById('proxyTableBody');
        
        tableBody.innerHTML = '<tr><td colspan="10" class="loading-cell">Loading proxies...</td></tr>';
        
        // Fetch real proxy data from API
        const timeRange = document.getElementById('time-range') ? document.getElementById('time-range').value : 24;
        const url = `/api/v1/stats/proxies?hours=${timeRange}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load proxy data');
                }
                return response.json();
            })
            .then(data => {
                displayProxies(data);
            })
            .catch(error => {
                console.error('Error loading proxies:', error);
                tableBody.innerHTML = `<tr><td colspan="10" class="error-cell">Error loading proxy data: ${error.message}</td></tr>`;
            });
    }
    
    function displayProxies(proxyData) {
        const groupFilter = document.getElementById('proxy-group-filter').value;
        const statusFilter = document.getElementById('proxy-status-filter').value;
        const tableBody = document.getElementById('proxyTableBody');
        let tableContent = '';
        
        // Check if we have any proxy data
        if (!proxyData || Object.keys(proxyData).length === 0) {
            tableBody.innerHTML = '<tr><td colspan="10" class="info-cell">No proxy data available</td></tr>';
            return;
        }
        
        // Process real API data
        Object.entries(proxyData).forEach(([proxyId, stats]) => {
            // Extract proxy details from the ID (format usually ip:port)
            let [ip, port] = proxyId.split(':');
            if (!port) port = ''; // Handle case where proxy_id format is different
            
            // Extract or default other values
            const type = stats.type || 'HTTP';
            const group = stats.group || 'unknown';
            const location = stats.location || 'Unknown';
            
            // Determine status based on performance metrics
            let status = 'inactive';
            if (stats.total_requests && stats.total_requests > 0) {
                if (stats.success_rate > 90) {
                    status = 'active';
                } else if (stats.success_rate > 50) {
                    status = 'warning';
                } else {
                    status = 'error';
                }
            }
            
            // Apply filters
            if ((groupFilter !== 'all' && group !== groupFilter) || 
                (statusFilter !== 'all' && status !== statusFilter)) {
                return; // Skip this proxy
            }
            
            // Format metrics
            const successRate = stats.success_rate || 0;
            const responseTime = stats.avg_response_time ? Math.round(stats.avg_response_time * 1000) : 0; // Convert to ms
            const lastUsed = stats.last_used || 'Never';
            
            // Determine CSS classes
            const statusClass = `status-${status}`;
            const successRateClass = successRate > 90 ? 'high' : successRate > 70 ? 'medium' : 'low';
            
            tableContent += `
                <tr>
                    <td><code>${ip}</code></td>
                    <td>${port}</td>
                    <td>${type}</td>
                    <td>${group.toUpperCase()}</td>
                    <td>${location}</td>
                    <td><span class="${statusClass}">${status.charAt(0).toUpperCase() + status.slice(1)}</span></td>
                    <td><span class="success-rate ${successRateClass}">${successRate.toFixed(1)}%</span></td>
                    <td>${responseTime} ms</td>
                    <td>${formatDateString(lastUsed)}</td>
                    <td>
                        <button class="btn-icon" title="Test Proxy"><i class="fas fa-vial"></i></button>
                        <button class="btn-icon" title="Edit Proxy"><i class="fas fa-edit"></i></button>
                        <button class="btn-icon" title="Delete Proxy"><i class="fas fa-trash"></i></button>
                    </td>
                </tr>
            `;
        });
        
        if (tableContent === '') {
            tableBody.innerHTML = '<tr><td colspan="10" class="info-cell">No proxies match the current filters</td></tr>';
        } else {
            tableBody.innerHTML = tableContent;
        }
    }
    
    // Helper function to format dates
    function formatDateString(dateStr) {
        if (!dateStr || dateStr === 'Never') return 'Never';
        
        try {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr; // Return original if invalid
            
            return date.toLocaleString();
        } catch (e) {
            return dateStr; // Return original on error
        }
    }
    }
    
    function initProxyHealthChart() {
        const ctx = document.getElementById('proxyHealthChart').getContext('2d');
        
        // First, fetch proxy data from API
        const timeRange = document.getElementById('time-range') ? document.getElementById('time-range').value : 24;
        fetch(`/api/v1/stats/proxies?hours=${timeRange}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load proxy data');
                }
                return response.json();
            })
            .then(data => {
                // Calculate proxy health statistics from real data
                let active = 0, inactive = 0, error = 0, banned = 0;
                
                Object.values(data).forEach(proxy => {
                    if (!proxy.total_requests || proxy.total_requests === 0) {
                        inactive++;
                    } else if (proxy.success_rate > 90) {
                        active++;
                    } else if (proxy.success_rate > 50) {
                        error++; // Minor issues
                    } else {
                        banned++; // Major issues, likely blocked
                    }
                });
                
                // If no data, show empty state
                if (Object.keys(data).length === 0) {
                    active = 0;
                    inactive = 1;
                    error = 0;
                    banned = 0;
                }
                
                // Create chart with real data
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Active', 'Inactive', 'Warning', 'Error'],
                        datasets: [{
                            data: [active, inactive, error, banned],
                            backgroundColor: [
                                'rgba(46, 204, 113, 0.2)',
                                'rgba(52, 152, 219, 0.2)',
                                'rgba(243, 156, 18, 0.2)',
                                'rgba(231, 76, 60, 0.2)'
                            ],
                            borderColor: [
                                'rgba(46, 204, 113, 1)',
                                'rgba(52, 152, 219, 1)',
                                'rgba(243, 156, 18, 1)',
                                'rgba(231, 76, 60, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            })
            .catch(error => {
                console.error('Error loading proxy health data:', error);
                // Create empty chart on error
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['No Data Available'],
                        datasets: [{
                            data: [1],
                            backgroundColor: ['rgba(200, 200, 200, 0.2)'],
                            borderColor: ['rgba(200, 200, 200, 1)'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            });
    }
    
    function initSuccessRateChart() {
        const ctx = document.getElementById('successRateChart').getContext('2d');
        
        // Fetch real proxy data from API
        const timeRange = document.getElementById('time-range') ? document.getElementById('time-range').value : 24;
        fetch(`/api/v1/stats/proxies?hours=${timeRange}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load proxy data');
                }
                return response.json();
            })
            .then(data => {
                // Group proxies by their group attribute
                const groups = {};
                
                Object.entries(data).forEach(([proxyId, stats]) => {
                    // Extract group from proxy data, default to "unknown" if not present
                    const group = stats.group || getGroupFromProxyId(proxyId);
                    
                    if (!groups[group]) {
                        groups[group] = {
                            totalRequests: 0,
                            successfulRequests: 0
                        };
                    }
                    
                    groups[group].totalRequests += stats.total_requests || 0;
                    groups[group].successfulRequests += stats.successful_requests || 0;
                });
                
                // Calculate success rates
                const labels = [];
                const successRates = [];
                
                Object.entries(groups).forEach(([group, stats]) => {
                    labels.push(group.toUpperCase());
                    const rate = stats.totalRequests > 0 ? 
                        (stats.successfulRequests / stats.totalRequests) * 100 : 0;
                    successRates.push(rate);
                });
                
                // If no data, show empty state
                if (labels.length === 0) {
                    labels.push('No Data');
                    successRates.push(0);
                }
                
                // Create chart with real data
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Success Rate (%)',
                            data: successRates,
                            backgroundColor: 'rgba(46, 204, 113, 0.2)',
                            borderColor: 'rgba(46, 204, 113, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading success rate data:', error);
                // Create empty chart on error
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['No Data Available'],
                        datasets: [{
                            label: 'Success Rate (%)',
                            data: [0],
                            backgroundColor: 'rgba(200, 200, 200, 0.2)',
                            borderColor: 'rgba(200, 200, 200, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            });
    }
    
    function initResponseTimeChart() {
        const ctx = document.getElementById('responseTimeChart').getContext('2d');
        
        // Fetch real proxy data from API
        const timeRange = document.getElementById('time-range') ? document.getElementById('time-range').value : 24;
        fetch(`/api/v1/stats/proxies?hours=${timeRange}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load proxy data');
                }
                return response.json();
            })
            .then(data => {
                // Group proxies by their group attribute
                const groups = {};
                
                Object.entries(data).forEach(([proxyId, stats]) => {
                    // Extract group from proxy data
                    const group = stats.group || getGroupFromProxyId(proxyId);
                    
                    if (!groups[group]) {
                        groups[group] = {
                            totalResponseTime: 0,
                            count: 0
                        };
                    }
                    
                    if (stats.avg_response_time) {
                        groups[group].totalResponseTime += stats.avg_response_time * 1000; // Convert to ms
                        groups[group].count++;
                    }
                });
                
                // Calculate average response times
                const labels = [];
                const responseTimes = [];
                
                Object.entries(groups).forEach(([group, stats]) => {
                    labels.push(group.toUpperCase());
                    const avgTime = stats.count > 0 ? 
                        Math.round(stats.totalResponseTime / stats.count) : 0;
                    responseTimes.push(avgTime);
                });
                
                // If no data, show empty state
                if (labels.length === 0) {
                    labels.push('No Data');
                    responseTimes.push(0);
                }
                
                // Create chart with real data
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Avg. Response Time (ms)',
                            data: responseTimes,
                            backgroundColor: 'rgba(52, 152, 219, 0.2)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading response time data:', error);
                // Create empty chart on error
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['No Data Available'],
                        datasets: [{
                            label: 'Avg. Response Time (ms)',
                            data: [0],
                            backgroundColor: 'rgba(200, 200, 200, 0.2)',
                            borderColor: 'rgba(200, 200, 200, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
    }
    
    function initUsageChart() {
        const ctx = document.getElementById('usageChart').getContext('2d');
        
        // Fetch real proxy data from API - for usage chart we would ideally have time-series data
        // Since the current API doesn't provide historical data, we'll aggregate by group
        const timeRange = document.getElementById('time-range') ? document.getElementById('time-range').value : 24;
        fetch(`/api/v1/stats/proxies?hours=${timeRange}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load proxy data');
                }
                return response.json();
            })
            .then(data => {
                // Group proxies by their group attribute
                const groupCounts = {};
                
                Object.entries(data).forEach(([proxyId, stats]) => {
                    // Extract group from proxy data
                    const group = stats.group || getGroupFromProxyId(proxyId);
                    
                    if (!groupCounts[group]) {
                        groupCounts[group] = {
                            requests: stats.total_requests || 0
                        };
                    } else {
                        groupCounts[group].requests += stats.total_requests || 0;
                    }
                });
                
                // Create datasets for the chart
                const datasets = [];
                const colors = {
                    'us': { border: 'rgba(231, 76, 60, 1)', bg: 'rgba(231, 76, 60, 0.1)' },
                    'eu': { border: 'rgba(52, 152, 219, 1)', bg: 'rgba(52, 152, 219, 0.1)' },
                    'asia': { border: 'rgba(46, 204, 113, 1)', bg: 'rgba(46, 204, 113, 0.1)' },
                    'residential': { border: 'rgba(155, 89, 182, 1)', bg: 'rgba(155, 89, 182, 0.1)' },
                    'datacenter': { border: 'rgba(243, 156, 18, 1)', bg: 'rgba(243, 156, 18, 0.1)' }
                };
                
                // Since we don't have time-series data, we'll show the current total as a point
                // In a real implementation, you would fetch historical data from a different endpoint
                const labels = ['Current'];
                
                Object.entries(groupCounts).forEach(([group, stats]) => {
                    const color = colors[group.toLowerCase()] || 
                        { border: getRandomColor(), bg: getRandomColor(0.1) };
                    
                    datasets.push({
                        label: `${group.toUpperCase()} Proxies`,
                        data: [stats.requests],
                        borderColor: color.border,
                        backgroundColor: color.bg,
                        tension: 0.3
                    });
                });
                
                // If no data, show empty state
                if (datasets.length === 0) {
                    datasets.push({
                        label: 'No Data Available',
                        data: [0],
                        borderColor: 'rgba(200, 200, 200, 1)',
                        backgroundColor: 'rgba(200, 200, 200, 0.1)',
                        tension: 0.3
                    });
                }
                
                // Create chart with real data
                new Chart(ctx, {
                    type: 'bar', // Changed to bar since we only have one data point
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error loading usage data:', error);
                // Create empty chart on error
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['No Data Available'],
                        datasets: [{
                            label: 'Requests',
                            data: [0],
                            borderColor: 'rgba(200, 200, 200, 1)',
                            backgroundColor: 'rgba(200, 200, 200, 0.1)',
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
    }
    
    // Helper function to generate a random color
    function getRandomColor(alpha = 1) {
        const r = Math.floor(Math.random() * 255);
        const g = Math.floor(Math.random() * 255);
        const b = Math.floor(Math.random() * 255);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    
    // Helper function to extract group from proxy ID when not explicitly provided
    function getGroupFromProxyId(proxyId) {
        // Extract group from proxy ID if possible, otherwise return "unknown"
        if (proxyId.includes('us-')) return 'us';
        if (proxyId.includes('eu-')) return 'eu';
        if (proxyId.includes('asia-')) return 'asia';
        if (proxyId.includes('res-')) return 'residential';
        if (proxyId.includes('dc-')) return 'datacenter';
        return 'unknown';
    }
    }
});
</script>
{% endblock %}

{% block head %}
<style>
.proxy-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: flex-end;
}

.proxy-controls .form-group {
    flex: 1;
}

.status-active {
    color: #2ecc71;
    font-weight: bold;
}

.status-inactive {
    color: #3498db;
    font-weight: bold;
}

.status-error {
    color: #f39c12;
    font-weight: bold;
}

.status-banned {
    color: #e74c3c;
    font-weight: bold;
}

.success-rate {
    font-weight: bold;
}

.success-rate.high {
    color: #2ecc71;
}

.success-rate.medium {
    color: #f39c12;
}

.success-rate.low {
    color: #e74c3c;
}

.loading-cell {
    text-align: center;
    padding: 1rem;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 10% auto;
    border-radius: 5px;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #eee;
    text-align: right;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

.btn-icon {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
}

.btn-icon:hover {
    color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

@media (max-width: 768px) {
    .proxy-controls {
        flex-direction: column;
    }
}
</style>
{% endblock %}