{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>Run Integration Test</h2>
    <form id="integration-test-form" class="form">
        <div class="form-group">
            <label for="target-type">Target Type:</label>
            <select id="target-type" class="form-control" required>
                <option value="scraper">Scraper</option>
                <option value="proxy">Proxy</option>
                <option value="captcha_solver">CAPTCHA Solver</option>
            </select>
        </div>
        <div class="form-group">
            <label for="target-name">Target Name:</label>
            <input type="text" id="target-name" class="form-control" required>
        </div>
        <div class="form-group">
            <label for="test-type">Test Type:</label>
            <select id="test-type" class="form-control" required>
                <option value="connectivity">Connectivity</option>
                <option value="performance">Performance</option>
                <option value="accuracy">Accuracy</option>
            </select>
        </div>
        <div class="form-group">
            <label for="params">Parameters (JSON):</label>
            <textarea id="params" class="form-control" rows="4" placeholder='{"key": "value"}'></textarea>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">Run Test</button>
        </div>
    </form>
    <div id="test-result" class="test-result"></div>
</div>

<div class="dashboard-card">
    <h2>Integration Test History</h2>
    <div class="table-container" id="testHistoryTable">
        <p>Loading test history...</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const integrationTestForm = document.getElementById('integration-test-form');
    const testResultDiv = document.getElementById('test-result');

    // Load test history when page loads
    loadTestHistory();

    // Handle form submission
    integrationTestForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const targetType = document.getElementById('target-type').value;
        const targetName = document.getElementById('target-name').value;
        const testType = document.getElementById('test-type').value;
        const paramsText = document.getElementById('params').value;

        // Parse params or set to null if empty
        let params = null;
        if (paramsText.trim()) {
            try {
                params = JSON.parse(paramsText);
            } catch (err) {
                testResultDiv.innerHTML = `<div class="alert alert-error">
                    <p>Invalid JSON parameters: ${err.message}</p>
                </div>`;
                return;
            }
        }

        // Create test request
        const testRequest = {
            target_type: targetType,
            target_name: targetName,
            test_type: testType,
            params: params
        };

        // Show loading message
        testResultDiv.innerHTML = `<div class="loading">
            <p>Running integration test...</p>
        </div>`;

        // Submit test request to API
        fetch('/api/v1/integration/run', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(testRequest)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Display test result
            const result = data.result;
            const status = result.passed ? 'success' : 'error';

            testResultDiv.innerHTML = `
                <div class="alert alert-${status}">
                    <h3>Test Result: ${result.passed ? 'PASSED' : 'FAILED'}</h3>
                    <p><strong>Test ID:</strong> ${data.test_id}</p>
                    <p><strong>Duration:</strong> ${result.duration.toFixed(2)}s</p>
                    <p><strong>Message:</strong> ${result.message}</p>
                    ${result.details ? `<pre>${JSON.stringify(result.details, null, 2)}</pre>` : ''}
                </div>
            `;

            // Reload test history
            loadTestHistory();
        })
        .catch(error => {
            console.error('Error running integration test:', error);
            testResultDiv.innerHTML = `<div class="alert alert-error">
                <p>Error running integration test: ${error.message}</p>
            </div>`;
        });
    });

    function loadTestHistory() {
        const historyTable = document.getElementById('testHistoryTable');

        fetch('/api/v1/integration/history', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                historyTable.innerHTML = '<p>No integration tests have been run yet.</p>';
                return;
            }

            // Create table with test history
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Test ID</th>
                            <th>Target Type</th>
                            <th>Target Name</th>
                            <th>Test Type</th>
                            <th>Status</th>
                            <th>Time</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.forEach(test => {
                const timestamp = new Date(test.timestamp).toLocaleString();
                tableHTML += `
                    <tr>
                        <td>${test.test_id}</td>
                        <td>${test.target_type}</td>
                        <td>${test.target_name}</td>
                        <td>${test.test_type}</td>
                        <td>
                            <span class="status-badge status-${test.passed ? 'good' : 'error'}">
                                ${test.passed ? 'PASSED' : 'FAILED'}
                            </span>
                        </td>
                        <td>${timestamp}</td>
                        <td>${test.duration.toFixed(2)}s</td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            historyTable.innerHTML = tableHTML;
        })
        .catch(error => {
            console.error('Error loading test history:', error);
            historyTable.innerHTML = `<div class="alert alert-error">
                <p>Error loading test history: ${error.message}</p>
            </div>`;
        });
    }
});
</script>
{% endblock %}

{% block head %}
<style>
.test-result {
    margin-top: 1.5rem;
}

.loading {
    text-align: center;
    padding: 1rem;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 3px;
}

pre {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 3px;
    overflow: auto;
    max-height: 300px;
    margin-top: 0.75rem;
}
</style>
{% endblock %}