{% extends "base.html" %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2>Register</h2>
        <form id="register-form" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="full_name">Full Name</label>
                <input type="text" id="full_name" name="full_name" class="form-control">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="confirm_password">Confirm Password</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Register</button>
            </div>
            <div class="auth-message" id="register-message"></div>
        </form>
        <div class="auth-footer">
            <p>Already have an account? <a href="/login">Login</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('register-form');
    const registerMessage = document.getElementById('register-message');

    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const email = document.getElementById('email').value;
        const fullName = document.getElementById('full_name').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        // Check if passwords match
        if (password !== confirmPassword) {
            registerMessage.innerHTML = `<div class="alert alert-error">
                <p>Passwords do not match.</p>
            </div>`;
            return;
        }

        // Create user data for API submission
        const userData = {
            username: username,
            email: email,
            password: password,
            full_name: fullName
        };

        // This registration approach is a security issue and should not be used in production
        // Users should register without admin privileges and admin should approve them
        // or a secure registration token system should be used
        registerMessage.innerHTML = `<div class="alert alert-info">
            <p>Please contact an administrator to create your account.</p>
        </div>`;
        return;

        // This section is disabled due to security concerns
        // Using hardcoded admin credentials is not secure
        /*
        const adminFormData = new FormData();
        adminFormData.append('username', 'admin');
        // Removed hardcoded password
        */

        fetch('/api/v1/auth/token', {
            method: 'POST',
            body: adminFormData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Admin authentication failed');
            }
            return response.json();
        })
        .then(adminData => {
            // Now use the admin token to create the user
            return fetch('/api/v1/users/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${adminData.access_token}`
                },
                body: JSON.stringify(userData)
            });
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Registration failed');
            }
            return response.json();
        })
        .then(data => {
            registerMessage.innerHTML = `<div class="alert alert-success">
                <p>Registration successful! <a href="/login">Login</a> to continue.</p>
            </div>`;
            registerForm.reset();
        })
        .catch(error => {
            registerMessage.innerHTML = `<div class="alert alert-error">
                <p>Registration failed: ${error.message}</p>
            </div>`;
        });
    });
});
</script>
{% endblock %}