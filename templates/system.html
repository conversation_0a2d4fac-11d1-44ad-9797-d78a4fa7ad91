{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card full-width">
        <h2>System Information</h2>
        <div id="systemInfo"><p>Loading system information...</p></div>
    </div>

    <div class="dashboard-card">
        <h2>System Resource Usage (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="systemResourcesChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>API Service Health (Last 24h)</h2>
        <div class="chart-container">
            <canvas id="apiHealthChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Database Performance (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="dbPerformanceChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card full-width">
        <h2>Recent System Events</h2>
        <div id="systemEvents" class="events-list"><p>Loading system events...</p></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const systemInfoDiv = document.getElementById('systemInfo');
    const systemEventsDiv = document.getElementById('systemEvents');

    // Ensure divs exist before trying to update them
    if (!systemInfoDiv) {
        console.error("System page error: 'systemInfo' div not found.");
        return; // Stop if essential elements are missing
    }
    if (!systemEventsDiv) {
        console.error("System page error: 'systemEvents' div not found.");
        return; // Stop if essential elements are missing
    }

    // Simplified fetchWithAuth for example (actual implementation might be more complex)
    // This assumes the cookie handles auth for GET requests, or token is in localStorage for manual header setting
    async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('access_token'); // Still attempt to use localStorage for JS-initiated calls
        const headers = {
            'Content-Type': 'application/json', // Good practice to set content type for fetch
            ...options.headers
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        // Cookies will be sent automatically by the browser for same-origin requests.
        return fetch(url, { ...options, headers });
    }

    function formatKey(key) {
        const result = key.replace(/_/g, " ").replace(/([A-Z])/g, " $1"); // Handle snake_case first, then camelCase
        return result.charAt(0).toUpperCase() + result.slice(1).trim();
    }

    // Global variables for real-time updates
    let systemInfoUpdateInterval;
    let lastUpdateTime = 0;
    let isUpdating = false;

    function formatSystemValue(key, value) {
        // Special formatting for certain system metrics
        if (key === 'cpu_usage' || key === 'memory_usage' || key === 'disk_usage') {
            return `<span class="metric-value" data-metric="${key}">${value}</span>`;
        }
        if (key === 'uptime') {
            return `<span class="uptime-value">${value}</span>`;
        }
        if (key === 'timestamp') {
            return ''; // Don't display timestamp
        }
        return value;
    }

    function updateSystemInfoDisplay(data) {
        if (!data || typeof data !== 'object') {
            return;
        }

        // Create modern card-based layout
        let html = '<div class="system-info-container real-time">';
        html += '<div class="system-info-grid">';

        // Group system information into logical cards
        const infoGroups = [
            {
                title: 'System Identity',
                icon: '🖥️',
                fields: ['hostname', 'os_info', 'kernel', 'python_version']
            },
            {
                title: 'Hardware Info',
                icon: '⚙️',
                fields: ['cpu_info']
            },
            {
                title: 'Resource Usage',
                icon: '📊',
                fields: ['cpu_usage', 'memory_usage', 'disk_usage']
            },
            {
                title: 'System Status',
                icon: '⏱️',
                fields: ['uptime', 'load_avg']
            }
        ];

        infoGroups.forEach(group => {
            const groupFields = group.fields.filter(field => 
                data.hasOwnProperty(field) && 
                field !== 'timestamp' && 
                field !== 'cpu_percent' && 
                field !== 'memory_percent' && 
                field !== 'disk_percent'
            );

            if (groupFields.length > 0) {
                html += `<div class="info-card">
                    <div class="info-card-header">
                        <div class="info-icon">${group.icon}</div>
                        <div class="info-label">${group.title}</div>
                    </div>`;

                groupFields.forEach(field => {
                    const formattedValue = formatSystemValue(field, data[field]);
                    if (formattedValue !== '') {
                        html += `<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
                            <span style="color: var(--text-secondary); font-size: 0.875rem; font-weight: 500;">${formatKey(field)}</span>
                            <span class="info-value">${formattedValue}</span>
                        </div>`;
                    }
                });

                html += '</div>';
            }
        });

        html += '</div>'; // Close grid

        // Add last updated timestamp with modern styling (24-hour format)
        const now = new Date();
        html += `<div class="last-updated">Last updated: ${now.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit', second: '2-digit' })}</div>`;
        
        html += '</div>'; // Close container

        systemInfoDiv.innerHTML = html;

        // Add visual indicators for high usage
        addUsageIndicators(data);
    }

    function addUsageIndicators(data) {
        // Add color coding for CPU usage
        const cpuMetric = document.querySelector('[data-metric="cpu_usage"]');
        if (cpuMetric && data.cpu_percent !== undefined) {
            const cpuPercent = parseFloat(data.cpu_percent);
            cpuMetric.className = 'metric-value';
            if (cpuPercent > 80) {
                cpuMetric.classList.add('high-usage');
            } else if (cpuPercent > 60) {
                cpuMetric.classList.add('medium-usage');
            } else {
                cpuMetric.classList.add('low-usage');
            }
        }

        // Add color coding for memory usage
        const memoryMetric = document.querySelector('[data-metric="memory_usage"]');
        if (memoryMetric && data.memory_percent !== undefined) {
            const memoryPercent = parseFloat(data.memory_percent);
            memoryMetric.className = 'metric-value';
            if (memoryPercent > 85) {
                memoryMetric.classList.add('high-usage');
            } else if (memoryPercent > 70) {
                memoryMetric.classList.add('medium-usage');
            } else {
                memoryMetric.classList.add('low-usage');
            }
        }

        // Add color coding for disk usage
        const diskMetric = document.querySelector('[data-metric="disk_usage"]');
        if (diskMetric && data.disk_percent !== undefined) {
            const diskPercent = parseFloat(data.disk_percent);
            diskMetric.className = 'metric-value';
            if (diskPercent > 90) {
                diskMetric.classList.add('high-usage');
            } else if (diskPercent > 75) {
                diskMetric.classList.add('medium-usage');
            } else {
                diskMetric.classList.add('low-usage');
            }
        }
    }

    function loadSystemInfo() {
        if (isUpdating) {
            return; // Prevent overlapping requests
        }

        isUpdating = true;
        console.log("Loading system info...");

        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info API response:", data);
                updateSystemInfoDisplay(data);
                lastUpdateTime = Date.now();
            })
            .catch(error => {
                console.error('Error loading system info:', error);
                systemInfoDiv.innerHTML = `<p class="alert alert-danger">Error loading system information: ${error.message}</p>`;
            })
            .finally(() => {
                isUpdating = false;
            });
    }

    function startRealTimeUpdates() {
        // Clear any existing interval
        if (systemInfoUpdateInterval) {
            clearInterval(systemInfoUpdateInterval);
        }

        // Start real-time updates every 5 seconds
        systemInfoUpdateInterval = setInterval(() => {
            loadSystemInfo();
        }, 5000);

        console.log("Real-time system info updates started (every 5 seconds)");
    }

    function stopRealTimeUpdates() {
        if (systemInfoUpdateInterval) {
            clearInterval(systemInfoUpdateInterval);
            systemInfoUpdateInterval = null;
            console.log("Real-time system info updates stopped");
        }
    }

    function loadSystemEvents() {
        console.log("Attempting to load system events...");
        systemEventsDiv.innerHTML = '<p>Loading system events...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System events API response:", data);

                if (data && Array.isArray(data) && data.length > 0) {
                    let html = '';
                    data.forEach(event => {
                        html += `<div class="event-item alert alert-info">
                                     <span class="timestamp"><strong>Time:</strong> ${event.time || 'N/A'}</span>
                                     <strong class="event-type">Type:</strong> ${event.type || 'N/A'}
                                     <p><strong>Message:</strong> ${event.message || 'N/A'}</p>
                                 </div>`;
                    });
                    systemEventsDiv.innerHTML = html;
                } else {
                    systemEventsDiv.innerHTML = '<p class="alert alert-warning">No system events available.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system events:', error);
                systemEventsDiv.innerHTML = `<p class="alert alert-danger">Error loading system events: ${error.message}</p>`;
            });
    }

    // Chart initialization functions
    function initSystemResourcesChart() {
        const canvas = document.getElementById('systemResourcesChart');
        if (!canvas) {
            console.error("System Resources Chart canvas not found!");
            return;
        }

        console.log("Loading system resources chart...");

        // Get current system info and create a simple chart
        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info for resources chart:", data);

                // Extract percentage values from the system info
                let cpuUsage = 0;
                let memoryUsage = 0;
                let diskUsage = 0;

                // Parse CPU usage
                if (data.cpu_usage && data.cpu_usage.includes('%')) {
                    cpuUsage = parseFloat(data.cpu_usage.replace('%', ''));
                }

                // Parse memory usage
                if (data.memory_usage && data.memory_usage.includes('(') && data.memory_usage.includes('%)')) {
                    const match = data.memory_usage.match(/\((\d+\.?\d*)%\)/);
                    if (match) {
                        memoryUsage = parseFloat(match[1]);
                    }
                }

                // Parse disk usage
                if (data.disk_usage && data.disk_usage.includes('(') && data.disk_usage.includes('%)')) {
                    const match = data.disk_usage.match(/\((\d+\.?\d*)%\)/);
                    if (match) {
                        diskUsage = parseFloat(match[1]);
                    }
                }

                console.log("Parsed usage values:", {cpuUsage, memoryUsage, diskUsage});

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['CPU Usage', 'Memory Usage', 'Disk Usage'],
                        datasets: [{
                            data: [cpuUsage, memoryUsage, diskUsage],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 205, 86, 0.8)'
                            ],
                            borderColor: [
                                'rgb(255, 99, 132)',
                                'rgb(54, 162, 235)',
                                'rgb(255, 205, 86)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Current System Resource Usage'
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed + '%';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log("System Resources Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading system resources:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No system resource data available.</p>';
            });
    }

    function initApiHealthChart() {
        const canvas = document.getElementById('apiHealthChart');
        if (!canvas) {
            console.error("API Health Chart canvas not found!");
            return;
        }

        console.log("Loading API health chart...");
        fetchWithAuth('/api/v1/system/api-health')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("API health data:", data);

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Uptime', 'Success Rate', 'Requests/Min', 'Error Rate'],
                        datasets: [{
                            label: 'API Health Metrics',
                            data: [
                                parseFloat(data.uptime?.replace('%', '') || '0'),
                                parseFloat(data.success_rate?.replace('%', '') || '0'),
                                parseFloat(data.requests_per_minute || '0'),
                                parseFloat(data.error_rate?.replace('%', '') || '0')
                            ],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(255, 206, 86, 0.6)',
                                'rgba(255, 99, 132, 0.6)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'API Service Health Metrics'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log("API Health Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading API health:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No API health data available.</p>';
            });
    }

    function initDbPerformanceChart() {
        const canvas = document.getElementById('dbPerformanceChart');
        if (!canvas) {
            console.error("DB Performance Chart canvas not found!");
            return;
        }

        console.log("Loading database performance chart...");
        fetchWithAuth('/api/v1/system/db-performance?hours=1')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("DB performance data:", data);

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Queries/Min', 'Success Rate'],
                        datasets: [{
                            label: 'Database Performance',
                            data: [
                                parseFloat(data.queries_per_minute || '0'),
                                parseFloat(data.success_rate?.replace('%', '') || '0')
                            ],
                            backgroundColor: [
                                'rgba(153, 102, 255, 0.6)',
                                'rgba(255, 159, 64, 0.6)'
                            ],
                            borderColor: [
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Database Performance Metrics'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log("DB Performance Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading DB performance:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No database performance data available.</p>';
            });
    }

    // Load initial data
    loadSystemInfo();
    loadSystemEvents();

    // Start real-time updates
    startRealTimeUpdates();

    // Initialize charts with delay to ensure DOM is ready
    console.log("Initializing charts...");
    setTimeout(() => {
        console.log("Starting chart initialization...");
        initSystemResourcesChart();
        initApiHealthChart();
        initDbPerformanceChart();
        console.log("Chart initialization complete.");
    }, 1000);

    // Handle page visibility changes to pause/resume updates
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            console.log("Page hidden, stopping real-time updates");
            stopRealTimeUpdates();
        } else {
            console.log("Page visible, resuming real-time updates");
            loadSystemInfo(); // Immediate update
            startRealTimeUpdates();
        }
    });

    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
        stopRealTimeUpdates();
    });

});
</script>
{% endblock %}

{% block head %}
<style>
.full-width {
    grid-column: 1 / -1; /* Span all columns */
}

/* Modern System Information Styles - Matching Dashboard Design */
.system-info-container {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
}

.system-info-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
    border-color: rgba(102, 126, 234, 0.3);
}

.system-info-container:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.info-card {
    background: var(--bg-glass-strong);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-md);
    padding: 1.25rem;
    position: relative;
    transition: var(--transition-fast);
}

.info-card:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-1px);
}

.info-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.75rem;
    opacity: 0.8;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
    font-weight: 600;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-value {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    word-break: break-all;
}

/* Enhanced metric indicators with modern design */
.metric-value {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-value:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.metric-value.low-usage {
    background: rgba(67, 233, 123, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(67, 233, 123, 0.3);
    box-shadow: 0 0 10px rgba(67, 233, 123, 0.1);
}

.metric-value.medium-usage {
    background: rgba(254, 225, 64, 0.15);
    color: var(--warning-color);
    border: 1px solid rgba(254, 225, 64, 0.3);
    box-shadow: 0 0 10px rgba(254, 225, 64, 0.1);
}

.metric-value.high-usage {
    background: rgba(255, 154, 158, 0.15);
    color: var(--error-color);
    border: 1px solid rgba(255, 154, 158, 0.3);
    box-shadow: 0 0 10px rgba(255, 154, 158, 0.1);
    animation: pulse-glow 2s infinite;
}

.metric-value:hover:before {
    left: 0;
}

.uptime-value {
    font-weight: 600;
    color: var(--success-color);
    background: rgba(67, 233, 123, 0.1);
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    border: 1px solid rgba(67, 233, 123, 0.2);
}

.last-updated {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.last-updated:before {
    content: '🔄';
    margin-right: 0.5rem;
    opacity: 0.7;
}

/* Enhanced animations */
@keyframes fadeIn {
    from { 
        opacity: 0;
        transform: translateY(10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse-glow {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(255, 154, 158, 0.1);
    }
    50% { 
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(255, 154, 158, 0.2);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(255, 154, 158, 0.1);
    }
}

.system-info-container.real-time {
    animation: fadeIn 0.4s ease-out;
}

/* Modern Events List Styles */
.events-list {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.events-list::-webkit-scrollbar {
    width: 6px;
}

.events-list::-webkit-scrollbar-track {
    background: transparent;
}

.events-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.event-item {
    background: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--accent-color);
    border-radius: var(--radius-md);
    padding: 1rem 1.25rem;
    margin-bottom: 0.75rem;
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.event-item:hover {
    background: var(--bg-glass-strong);
    transform: translateX(4px);
    border-left-color: var(--primary-color);
}

.event-item .timestamp {
    display: block;
    font-size: 0.8rem;
    color: var(--text-tertiary);
    margin-bottom: 0.375rem;
    font-weight: 500;
}

.event-item .event-type {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.event-item p {
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* Enhanced Chart Container Styles */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    padding: 1rem;
    background: var(--bg-glass);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-container canvas {
    max-height: 280px;
    border-radius: var(--radius-sm);
}

/* Fixed Dropdown Styles - Global Fix */
select,
.form-control[data-toggle="dropdown"],
.dropdown-menu {
    position: relative;
    z-index: 1000;
}

select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    cursor: pointer;
}

select:focus {
    z-index: 1001;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

select option {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.5rem;
    border: none;
}

/* Enhanced form controls to match dashboard design */
.form-control {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    position: relative;
    z-index: 10;
}

.form-control:focus {
    background: var(--bg-glass-strong);
    border-color: var(--primary-color);
    z-index: 1001;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .system-info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .info-card {
        padding: 1rem;
    }
    
    .info-value {
        font-size: 1rem;
    }
    
    .system-info-container {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .info-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .info-icon {
        margin-right: 0;
    }
}</style>
{% endblock %}
