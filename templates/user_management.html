{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>User Management</h2>
    
    <div class="user-actions">
        <button id="add-user-btn" class="btn btn-primary">Add New User</button>
    </div>
    
    <div class="divider"></div>
    
    <div class="table-container" id="users-table">
        <p>Loading users...</p>
    </div>
</div>

<!-- Add User Modal -->
<div id="addUserModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Add New User</h2>
            <button class="close">&times;</button>
        </div>
        <form id="add-user-form" class="form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="full_name">Full Name</label>
                <input type="text" id="full_name" name="full_name" class="form-control">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="is_admin" class="d-flex items-center gap-2">
                    <input type="checkbox" id="is_admin" name="is_admin">
                    Admin Privileges
                </label>
            </div>
        </form>
        <div id="add-user-message" class="message-container"></div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancel-add-user">Cancel</button>
            <button type="submit" form="add-user-form" class="btn btn-primary">Add User</button>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div id="editProfileModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Edit User Profile</h2>
            <button class="close">&times;</button>
        </div>
        <form id="edit-profile-form" class="form">
            <input type="hidden" id="edit-username" name="edit-username">
            <div class="form-group">
                <label for="edit-username-display">Username</label>
                <input type="text" id="edit-username-display" class="form-control" readonly>
                <small class="form-text text-tertiary">Username cannot be changed</small>
            </div>
            <div class="form-group">
                <label for="edit-email">Email</label>
                <input type="email" id="edit-email" name="edit-email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="edit-full-name">Full Name</label>
                <input type="text" id="edit-full-name" name="edit-full-name" class="form-control">
            </div>
        </form>
        <div id="edit-profile-message" class="message-container"></div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancel-edit-profile">Cancel</button>
            <button type="submit" form="edit-profile-form" class="btn btn-primary">Update Profile</button>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Reset User Password</h2>
            <button class="close">&times;</button>
        </div>
        <form id="reset-password-form" class="form">
            <input type="hidden" id="reset-username" name="reset-username">
            <div class="form-group">
                <label for="reset-password">New Password</label>
                <input type="password" id="reset-password" name="reset-password" class="form-control" required>
            </div>
        </form>
        <div id="reset-password-message" class="message-container"></div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancel-reset-password">Cancel</button>
            <button type="submit" form="reset-password-form" class="btn btn-primary">Reset Password</button>
        </div>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
/* Additional utility styles for user management */
.user-actions {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.divider {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.user-role {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.user-role:not(.admin) {
    background: rgba(79, 172, 254, 0.1);
    color: var(--accent-color);
}

.user-role.admin {
    background: var(--error-gradient);
    color: white;
}

.user-actions-cell {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.form-text {
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.text-tertiary {
    color: var(--text-tertiary);
}

.message-container {
    margin-top: 1rem;
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addUserBtn = document.getElementById('add-user-btn');
    const addUserModal = document.getElementById('addUserModal');
    const editProfileModal = document.getElementById('editProfileModal');
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    const closeBtns = document.querySelectorAll('.close');
    
    // Load users when page loads
    loadUsers();
    
    // Open Add User modal
    addUserBtn.addEventListener('click', function() {
        addUserModal.classList.add('show');
    });
    
    // Close modals when clicking on X
    closeBtns.forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            addUserModal.classList.remove('show');
            editProfileModal.classList.remove('show');
            resetPasswordModal.classList.remove('show');
        });
    });
    
    // Cancel buttons
    document.getElementById('cancel-add-user').addEventListener('click', () => {
        addUserModal.classList.remove('show');
    });
    
    document.getElementById('cancel-edit-profile').addEventListener('click', () => {
        editProfileModal.classList.remove('show');
    });
    
    document.getElementById('cancel-reset-password').addEventListener('click', () => {
        resetPasswordModal.classList.remove('show');
    });
    
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === addUserModal) {
            addUserModal.classList.remove('show');
        }
        if (event.target === editProfileModal) {
            editProfileModal.classList.remove('show');
        }
        if (event.target === resetPasswordModal) {
            resetPasswordModal.classList.remove('show');
        }
    });
    
    // Add User form submission
    const addUserForm = document.getElementById('add-user-form');
    const addUserMessage = document.getElementById('add-user-message');
    
    addUserForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const userData = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            password: document.getElementById('password').value,
            full_name: document.getElementById('full_name').value || undefined,
            is_admin: document.getElementById('is_admin').checked
        };
        
        fetch('/api/v1/users/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(userData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            addUserMessage.innerHTML = `<div class="alert alert-success">
                <p>User ${data.username} added successfully!</p>
            </div>`;
            addUserForm.reset();
            loadUsers(); // Reload the users list
            
            // Close modal after a short delay
            setTimeout(() => {
                addUserModal.classList.remove('show');
                addUserMessage.innerHTML = '';
            }, 2000);
        })
        .catch(error => {
            addUserMessage.innerHTML = `<div class="alert alert-error">
                <p>${error.message}</p>
            </div>`;
        });
    });
    
    // Edit Profile form submission
    const editProfileForm = document.getElementById('edit-profile-form');
    const editProfileMessage = document.getElementById('edit-profile-message');
    
    editProfileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('edit-username').value;
        const email = document.getElementById('edit-email').value;
        const fullName = document.getElementById('edit-full-name').value;
        
        const profileData = {
            email: email,
            full_name: fullName || null
        };
        
        fetch(`/api/v1/users/${username}/profile`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: JSON.stringify(profileData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            editProfileMessage.innerHTML = `<div class="alert alert-success">
                <p>${data.message}</p>
            </div>`;
            loadUsers(); // Reload the users list
            
            // Close modal after a short delay
            setTimeout(() => {
                editProfileModal.classList.remove('show');
                editProfileMessage.innerHTML = '';
            }, 2000);
        })
        .catch(error => {
            editProfileMessage.innerHTML = `<div class="alert alert-error">
                <p>${error.message}</p>
            </div>`;
        });
    });
    
    // Reset Password form submission
    const resetPasswordForm = document.getElementById('reset-password-form');
    const resetPasswordMessage = document.getElementById('reset-password-message');
    
    resetPasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('reset-username').value;
        const newPassword = document.getElementById('reset-password').value;
        
        // Create form data
        const formData = new FormData();
        formData.append('new_password', newPassword);
        
        fetch(`/api/v1/users/${username}/reset-password`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            resetPasswordMessage.innerHTML = `<div class="alert alert-success">
                <p>${data.message}</p>
            </div>`;
            resetPasswordForm.reset();
            
            // Close modal after a short delay
            setTimeout(() => {
                resetPasswordModal.classList.remove('show');
                resetPasswordMessage.innerHTML = '';
            }, 2000);
        })
        .catch(error => {
            resetPasswordMessage.innerHTML = `<div class="alert alert-error">
                <p>${error.message}</p>
            </div>`;
        });
    });
    
    // Load users function
    function loadUsers() {
        const usersTable = document.getElementById('users-table');
        
        fetch('/api/v1/users/', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(users => {
            if (users.length === 0) {
                usersTable.innerHTML = '<p>No users found.</p>';
                return;
            }
            
            let tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Full Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            users.forEach(user => {
                tableHTML += `
                    <tr>
                        <td>
                            <div class="user-row">
                                ${user.username}
                            </div>
                        </td>
                        <td>${user.email}</td>
                        <td>${user.full_name || '-'}</td>
                        <td>
                            <span class="user-role ${user.is_admin ? 'admin' : ''}">
                                ${user.is_admin ? 'Admin' : 'User'}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge ${user.disabled ? 'status-offline' : 'status-online'}">
                                ${user.disabled ? 'Disabled' : 'Active'}
                            </span>
                        </td>
                        <td class="user-actions-cell">
                            <button class="btn btn-small btn-edit edit-profile-btn" data-username="${user.username}" data-email="${user.email}" data-fullname="${user.full_name || ''}">
                                Edit Profile
                            </button>
                            <button class="btn btn-small btn-reset reset-password-btn" data-username="${user.username}">
                                Reset Password
                            </button>
                            <button class="btn btn-small ${user.is_admin ? 'btn-warning' : 'btn-success'} promote-admin-btn" data-username="${user.username}" data-is-admin="${user.is_admin}">
                                ${user.is_admin ? 'Remove Admin' : 'Make Admin'}
                            </button>
                            <button class="btn btn-small btn-delete delete-user-btn" data-username="${user.username}">
                                Delete
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableHTML += `
                    </tbody>
                </table>
            `;
            
            usersTable.innerHTML = tableHTML;
            
            // Add event listeners to the edit profile buttons
            document.querySelectorAll('.edit-profile-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    const email = this.getAttribute('data-email');
                    const fullName = this.getAttribute('data-fullname');
                    
                    // Populate the edit form
                    document.getElementById('edit-username').value = username;
                    document.getElementById('edit-username-display').value = username;
                    document.getElementById('edit-email').value = email;
                    document.getElementById('edit-full-name').value = fullName;
                    
                    editProfileModal.classList.add('show');
                });
            });
            
            // Add event listeners to the reset password buttons
            document.querySelectorAll('.reset-password-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    document.getElementById('reset-username').value = username;
                    resetPasswordModal.classList.add('show');
                });
            });
            
            // Add event listeners to the delete user buttons
            document.querySelectorAll('.delete-user-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    if (confirm(`Are you sure you want to delete user "${username}"?`)) {
                        deleteUser(username);
                    }
                });
            });
            
            // Add event listeners to the promote/demote admin buttons
            document.querySelectorAll('.promote-admin-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const username = this.getAttribute('data-username');
                    const isAdmin = this.getAttribute('data-is-admin') === 'true';
                    const action = isAdmin ? 'remove admin privileges from' : 'grant admin privileges to';
                    
                    if (confirm(`Are you sure you want to ${action} user "${username}"?`)) {
                        promoteAdmin(username, !isAdmin);
                    }
                });
            });
        })
        .catch(error => {
            usersTable.innerHTML = `<div class="alert alert-error">
                <p>Error loading users: ${error.message}</p>
            </div>`;
        });
    }
    
    // Delete user function
    function deleteUser(username) {
        fetch(`/api/v1/users/${username}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            alert(data.message);
            loadUsers(); // Reload the users list
        })
        .catch(error => {
            alert(`Error deleting user: ${error.message}`);
        });
    }
    
    // Promote/demote admin function
    function promoteAdmin(username, promote) {
        const formData = new FormData();
        formData.append('promote', promote);
        
        fetch(`/api/v1/users/${username}/promote-admin`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            alert(data.message);
            loadUsers(); // Reload the users list
        })
        .catch(error => {
            alert(`Error changing admin status: ${error.message}`);
        });
    }
});
</script>
{% endblock %}