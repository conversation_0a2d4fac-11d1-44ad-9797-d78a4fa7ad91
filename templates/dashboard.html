{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Scraper Performance</h2>
        <div class="chart-container">
            <canvas id="scraperChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/scrapers" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>CAPTCHA Detection</h2>
        <div class="chart-container">
            <canvas id="captchaChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/captchas" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Proxy Performance</h2>
        <div class="chart-container">
            <canvas id="proxyChart"></canvas>
        </div>
        <div class="card-footer">
            <a href="/proxies" class="btn">Details</a>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Recent Alerts</h2>
        <div class="alerts-list" id="recentAlerts">
            <p>Loading alerts...</p>
        </div>
        <div class="card-footer">
            <a href="/alerts" class="btn">View All</a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard data from API
    fetch('/api/v1/stats/scrapers?scraper_name=all&hours=24')
        .then(response => response.json())
        .then(data => {
            // Initialize charts with data
            initScraperChart(data);
        })
        .catch(error => console.error('Error loading scraper data:', error));

    fetch('/api/v1/stats/proxies?hours=24')
        .then(response => response.json())
        .then(data => {
            initProxyChart(data);
        })
        .catch(error => console.error('Error loading proxy data:', error));
});

function initScraperChart(data) {
    const ctx = document.getElementById('scraperChart').getContext('2d');
    const scraperChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(data),
            datasets: [{
                label: 'Success Rate (%)',
                data: Object.values(data).map(value =>
                    value.total_requests > 0 ?
                    (value.successful_requests / value.total_requests) * 100 : 0
                ),
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function initProxyChart(data) {
    const ctx = document.getElementById('proxyChart').getContext('2d');
    const proxyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(data),
            datasets: [{
                label: 'Success Rate (%)',
                data: Object.values(data).map(value => value.success_rate || 0),
                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                borderColor: 'rgba(153, 102, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function initCaptchaChart() {
    const ctx = document.getElementById('captchaChart').getContext('2d');
    const captchaChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Solved', 'Failed'],
            datasets: [{
                data: [85, 15],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(255, 99, 132, 0.2)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true
        }
    });
}

// Initialize dummy data for CAPTCHA chart
initCaptchaChart();

// Load recent alerts
const alertElement = document.getElementById('recentAlerts');
alertElement.innerHTML = `
    <div class="alert alert-warning">
        <span class="timestamp">Today 14:23</span>
        <p>High CAPTCHA rate detected for Twitter scraper (15%)</p>
    </div>
    <div class="alert alert-error">
        <span class="timestamp">Yesterday 22:05</span>
        <p>Proxy failure rate above threshold (25%)</p>
    </div>
    <div class="alert alert-info">
        <span class="timestamp">May 7, 09:12</span>
        <p>New scraper registered: CoinMarketCap</p>
    </div>
`;
</script>
{% endblock %}