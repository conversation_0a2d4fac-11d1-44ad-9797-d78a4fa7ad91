{% extends "base.html" %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <h2>Login</h2>
        <form id="login-form" class="auth-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Login</button>
            </div>
            <div class="auth-message" id="login-message"></div>
        </form>
        <!-- Registration is disabled - Contact an administrator to create your account -->
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginMessage = document.getElementById('login-message');

    // Get return URL from query parameters
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('return_url') || '/dashboard';

    // Check if user is already logged in
    const token = localStorage.getItem('access_token');
    if (token) {
        // Try to verify the token is still valid
        fetch('/api/v1/auth/me', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (response.ok) {
                // Token is valid, redirect to return URL
                console.log('User already authenticated, redirecting to:', returnUrl);
                window.location.href = returnUrl;
            } else {
                // Token is invalid, remove it
                localStorage.removeItem('access_token');
            }
        })
        .catch(error => {
            console.warn('Token verification failed:', error);
            localStorage.removeItem('access_token');
        });
    }

    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Show loading state
        const submitButton = loginForm.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Logging in...';
        submitButton.disabled = true;

        // Create form data for API submission
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        fetch('/api/v1/auth/token', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Login failed');
            }
            return response.json();
        })
        .then(data => {
            // Store the token in localStorage
            localStorage.setItem('access_token', data.access_token);
            
            // Store user data if available
            if (data.user) {
                localStorage.setItem('user_data', JSON.stringify(data.user));
            }

            console.log('Login successful, redirecting to:', returnUrl);

            // Check for needs_password_change flag and redirect accordingly
            if (data.needs_password_change === true) {
                window.location.href = '/account'; // Redirect to account page to change password
            } else {
                window.location.href = returnUrl; // Redirect to original page or dashboard
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            loginMessage.innerHTML = `<div class="alert alert-error">
                <p>Invalid username or password.</p>
            </div>`;
            
            // Reset button state
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        });
    });

    // Display return URL info if present
    if (returnUrl && returnUrl !== '/dashboard') {
        const returnInfo = document.createElement('div');
        returnInfo.className = 'alert alert-info';
        returnInfo.style.marginBottom = '1rem';
        returnInfo.innerHTML = `<p>Please log in to access <strong>${decodeURIComponent(returnUrl)}</strong></p>`;
        loginForm.parentNode.insertBefore(returnInfo, loginForm);
    }
});
</script>
{% endblock %}