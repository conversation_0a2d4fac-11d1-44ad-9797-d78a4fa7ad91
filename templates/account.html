{% extends "base.html" %}

{% block content %}
<div class="dashboard-card">
    <h2>Account Settings</h2>

    {% if prompt_change_message %}
    <div class="alert alert-warning" role="alert" style="margin-bottom: 1rem;">
        {{ prompt_change_message }}
    </div>
    {% endif %}

    <div class="dashboard-grid">
        <!-- Password Change Section -->
        <div class="dashboard-card">
            <h3>Change Password</h3>
            <form id="change-password-form">
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <input type="password" id="current_password" name="current_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="confirm_new_password">Confirm New Password</label>
                    <input type="password" id="confirm_new_password" name="confirm_new_password" class="form-control" required>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Change Password</button>
                </div>
            </form>
            <div id="change-password-message" class="mt-2"></div>
        </div>

        <!-- Profile Edit Section -->
        <div class="dashboard-card">
            <h3>User Profile</h3>
            <form id="edit-profile-form">
                <div class="form-group">
                    <label for="username-display">Username</label>
                    <input type="text" id="username-display" class="form-control" value="{{ user.username }}" readonly>
                    <small class="form-text text-tertiary">Username cannot be changed</small>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" class="form-control" value="{{ user.email }}" required>
                </div>
                <div class="form-group">
                    <label for="full_name">Full Name</label>
                    <input type="text" id="full_name" name="full_name" class="form-control" value="{{ user.full_name if user.full_name else '' }}">
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                    <button type="button" id="cancel-profile-changes" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
            <div id="edit-profile-message" class="mt-2"></div>
        </div>
    </div>

</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const changePasswordForm = document.getElementById('change-password-form');
    const changePasswordMessage = document.getElementById('change-password-message'); // Ensure this div exists in the HTML part

    if (changePasswordForm && changePasswordMessage) {
        changePasswordForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmNewPassword = document.getElementById('confirm_new_password').value;

            changePasswordMessage.innerHTML = ''; // Clear previous messages

            if (newPassword !== confirmNewPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New passwords do not match.</div>';
                return;
            }
            if (!newPassword) {
                changePasswordMessage.innerHTML = '<div class="alert alert-error">New password cannot be empty.</div>';
                return;
            }
            if (!currentPassword) { // Also check current password
                changePasswordMessage.innerHTML = '<div class="alert alert-error">Current password cannot be empty.</div>';
                return;
            }

            const payload = {
                current_password: currentPassword,
                new_password: newPassword
            };

            try {
                const response = await fetch('/api/v1/users/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
                    },
                    body: JSON.stringify(payload)
                });

                const responseData = await response.json();

                if (response.ok) {
                    changePasswordMessage.innerHTML = `<div class="alert alert-success">${responseData.message || 'Password updated successfully! Redirecting...'}</div>`;
                    if (responseData.access_token) {
                        localStorage.setItem('access_token', responseData.access_token);
                    }
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000); // Redirect after 2 seconds
                } else {
                    changePasswordMessage.innerHTML = `<div class="alert alert-error">${responseData.detail || 'Failed to change password.'}</div>`;
                }
            } catch (error) {
                console.error('Password change error:', error);
                changePasswordMessage.innerHTML = '<div class="alert alert-error">An unexpected error occurred. Please try again.</div>';
            }
        });
    }

    // Profile edit form handling
    const editProfileForm = document.getElementById('edit-profile-form');
    const emailInput = document.getElementById('email');
    const fullNameInput = document.getElementById('full_name');
    const cancelProfileChangesButton = document.getElementById('cancel-profile-changes');
    const editProfileMessage = document.getElementById('edit-profile-message');

    if (editProfileForm && emailInput && fullNameInput && cancelProfileChangesButton && editProfileMessage) {
        let initialEmail = emailInput.value;
        let initialFullName = fullNameInput.value;

        editProfileForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const emailValue = emailInput.value;
            const fullNameValue = fullNameInput.value;

            editProfileMessage.innerHTML = ''; // Clear previous messages

            // Basic email validation
            const emailPattern = /\S+@\S+\.\S+/;
            if (!emailValue || !emailPattern.test(emailValue)) {
                editProfileMessage.innerHTML = '<div class="alert alert-error">Please enter a valid email address.</div>';
                return;
            }

            const payload = {
                email: emailValue,
                full_name: fullNameValue
            };

            try {
                const response = await fetch('/api/v1/users/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + localStorage.getItem('access_token')
                    },
                    body: JSON.stringify(payload)
                });

                const responseData = await response.json();

                if (response.ok) {
                    editProfileMessage.innerHTML = `<div class="alert alert-success">${responseData.message || 'Profile updated successfully!'}</div>`;
                    // Update initial values to the new saved values
                    initialEmail = emailValue;
                    initialFullName = fullNameValue;
                    // Optionally update input fields if server response contains updated data
                    if (responseData.user) {
                        emailInput.value = responseData.user.email;
                        fullNameInput.value = responseData.user.full_name;
                        initialEmail = responseData.user.email; // ensure initial values are also updated from server response
                        initialFullName = responseData.user.full_name;
                    }
                } else {
                    editProfileMessage.innerHTML = `<div class="alert alert-error">${responseData.detail || 'Failed to update profile.'}</div>`;
                }
            } catch (error) {
                console.error('Profile update error:', error);
                editProfileMessage.innerHTML = '<div class="alert alert-error">An unexpected error occurred. Please try again.</div>';
            }
        });

        cancelProfileChangesButton.addEventListener('click', function() {
            emailInput.value = initialEmail;
            fullNameInput.value = initialFullName;
            editProfileMessage.innerHTML = ''; // Clear any messages
        });
    }
});
</script>
{% endblock %}