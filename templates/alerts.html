{% extends "base.html" %}

{% block content %}
<div class="alert-controls">
    <div class="form-group">
        <label for="severity-filter">Filter by Severity:</label>
        <select id="severity-filter" class="form-control">
            <option value="all">All Severities</option>
            <option value="info">Info</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="critical">Critical</option>
        </select>
    </div>
    <div class="form-group">
        <label for="time-range">Time Range:</label>
        <select id="time-range" class="form-control">
            <option value="24">Last 24 Hours</option>
            <option value="48">Last 48 Hours</option>
            <option value="168">Last 7 Days</option>
            <option value="720">Last 30 Days</option>
        </select>
    </div>
    <button id="apply-filters" class="btn">Apply Filters</button>
</div>

<div class="dashboard-grid">
    <div class="dashboard-card">
        <h2>Alerts by Severity</h2>
        <div class="chart-container">
            <canvas id="severityChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Alert Trend</h2>
        <div class="chart-container">
            <canvas id="trendChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Alerts by Component</h2>
        <div class="chart-container">
            <canvas id="componentChart"></canvas>
        </div>
    </div>
    
    <div class="dashboard-card">
        <h2>Alert Settings</h2>
        <div class="alert-settings">
            <h3>Notification Channels</h3>
            <div class="channel-settings">
                <div class="form-check">
                    <input type="checkbox" id="email-notifications" class="form-check-input" checked>
                    <label for="email-notifications" class="form-check-label">Email Notifications</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" id="slack-notifications" class="form-check-input" checked>
                    <label for="slack-notifications" class="form-check-label">Slack Notifications</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" id="sms-notifications" class="form-check-input">
                    <label for="sms-notifications" class="form-check-label">SMS Notifications</label>
                </div>
            </div>
            
            <h3>Alert Thresholds</h3>
            <table class="threshold-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Threshold</th>
                        <th>Severity</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>CPU Usage</td>
                        <td>90%</td>
                        <td><span class="severity warning">Warning</span></td>
                    </tr>
                    <tr>
                        <td>CPU Usage</td>
                        <td>95%</td>
                        <td><span class="severity critical">Critical</span></td>
                    </tr>
                    <tr>
                        <td>Memory Usage</td>
                        <td>85%</td>
                        <td><span class="severity warning">Warning</span></td>
                    </tr>
                    <tr>
                        <td>Memory Usage</td>
                        <td>95%</td>
                        <td><span class="severity critical">Critical</span></td>
                    </tr>
                    <tr>
                        <td>Disk Space</td>
                        <td>90%</td>
                        <td><span class="severity warning">Warning</span></td>
                    </tr>
                    <tr>
                        <td>CAPTCHA Fail Rate</td>
                        <td>30%</td>
                        <td><span class="severity warning">Warning</span></td>
                    </tr>
                    <tr>
                        <td>Proxy Success Rate</td>
                        <td>&lt; 70%</td>
                        <td><span class="severity warning">Warning</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="dashboard-card">
    <h2>Recent Alerts</h2>
    <div class="table-container" id="alertTable">
        <table>
            <thead>
                <tr>
                    <th>Timestamp</th>
                    <th>Severity</th>
                    <th>Component</th>
                    <th>Message</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="alertTableBody">
                <tr>
                    <td colspan="6" class="loading-cell">Loading alerts...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const applyFiltersBtn = document.getElementById('apply-filters');
    applyFiltersBtn.addEventListener('click', loadAlerts);
    
    // Initial data load and chart initialization
    loadAlerts();
    initSeverityChart();
    initTrendChart();
    initComponentChart();
    
    function loadAlerts() {
        const severityFilter = document.getElementById('severity-filter');
        const timeRange = document.getElementById('time-range');
        
        const severity = severityFilter.value;
        const hours = timeRange.value;
        
        // Simulate loading alerts from API
        setTimeout(() => {
            displaySampleAlerts();
        }, 500);
    }
    
    function displaySampleAlerts() {
        const alerts = [
            {
                timestamp: "2025-05-08 18:45:32",
                severity: "critical",
                component: "CAPTCHA Solver",
                message: "CAPTCHA solver service unreachable for more than 5 minutes",
                status: "Active"
            },
            {
                timestamp: "2025-05-08 15:23:10",
                severity: "warning",
                component: "Proxy Manager",
                message: "Success rate below 70% for EU proxies group",
                status: "Active"
            },
            {
                timestamp: "2025-05-08 12:10:54",
                severity: "error",
                component: "Scraper",
                message: "Twitter scraper exceeded rate limit threshold",
                status: "Active"
            },
            {
                timestamp: "2025-05-08 09:32:17",
                severity: "info",
                component: "System",
                message: "Scheduled database backup completed",
                status: "Cleared"
            },
            {
                timestamp: "2025-05-07 22:15:09",
                severity: "warning",
                component: "Memory Usage",
                message: "System memory usage above 85% threshold",
                status: "Cleared"
            },
            {
                timestamp: "2025-05-07 17:40:23",
                severity: "critical",
                component: "Database",
                message: "Database connection failures detected",
                status: "Cleared"
            },
            {
                timestamp: "2025-05-07 14:05:11",
                severity: "error",
                component: "API Service",
                message: "REST API high latency detected",
                status: "Cleared"
            }
        ];
        
        const tableBody = document.getElementById('alertTableBody');
        let tableContent = '';
        
        alerts.forEach(alert => {
            const severityClass = `severity-${alert.severity}`;
            const statusClass = alert.status === 'Active' ? 'status-active' : 'status-cleared';
            
            tableContent += `
                <tr>
                    <td>${alert.timestamp}</td>
                    <td><span class="severity ${severityClass}">${alert.severity.charAt(0).toUpperCase() + alert.severity.slice(1)}</span></td>
                    <td>${alert.component}</td>
                    <td>${alert.message}</td>
                    <td><span class="${statusClass}">${alert.status}</span></td>
                    <td>
                        <button class="btn-icon" title="View Details"><i class="fas fa-eye"></i></button>
                        <button class="btn-icon" title="Acknowledge"><i class="fas fa-check"></i></button>
                        <button class="btn-icon" title="Mute"><i class="fas fa-bell-slash"></i></button>
                    </td>
                </tr>
            `;
        });
        
        tableBody.innerHTML = tableContent;
    }
    
    function initSeverityChart() {
        const ctx = document.getElementById('severityChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Info', 'Warning', 'Error', 'Critical'],
                datasets: [{
                    data: [12, 19, 7, 3],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(255, 159, 64, 0.2)',
                        'rgba(255, 99, 132, 0.2)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true
            }
        });
    }
    
    function initTrendChart() {
        const ctx = document.getElementById('trendChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['7 days ago', '6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
                datasets: [
                    {
                        label: 'Critical',
                        data: [1, 0, 2, 3, 1, 0, 2, 3],
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: 'Error',
                        data: [3, 5, 2, 1, 4, 3, 2, 7],
                        borderColor: 'rgba(255, 159, 64, 1)',
                        backgroundColor: 'rgba(255, 159, 64, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: 'Warning',
                        data: [8, 6, 7, 5, 10, 8, 12, 19],
                        borderColor: 'rgba(255, 206, 86, 1)',
                        backgroundColor: 'rgba(255, 206, 86, 0.1)',
                        tension: 0.3,
                        fill: true
                    },
                    {
                        label: 'Info',
                        data: [5, 8, 10, 4, 6, 9, 8, 12],
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.3,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        stacked: false
                    }
                }
            }
        });
    }
    
    function initComponentChart() {
        const ctx = document.getElementById('componentChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Scraper', 'Proxy Manager', 'CAPTCHA Solver', 'Database', 'API Service', 'System'],
                datasets: [{
                    label: 'Number of Alerts',
                    data: [12, 19, 8, 5, 7, 3],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)',
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 159, 64, 0.2)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}

{% block head %}
<style>
.alert-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: flex-end;
}

.alert-controls .form-group {
    flex: 1;
}

.alert-controls .btn {
    height: 38px;
}

.severity {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-weight: bold;
}

.severity-info {
    background-color: rgba(54, 162, 235, 0.2);
    color: #3498db;
}

.severity-warning {
    background-color: rgba(255, 206, 86, 0.2);
    color: #f1c40f;
}

.severity-error {
    background-color: rgba(255, 159, 64, 0.2);
    color: #e67e22;
}

.severity-critical {
    background-color: rgba(255, 99, 132, 0.2);
    color: #e74c3c;
}

.warning {
    background-color: rgba(255, 206, 86, 0.2);
    color: #f1c40f;
}

.critical {
    background-color: rgba(255, 99, 132, 0.2);
    color: #e74c3c;
}

.status-active {
    color: #e74c3c;
    font-weight: bold;
}

.status-cleared {
    color: #2ecc71;
    font-weight: bold;
}

.loading-cell {
    text-align: center;
    padding: 1rem;
}

.threshold-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.threshold-table th,
.threshold-table td {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.channel-settings {
    margin-bottom: 1.5rem;
}

.alert-settings h3 {
    font-size: 1rem;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    color: #3498db;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
}

.btn-icon:hover {
    color: #2980b9;
}

@media (max-width: 768px) {
    .alert-controls {
        flex-direction: column;
    }
}
</style>
{% endblock %}