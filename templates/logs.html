{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card full-width">
        <h2>System Logs</h2>
        <p class="subtitle">Security and access monitoring (Admin only)</p>
        
        <div class="log-controls">
            <div class="control-group">
                <label for="log-type">Log Type:</label>
                <select id="log-type">
                    <option value="security">Security Logs</option>
                    <option value="access">Access Logs</option>
                    <option value="errors">Error Logs</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="log-lines">Lines to show:</label>
                <select id="log-lines">
                    <option value="50">50</option>
                    <option value="100" selected>100</option>
                    <option value="200">200</option>
                    <option value="500">500</option>
                </select>
            </div>
            
            <button id="refresh-logs" class="btn btn-primary">
                <i class="icon-refresh"></i> Refresh Logs
            </button>
            
            <button id="auto-refresh-toggle" class="btn btn-secondary">
                <i class="icon-play"></i> Auto Refresh: Off
            </button>
        </div>
    </div>

    <div class="dashboard-card full-width">
        <h3 id="current-log-title">Security Logs</h3>
        <div class="log-stats">
            <span id="log-count">0 entries</span>
            <span id="last-updated">Last updated: Never</span>
        </div>
        
        <div class="log-container">
            <div id="log-entries" class="log-entries">
                <p class="loading">Loading logs...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.log-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.control-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #e2e8f0;
}

.control-group select {
    padding: 0.5rem;
    border: 1px solid #374151;
    border-radius: 4px;
    background: #1f2937;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.log-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #374151;
    font-size: 0.875rem;
    color: #9ca3af;
}

.log-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #374151;
    border-radius: 8px;
    background: #111827;
}

.log-entries {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
    line-height: 1.4;
    padding: 1rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-left: 3px solid transparent;
    border-radius: 4px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-entry.security {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    color: #fbbf24;
}

.log-entry.access {
    border-left-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    color: #34d399;
}

.log-entry.error {
    border-left-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    color: #f87171;
}

.log-entry.warning {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    color: #fbbf24;
}

.log-entry.info {
    border-left-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    color: #60a5fa;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.loading {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 2rem;
}

.error-message {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #ef4444;
}

.auto-refresh-active {
    background: #10b981 !important;
    color: white !important;
}

@media (max-width: 768px) {
    .log-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .log-stats {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const logTypeSelect = document.getElementById('log-type');
    const logLinesSelect = document.getElementById('log-lines');
    const refreshButton = document.getElementById('refresh-logs');
    const autoRefreshButton = document.getElementById('auto-refresh-toggle');
    const logEntries = document.getElementById('log-entries');
    const logTitle = document.getElementById('current-log-title');
    const logCount = document.getElementById('log-count');
    const lastUpdated = document.getElementById('last-updated');
    
    let autoRefreshInterval = null;
    let isAutoRefresh = false;

    // Function to format log entry based on content
    function formatLogEntry(logLine) {
        const entry = document.createElement('div');
        entry.className = 'log-entry';
        
        // Determine log type and styling based on content
        if (logLine.includes('SECURITY') || logLine.includes('404_ATTEMPT')) {
            entry.classList.add('security');
        } else if (logLine.includes('ACCESS')) {
            entry.classList.add('access');
        } else if (logLine.includes('ERROR') || logLine.includes('500_ERROR')) {
            entry.classList.add('error');
        } else if (logLine.includes('WARNING')) {
            entry.classList.add('warning');
        } else {
            entry.classList.add('info');
        }
        
        entry.textContent = logLine;
        return entry;
    }

    // Function to load logs
    async function loadLogs() {
        const logType = logTypeSelect.value;
        const lines = logLinesSelect.value;
        
        try {
            refreshButton.disabled = true;
            refreshButton.innerHTML = '<i class="icon-loading"></i> Loading...';
            
            const response = await authenticatedFetch(`/api/v1/logs/${logType}?lines=${lines}`);
            
            if (!response.ok) {
                throw new Error(`Failed to load logs: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Update title
            const titles = {
                'security': 'Security Logs',
                'access': 'Access Logs', 
                'errors': 'Error Logs'
            };
            logTitle.textContent = titles[logType] || 'System Logs';
            
            // Update stats
            logCount.textContent = `${data.total || data.logs.length} entries`;
            lastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
            
            // Clear and populate log entries
            logEntries.innerHTML = '';
            
            if (data.logs && data.logs.length > 0) {
                data.logs.forEach(logLine => {
                    const entry = formatLogEntry(logLine);
                    logEntries.appendChild(entry);
                });
                
                // Scroll to bottom to show most recent logs
                logEntries.scrollTop = logEntries.scrollHeight;
            } else {
                logEntries.innerHTML = '<p class="loading">No log entries found.</p>';
            }
            
            if (data.message) {
                const messageEntry = document.createElement('div');
                messageEntry.className = 'log-entry info';
                messageEntry.textContent = `Info: ${data.message}`;
                logEntries.appendChild(messageEntry);
            }
            
        } catch (error) {
            console.error('Error loading logs:', error);
            logEntries.innerHTML = `<div class="error-message">Error loading logs: ${error.message}</div>`;
        } finally {
            refreshButton.disabled = false;
            refreshButton.innerHTML = '<i class="icon-refresh"></i> Refresh Logs';
        }
    }

    // Function to toggle auto-refresh
    function toggleAutoRefresh() {
        if (isAutoRefresh) {
            // Stop auto-refresh
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            isAutoRefresh = false;
            autoRefreshButton.innerHTML = '<i class="icon-play"></i> Auto Refresh: Off';
            autoRefreshButton.classList.remove('auto-refresh-active');
        } else {
            // Start auto-refresh
            autoRefreshInterval = setInterval(loadLogs, 10000); // Refresh every 10 seconds
            isAutoRefresh = true;
            autoRefreshButton.innerHTML = '<i class="icon-pause"></i> Auto Refresh: On';
            autoRefreshButton.classList.add('auto-refresh-active');
        }
    }

    // Event listeners
    refreshButton.addEventListener('click', loadLogs);
    autoRefreshButton.addEventListener('click', toggleAutoRefresh);
    logTypeSelect.addEventListener('change', loadLogs);
    logLinesSelect.addEventListener('change', loadLogs);

    // Initial load
    loadLogs();
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    });
});
</script>
{% endblock %}
