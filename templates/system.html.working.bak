{% extends "base.html" %}

{% block content %}
<div class="dashboard-grid">
    <div class="dashboard-card full-width">
        <h2>System Information</h2>
        <div id="systemInfo"><p>Loading system information...</p></div>
    </div>

    <div class="dashboard-card">
        <h2>System Resource Usage (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="systemResourcesChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>API Service Health (Last 24h)</h2>
        <div class="chart-container">
            <canvas id="apiHealthChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card">
        <h2>Database Performance (Last Hour)</h2>
        <div class="chart-container">
            <canvas id="dbPerformanceChart"></canvas>
        </div>
    </div>

    <div class="dashboard-card full-width">
        <h2>Recent System Events</h2>
        <div id="systemEvents" class="events-list"><p>Loading system events...</p></div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const systemInfoDiv = document.getElementById('systemInfo');
    const systemEventsDiv = document.getElementById('systemEvents');

    // Ensure divs exist before trying to update them
    if (!systemInfoDiv) {
        console.error("System page error: 'systemInfo' div not found.");
        return; // Stop if essential elements are missing
    }
    if (!systemEventsDiv) {
        console.error("System page error: 'systemEvents' div not found.");
        return; // Stop if essential elements are missing
    }

    // Simplified fetchWithAuth for example (actual implementation might be more complex)
    // This assumes the cookie handles auth for GET requests, or token is in localStorage for manual header setting
    async function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('access_token'); // Still attempt to use localStorage for JS-initiated calls
        const headers = {
            'Content-Type': 'application/json', // Good practice to set content type for fetch
            ...options.headers
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        // Cookies will be sent automatically by the browser for same-origin requests.
        return fetch(url, { ...options, headers });
    }

    function formatKey(key) {
        const result = key.replace(/_/g, " ").replace(/([A-Z])/g, " $1"); // Handle snake_case first, then camelCase
        return result.charAt(0).toUpperCase() + result.slice(1).trim();
    }

    function loadSystemInfo() {
        console.log("Attempting to load system info...");
        systemInfoDiv.innerHTML = '<p>Loading system information...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info API response:", data);
                if (data && typeof data === 'object') {
                    let html = '<dl class="system-info-list">'; // Using a definition list for semantics
                    for (const key in data) {
                        if (data.hasOwnProperty(key)) {
                             html += `<dt>${formatKey(key)}</dt><dd>${data[key]}</dd>`;
                        }
                    }
                    html += '</dl>';
                    systemInfoDiv.innerHTML = html;
                } else {
                    console.log("System info unavailable. API returned:", data);
                    systemInfoDiv.innerHTML = '<p class="alert alert-warning">System information is currently unavailable.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system info:', error);
                systemInfoDiv.innerHTML = `<p class="alert alert-danger">Error loading system information: ${error.message}</p>`;
            });
    }

    function loadSystemEvents() {
        console.log("Attempting to load system events...");
        systemEventsDiv.innerHTML = '<p>Loading system events...</p>'; // Reset on each load attempt
        fetchWithAuth('/api/v1/system/events')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System events API response:", data);

                if (data && Array.isArray(data) && data.length > 0) {
                    let html = '';
                    data.forEach(event => {
                        html += `<div class="event-item alert alert-info">
                                     <span class="timestamp"><strong>Time:</strong> ${event.time || 'N/A'}</span>
                                     <strong class="event-type">Type:</strong> ${event.type || 'N/A'}
                                     <p><strong>Message:</strong> ${event.message || 'N/A'}</p>
                                 </div>`;
                    });
                    systemEventsDiv.innerHTML = html;
                } else {
                    systemEventsDiv.innerHTML = '<p class="alert alert-warning">No system events available.</p>';
                }
            })
            .catch(error => {
                console.error('Error loading system events:', error);
                systemEventsDiv.innerHTML = `<p class="alert alert-danger">Error loading system events: ${error.message}</p>`;
            });
    }

    // Chart initialization functions
    function initSystemResourcesChart() {
        const canvas = document.getElementById('systemResourcesChart');
        if (!canvas) {
            console.error("System Resources Chart canvas not found!");
            return;
        }

        console.log("Loading system resources chart...");

        // Get current system info and create a simple chart
        fetchWithAuth('/api/v1/system/info')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("System info for resources chart:", data);

                // Extract percentage values from the system info
                let cpuUsage = 0;
                let memoryUsage = 0;
                let diskUsage = 0;

                // Parse CPU usage
                if (data.cpu_usage && data.cpu_usage.includes('%')) {
                    cpuUsage = parseFloat(data.cpu_usage.replace('%', ''));
                }

                // Parse memory usage
                if (data.memory_usage && data.memory_usage.includes('(') && data.memory_usage.includes('%)')) {
                    const match = data.memory_usage.match(/\((\d+\.?\d*)%\)/);
                    if (match) {
                        memoryUsage = parseFloat(match[1]);
                    }
                }

                // Parse disk usage
                if (data.disk_usage && data.disk_usage.includes('(') && data.disk_usage.includes('%)')) {
                    const match = data.disk_usage.match(/\((\d+\.?\d*)%\)/);
                    if (match) {
                        diskUsage = parseFloat(match[1]);
                    }
                }

                console.log("Parsed usage values:", {cpuUsage, memoryUsage, diskUsage});

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['CPU Usage', 'Memory Usage', 'Disk Usage'],
                        datasets: [{
                            data: [cpuUsage, memoryUsage, diskUsage],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 205, 86, 0.8)'
                            ],
                            borderColor: [
                                'rgb(255, 99, 132)',
                                'rgb(54, 162, 235)',
                                'rgb(255, 205, 86)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Current System Resource Usage'
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed + '%';
                                    }
                                }
                            }
                        }
                    }
                });
                console.log("System Resources Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading system resources:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No system resource data available.</p>';
            });
    }

    function initApiHealthChart() {
        const canvas = document.getElementById('apiHealthChart');
        if (!canvas) {
            console.error("API Health Chart canvas not found!");
            return;
        }

        console.log("Loading API health chart...");
        fetchWithAuth('/api/v1/system/api-health')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("API health data:", data);

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Uptime', 'Success Rate', 'Requests/Min', 'Error Rate'],
                        datasets: [{
                            label: 'API Health Metrics',
                            data: [
                                parseFloat(data.uptime?.replace('%', '') || '0'),
                                parseFloat(data.success_rate?.replace('%', '') || '0'),
                                parseFloat(data.requests_per_minute || '0'),
                                parseFloat(data.error_rate?.replace('%', '') || '0')
                            ],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(255, 206, 86, 0.6)',
                                'rgba(255, 99, 132, 0.6)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 206, 86, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'API Service Health Metrics'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log("API Health Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading API health:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No API health data available.</p>';
            });
    }

    function initDbPerformanceChart() {
        const canvas = document.getElementById('dbPerformanceChart');
        if (!canvas) {
            console.error("DB Performance Chart canvas not found!");
            return;
        }

        console.log("Loading database performance chart...");
        fetchWithAuth('/api/v1/system/db-performance?hours=1')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("DB performance data:", data);

                const ctx = canvas.getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Queries/Min', 'Success Rate'],
                        datasets: [{
                            label: 'Database Performance',
                            data: [
                                parseFloat(data.queries_per_minute || '0'),
                                parseFloat(data.success_rate?.replace('%', '') || '0')
                            ],
                            backgroundColor: [
                                'rgba(153, 102, 255, 0.6)',
                                'rgba(255, 159, 64, 0.6)'
                            ],
                            borderColor: [
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Database Performance Metrics'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log("DB Performance Chart created successfully!");
            })
            .catch(error => {
                console.error('Error loading DB performance:', error);
                canvas.parentElement.innerHTML = '<p class="alert alert-warning">No database performance data available.</p>';
            });
    }

    // Load initial data
    loadSystemInfo();
    loadSystemEvents();

    // Initialize charts with delay to ensure DOM is ready
    console.log("Initializing charts...");
    setTimeout(() => {
        console.log("Starting chart initialization...");
        initSystemResourcesChart();
        initApiHealthChart();
        initDbPerformanceChart();
        console.log("Chart initialization complete.");
    }, 1000);

});
</script>
{% endblock %}

{% block head %}
<style>
.full-width {
    grid-column: 1 / -1; /* Span all columns */
}
.system-info-list dt {
    font-weight: bold;
    margin-top: 0.5rem;
}
.system-info-list dd {
    margin-left: 1rem;
    margin-bottom: 0.5rem;
}
.events-list .event-item {
    margin-bottom: 10px;
    padding: 10px;
    border-left-width: 5px;
}
.events-list .timestamp {
    display: block;
    font-size: 0.9em;
    color: #555;
    margin-bottom: 5px;
}
.events-list .event-type {
    font-weight: bold;
}
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
    padding: 10px;
}
.chart-container canvas {
    max-height: 280px;
}
</style>
{% endblock %}
