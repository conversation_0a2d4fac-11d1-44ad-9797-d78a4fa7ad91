#!/usr/bin/env python3
"""
Monitoring Extension Module

This module extends the Monitor class with additional functionality
for system monitoring, database performance tracking, and API health checks.
"""

import os
import time
import datetime
import logging
import psycopg2
from typing import Dict, Any, List, Optional, Union
import traceback

# Try importing optional dependencies
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available. System monitoring will be limited.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def extend_monitor_class():
    """
    Extend the Monitor class with additional functionality.
    This function is called when the module is imported.
    """
    from monitoring import Monitor

    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system information including CPU, memory, disk usage, etc.

        Returns:
            Dictionary with system information
        """
        try:
            if not PSUTIL_AVAILABLE:
                return {
                    "hostname": "N/A",
                    "cpu_usage": "N/A",
                    "memory_usage": "N/A",
                    "disk_usage": "N/A",
                    "os_info": "N/A",
                    "kernel": "N/A",
                    "cpu_info": "N/A",
                    "python_version": "N/A",
                    "uptime": "N/A",
                    "load_avg": "N/A"
                }

            import platform
            from datetime import datetime

            # Get CPU info - use shorter interval for faster response
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Get memory info
            memory = psutil.virtual_memory()
            memory_used = round(memory.used / (1024 * 1024))
            memory_total = round(memory.total / (1024 * 1024))
            memory_percent = memory.percent

            # Get disk info
            disk = psutil.disk_usage('/')
            disk_used = round(disk.used / (1024 * 1024 * 1024))
            disk_total = round(disk.total / (1024 * 1024 * 1024))
            disk_percent = disk.percent

            # Get system uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time

            # Format uptime string to avoid long line
            days_part = f"up {uptime.days} days"
            hours_part = f"{uptime.seconds // 3600}:{(uptime.seconds // 60) % 60:02d}"
            uptime_str = f"{days_part} {hours_part}"

            # Get load average
            load_avg = psutil.getloadavg()

            # Format system info
            system_info = {
                "hostname": platform.node(),
                "cpu_usage": f"{cpu_percent}%",
                "memory_usage": f"{memory_used}/{memory_total}MB ({memory_percent}%)",
                "disk_usage": f"{disk_used}/{disk_total}GB ({disk_percent}%)",
                "os_info": platform.platform(),
                "kernel": platform.release(),
                "cpu_info": platform.processor(),
                "python_version": platform.python_version(),
                "uptime": uptime_str,
                "load_avg": f"{load_avg[0]:.2f}, {load_avg[1]:.2f}, {load_avg[2]:.2f}"
            }

            return system_info
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {
                "hostname": "N/A",
                "cpu_usage": "N/A",
                "memory_usage": "N/A",
                "disk_usage": "N/A",
                "os_info": "N/A",
                "kernel": "N/A",
                "cpu_info": "N/A",
                "python_version": "N/A",
                "uptime": "N/A",
                "load_avg": "N/A"
            }

    def get_system_resources(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get historical system resource usage (CPU, memory, disk).

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with system resource data
        """
        try:
            if not hasattr(self, 'connection') or self.connection is None:
                return {
                    "labels": [],
                    "cpu_usage": [],
                    "memory_usage": [],
                    "disk_usage": []
                }

            cursor = self.connection.cursor()

            # Calculate timestamp for hours ago
            time_now = datetime.datetime.now()
            time_limit = time_now - datetime.timedelta(hours=hours)

            cursor.execute('''
                SELECT
                    timestamp,
                    cpu_usage,
                    memory_usage,
                    disk_usage
                FROM system_metrics
                WHERE timestamp > %s
                ORDER BY timestamp ASC
            ''', (time_limit,))

            results = cursor.fetchall()
            cursor.close()

            labels = []
            cpu_usage = []
            memory_usage = []
            disk_usage = []

            for result in results:
                timestamp, cpu, memory, disk = result
                labels.append(timestamp.strftime('%H:%M:%S'))
                cpu_usage.append(float(cpu))
                memory_usage.append(float(memory))
                disk_usage.append(float(disk))

            return {
                "labels": labels,
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage
            }
        except Exception as e:
            logger.error(f"Error getting system resources: {e}")
            return {
                "labels": [],
                "cpu_usage": [],
                "memory_usage": [],
                "disk_usage": []
            }

    def get_api_health(self) -> Dict[str, Any]:
        """
        Get API service health statistics.

        Returns:
            Dictionary with API health metrics
        """
        try:
            if not hasattr(self, 'connection') or self.connection is None:
                return {
                    "uptime": "N/A",
                    "success_rate": "N/A",
                    "avg_response_time": "N/A",
                    "requests_per_minute": "N/A",
                    "error_rate": "N/A",
                    "endpoints": []
                }

            cursor = self.connection.cursor()

            # Calculate timestamp for 1 hour ago
            time_now = datetime.datetime.now()
            time_limit = time_now - datetime.timedelta(hours=1)

            # Get API metrics from the last hour
            cursor.execute('''
                SELECT
                    endpoint,
                    AVG(response_time) as avg_response_time,
                    COUNT(*) as request_count,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as success_count
                FROM api_metrics
                WHERE timestamp > %s
                GROUP BY endpoint
                ORDER BY request_count DESC
            ''', (time_limit,))

            results = cursor.fetchall()

            # Get total requests and success rate
            cursor.execute('''
                SELECT
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as total_success
                FROM api_metrics
                WHERE timestamp > %s
            ''', (time_limit,))

            total_result = cursor.fetchone()
            cursor.close()

            # Calculate metrics
            if total_result and total_result[0]:
                total_requests = total_result[0]
                total_success = total_result[1] or 0

                success_rate = (total_success / total_requests) * 100
                error_rate = 100 - success_rate
                requests_per_minute = total_requests / 60
            else:
                success_rate = 0
                error_rate = 0
                requests_per_minute = 0

            # Format endpoint data
            endpoints = []
            for result in results:
                endpoint, avg_resp_time, request_count, success_count = result

                if request_count > 0:
                    endpoint_success_rate = (success_count / request_count) * 100
                    avg_resp_str = f"{avg_resp_time:.2f} ms" if avg_resp_time else "N/A"
                else:
                    endpoint_success_rate = 0
                    avg_resp_str = "N/A"

                endpoints.append({
                    "endpoint": endpoint,
                    "avg_response_time": avg_resp_str,
                    "request_count": request_count,
                    "success_rate": f"{endpoint_success_rate:.1f}%"
                })

            return {
                "uptime": "100%",  # Placeholder - would need actual uptime tracking
                "success_rate": f"{success_rate:.1f}%",
                "avg_response_time": "N/A" if not total_result or not total_result[0]
                    else f"{total_result[0]:.2f} ms",
                "requests_per_minute": f"{requests_per_minute:.1f}",
                "error_rate": f"{error_rate:.1f}%",
                "endpoints": endpoints
            }
        except Exception as e:
            logger.error(f"Error getting API health: {e}")
            return {
                "uptime": "N/A",
                "success_rate": "N/A",
                "avg_response_time": "N/A",
                "requests_per_minute": "N/A",
                "error_rate": "N/A",
                "endpoints": []
            }

    def get_db_performance(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get database performance metrics.

        Args:
            hours: Number of hours to look back

        Returns:
            Dictionary with database performance data
        """
        try:
            if not hasattr(self, 'connection') or self.connection is None:
                return {
                    "queries_per_minute": "N/A",
                    "avg_query_time": "N/A",
                    "success_rate": "N/A",
                    "query_types": []
                }

            cursor = self.connection.cursor()

            # Calculate timestamp for the specified hours ago
            time_now = datetime.datetime.now()
            time_limit = time_now - datetime.timedelta(hours=hours)

            # Get DB performance metrics grouped by query type
            cursor.execute('''
                SELECT
                    query_type,
                    AVG(query_duration) as avg_duration,
                    COUNT(*) as query_count,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as success_count
                FROM db_performance
                WHERE timestamp > %s
                GROUP BY query_type
                ORDER BY query_count DESC
            ''', (time_limit,))

            results = cursor.fetchall()

            # Get total queries and success rate
            cursor.execute('''
                SELECT
                    COUNT(*) as total_queries,
                    SUM(CASE WHEN success=true THEN 1 ELSE 0 END) as total_success,
                    AVG(query_duration) as avg_duration
                FROM db_performance
                WHERE timestamp > %s
            ''', (time_limit,))

            total_result = cursor.fetchone()
            cursor.close()

            # Calculate metrics
            if total_result and total_result[0]:
                total_queries = total_result[0]
                total_success = total_result[1] or 0
                avg_duration = total_result[2] or 0

                if total_queries > 0:
                    success_rate = (total_success / total_queries) * 100
                    queries_per_minute = total_queries / (hours * 60)
                else:
                    success_rate = 0
                    queries_per_minute = 0
            else:
                total_queries = 0
                success_rate = 0
                queries_per_minute = 0
                avg_duration = 0

            # Format query type data
            query_types = []
            for result in results:
                qtype, avg_dur, query_count, success_count = result

                if query_count > 0:
                    type_success_rate = (success_count / query_count) * 100
                    avg_dur_str = f"{avg_dur:.2f} ms" if avg_dur else "N/A"
                else:
                    type_success_rate = 0
                    avg_dur_str = "N/A"

                query_types.append({
                    "type": qtype,
                    "avg_duration": avg_dur_str,
                    "query_count": query_count,
                    "success_rate": f"{type_success_rate:.1f}%"
                })

            return {
                "queries_per_minute": f"{queries_per_minute:.1f}",
                "avg_query_time": f"{avg_duration:.2f} ms" if avg_duration else "N/A",
                "success_rate": f"{success_rate:.1f}%",
                "query_types": query_types
            }
        except Exception as e:
            logger.error(f"Error getting DB performance: {e}")
            return {
                "queries_per_minute": "N/A",
                "avg_query_time": "N/A",
                "success_rate": "N/A",
                "query_types": []
            }

    def get_system_events(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Get recent system events.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of system events
        """
        try:
            if not hasattr(self, 'connection') or self.connection is None:
                return []

            cursor = self.connection.cursor()

            cursor.execute('''
                SELECT
                    timestamp,
                    event_type,
                    severity,
                    message,
                    details
                FROM system_events
                ORDER BY timestamp DESC
                LIMIT %s
            ''', (limit,))

            results = cursor.fetchall()
            cursor.close()

            events = []
            for result in results:
                timestamp, event_type, severity, message, details = result
                events.append({
                    "timestamp": timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    "event_type": event_type,
                    "severity": severity,
                    "message": message,
                    "details": details
                })

            return events
        except Exception as e:
            logger.error(f"Error getting system events: {e}")
            return []

    def add_proxy(self, proxy_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new proxy to the system.

        Args:
            proxy_data: Dictionary with proxy details

        Returns:
            Dictionary with result of operation
        """
        try:
            if not hasattr(self, 'connection') or self.connection is None:
                return {"success": False, "message": "Database not available"}

            # Map frontend field names to backend field names if needed
            mapped_data = proxy_data.copy()

            # Handle field name differences between frontend and backend
            if 'ip' in proxy_data and 'ip_address' not in proxy_data:
                mapped_data['ip_address'] = proxy_data['ip']

            if 'type' in proxy_data and 'proxy_type' not in proxy_data:
                mapped_data['proxy_type'] = proxy_data['type']

            if 'group' in proxy_data and 'proxy_group' not in proxy_data:
                mapped_data['proxy_group'] = proxy_data['group']

            # Validate required fields
            required_fields = ["ip_address", "port", "proxy_type"]
            missing_fields = [f for f in required_fields if f not in mapped_data]

            if missing_fields:
                return {
                    "success": False,
                    "message": f"Missing required fields: {', '.join(missing_fields)}"
                }

            cursor = self.connection.cursor()

            # Check if proxy already exists
            cursor.execute('''
                SELECT id FROM proxies
                WHERE ip_address = %s AND port = %s
            ''', (mapped_data['ip_address'], mapped_data['port']))

            existing_proxy = cursor.fetchone()
            if existing_proxy:
                cursor.close()
                return {"success": False, "message": "Proxy already exists"}

            # Insert new proxy
            cursor.execute('''
                INSERT INTO proxies (
                    ip_address,
                    port,
                    proxy_type,
                    username,
                    password,
                    proxy_group,
                    location
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            ''', (
                mapped_data['ip_address'],
                mapped_data['port'],
                mapped_data['proxy_type'],
                mapped_data.get('username'),
                mapped_data.get('password'),
                mapped_data.get('proxy_group'),
                mapped_data.get('location')
            ))

            new_id = cursor.fetchone()[0]
            self.connection.commit()
            cursor.close()

            return {
                "success": True,
                "message": "Proxy added successfully",
                "id": new_id
            }
        except Exception as e:
            logger.error(f"Error adding proxy: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}

    # Add the methods to the Monitor class
    Monitor.get_system_info = get_system_info
    Monitor.get_system_resources = get_system_resources
    Monitor.get_api_health = get_api_health
    Monitor.get_db_performance = get_db_performance
    Monitor.get_system_events = get_system_events
    Monitor.add_proxy = add_proxy

    logger.info("Successfully extended Monitor class with additional functionality")

# Execute extension when imported
extend_monitor_class()
