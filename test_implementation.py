#!/usr/bin/env python3
"""
Test script for NanoLLM implementation.
"""

import os
import logging
import json
import torch
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_semantic_velocity_analyzer():
    """Test SemanticVelocityAnalyzer implementation."""
    try:
        from semantic_velocity_analyzer import SemanticVelocityAnalyzer
        logger.info("SemanticVelocityAnalyzer imported successfully")
        
        # Create analyzer
        analyzer = SemanticVelocityAnalyzer()
        logger.info("SemanticVelocityAnalyzer initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error testing SemanticVelocityAnalyzer: {e}")
        return False

def test_hybrid_distillation_gate():
    """Test HybridDistillationGate implementation."""
    try:
        from hybrid_distillation_gate import HybridDistillationGate, RateLimitedTeacher
        logger.info("HybridDistillationGate imported successfully")
        
        # Create teachers
        teachers = {
            "teacher1": RateLimitedTeacher(
                req_limit=0.25,
                token_limit=0.0023,
                sanitizer=None,
                api_key="test_key",
                base_url="https://api.example.com",
                model_name="test_model"
            ),
            "teacher2": RateLimitedTeacher(
                req_limit=0.5,
                token_limit=0.081,
                sanitizer=None,
                api_key="test_key",
                base_url="https://api.example.com",
                model_name="test_model"
            )
        }
        
        # Create gate
        gate = HybridDistillationGate(
            teachers=teachers,
            rate_limits=[(0.25, 0.0023), (0.5, 0.081)]
        )
        logger.info("HybridDistillationGate initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error testing HybridDistillationGate: {e}")
        return False

def test_meta_learning_integration():
    """Test MetaLearningIntegrator implementation."""
    try:
        from meta_learning_integration import MetaLearningIntegrator
        logger.info("MetaLearningIntegrator imported successfully")
        
        # Create model
        model = torch.nn.Sequential(
            torch.nn.Linear(10, 10),
            torch.nn.ReLU(),
            torch.nn.Linear(10, 1)
        )
        
        # Create integrator
        integrator = MetaLearningIntegrator(model=model)
        logger.info("MetaLearningIntegrator initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error testing MetaLearningIntegrator: {e}")
        return False

def test_performance_optimizations():
    """Test PerformanceOptimizer implementation."""
    try:
        from performance_optimizations import PerformanceOptimizer, MemoryTracker
        logger.info("PerformanceOptimizer imported successfully")
        
        # Create model
        model = torch.nn.Sequential(
            torch.nn.Linear(10, 10),
            torch.nn.ReLU(),
            torch.nn.Linear(10, 1)
        )
        
        # Create optimizer
        optimizer = PerformanceOptimizer(
            model=model,
            use_mixed_precision=True,
            use_gradient_checkpointing=True,
            memory_limit_mb=500
        )
        logger.info("PerformanceOptimizer initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error testing PerformanceOptimizer: {e}")
        return False

def main():
    """Main entry point."""
    logger.info("Testing NanoLLM implementation")
    
    # Test components
    results = {
        "semantic_velocity_analyzer": test_semantic_velocity_analyzer(),
        "hybrid_distillation_gate": test_hybrid_distillation_gate(),
        "meta_learning_integration": test_meta_learning_integration(),
        "performance_optimizations": test_performance_optimizations()
    }
    
    # Print results
    logger.info("Test results:")
    for component, success in results.items():
        logger.info(f"  {component}: {'SUCCESS' if success else 'FAILURE'}")
    
    # Overall result
    if all(results.values()):
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed")
        return 1

if __name__ == "__main__":
    exit(main())
