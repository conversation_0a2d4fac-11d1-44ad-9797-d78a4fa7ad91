#!/usr/bin/env python3
"""
Test navigation click interception by simulating the JavaScript behavior
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def simulate_navigation_click_test():
    """Simulate what happens when clicking navigation links"""
    
    print("=== Testing Navigation Click Interception ===\n")
    
    # Test routes that should require authentication
    protected_routes = ["/scrapers", "/proxies", "/captchas", "/alerts", "/system", "/account"]
    
    print("1. Testing unauthenticated navigation clicks:")
    print("-" * 60)
    
    for route in protected_routes:
        print(f"\n🖱️  Simulating click on '{route}' link...")
        
        # Step 1: JavaScript would check localStorage for token (simulate: no token)
        print("   ❌ No token in localStorage")
        
        # Step 2: JavaScript would make a fetch request to check access
        try:
            response = requests.get(f"{BASE_URL}{route}", 
                                  headers={'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'},
                                  allow_redirects=False)
            print(f"   📡 Fetch request result: {response.status_code} {response.reason}")
            
            if response.status_code == 401:
                print(f"   ➡️  JavaScript should redirect to login (401 Unauthorized)")
                print(f"   ✅ EXPECTED: User redirected to /login")
            else:
                print(f"   ❌ UNEXPECTED: Should have been 401, got {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
    
    print(f"\n2. Testing authenticated navigation clicks:")
    print("-" * 60)
    
    # Login to get token
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/api/v1/auth/token", data=login_data)
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get('access_token')
            print(f"✅ Login successful, got token: {token[:20]}...")
            
            # Test protected routes with authentication
            for route in protected_routes:
                print(f"\n🖱️  Simulating authenticated click on '{route}' link...")
                print(f"   ✅ Token found in localStorage")
                
                try:
                    response = requests.get(f"{BASE_URL}{route}", 
                                          headers={
                                              'Authorization': f'Bearer {token}',
                                              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                                          },
                                          allow_redirects=False)
                    print(f"   📡 Fetch request result: {response.status_code} {response.reason}")
                    
                    if response.status_code == 200:
                        print(f"   ➡️  JavaScript should navigate to {route}")
                        print(f"   ✅ EXPECTED: User successfully navigated")
                    elif response.status_code == 401:
                        print(f"   ➡️  JavaScript should redirect to login (token expired)")
                        print(f"   ⚠️  WARNING: Token may be expired")
                    else:
                        print(f"   ❌ UNEXPECTED: Got {response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ ERROR: {e}")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ Login error: {e}")
    
    print(f"\n3. Testing browser navigation behavior:")
    print("-" * 60)
    print("🌐 Open http://localhost:8000/login in browser")
    print("🔑 Login with admin/admin")
    print("🖱️  Click on any navigation tab (scrapers, proxies, etc.)")
    print("✅ Should navigate successfully to the page")
    print("🚪 Logout and try clicking navigation tabs again")
    print("➡️  Should automatically redirect to login page")

if __name__ == "__main__":
    simulate_navigation_click_test()
