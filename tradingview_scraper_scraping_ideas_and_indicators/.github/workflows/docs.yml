name: docs
on:
  push:
    branches: [ "dev", "main" ]

jobs:
  docs:
    name: <PERSON>s
    runs-on: ubuntu-latest
    steps:

    - uses: actions/checkout@v4

    - name: Set up Python 3.8
      uses: actions/setup-python@v3
      with:
        python-version: "3.8"

    - name: Install requirements
      run: |
        pip3 install sphinx-rtd-theme
    
    - name: Build docs
      run: |
        cd docs
        make html
    # https://github.com/peaceiris/actions-gh-pages
    - name: Deploy
      if: success()
      uses: peaceiris/actions-gh-pages@v3
      with:
        publish_branch: gh-pages
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: docs/_build/html/
