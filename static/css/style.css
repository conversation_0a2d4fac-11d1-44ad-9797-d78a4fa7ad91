/* Main styles for Trend Crawler Dashboard - Futuristic Design */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

:root {
    /* Futuristic Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --error-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    
    /* Modern Dark Theme */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f35;
    --bg-tertiary: #252b47;
    --bg-glass: rgba(255, 255, 255, 0.05);
    --bg-glass-strong: rgba(255, 255, 255, 0.08);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #a8b2d1;
    --text-tertiary: #6c7293;
    --text-accent: #64b5f6;
    
    /* Modern Colors */
    --primary-color: #667eea;
    --secondary-color: #43e97b;
    --accent-color: #4facfe;
    --warning-color: #fee140;
    --error-color: #ff9a9e;
    --success-color: #43e97b;
    
    /* Shadows & Effects */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
    --shadow-glow-accent: 0 0 20px rgba(79, 172, 254, 0.3);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(67, 233, 123, 0.05) 0%, transparent 50%);
    min-height: 100vh;
    font-weight: 400;
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Modern Header with Glassmorphism */
header {
    background: rgba(26, 31, 53, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition-normal);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
}

.logo {
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.02em;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

nav ul li {
    position: relative;
}

nav a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 12px 20px;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.95rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

nav a:hover {
    color: var(--text-primary);
    background: var(--bg-glass);
    transform: translateY(-1px);
}

nav a:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: var(--transition-normal);
    z-index: -1;
    opacity: 0;
}

nav a:hover:before {
    left: 0;
    opacity: 0.1;
}

/* Special styling for logout button */
nav a.logout-btn {
    background: linear-gradient(135deg, var(--accent-color), #e74c3c);
    color: white;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

nav a.logout-btn:hover {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

main {
    padding: 3rem 0;
    min-height: calc(100vh - 140px);
}

h1 {
    margin-bottom: 3rem;
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.02em;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

/* Futuristic Dashboard Cards */
.dashboard-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(102, 126, 234, 0.3);
}

.dashboard-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

.dashboard-card h2 {
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    color: var(--text-primary);
    font-weight: 600;
    letter-spacing: -0.01em;
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.chart-container {
    height: 280px;
    margin-bottom: 1.5rem;
    border-radius: var(--radius-md);
    overflow: hidden;
}

.card-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-gradient);
    color: var(--text-primary);
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary-gradient);
}

.btn-secondary {
    background: var(--bg-tertiary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
}

.btn-secondary:hover {
    background: var(--bg-glass-strong);
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.875rem;
    min-width: auto;
}

.btn-edit {
    background: var(--accent-gradient);
}

.btn-edit:hover {
    box-shadow: var(--shadow-glow-accent);
}

.btn-reset {
    background: var(--warning-gradient);
}

.btn-delete {
    background: var(--error-gradient);
}

/* Modern Alert Styles */
.alerts-list {
    max-height: 300px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.alerts-list::-webkit-scrollbar {
    width: 6px;
}

.alerts-list::-webkit-scrollbar-track {
    background: transparent;
}

.alerts-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 3px;
}

.alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert:last-child {
    margin-bottom: 0;
}

.alert-warning {
    background: rgba(254, 225, 64, 0.1);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.alert-error {
    background: rgba(255, 154, 158, 0.1);
    border-color: var(--error-color);
    color: var(--text-primary);
}

.alert-info {
    background: rgba(79, 172, 254, 0.1);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

.alert-success {
    background: rgba(67, 233, 123, 0.1);
    border-color: var(--success-color);
    color: var(--text-primary);
}

.timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
    margin-bottom: 0.25rem;
}

footer {
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
    color: #7f8c8d;
    font-size: 0.875rem;
}

/* Modern Form Styles */
.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.95rem;
    letter-spacing: 0.02em;
}

.form-control {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    background: var(--bg-glass);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition-normal);
    outline: none;
    position: relative;
    z-index: 10;
}

/* Enhanced Select/Dropdown Styling */
select.form-control,
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a8b2d1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.25em 1.25em;
    padding-right: 3rem;
    cursor: pointer;
    background-color: var(--bg-glass);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

select:focus,
select.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-glass-strong);
    transform: translateY(-1px);
    z-index: 1001;
}

select option {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.75rem;
    border: none;
}

select:hover {
    background: var(--bg-glass-strong);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Dropdown menu positioning fixes */
.dropdown,
.dropdown-menu,
[data-toggle="dropdown"] {
    position: relative;
    z-index: 1000;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1001;
    display: none;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    background: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: var(--text-secondary);
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    transition: var(--transition-fast);
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: var(--text-primary);
    background-color: var(--bg-glass);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-glass-strong);
    transform: translateY(-1px);
    z-index: 1001;
}

.form-control:disabled,
.form-control[readonly] {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
    cursor: not-allowed;
    opacity: 0.6;
}

.form-control::placeholder {
    color: var(--text-tertiary);
}

/* Advanced Table Styles */
.table-container {
    background: var(--bg-glass);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-md);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
}

thead {
    background: rgba(102, 126, 234, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    padding: 1.5rem 1.25rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
    letter-spacing: 0.02em;
    text-transform: uppercase;
    border-bottom: none;
}

td {
    padding: 1.25rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.95rem;
    vertical-align: middle;
}

tbody tr {
    transition: var(--transition-fast);
    position: relative;
}

tbody tr:hover {
    background: rgba(255, 255, 255, 0.03);
    transform: scale(1.001);
}

tbody tr:hover td {
    color: var(--text-primary);
}

/* Status indicators */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.status-online {
    background: var(--success-gradient);
    color: white;
}

.status-offline {
    background: var(--error-gradient);
    color: white;
}

.status-warning {
    background: var(--warning-gradient);
    color: white;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 14, 26, 0.8);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    position: relative;
    transform: scale(0.9);
    animation: modalSlideIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.9) translateY(20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close {
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-tertiary);
    cursor: pointer;
    transition: var(--transition-fast);
    padding: 0;
    line-height: 1;
}

.close:hover {
    color: var(--text-primary);
    transform: scale(1.1);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 20px;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    nav {
        flex-direction: column;
        padding: 1rem;
    }

    nav ul {
        margin-top: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .dashboard-card {
        padding: 1.5rem;
    }

    h1 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .modal-content {
        padding: 2rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    table {
        font-size: 0.875rem;
    }

    th, td {
        padding: 1rem 0.75rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    nav ul {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    nav a {
        text-align: center;
        width: 100%;
    }

    .modal-content {
        padding: 1.5rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Authentication Pages */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 2rem 0;
}

.auth-card {
    background: var(--bg-glass);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 450px;
    position: relative;
    overflow: hidden;
}

.auth-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.8;
}

.auth-form {
    margin-bottom: 2rem;
}

.auth-footer {
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    margin-top: 2rem;
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.auth-footer a:hover {
    color: var(--text-primary);
    text-decoration: underline;
}

.auth-message {
    margin-top: 1.5rem;
}

/* Enhanced alert styles */
.alert {
    position: relative;
    border-radius: var(--radius-md);
    border: none;
    border-left: 4px solid;
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    animation: slideInAlert 0.3s ease-out;
}

@keyframes slideInAlert {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Form text and helper styles */
.form-text {
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
}

.form-text.text-tertiary {
    color: var(--text-tertiary);
}

/* Enhanced checkbox styling */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    border-radius: var(--radius-sm);
}

/* User role specific styling */
.user-role {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.user-role:not(.admin) {
    background: rgba(79, 172, 254, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(79, 172, 254, 0.3);
}

.user-role.admin {
    background: var(--error-gradient);
    color: white;
    border: 1px solid transparent;
}

/* Action buttons in tables */
.user-actions-cell {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

/* Enhanced divider */
.divider {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    margin: 2rem 0;
}

/* Print styles */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
    
    nav, footer, .btn, .modal {
        display: none !important;
    }
    
    .dashboard-card {
        border: 1px solid #ccc !important;
        break-inside: avoid;
        margin-bottom: 1rem;
    }
}
