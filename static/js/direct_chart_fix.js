/**
 * Direct Chart Fix for Trend-Crawler Dashboard
 * This script provides a direct fix for chart issues
 */

// Execute immediately to ensure it runs before other scripts
(function() {
    console.log("Loading direct chart fix...");
    
    // Store original Chart constructor
    const OriginalChart = window.Chart;
    
    // Create a registry to track all charts
    window.chartRegistry = {};
    
    // Override Chart constructor
    window.Chart = function(ctx, config) {
        // Get canvas ID
        const canvasId = ctx.canvas ? ctx.canvas.id : 'unknown-chart';
        
        // If there's an existing chart with this ID, destroy it properly
        if (window.chartRegistry[canvasId]) {
            try {
                window.chartRegistry[canvasId].destroy();
                delete window.chartRegistry[canvasId];
            } catch (e) {
                console.warn(`Error destroying existing chart ${canvasId}:`, e);
                
                // Force clean up by replacing the canvas
                const canvas = document.getElementById(canvasId);
                if (canvas && canvas.parentNode) {
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = canvasId;
                    canvas.parentNode.replaceChild(newCanvas, canvas);
                    
                    // Update context to use the new canvas
                    ctx = newCanvas.getContext('2d');
                }
            }
        }
        
        // Create new chart
        const chart = new OriginalChart(ctx, config);
        
        // Register chart
        window.chartRegistry[canvasId] = chart;
        
        // Add a proper destroy method if it doesn't exist
        if (!chart.destroy || typeof chart.destroy !== 'function') {
            chart.destroy = function() {
                // Remove from registry
                delete window.chartRegistry[canvasId];
                
                // Replace canvas
                const canvas = document.getElementById(canvasId);
                if (canvas && canvas.parentNode) {
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = canvasId;
                    canvas.parentNode.replaceChild(newCanvas, canvas);
                }
            };
        }
        
        return chart;
    };
    
    // Copy static properties from original Chart
    for (const prop in OriginalChart) {
        if (OriginalChart.hasOwnProperty(prop)) {
            window.Chart[prop] = OriginalChart[prop];
        }
    }
    
    // Add utility methods
    window.Chart.getChart = function(canvasId) {
        return window.chartRegistry[canvasId];
    };
    
    window.Chart.destroyChart = function(canvasId) {
        const chart = window.chartRegistry[canvasId];
        if (chart) {
            try {
                chart.destroy();
            } catch (e) {
                console.warn(`Error destroying chart ${canvasId}:`, e);
                
                // Force clean up by replacing the canvas
                const canvas = document.getElementById(canvasId);
                if (canvas && canvas.parentNode) {
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = canvasId;
                    canvas.parentNode.replaceChild(newCanvas, canvas);
                }
            }
            delete window.chartRegistry[canvasId];
            return true;
        }
        return false;
    };
    
    // Fix for specific charts
    window.fixCharts = function() {
        // List of chart IDs to check
        const chartIds = [
            'successRateChart',
            'responseTimeChart',
            'captchaRateChart',
            'requestVolumeChart',
            'resourceChart',
            'apiHealthChart',
            'dbPerformanceChart'
        ];
        
        // Fix each chart
        chartIds.forEach(chartId => {
            // Check if chart exists in global scope
            if (window[chartId] && (!window[chartId].destroy || typeof window[chartId].destroy !== 'function')) {
                console.log(`Fixing ${chartId}`);
                
                // Add destroy method
                window[chartId].destroy = function() {
                    // Replace canvas
                    const canvas = document.getElementById(chartId);
                    if (canvas && canvas.parentNode) {
                        const newCanvas = document.createElement('canvas');
                        newCanvas.id = chartId;
                        canvas.parentNode.replaceChild(newCanvas, canvas);
                    }
                    
                    // Remove reference
                    window[chartId] = null;
                };
            }
        });
    };
    
    // Run fix when DOM is loaded
    document.addEventListener('DOMContentLoaded', window.fixCharts);
    
    // Also run immediately in case DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        window.fixCharts();
    }
    
    // Run periodically to catch any new charts
    setInterval(window.fixCharts, 2000);
})();
