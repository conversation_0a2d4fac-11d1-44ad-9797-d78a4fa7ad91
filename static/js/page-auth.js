/* Page-Level Authentication Middleware */

// This script provides automatic authentication for individual pages
// Include this script in templates that require authentication

window.PageAuth = (function() {
    'use strict';
    
    let isAuthChecked = false;
    let authCallbacks = [];
    
    // Configuration
    const config = {
        loginUrl: '/login',
        publicPaths: ['/login', '/auth_test_comprehensive.html'],
        apiAuthEndpoint: '/api/v1/auth/me',
        tokenKey: 'access_token',
        userDataKey: 'user_data',
        checkInterval: 5 * 60 * 1000, // 5 minutes
        showLoadingOverlay: true
    };
    
    // Utility functions
    const utils = {
        getToken() {
            return localStorage.getItem(config.tokenKey);
        },
        
        removeToken() {
            localStorage.removeItem(config.tokenKey);
            localStorage.removeItem(config.userDataKey);
        },
        
        isTokenExpired(token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                return Date.now() / 1000 >= payload.exp;
            } catch {
                return true;
            }
        },
        
        isPublicPage() {
            const path = window.location.pathname;
            return config.publicPaths.some(publicPath => 
                path === publicPath || path.includes(publicPath)
            );
        },
        
        buildLoginUrl() {
            const currentUrl = window.location.pathname + window.location.search;
            const returnUrl = encodeURIComponent(currentUrl);
            return `${config.loginUrl}?return_url=${returnUrl}`;
        },
        
        redirectToLogin() {
            window.location.href = utils.buildLoginUrl();
        }
    };
    
    // Loading overlay
    const overlay = {
        create() {
            if (!config.showLoadingOverlay) return null;
            
            const div = document.createElement('div');
            div.id = 'page-auth-overlay';
            div.innerHTML = `
                <div class="auth-overlay-content">
                    <div class="auth-spinner"></div>
                    <div class="auth-message">Authenticating...</div>
                </div>
            `;
            
            // Add styles
            const style = document.createElement('style');
            style.textContent = `
                #page-auth-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(10, 14, 26, 0.95);
                    backdrop-filter: blur(10px);
                    z-index: 999999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                }
                .auth-overlay-content {
                    text-align: center;
                    color: white;
                }
                .auth-spinner {
                    width: 40px;
                    height: 40px;
                    border: 3px solid rgba(255,255,255,0.3);
                    border-top: 3px solid #00d4aa;
                    border-radius: 50%;
                    animation: auth-spin 1s linear infinite;
                    margin: 0 auto 1rem;
                }
                .auth-message {
                    font-size: 1.1rem;
                    font-weight: 500;
                }
                @keyframes auth-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(div);
            return div;
        },
        
        remove() {
            const overlay = document.getElementById('page-auth-overlay');
            if (overlay) {
                overlay.style.opacity = '0';
                overlay.style.transition = 'opacity 0.3s ease-out';
                setTimeout(() => overlay.remove(), 300);
            }
        }
    };
    
    // Authentication functions
    async function verifyTokenWithServer(token) {
        try {
            const response = await fetch(config.apiAuthEndpoint, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const userData = await response.json();
                localStorage.setItem(config.userDataKey, JSON.stringify(userData));
                return true;
            }
            
            return false;
        } catch (error) {
            console.warn('Server authentication check failed:', error);
            return false;
        }
    }
    
    async function checkAuthentication() {
        // Skip if already checked or on public page
        if (isAuthChecked || utils.isPublicPage()) {
            return true;
        }
        
        const overlayElement = overlay.create();
        
        try {
            const token = utils.getToken();
            
            // No token found
            if (!token) {
                overlay.remove();
                utils.redirectToLogin();
                return false;
            }
            
            // Check if token is expired
            if (utils.isTokenExpired(token)) {
                utils.removeToken();
                overlay.remove();
                utils.redirectToLogin();
                return false;
            }
            
            // Verify with server
            const isValid = await verifyTokenWithServer(token);
            
            if (!isValid) {
                utils.removeToken();
                overlay.remove();
                utils.redirectToLogin();
                return false;
            }
            
            isAuthChecked = true;
            overlay.remove();
            
            // Execute callbacks
            authCallbacks.forEach(callback => {
                try {
                    callback(true);
                } catch (error) {
                    console.error('Auth callback error:', error);
                }
            });
            
            return true;
            
        } catch (error) {
            console.error('Authentication check failed:', error);
            overlay.remove();
            utils.redirectToLogin();
            return false;
        }
    }
    
    // Periodic authentication check
    function startPeriodicCheck() {
        setInterval(async () => {
            if (!utils.isPublicPage()) {
                const token = utils.getToken();
                if (!token || utils.isTokenExpired(token)) {
                    utils.removeToken();
                    utils.redirectToLogin();
                } else {
                    await verifyTokenWithServer(token);
                }
            }
        }, config.checkInterval);
    }
    
    // Initialize authentication
    function init() {
        if (utils.isPublicPage()) {
            console.log('PageAuth: Skipping auth check for public page');
            return;
        }
        
        console.log('PageAuth: Initializing authentication check');
        checkAuthentication().then(success => {
            if (success) {
                console.log('PageAuth: Authentication successful');
                startPeriodicCheck();
            }
        });
    }
    
    // Public API
    return {
        init,
        checkAuth: checkAuthentication,
        isAuthenticated() {
            const token = utils.getToken();
            return token && !utils.isTokenExpired(token);
        },
        getToken: utils.getToken,
        getUserData() {
            try {
                const data = localStorage.getItem(config.userDataKey);
                return data ? JSON.parse(data) : null;
            } catch {
                return null;
            }
        },
        logout() {
            utils.removeToken();
            utils.redirectToLogin();
        },
        onAuthComplete(callback) {
            if (isAuthChecked) {
                callback(true);
            } else {
                authCallbacks.push(callback);
            }
        },
        configure(options) {
            Object.assign(config, options);
        }
    };
})();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', PageAuth.init);
} else {
    PageAuth.init();
}
