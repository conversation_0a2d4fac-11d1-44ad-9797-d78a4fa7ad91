/**
 * Chart.js Fixes for Trend-Crawler Dashboard
 * This script adds more robust error handling to Chart.js usage
 */

// Safe destroy function for charts
function safeDestroyChart(chartInstance) {
    try {
        if (chartInstance && typeof chartInstance.destroy === 'function') {
            chartInstance.destroy();
            return true;
        }
    } catch (error) {
        console.warn('Error destroying chart:', error);
    }
    return false;
}

// Handle chart initialization failures
function initializeChart(elementId, config) {
    try {
        const ctx = document.getElementById(elementId);
        if (!ctx) {
            console.error(`Chart element with ID "${elementId}" not found`);
            return null;
        }
        
        // Create the chart
        return new Chart(ctx.getContext('2d'), config);
    } catch (error) {
        console.error(`Failed to initialize chart "${elementId}":`, error);
        return null;
    }
}

// Add error handling for API data fetching
function fetchApiDataWithFallback(url, defaultData, processFn) {
    return fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`API request failed with status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data) {
                throw new Error('API returned empty data');
            }
            return processFn(data);
        })
        .catch(error => {
            console.error(`Error fetching data from ${url}:`, error);
            return processFn(defaultData);
        });
}

// Generate fallback data for charts when API calls fail
function generateFallbackChartData(chartType, numPoints = 7) {
    const labels = [];
    const data = [];
    const now = new Date();
    
    // Generate labels with recent timestamps
    for (let i = numPoints - 1; i >= 0; i--) {
        const date = new Date(now - i * 60 * 60 * 1000); // hourly intervals
        labels.push(date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}));
        data.push(0); // Zero data as fallback
    }
    
    return {
        labels: labels,
        data: data
    };
}

// Replace window.successRateChart.destroy with safer version
document.addEventListener('DOMContentLoaded', function() {
    // Add method to safely destroy and recreate charts
    window.chartUtils = {
        safeDestroyChart: safeDestroyChart,
        createChart: initializeChart,
        
        // Helper for typical chart patterns
        updateOrCreateChart: function(chartName, elementId, config) {
            // Try to destroy existing chart
            if (window[chartName]) {
                safeDestroyChart(window[chartName]);
            }
            
            // Create new chart
            window[chartName] = initializeChart(elementId, config);
            return window[chartName];
        },
        
        // Initialize a chart with a fallback if data is not available
        initChartWithFallback: function(chartName, elementId, config, fallbackData) {
            // Try to destroy existing chart
            if (window[chartName]) {
                safeDestroyChart(window[chartName]);
            }
            
            // Use fallback data if provided data is empty
            if (!config.data.labels || config.data.labels.length === 0) {
                if (fallbackData) {
                    config.data.labels = fallbackData.labels || [];
                    config.data.datasets.forEach((dataset, index) => {
                        dataset.data = fallbackData.data || [];
                    });
                }
            }
            
            // Create new chart
            window[chartName] = initializeChart(elementId, config);
            return window[chartName];
        },
        
        // Generate fallback data
        generateFallbackData: generateFallbackChartData
    };
});
