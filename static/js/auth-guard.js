/* Authentication Guard - Standalone script for page protection */

// Minimal authentication guard that can be included in any page
(function() {
    'use strict';
    
    // Simple auth check function
    function checkAuth() {
        const token = localStorage.getItem('access_token');
        
        if (!token) {
            console.warn('No authentication token found');
            redirectToLogin();
            return false;
        }
        
        // Check if token is expired
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            
            if (payload.exp < now) {
                console.warn('Authentication token expired');
                localStorage.removeItem('access_token');
                redirectToLogin();
                return false;
            }
        } catch (error) {
            console.warn('Invalid authentication token format');
            localStorage.removeItem('access_token');
            redirectToLogin();
            return false;
        }
        
        return true;
    }
    
    // Redirect to login with return URL
    function redirectToLogin() {
        const currentPath = window.location.pathname + window.location.search;
        const returnUrl = encodeURIComponent(currentPath);
        const loginUrl = `/login?return_url=${returnUrl}`;
        
        console.log('AuthGuard: Redirecting to login');
        console.log('AuthGuard: Current path:', currentPath);
        console.log('AuthGuard: Login URL:', loginUrl);
        
        // Prevent multiple redirects
        if (window.location.href.includes('/login')) {
            console.log('AuthGuard: Already on login page, skipping redirect');
            return;
        }
        
        // Add a slight delay to ensure console logs are visible
        setTimeout(() => {
            window.location.href = loginUrl;
        }, 100);
    }
    
    // Server-side token verification
    async function verifyTokenWithServer() {
        const token = localStorage.getItem('access_token');
        
        if (!token) {
            console.warn('AuthGuard: No token for server verification');
            return false;
        }
        
        try {
            const response = await fetch('/api/v1/auth/me', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('AuthGuard: Server response status:', response.status);
            
            if (response.status === 401 || response.status === 403) {
                console.warn('AuthGuard: Server rejected authentication token (401/403)');
                localStorage.removeItem('access_token');
                localStorage.removeItem('user_data');
                redirectToLogin();
                return false;
            }
            
            if (!response.ok) {
                console.warn('AuthGuard: Server verification failed with status:', response.status);
                return false;
            }
            
            // Try to parse user data
            try {
                const userData = await response.json();
                localStorage.setItem('user_data', JSON.stringify(userData));
                console.log('AuthGuard: User data stored successfully');
            } catch (parseError) {
                console.warn('AuthGuard: Could not parse user data, but auth succeeded');
            }
            
            return true;
        } catch (error) {
            console.error('AuthGuard: Token verification request failed:', error);
            // Network errors should redirect to be safe
            return false;
        }
    }
    
    // Show authentication loading overlay
    function showAuthOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'auth-guard-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 14, 26, 0.95);
            backdrop-filter: blur(10px);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 1.1rem;
            font-weight: 500;
        `;
        
        overlay.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 2rem; margin-bottom: 1rem;">🔐</div>
                <div>Verifying authentication...</div>
                <div style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.7;">
                    Please wait while we verify your access
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
        return overlay;
    }
    
    // Hide authentication loading overlay
    function hideAuthOverlay() {
        const overlay = document.getElementById('auth-guard-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            overlay.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }
    
    // Main authentication guard initialization
    function initAuthGuard() {
        // Skip auth check for public pages
        const currentPath = window.location.pathname;
        const publicPaths = ['/login', '/auth_test_comprehensive.html', '/test_auth.html'];
        
        console.log('AuthGuard: Checking path:', currentPath);
        
        if (publicPaths.includes(currentPath) || currentPath.includes('/static/')) {
            console.log('AuthGuard: Skipping auth check for public/static path');
            return;
        }
        
        console.log('AuthGuard: Starting authentication check');
        
        // Immediate client-side check - don't show overlay for obvious failures
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.warn('AuthGuard: No token found, redirecting immediately');
            redirectToLogin();
            return;
        }
        
        // Quick expiration check
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            if (payload.exp < now) {
                console.warn('AuthGuard: Token expired, redirecting immediately');
                localStorage.removeItem('access_token');
                localStorage.removeItem('user_data');
                redirectToLogin();
                return;
            }
        } catch (error) {
            console.warn('AuthGuard: Invalid token format, redirecting immediately');
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_data');
            redirectToLogin();
            return;
        }
        
        // Show loading overlay only after basic checks pass
        const overlay = showAuthOverlay();
        
        // Verify with server
        verifyTokenWithServer().then(isValid => {
            hideAuthOverlay();
            
            if (!isValid) {
                console.warn('AuthGuard: Server token verification failed, redirecting');
                localStorage.removeItem('access_token');
                localStorage.removeItem('user_data');
                redirectToLogin();
            } else {
                console.log('AuthGuard: Authentication verified successfully');
            }
        }).catch(error => {
            console.error('AuthGuard: Authentication verification error:', error);
            hideAuthOverlay();
            // On network errors, redirect to be safe
            console.warn('AuthGuard: Network error during verification, redirecting to be safe');
            redirectToLogin();
        });
    }
    
    // Initialize immediately - don't wait for DOM ready for critical auth checks
    console.log('AuthGuard: Script loaded, initializing immediately');
    initAuthGuard();
    
    // Navigation click interceptor for proper authentication handling
    function interceptNavigationClicks() {
        document.addEventListener('click', function(event) {
            const link = event.target.closest('a');
            
            // Only intercept internal navigation links
            if (link && link.href) {
                const href = link.getAttribute('href');
                
                // Skip external links (different hostname or protocol)
                if (link.hostname && link.hostname !== window.location.hostname) {
                    return; // Allow normal navigation for external links
                }
                
                // Only handle relative paths that start with /
                if (!href || !href.startsWith('/')) {
                    return; // Allow normal navigation for non-relative links
                }
                
                // Skip interception for public paths and logout
                const publicPaths = ['/login', '/logout'];
                if (publicPaths.some(path => href.startsWith(path))) {
                    return; // Allow normal navigation
                }
                
                // Skip if it's a hash link or has special attributes
                if (link.target === '_blank' || link.download || href.startsWith('#')) {
                    return;
                }
                
                console.log('AuthGuard: Intercepting navigation to:', href);
                
                // Prevent default navigation
                event.preventDefault();
                
                // Check authentication before navigating
                const token = localStorage.getItem('access_token');
                if (!token) {
                    console.warn('AuthGuard: No token, redirecting to login');
                    redirectToLogin();
                    return;
                }
                
                // Make an authenticated request to check if we can access the page
                fetch(href, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    }
                }).then(response => {
                    console.log('AuthGuard: Navigation response status:', response.status);
                    
                    if (response.status === 401 || response.status === 403) {
                        console.warn('AuthGuard: Unauthorized access, redirecting to login');
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('user_data');
                        redirectToLogin();
                    } else if (response.ok) {
                        console.log('AuthGuard: Access granted, navigating to:', href);
                        window.location.href = href;
                    } else {
                        console.warn('AuthGuard: Unexpected response status:', response.status);
                        // For other errors, try normal navigation
                        window.location.href = href;
                    }
                }).catch(error => {
                    console.error('AuthGuard: Navigation request failed:', error);
                    // On network errors, try normal navigation (might be offline)
                    window.location.href = href;
                });
            }
        });
    }
    
    // Also initialize on DOM ready as a fallback
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AuthGuard: DOM ready, running secondary check');
            // Only run if we haven't already redirected
            if (window.location.pathname === document.location.pathname) {
                initAuthGuard();
            }
            
            // Set up navigation click interception
            interceptNavigationClicks();
        });
    } else {
        // DOM is already ready
        interceptNavigationClicks();
    }
    
    // Expose auth check function globally for manual use
    window.AuthGuard = {
        checkAuth: checkAuth,
        verifyToken: verifyTokenWithServer,
        redirectToLogin: redirectToLogin
    };
    
})();
