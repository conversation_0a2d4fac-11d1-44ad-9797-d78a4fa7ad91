/* Main JavaScript for Trend Crawler Dashboard */

// Authentication utility functions
const AuthUtils = {
    // Check if user is authenticated
    isAuthenticated() {
        const token = this.getToken();
        return token && !this.isTokenExpired(token);
    },

    // Get token from localStorage
    getToken() {
        return localStorage.getItem('access_token');
    },

    // Check if token is expired
    isTokenExpired(token) {
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp < now;
        } catch (error) {
            console.warn('Error parsing token:', error);
            return true; // If we can't parse, assume expired
        }
    },

    // Remove token and redirect to login
    logout() {
        console.log('Logging out user');
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token'); // Also remove refresh token if exists
        
        // Clear any user data
        localStorage.removeItem('user_data');
        
        // Redirect to login with return URL
        const currentPath = window.location.pathname;
        const returnUrl = currentPath !== '/login' ? encodeURIComponent(currentPath) : '';
        const loginUrl = returnUrl ? `/login?return_url=${returnUrl}` : '/login';
        
        window.location.href = loginUrl;
    },

    // Check authentication and redirect if needed
    requireAuth() {
        if (!this.isAuthenticated()) {
            console.warn('Authentication required, redirecting to login');
            this.logout();
            return false;
        }
        return true;
    },

    // Verify token with server
    async verifyToken() {
        const token = this.getToken();
        if (!token) {
            console.warn('No token found for verification');
            return false;
        }

        try {
            const response = await fetch('/api/v1/auth/me', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 401) {
                console.warn('Token verification failed: 401 Unauthorized');
                this.logout();
                return false;
            }

            if (!response.ok) {
                console.warn('Token verification failed:', response.status, response.statusText);
                return false;
            }

            // Store user data if available
            try {
                const userData = await response.json();
                localStorage.setItem('user_data', JSON.stringify(userData));
                console.log('Token verified successfully');
            } catch (error) {
                console.log('Token verified but no user data returned');
            }

            return true;
        } catch (error) {
            console.error('Token verification failed:', error);
            // Don't automatically logout on network errors
            return false;
        }
    },

    // Get user data from localStorage
    getUserData() {
        try {
            const userData = localStorage.getItem('user_data');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.warn('Error parsing user data:', error);
            return null;
        }
    },

    // Check if user has specific role/permission
    hasRole(role) {
        const userData = this.getUserData();
        return userData && userData.roles && userData.roles.includes(role);
    },

    // Check if user is admin
    isAdmin() {
        const userData = this.getUserData();
        return userData && (userData.is_admin === true || this.hasRole('admin'));
    }
};

// Function to format dates
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

// Function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat('en-US').format(num);
}

// Function to format percentage
function formatPercent(num) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(num / 100);
}

// Function to handle API errors
function handleApiError(error, elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `<div class="alert alert-error">
            <p>Error loading data: ${error.message}</p>
        </div>`;
    }
    console.error('API Error:', error);
    
    // Check if it's an authentication error
    if (error.status === 401) {
        AuthUtils.logout();
    }
}

// Enhanced fetch function with authentication
async function authenticatedFetch(url, options = {}) {
    const token = AuthUtils.getToken();
    
    if (!token) {
        AuthUtils.logout();
        return;
    }

    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
    };

    try {
        const response = await fetch(url, {
            ...options,
            headers
        });

        if (response.status === 401) {
            AuthUtils.logout();
            return;
        }

        return response;
    } catch (error) {
        console.error('Authenticated fetch failed:', error);
        throw error;
    }
}

// Function to refresh dashboard data
function refreshDashboard() {
    const refreshButtons = document.querySelectorAll('.refresh-btn');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.innerHTML = '<p>Loading data...</p>';
                // Load data based on target ID
                // Implementation depends on the specific dashboard section
            }
        });
    });
}

// Initialize event handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication on page load for protected pages
    const currentPath = window.location.pathname;
    const publicPaths = ['/login', '/auth_redirect.html', '/test_auth.html'];
    
    // Skip auth check for public pages and static files
    if (!publicPaths.includes(currentPath) && !currentPath.startsWith('/static/')) {
        console.log('Checking authentication for protected page:', currentPath);
        
        // Show loading indicator
        showAuthLoading();
        
        // First check if token exists and is not expired
        if (!AuthUtils.requireAuth()) {
            hideAuthLoading();
            return; // Will redirect to login
        }
        
        // Verify token with server
        AuthUtils.verifyToken().then(isValid => {
            hideAuthLoading();
            if (!isValid) {
                console.warn('Token verification failed, redirecting to login');
                AuthUtils.logout();
                return;
            }
            console.log('Authentication verified successfully');
        }).catch(error => {
            console.error('Authentication verification error:', error);
            hideAuthLoading();
            AuthUtils.logout();
        });
        
        // Set up periodic token verification (every 3 minutes)
        setInterval(() => {
            AuthUtils.verifyToken().catch(error => {
                console.error('Periodic auth check failed:', error);
            });
        }, 3 * 60 * 1000);
    } else {
        console.log('Skipping authentication check for public page:', currentPath);
    }

    // Initialize refresh buttons
    refreshDashboard();

    // Set up interval for auto-refresh if data-refresh attribute exists
    document.querySelectorAll('[data-refresh]').forEach(element => {
        const refreshInterval = parseInt(element.getAttribute('data-refresh')) * 1000;
        if (!isNaN(refreshInterval) && refreshInterval > 0) {
            setInterval(() => {
                // Trigger refresh for this element
                const event = new Event('refresh');
                element.dispatchEvent(event);
            }, refreshInterval);
        }
    });

    // Add logout functionality to logout links
    document.querySelectorAll('a[href="/logout"], .logout-btn').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            AuthUtils.logout();
        });
    });

    // Add authentication headers to all API requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        const options = args[1] || {};
        
        // Only add auth headers to API requests
        if (typeof url === 'string' && url.includes('/api/')) {
            const token = AuthUtils.getToken();
            if (token) {
                options.headers = {
                    ...options.headers,
                    'Authorization': `Bearer ${token}`
                };
            }
        }
        
        return originalFetch.apply(this, [url, options]).then(response => {
            // Handle 401 responses globally
            if (response.status === 401 && !url.includes('/login') && !url.includes('/token')) {
                console.warn('Received 401 response, redirecting to login');
                AuthUtils.logout();
            }
            return response;
        });
    };
});

// Loading indicator functions
function showAuthLoading() {
    const existingLoader = document.getElementById('auth-loader');
    if (existingLoader) return;
    
    const loader = document.createElement('div');
    loader.id = 'auth-loader';
    loader.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: rgba(10, 14, 26, 0.9);
        backdrop-filter: blur(10px);
        z-index: 9999;
        padding: 1rem;
        text-align: center;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        border-bottom: 1px solid rgba(255,255,255,0.1);
    `;
    loader.innerHTML = '🔐 Verifying authentication...';
    document.body.appendChild(loader);
}

function hideAuthLoading() {
    const loader = document.getElementById('auth-loader');
    if (loader) {
        loader.style.opacity = '0';
        loader.style.transform = 'translateY(-100%)';
        loader.style.transition = 'all 0.3s ease-out';
        setTimeout(() => {
            if (loader.parentNode) {
                loader.parentNode.removeChild(loader);
            }
        }, 300);
    }
}