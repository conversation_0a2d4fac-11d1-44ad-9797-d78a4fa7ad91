/**
 * Emergency fixes for Chart.js in Trend-Crawler Dashboard
 * This script directly patches Chart.js issues
 */

// Immediately execute to ensure it runs before other scripts
(function() {
    console.log("Loading chart emergency fixes...");
    
    // Global chart registry to track all charts
    window.chartRegistry = {};
    
    // Override Chart constructor to register all charts
    const originalChart = window.Chart;
    if (originalChart) {
        window.Chart = function(ctx, config) {
            const chart = new originalChart(ctx, config);
            
            // Register this chart instance with a unique ID
            const canvasId = ctx.canvas ? ctx.canvas.id : 'unknown-' + Math.random().toString(36).substr(2, 9);
            window.chartRegistry[canvasId] = chart;
            
            return chart;
        };
        
        // Copy over all properties from the original Chart
        for (const prop in originalChart) {
            if (originalChart.hasOwnProperty(prop)) {
                window.Chart[prop] = originalChart[prop];
            }
        }
        
        // Add our own utility methods
        window.Chart.safeDestroy = function(chartInstance) {
            if (!chartInstance) return false;
            
            try {
                if (typeof chartInstance.destroy === 'function') {
                    chartInstance.destroy();
                    return true;
                }
            } catch (e) {
                console.warn('Error safely destroying chart:', e);
            }
            return false;
        };
        
        // Add a method to safely get a chart by canvas ID
        window.Chart.getChart = function(canvasId) {
            return window.chartRegistry[canvasId] || null;
        };
        
        // Add a method to safely destroy a chart by canvas ID
        window.Chart.destroyChart = function(canvasId) {
            const chart = window.chartRegistry[canvasId];
            if (chart) {
                window.Chart.safeDestroy(chart);
                delete window.chartRegistry[canvasId];
                return true;
            }
            return false;
        };
    }
    
    // Direct fix for successRateChart issue
    window.fixChartIssues = function() {
        // Fix for window.successRateChart.destroy
        if (window.successRateChart && !window.successRateChart.destroy) {
            console.log("Fixing missing destroy method on successRateChart");
            window.successRateChart.destroy = function() {
                // Find the canvas element
                const canvas = document.getElementById('successRateChart');
                if (canvas) {
                    // Replace the canvas with a fresh one
                    const parent = canvas.parentNode;
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = 'successRateChart';
                    parent.removeChild(canvas);
                    parent.appendChild(newCanvas);
                }
                // Remove the reference
                window.successRateChart = null;
            };
        }
        
        // Apply similar fixes to other charts
        const chartNames = [
            'responseTimeChart', 
            'captchaRateChart', 
            'requestVolumeChart',
            'resourceChart',
            'apiHealthChart',
            'dbPerformanceChart'
        ];
        
        chartNames.forEach(chartName => {
            if (window[chartName] && !window[chartName].destroy) {
                console.log(`Fixing missing destroy method on ${chartName}`);
                window[chartName].destroy = function() {
                    // Find the canvas element
                    const canvas = document.getElementById(chartName);
                    if (canvas) {
                        // Replace the canvas with a fresh one
                        const parent = canvas.parentNode;
                        const newCanvas = document.createElement('canvas');
                        newCanvas.id = chartName;
                        parent.removeChild(canvas);
                        parent.appendChild(newCanvas);
                    }
                    // Remove the reference
                    window[chartName] = null;
                };
            }
        });
    };
    
    // Run the fix when DOM is loaded and also periodically
    document.addEventListener('DOMContentLoaded', function() {
        window.fixChartIssues();
        
        // Run the fix every 2 seconds to catch any newly created charts
        setInterval(window.fixChartIssues, 2000);
    });
    
    // Also run immediately in case DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        window.fixChartIssues();
    }
})();
