#!/usr/bin/env python3
"""
Proxy Management Module

This module provides advanced proxy management capabilities including:
- Health checking
- Rotation
- Performance tracking
- Geographic distribution
- Proxy chaining
"""

import os
import time
import json
import random
import logging
import asyncio
import threading
from typing import Dict, Any, List, Optional, Union, Tu<PERSON>
from datetime import datetime, timedelta
import traceback
import statistics
import ipaddress
from pathlib import Path
from urllib.parse import urlparse

import aiohttp
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError, ProxyError
import psycopg2
from psycopg2.extras import <PERSON>son as PsycopgJson
from fp.fp import FreeProxy

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add file handler for persistent logging
try:
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/proxy_manager.log')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
except Exception as e:
    logger.error(f"Failed to set up file logging: {e}")

class ProxyManager:
    """Advanced proxy management system."""

    def __init__(self, config_path: str = "proxy_config.json"):
        """
        Initialize the proxy manager.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config = self._load_config(config_path)
        self.db_conn = self._init_db_connection()
        self.proxies = self._load_proxies()
        self.proxy_stats = {}
        self.last_proxy_check_time = {}
        self.rotating_index = 0
        self.proxy_lock = threading.RLock()
        self.geo_cache = {}
        self._load_proxy_stats()
        
        # Set up health check thread
        self.health_check_interval = self.config.get('health_check_interval', 300)  # Default 5 minutes
        self.health_check_active = False
        self.health_check_thread = None
        
        # Initialize distributed IP database if available
        self.ip_db = self._init_ip_database()
        
        logger.info(f"Proxy Manager initialized with {len(self.proxies)} proxies")
        
        # Start health check if enabled
        if self.config.get('auto_health_check', True) and len(self.proxies) > 0:
            self.start_health_checks()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(config_path) as f:
                config = json.load(f)
                logger.info(f"Configuration loaded from {config_path}")
                return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using empty config")
            return {}
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in config file {config_path}")
            return {}
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return {}

    def _init_db_connection(self) -> Optional[psycopg2.extensions.connection]:
        """Initialize database connection for proxy metrics."""
        if not self.config.get('db', {}).get('enabled', False):
            logger.info("Database metrics storage disabled")
            return None
        
        try:
            conn = psycopg2.connect(
                dbname=self.config['db']['name'],
                user=self.config['db']['user'],
                password=self.config['db']['password'],
                host=self.config['db']['host']
            )
            logger.info(f"Connected to metrics database at {self.config['db']['host']}")
            
            # Initialize tables if needed
            with conn.cursor() as cursor:
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS proxy_performance (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    proxy_id VARCHAR(100) NOT NULL,
                    proxy_type VARCHAR(50),
                    request_count INTEGER DEFAULT 0,
                    success_count INTEGER DEFAULT 0,
                    avg_latency FLOAT,
                    error_types JSONB,
                    last_used TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    last_check_status BOOLEAN,
                    consecutive_failures INTEGER DEFAULT 0,
                    location JSONB
                )
                """)
                
                cursor.execute("""
                CREATE TABLE IF NOT EXISTS proxy_health_checks (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    proxy_id VARCHAR(100) NOT NULL,
                    status BOOLEAN,
                    latency FLOAT,
                    error VARCHAR(255)
                )
                """)
                
            conn.commit()
            return conn
        
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None

    def _init_ip_database(self):
        """Initialize IP geolocation database if available."""
        try:
            import geoip2.database
            
            db_path = self.config.get('geoip_db_path')
            if not db_path:
                db_path = 'GeoLite2-City.mmdb'
            
            if os.path.exists(db_path):
                reader = geoip2.database.Reader(db_path)
                logger.info(f"Loaded GeoIP database from {db_path}")
                return reader
            else:
                logger.warning(f"GeoIP database file not found at {db_path}")
                return None
                
        except ImportError:
            logger.warning("geoip2 module not available, geolocation features disabled")
            return None
        except Exception as e:
            logger.error(f"Failed to initialize IP database: {e}")
            return None

    def _load_proxies(self) -> List[Dict[str, Any]]:
        """Load and validate proxy servers from configuration."""
        proxies = []
        
        try:
            # Load proxies from configuration
            if 'proxies' in self.config:
                for i, proxy in enumerate(self.config['proxies']):
                    proxy_id = proxy.get('id') or f"proxy_{i}"
                    proxies.append({
                        'id': proxy_id,
                        'protocol': proxy.get('protocol', 'http'),
                        'host': proxy['host'],
                        'port': proxy['port'],
                        'username': proxy.get('username'),
                        'password': proxy.get('password'),
                        'type': proxy.get('type', 'datacenter'),
                        'country': proxy.get('country'),
                        'city': proxy.get('city'),
                        'isp': proxy.get('isp'),
                        'active': proxy.get('active', True),
                        'source': 'config',
                        'last_check': None,
                        'check_status': None
                    })
            
            # Load proxies from additional providers if enabled and needed
            if self.config.get('auto_add_proxies', False) and len(proxies) < self.config.get('min_proxies', 5):
                additional_needed = self.config.get('min_proxies', 5) - len(proxies)
                
                # Add BrightData proxies if configured
                if self.config.get('brightdata', {}).get('enabled') and additional_needed > 0:
                    brightdata_proxies = self._load_brightdata_proxies()
                    proxies.extend(brightdata_proxies[:additional_needed])
                    additional_needed -= len(brightdata_proxies[:additional_needed])
                
                # Add Smartproxy proxies if configured
                if self.config.get('smartproxy', {}).get('enabled') and additional_needed > 0:
                    smartproxy_proxies = self._load_smartproxy_proxies()
                    proxies.extend(smartproxy_proxies[:additional_needed])
                    additional_needed -= len(smartproxy_proxies[:additional_needed])
                
                # Add free proxies as a last resort
                if additional_needed > 0 and self.config.get('allow_free_proxies', False):
                    free_proxies = self._load_free_proxies(additional_needed)
                    proxies.extend(free_proxies)
            
            logger.info(f"Loaded {len(proxies)} proxies")
            return proxies
            
        except Exception as e:
            logger.error(f"Failed to load proxies: {e}")
            logger.debug(f"Proxy loading error details: {traceback.format_exc()}")
            return []

    def _load_brightdata_proxies(self) -> List[Dict[str, Any]]:
        """Load proxies from BrightData (formerly Luminati)."""
        brightdata_proxies = []
        
        try:
            if not self.config.get('brightdata', {}).get('enabled'):
                return []
                
            config = self.config['brightdata']
            zones = config.get('zones', [])
            
            if not zones:
                return []
                
            for zone_name, zone_config in zones.items():
                hostname = zone_config.get('hostname', 'brd.superproxy.io')
                port = zone_config.get('port', 22225)
                username = zone_config.get('username')
                password = zone_config.get('password')
                
                if not username or not password:
                    continue
                
                brightdata_proxies.append({
                    'id': f"brightdata_{zone_name}",
                    'protocol': 'http',
                    'host': hostname,
                    'port': port,
                    'username': f"{username}-{zone_name}",
                    'password': password,
                    'type': 'residential',
                    'active': True,
                    'source': 'brightdata',
                    'last_check': None,
                    'check_status': None
                })
            
            logger.info(f"Loaded {len(brightdata_proxies)} BrightData proxies")
            return brightdata_proxies
            
        except Exception as e:
            logger.error(f"Failed to load BrightData proxies: {e}")
            return []

    def _load_smartproxy_proxies(self) -> List[Dict[str, Any]]:
        """Load proxies from Smartproxy."""
        smartproxy_proxies = []
        
        try:
            if not self.config.get('smartproxy', {}).get('enabled'):
                return []
                
            config = self.config['smartproxy']
            endpoints = config.get('endpoints', [])
            
            if not endpoints:
                return []
                
            for endpoint in endpoints:
                host = endpoint.get('host')
                port = endpoint.get('port')
                username = config.get('username')
                password = config.get('password')
                
                if not host or not port or not username or not password:
                    continue
                
                smartproxy_proxies.append({
                    'id': f"smartproxy_{host}_{port}",
                    'protocol': 'http',
                    'host': host,
                    'port': port,
                    'username': username,
                    'password': password,
                    'type': endpoint.get('type', 'residential'),
                    'active': True,
                    'source': 'smartproxy',
                    'last_check': None,
                    'check_status': None
                })
            
            logger.info(f"Loaded {len(smartproxy_proxies)} Smartproxy proxies")
            return smartproxy_proxies
            
        except Exception as e:
            logger.error(f"Failed to load Smartproxy proxies: {e}")
            return []

    def _load_free_proxies(self, count: int = 10) -> List[Dict[str, Any]]:
        """Load free proxies as a fallback."""
        free_proxies = []
        
        try:
            fp = FreeProxy(
                country_id=['US', 'GB', 'CA', 'AU', 'NL', 'DE', 'FR'],
                https=True,
                timeout=2
            )
            proxy_list = fp.get_proxy_list(as_json=True)
            
            for i, proxy_data in enumerate(proxy_list[:count]):
                # Parse the proxy string
                proxy_str = proxy_data['proxy']
                parsed = urlparse(proxy_str)
                
                protocol = parsed.scheme
                if not protocol:
                    protocol = 'http'
                
                host, port = parsed.netloc.split(':')
                
                free_proxies.append({
                    'id': f"free_{i}",
                    'protocol': protocol,
                    'host': host,
                    'port': int(port),
                    'username': None,
                    'password': None,
                    'type': 'free',
                    'active': True,
                    'source': 'free',
                    'last_check': None,
                    'check_status': None
                })
            
            logger.info(f"Loaded {len(free_proxies)} free proxies")
            return free_proxies
            
        except Exception as e:
            logger.error(f"Failed to load free proxies: {e}")
            return []

    def _load_proxy_stats(self) -> None:
        """Load proxy performance statistics from the database."""
        if not self.db_conn:
            return
        
        try:
            with self.db_conn.cursor() as cursor:
                # Load the most recent stats for each proxy
                cursor.execute("""
                SELECT 
                    proxy_id, 
                    request_count, 
                    success_count,
                    avg_latency,
                    last_used,
                    last_check_status,
                    consecutive_failures
                FROM (
                    SELECT 
                        *, 
                        ROW_NUMBER() OVER (PARTITION BY proxy_id ORDER BY timestamp DESC) as rn
                    FROM proxy_performance
                ) as ranked
                WHERE rn = 1
                """)
                
                results = cursor.fetchall()
                
                for row in results:
                    proxy_id, req_count, success_count, avg_latency, last_used, last_check, failures = row
                    
                    if not proxy_id:
                        continue
                    
                    if proxy_id not in self.proxy_stats:
                        self.proxy_stats[proxy_id] = {}
                    
                    stats = self.proxy_stats[proxy_id]
                    stats['request_count'] = req_count or 0
                    stats['success_count'] = success_count or 0
                    stats['avg_latency'] = avg_latency
                    stats['last_used'] = last_used
                    stats['last_check_status'] = last_check
                    stats['consecutive_failures'] = failures or 0
                    
                    # Calculate success rate
                    if req_count and req_count > 0:
                        stats['success_rate'] = (success_count / req_count) * 100
                    else:
                        stats['success_rate'] = 0
                
                logger.info(f"Loaded statistics for {len(results)} proxies")
                
        except Exception as e:
            logger.error(f"Failed to load proxy statistics: {e}")

    def get_proxy(self, country: str = None, type_filter: str = None, min_success_rate: float = None) -> Optional[Dict[str, Any]]:
        """
        Get the next proxy based on filtering and rotation strategy.
        
        Args:
            country: ISO country code to filter by
            type_filter: Type of proxy (residential, datacenter, etc.)
            min_success_rate: Minimum success rate in percent
            
        Returns:
            Proxy configuration dictionary or None if no suitable proxy is found
        """
        with self.proxy_lock:
            # Filter proxies based on criteria
            candidates = []
            for proxy in self.proxies:
                # Skip inactive proxies
                if not proxy.get('active', True):
                    continue
                
                # Apply country filter if specified
                if country and proxy.get('country') != country:
                    continue
                
                # Apply type filter if specified
                if type_filter and proxy.get('type') != type_filter:
                    continue
                
                # Apply success rate filter if specified
                if min_success_rate is not None:
                    stats = self.proxy_stats.get(proxy['id'], {})
                    success_rate = stats.get('success_rate', 0)
                    if success_rate < min_success_rate:
                        continue
                
                candidates.append(proxy)
            
            if not candidates:
                logger.warning(f"No suitable proxy found (country={country}, type={type_filter}, min_success_rate={min_success_rate})")
                return None
            
            # Determine rotation strategy based on config
            strategy = self.config.get('rotation_strategy', 'round_robin')
            
            if strategy == 'round_robin':
                # Simple round-robin rotation
                self.rotating_index = (self.rotating_index + 1) % len(candidates)
                selected = candidates[self.rotating_index]
            elif strategy == 'weighted_random':
                # Weighted random selection based on success rate
                weights = []
                for proxy in candidates:
                    stats = self.proxy_stats.get(proxy['id'], {})
                    success_rate = stats.get('success_rate', 50.0)  # Default 50% if unknown
                    
                    # Adjust weight based on success rate
                    weight = max(0.1, success_rate / 100)  # Convert to 0-1 scale, min 0.1
                    weights.append(weight)
                
                # Normalize weights
                total_weight = sum(weights) or len(weights)  # Avoid division by zero
                normalized_weights = [w/total_weight for w in weights]
                
                # Select based on weights
                selected = random.choices(candidates, normalized_weights)[0]
            elif strategy == 'best_performing':
                # Sort by success rate and select the best
                candidates.sort(
                    key=lambda p: self.proxy_stats.get(p['id'], {}).get('success_rate', 0),
                    reverse=True
                )
                selected = candidates[0]
            else:
                # Default to random selection
                selected = random.choice(candidates)
            
            logger.debug(f"Selected proxy: {selected['id']} ({selected.get('host')}:{selected.get('port')})")
            return selected

    def get_proxies_batch(self, count: int = 5, **filters) -> List[Dict[str, Any]]:
        """
        Get multiple proxies at once for batch operations.
        
        Args:
            count: Number of proxies to return
            **filters: Filters to apply (same as get_proxy)
            
        Returns:
            List of proxy configurations
        """
        # Apply the same filtering as get_proxy
        with self.proxy_lock:
            candidates = []
            for proxy in self.proxies:
                if not proxy.get('active', True):
                    continue
                
                if filters.get('country') and proxy.get('country') != filters['country']:
                    continue
                
                if filters.get('type_filter') and proxy.get('type') != filters['type_filter']:
                    continue
                
                if filters.get('min_success_rate') is not None:
                    stats = self.proxy_stats.get(proxy['id'], {})
                    success_rate = stats.get('success_rate', 0)
                    if success_rate < filters['min_success_rate']:
                        continue
                
                candidates.append(proxy)
            
            if not candidates:
                logger.warning(f"No suitable proxies found for batch request with filters: {filters}")
                return []
            
            # If we don't have enough, just return what we have
            if len(candidates) <= count:
                return candidates
            
            # Select based on strategy
            strategy = self.config.get('batch_selection', 'diverse')
            
            if strategy == 'diverse':
                # Try to get a diverse set of proxies
                selected = []
                
                # Group by country if we have that info
                by_country = {}
                for proxy in candidates:
                    country = proxy.get('country', 'unknown')
                    if country not in by_country:
                        by_country[country] = []
                    by_country[country].append(proxy)
                
                # Take one from each country until we have enough
                countries = list(by_country.keys())
                i = 0
                while len(selected) < count and i < len(countries) * 2:  # Avoid infinite loop
                    country = countries[i % len(countries)]
                    if by_country[country]:
                        selected.append(by_country[country].pop())
                    i += 1
                
                # If we still don't have enough, add random ones
                if len(selected) < count:
                    # Collect all remaining proxies
                    remaining = []
                    for country_proxies in by_country.values():
                        remaining.extend(country_proxies)
                    
                    # Add random ones
                    random.shuffle(remaining)
                    selected.extend(remaining[:count - len(selected)])
                    
                return selected
            else:
                # Default to random selection
                return random.sample(candidates, min(count, len(candidates)))

    def update_proxy_stats(self, 
                         proxy_id: str, 
                         success: bool, 
                         latency: float, 
                         error_type: str = None) -> None:
        """
        Update performance statistics for a proxy.
        
        Args:
            proxy_id: ID of the proxy
            success: Whether the request was successful
            latency: Request latency in seconds
            error_type: Type of error if the request failed
        """
        # Update in-memory stats
        with self.proxy_lock:
            if proxy_id not in self.proxy_stats:
                self.proxy_stats[proxy_id] = {
                    'request_count': 0,
                    'success_count': 0,
                    'avg_latency': 0,
                    'last_used': datetime.now(),
                    'consecutive_failures': 0,
                    'error_types': {}
                }
            
            stats = self.proxy_stats[proxy_id]
            stats['request_count'] += 1
            stats['last_used'] = datetime.now()
            
            if success:
                stats['success_count'] += 1
                stats['consecutive_failures'] = 0
            else:
                stats['consecutive_failures'] += 1
                
                # Track error types
                if 'error_types' not in stats:
                    stats['error_types'] = {}
                if error_type:
                    stats['error_types'][error_type] = stats['error_types'].get(error_type, 0) + 1
            
            # Update average latency with exponential moving average
            alpha = 0.2  # Weight for new value (0.2 = 20% weight to new value)
            if 'avg_latency' not in stats or stats['avg_latency'] is None:
                stats['avg_latency'] = latency
            else:
                stats['avg_latency'] = (alpha * latency) + ((1 - alpha) * stats['avg_latency'])
            
            # Calculate success rate
            stats['success_rate'] = (stats['success_count'] / stats['request_count']) * 100
            
            # Disable proxy if too many consecutive failures
            max_failures = self.config.get('max_consecutive_failures', 10)
            if stats['consecutive_failures'] >= max_failures:
                logger.warning(f"Disabling proxy {proxy_id} due to {stats['consecutive_failures']} consecutive failures")
                for proxy in self.proxies:
                    if proxy['id'] == proxy_id:
                        proxy['active'] = False
                        break
        
        # Update in database if available
        if self.db_conn:
            try:
                with self.db_conn.cursor() as cursor:
                    # Check if we have a record for this proxy
                    cursor.execute("""
                    SELECT id, request_count, success_count, avg_latency, error_types
                    FROM proxy_performance
                    WHERE proxy_id = %s
                    ORDER BY timestamp DESC
                    LIMIT 1
                    """, (proxy_id,))
                    
                    result = cursor.fetchone()
                    
                    error_types = stats.get('error_types', {})
                    
                    if result:
                        # Update existing record
                        cursor.execute("""
                        INSERT INTO proxy_performance
                        (proxy_id, request_count, success_count, avg_latency, error_types, 
                         last_used, consecutive_failures)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """, (
                            proxy_id,
                            stats['request_count'],
                            stats['success_count'],
                            stats['avg_latency'],
                            PsycopgJson(error_types) if error_types else None,
                            stats['last_used'],
                            stats['consecutive_failures']
                        ))
                    else:
                        # Find proxy type
                        proxy_type = None
                        for proxy in self.proxies:
                            if proxy['id'] == proxy_id:
                                proxy_type = proxy.get('type')
                                break
                        
                        # Insert new record
                        cursor.execute("""
                        INSERT INTO proxy_performance
                        (proxy_id, proxy_type, request_count, success_count, avg_latency, 
                         error_types, last_used, consecutive_failures)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            proxy_id,
                            proxy_type,
                            stats['request_count'],
                            stats['success_count'],
                            stats['avg_latency'],
                            PsycopgJson(error_types) if error_types else None,
                            stats['last_used'],
                            stats['consecutive_failures']
                        ))
                    
                    self.db_conn.commit()
            except Exception as e:
                logger.error(f"Failed to update proxy stats in database: {e}")
                logger.debug(f"Database update error details: {traceback.format_exc()}")

    def format_proxy_url(self, proxy: Dict[str, Any]) -> str:
        """Format a proxy configuration as a URL string."""
        if not proxy:
            return None
        
        protocol = proxy.get('protocol', 'http')
        host = proxy.get('host')
        port = proxy.get('port')
        
        if not host or not port:
            return None
        
        auth = ""
        if proxy.get('username') and proxy.get('password'):
            auth = f"{proxy['username']}:{proxy['password']}@"
        
        return f"{protocol}://{auth}{host}:{port}"

    def format_for_requests(self, proxy: Dict[str, Any]) -> Dict[str, str]:
        """Format proxy for the requests library."""
        if not proxy:
            return {}
        
        proxy_url = self.format_proxy_url(proxy)
        if not proxy_url:
            return {}
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def format_for_aiohttp(self, proxy: Dict[str, Any]) -> str:
        """Format proxy for aiohttp."""
        return self.format_proxy_url(proxy)

    def format_for_playwright(self, proxy: Dict[str, Any]) -> Dict[str, Any]:
        """Format proxy for Playwright."""
        if not proxy:
            return None
        
        protocol = proxy.get('protocol', 'http')
        host = proxy.get('host')
        port = proxy.get('port')
        
        if not host or not port:
            return None
        
        result = {
            'server': f"{protocol}://{host}:{port}",
            'bypass': '127.0.0.1, localhost'
        }
        
        if proxy.get('username') and proxy.get('password'):
            result['username'] = proxy['username']
            result['password'] = proxy['password']
        
        return result

    def check_proxy_health(self, proxy_id: str = None) -> Dict[str, Any]:
        """
        Check the health of a proxy or all proxies.
        
        Args:
            proxy_id: ID of specific proxy to check, or None to check all
            
        Returns:
            Dictionary with health check results
        """
        test_urls = self.config.get('health_check_urls', ['https://httpbin.org/ip', 'https://api.ipify.org?format=json'])
        timeout = self.config.get('health_check_timeout', 10)
        
        if proxy_id:
            # Check single proxy
            proxy = None
            for p in self.proxies:
                if p['id'] == proxy_id:
                    proxy = p
                    break
            
            if not proxy:
                return {'error': f"Proxy {proxy_id} not found"}
            
            result = self._check_single_proxy(proxy, test_urls, timeout)
            self._update_proxy_health(proxy['id'], result)
            return result
        else:
            # Check all proxies
            results = {}
            for proxy in self.proxies:
                result = self._check_single_proxy(proxy, test_urls, timeout)
                self._update_proxy_health(proxy['id'], result)
                results[proxy['id']] = result
            
            return results

    def _check_single_proxy(self, 
                          proxy: Dict[str, Any], 
                          test_urls: List[str],
                          timeout: int) -> Dict[str, Any]:
        """Check health of a single proxy."""
        start_time = time.time()
        
        proxy_url = self.format_proxy_url(proxy)
        if not proxy_url:
            return {
                'status': False,
                'latency': 0,
                'error': "Invalid proxy configuration"
            }
        
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        for url in test_urls:
            try:
                response = requests.get(
                    url,
                    proxies=proxies,
                    timeout=timeout,
                    verify=False  # Skip SSL verification for health checks
                )
                
                if response.status_code == 200:
                    latency = time.time() - start_time
                    
                    # Get IP information if possible
                    ip_info = {}
                    try:
                        data = response.json()
                        if 'ip' in data:
                            ip_info['ip'] = data['ip']
                            # Get geolocation if available
                            if self.ip_db:
                                geo = self._get_ip_geolocation(data['ip'])
                                if geo:
                                    ip_info.update(geo)
                    except:
                        pass
                    
                    return {
                        'status': True,
                        'latency': latency,
                        'url': url,
                        'ip_info': ip_info
                    }
            
            except RequestException as e:
                error_type = type(e).__name__
                error_msg = str(e)
        
        # If we get here, all URLs failed
        return {
            'status': False,
            'latency': time.time() - start_time,
            'error': f"{error_type}: {error_msg}"
        }

    def _update_proxy_health(self, proxy_id: str, result: Dict[str, Any]) -> None:
        """Update proxy health status in memory and database."""
        # Update in-memory status
        for proxy in self.proxies:
            if proxy['id'] == proxy_id:
                proxy['last_check'] = datetime.now()
                proxy['check_status'] = result['status']
                
                # Update geolocation if available
                if result.get('status') and result.get('ip_info'):
                    ip_info = result['ip_info']
                    if 'country' in ip_info:
                        proxy['country'] = ip_info['country']
                    if 'city' in ip_info:
                        proxy['city'] = ip_info['city']
                    if 'isp' in ip_info:
                        proxy['isp'] = ip_info['isp']
                
                break
        
        # Update in database
        if self.db_conn:
            try:
                with self.db_conn.cursor() as cursor:
                    # Record health check
                    cursor.execute("""
                    INSERT INTO proxy_health_checks
                    (proxy_id, status, latency, error)
                    VALUES (%s, %s, %s, %s)
                    """, (
                        proxy_id,
                        result['status'],
                        result.get('latency', 0),
                        result.get('error', None)
                    ))
                    
                    # Update proxy performance record
                    cursor.execute("""
                    UPDATE proxy_performance
                    SET last_check_status = %s
                    WHERE proxy_id = %s
                    ORDER BY timestamp DESC
                    LIMIT 1
                    """, (
                        result['status'],
                        proxy_id
                    ))
                    
                    self.db_conn.commit()
            except Exception as e:
                logger.error(f"Failed to update proxy health in database: {e}")

    async def async_check_proxy_health(self, proxy_id: str = None) -> Dict[str, Any]:
        """
        Asynchronously check the health of a proxy or all proxies.
        
        Args:
            proxy_id: ID of specific proxy to check, or None to check all
            
        Returns:
            Dictionary with health check results
        """
        test_urls = self.config.get('health_check_urls', ['https://httpbin.org/ip', 'https://api.ipify.org?format=json'])
        timeout = self.config.get('health_check_timeout', 10)
        
        if proxy_id:
            # Check single proxy
            proxy = None
            for p in self.proxies:
                if p['id'] == proxy_id:
                    proxy = p
                    break
            
            if not proxy:
                return {'error': f"Proxy {proxy_id} not found"}
            
            result = await self._async_check_single_proxy(proxy, test_urls, timeout)
            self._update_proxy_health(proxy['id'], result)
            return result
        else:
            # Check all proxies in parallel
            tasks = []
            for proxy in self.proxies:
                task = asyncio.create_task(self._async_check_single_proxy(proxy, test_urls, timeout))
                tasks.append((proxy['id'], task))
            
            results = {}
            for proxy_id, task in tasks:
                try:
                    result = await task
                    self._update_proxy_health(proxy_id, result)
                    results[proxy_id] = result
                except Exception as e:
                    logger.error(f"Error checking proxy {proxy_id}: {e}")
                    results[proxy_id] = {
                        'status': False,
                        'error': str(e)
                    }
            
            return results

    async def _async_check_single_proxy(self, 
                                      proxy: Dict[str, Any], 
                                      test_urls: List[str],
                                      timeout: int) -> Dict[str, Any]:
        """Asynchronously check health of a single proxy."""
        start_time = time.time()
        
        proxy_url = self.format_proxy_url(proxy)
        if not proxy_url:
            return {
                'status': False,
                'latency': 0,
                'error': "Invalid proxy configuration"
            }
        
        success = False
        error_type = "UnknownError"
        error_msg = "All URLs failed"
        
        async with aiohttp.ClientSession() as session:
            for url in test_urls:
                try:
                    async with session.get(
                        url,
                        proxy=proxy_url,
                        timeout=aiohttp.ClientTimeout(total=timeout),
                        ssl=False  # Skip SSL verification for health checks
                    ) as response:
                        if response.status == 200:
                            latency = time.time() - start_time
                            
                            # Get IP information if possible
                            ip_info = {}
                            try:
                                data = await response.json()
                                if 'ip' in data:
                                    ip_info['ip'] = data['ip']
                                    # Get geolocation if available
                                    if self.ip_db:
                                        geo = self._get_ip_geolocation(data['ip'])
                                        if geo:
                                            ip_info.update(geo)
                            except:
                                pass
                            
                            return {
                                'status': True,
                                'latency': latency,
                                'url': url,
                                'ip_info': ip_info
                            }
                
                except aiohttp.ClientError as e:
                    error_type = type(e).__name__
                    error_msg = str(e)
        
        # If we get here, all URLs failed
        return {
            'status': False,
            'latency': time.time() - start_time,
            'error': f"{error_type}: {error_msg}"
        }

    def _get_ip_geolocation(self, ip: str) -> Dict[str, Any]:
        """Get geolocation information for an IP address."""
        # Check cache first
        if ip in self.geo_cache:
            return self.geo_cache[ip]
        
        if self.ip_db:
            try:
                # Validate IP address
                try:
                    ipaddress.ip_address(ip)
                except ValueError:
                    return {}
                
                response = self.ip_db.city(ip)
                
                geo_info = {
                    'country': response.country.iso_code,
                    'city': response.city.name,
                    'latitude': response.location.latitude,
                    'longitude': response.location.longitude,
                    'isp': response.traits.isp,
                    'organization': response.traits.organization
                }
                
                # Cache result
                self.geo_cache[ip] = geo_info
                
                return geo_info
            except Exception as e:
                logger.warning(f"Failed to get geolocation for IP {ip}: {e}")
                return {}
        else:
            try:
                # Try alternative API-based method
                response = requests.get(f"https://ipapi.co/{ip}/json/", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    geo_info = {
                        'country': data.get('country_code'),
                        'city': data.get('city'),
                        'latitude': data.get('latitude'),
                        'longitude': data.get('longitude'),
                        'isp': data.get('org')
                    }
                    
                    # Cache result
                    self.geo_cache[ip] = geo_info
                    
                    return geo_info
            except Exception as e:
                logger.warning(f"Failed to get geolocation from API for IP {ip}: {e}")
                return {}

    def start_health_checks(self) -> None:
        """Start periodic health check thread."""
        if self.health_check_active:
            logger.warning("Health checks are already active")
            return
        
        self.health_check_active = True
        self.health_check_thread = threading.Thread(target=self._health_check_loop)
        self.health_check_thread.daemon = True
        self.health_check_thread.start()
        
        logger.info(f"Proxy health checks started with interval of {self.health_check_interval} seconds")

    def stop_health_checks(self) -> None:
        """Stop health check thread."""
        if not self.health_check_active:
            logger.warning("Health checks are not active")
            return
        
        self.health_check_active = False
        if self.health_check_thread:
            self.health_check_thread.join(timeout=1.0)
            self.health_check_thread = None
        
        logger.info("Proxy health checks stopped")

    def _health_check_loop(self) -> None:
        """Main health check loop."""
        while self.health_check_active:
            try:
                # Get list of proxies to check
                to_check = []
                now = datetime.now()
                
                for proxy in self.proxies:
                    # Skip if checked recently
                    last_check = proxy.get('last_check')
                    if last_check and (now - last_check).total_seconds() < self.health_check_interval:
                        continue
                    
                    to_check.append(proxy['id'])
                
                if to_check:
                    logger.info(f"Checking health of {len(to_check)} proxies")
                    
                    # Run async checks
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    results = loop.run_until_complete(self._batch_health_check(to_check))
                    loop.close()
                    
                    # Count results
                    healthy = sum(1 for r in results.values() if r.get('status', False))
                    logger.info(f"Health check completed: {healthy}/{len(results)} proxies healthy")
            
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
            
            # Sleep until next check
            time.sleep(min(60, self.health_check_interval))  # Check at least every minute

    async def _batch_health_check(self, proxy_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Run health checks for multiple proxies in parallel."""
        tasks = {}
        for proxy_id in proxy_ids:
            tasks[proxy_id] = asyncio.create_task(self.async_check_proxy_health(proxy_id))
        
        results = {}
        for proxy_id, task in tasks.items():
            try:
                results[proxy_id] = await task
            except Exception as e:
                logger.error(f"Error checking proxy {proxy_id}: {e}")
                results[proxy_id] = {
                    'status': False,
                    'error': str(e)
                }
        
        return results

    def get_proxy_stats(self, proxy_id: str = None) -> Dict[str, Any]:
        """
        Get statistics for a proxy.
        
        Args:
            proxy_id: ID of the proxy or None to get stats for all proxies
            
        Returns:
            Dictionary with proxy statistics
        """
        if proxy_id:
            return self.proxy_stats.get(proxy_id, {})
        else:
            return self.proxy_stats

    def get_best_performing_proxies(self, count: int = 5) -> List[Dict[str, Any]]:
        """
        Get the best performing proxies based on success rate.
        
        Args:
            count: Maximum number of proxies to return
            
        Returns:
            List of proxy configurations sorted by performance
        """
        # Sort proxies by success rate
        rated_proxies = []
        for proxy in self.proxies:
            if not proxy.get('active', True):
                continue
                
            stats = self.proxy_stats.get(proxy['id'], {})
            success_rate = stats.get('success_rate', 0)
            avg_latency = stats.get('avg_latency', float('inf'))
            
            # Only include proxies with at least 5 requests
            if stats.get('request_count', 0) >= 5:
                rated_proxies.append((proxy, success_rate, avg_latency))
        
        # Sort by success rate (high to low), then latency (low to high)
        rated_proxies.sort(key=lambda x: (-x[1], x[2]))
        
        # Return the best ones
        return [p[0] for p in rated_proxies[:count]]

    def setup_proxy_chain(self, target_country: str = None) -> List[Dict[str, Any]]:
        """
        Set up a proxy chain for enhanced anonymity.
        
        Args:
            target_country: Target country for the exit node
            
        Returns:
            List of proxies forming the chain
        """
        chain_length = self.config.get('proxy_chain_length', 2)
        chain_length = min(chain_length, 3)  # Limit to 3 for performance
        
        if chain_length < 1:
            return []
        
        # Get candidate proxies
        candidates = [p for p in self.proxies if p.get('active', True)]
        if not candidates:
            return []
        
        # If targeting a specific country, make sure the last proxy is in that country
        exit_node = None
        if target_country:
            country_proxies = [p for p in candidates if p.get('country') == target_country]
            if country_proxies:
                exit_node = random.choice(country_proxies)
                candidates = [p for p in candidates if p['id'] != exit_node['id']]
        
        # Build the chain
        chain = []
        
        # Select entry point (prefer datacenter proxies)
        datacenter_proxies = [p for p in candidates if p.get('type') == 'datacenter']
        if datacenter_proxies:
            entry_proxy = random.choice(datacenter_proxies)
            chain.append(entry_proxy)
            candidates = [p for p in candidates if p['id'] != entry_proxy['id']]
        elif candidates:
            entry_proxy = random.choice(candidates)
            chain.append(entry_proxy)
            candidates = [p for p in candidates if p['id'] != entry_proxy['id']]
        
        # Add middle proxies if needed
        middle_count = chain_length - 1 - (1 if exit_node else 0)
        if middle_count > 0 and candidates:
            # Prefer residential proxies for middle nodes
            residential_proxies = [p for p in candidates if p.get('type') == 'residential']
            middle_candidates = residential_proxies if residential_proxies else candidates
            
            # Add up to middle_count middle proxies
            for _ in range(min(middle_count, len(middle_candidates))):
                middle_proxy = random.choice(middle_candidates)
                chain.append(middle_proxy)
                middle_candidates = [p for p in middle_candidates if p['id'] != middle_proxy['id']]
        
        # Add exit node if specified
        if exit_node:
            chain.append(exit_node)
        elif chain_length > len(chain) and candidates:
            # Add a random exit node
            exit_proxy = random.choice(candidates)
            chain.append(exit_proxy)
        
        logger.info(f"Created proxy chain with {len(chain)} proxies")
        return chain

    def format_proxy_chain_url(self, chain: List[Dict[str, Any]]) -> Optional[str]:
        """
        Format a proxy chain as a URL string for supporting libraries.
        
        Note: Not all HTTP libraries support proxy chaining directly.
        
        Args:
            chain: List of proxy configurations
            
        Returns:
            Formatted proxy chain URL or None
        """
        if not chain:
            return None
        
        if len(chain) == 1:
            return self.format_proxy_url(chain[0])
        
        # Format as semicolon-separated list for curl-based libraries
        urls = []
        for proxy in chain:
            url = self.format_proxy_url(proxy)
            if url:
                urls.append(url)
        
        if not urls:
            return None
        
        return ";".join(urls)

    def add_new_proxy(self, proxy: Dict[str, Any]) -> bool:
        """
        Add a new proxy to the pool.
        
        Args:
            proxy: Proxy configuration
            
        Returns:
            True if added successfully, False otherwise
        """
        if not proxy.get('host') or not proxy.get('port'):
            return False
        
        # Generate ID if not provided
        if not proxy.get('id'):
            proxy['id'] = f"proxy_{int(time.time())}_{random.randint(1000, 9999)}"
        
        # Set defaults
        proxy.setdefault('protocol', 'http')
        proxy.setdefault('active', True)
        proxy.setdefault('source', 'api')
        
        # Check for duplicates
        for existing in self.proxies:
            if (existing['host'] == proxy['host'] and 
                existing['port'] == proxy['port'] and
                existing.get('username') == proxy.get('username')):
                return False
        
        # Add to pool
        self.proxies.append(proxy)
        return True

    def remove_proxy(self, proxy_id: str) -> bool:
        """
        Remove a proxy from the pool.
        
        Args:
            proxy_id: ID of the proxy to remove
            
        Returns:
            True if removed successfully, False otherwise
        """
        for i, proxy in enumerate(self.proxies):
            if proxy['id'] == proxy_id:
                self.proxies.pop(i)
                return True
        
        return False

    def export_config(self, path: str = None) -> Dict[str, Any]:
        """
        Export proxy configuration.
        
        Args:
            path: Path to save configuration or None to return without saving
            
        Returns:
            Exported configuration dictionary
        """
        export = self.config.copy()
        
        # Remove sensitive sections
        if 'db' in export:
            if 'password' in export['db']:
                export['db']['password'] = '********'
        
        # Update proxies section with current proxies
        export['proxies'] = []
        for proxy in self.proxies:
            # Create a clean copy without runtime data
            clean = proxy.copy()
            if 'last_check' in clean:
                clean['last_check'] = None
            if 'check_status' in clean:
                del clean['check_status']
            
            export['proxies'].append(clean)
        
        if path:
            try:
                with open(path, 'w') as f:
                    json.dump(export, f, indent=2)
            except Exception as e:
                logger.error(f"Failed to save configuration to {path}: {e}")
        
        return export

    def close(self) -> None:
        """Clean up resources."""
        self.stop_health_checks()
        if self.db_conn:
            self.db_conn.close()
            self.db_conn = None
        if self.ip_db:
            self.ip_db.close()
            self.ip_db = None
        
        logger.info("Proxy Manager closed")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

# Create a singleton instance
proxy_manager = ProxyManager()