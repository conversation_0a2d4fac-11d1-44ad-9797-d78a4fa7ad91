#!/usr/bin/env python3
"""
Tests for the CaptchaSolver module.
"""

import os
import json
import time
import pytest
from unittest.mock import patch, MagicMock, mock_open

from captcha_solver import (
    CaptchaSolver,
    SolverResult,
    CaptchaType,
    CaptchaError,
    CaptchaTimeoutError
)


@pytest.fixture
def test_config():
    """Create a test configuration."""
    return {
        "twocaptcha_key": "test_2captcha_key",
        "anticaptcha_key": "test_anticaptcha_key",
        "capsolver_key": "test_capsolver_key",
        "service_priority": {
            "2captcha": 1,
            "anticaptcha": 2,
            "capsolver": 3
        },
        "max_retries": 3,
        "retry_delay": 0.1,
        "captcha_timeout": 30,
        "solution_cache_time": 600,
        "use_cache": True
    }


@pytest.fixture
def mock_solver(test_config):
    """Create a test CaptchaSolver instance with mocked API calls."""
    with patch('captcha_solver.CaptchaSolver._create_api_clients') as mock_create_clients:
        solver = CaptchaSolver(test_config)
        solver.twocaptcha_client = MagicMock()
        solver.anticaptcha_client = MagicMock()
        solver.capsolver_client = MagicMock()
        return solver


class TestCaptchaSolver:
    """Tests for the CaptchaSolver class."""
    
    def test_init(self, test_config):
        """Test solver initialization."""
        with patch('captcha_solver.CaptchaSolver._create_api_clients') as mock_create_clients:
            solver = CaptchaSolver(test_config)
            
            assert solver.config == test_config
            assert solver.max_retries == 3
            assert solver.retry_delay == 0.1
            assert solver.solution_cache == {}
            assert solver.cache_time == 600
            assert solver.use_cache is True
            
            mock_create_clients.assert_called_once()
    
    def test_create_api_clients(self, test_config):
        """Test API client creation."""
        with patch('captcha_solver.TwoCaptcha') as mock_twocaptcha, \
             patch('captcha_solver.AntiCaptchaClient') as mock_anticaptcha, \
             patch('captcha_solver.CapSolverClient') as mock_capsolver:
            
            solver = CaptchaSolver(test_config)
            
            # Verify clients were created with correct API keys
            mock_twocaptcha.assert_called_once_with(test_config["twocaptcha_key"])
            mock_anticaptcha.assert_called_once()
            mock_capsolver.assert_called_once()
    
    def test_solve_recaptcha_v2(self, mock_solver):
        """Test solving reCAPTCHA v2."""
        # Mock successful response
        mock_solver.twocaptcha_client.normal.return_value = "SOLVED_CAPTCHA_CODE"
        
        result = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert isinstance(result, SolverResult)
        assert result.solution == "SOLVED_CAPTCHA_CODE"
        assert result.success is True
        assert result.service == "2captcha"
        
        # Verify API was called with correct parameters
        mock_solver.twocaptcha_client.recaptcha.assert_called_once_with(
            sitekey="test_site_key",
            url="https://example.com/login"
        )
    
    def test_solve_recaptcha_v2_with_proxy(self, mock_solver):
        """Test solving reCAPTCHA v2 with a proxy."""
        # Mock successful response
        mock_solver.twocaptcha_client.normal.return_value = "SOLVED_CAPTCHA_CODE"
        
        # Test with dict proxy format
        proxy = {
            "id": "test_proxy",
            "host": "proxy.example.com",
            "port": 8080,
            "username": "user",
            "password": "pass"
        }
        
        result = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login",
            proxy=proxy
        )
        
        assert result.solution == "SOLVED_CAPTCHA_CODE"
        
        # Verify API was called with correct proxy format
        mock_solver.twocaptcha_client.recaptcha.assert_called_with(
            sitekey="test_site_key",
            url="https://example.com/login",
            proxy="user:<EMAIL>:8080"
        )
        
        # Reset mock for next test
        mock_solver.twocaptcha_client.recaptcha.reset_mock()
        
        # Test with string proxy format
        proxy_str = "user:<EMAIL>:8080"
        
        result = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login",
            proxy=proxy_str
        )
        
        # Verify API was called with correct proxy string
        mock_solver.twocaptcha_client.recaptcha.assert_called_with(
            sitekey="test_site_key",
            url="https://example.com/login",
            proxy=proxy_str
        )
    
    def test_solve_hcaptcha(self, mock_solver):
        """Test solving hCaptcha."""
        # Mock successful response
        mock_solver.twocaptcha_client.hcaptcha.return_value = "SOLVED_HCAPTCHA_CODE"
        
        result = mock_solver.solve_hcaptcha(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert result.solution == "SOLVED_HCAPTCHA_CODE"
        assert result.success is True
        
        # Verify API was called with correct parameters
        mock_solver.twocaptcha_client.hcaptcha.assert_called_once_with(
            sitekey="test_site_key",
            url="https://example.com/login"
        )
    
    def test_solve_image_captcha(self, mock_solver):
        """Test solving image CAPTCHA."""
        # Mock successful response
        mock_solver.twocaptcha_client.normal.return_value = "CAPTCHA123"
        
        # Test with file path
        with patch("builtins.open", mock_open(read_data=b"captcha_image_data")) as mock_file:
            result = mock_solver.solve_image_captcha(
                image_path="/path/to/captcha.jpg"
            )
            
            assert result.solution == "CAPTCHA123"
            assert result.success is True
            
            # Verify file was opened
            mock_file.assert_called_once_with("/path/to/captcha.jpg", "rb")
        
        # Reset mock for next test
        mock_solver.twocaptcha_client.normal.reset_mock()
        
        # Test with base64 data
        base64_data = "base64_encoded_image_data"
        
        result = mock_solver.solve_image_captcha(
            image_base64=base64_data
        )
        
        assert result.solution == "CAPTCHA123"
        
        # Verify API was called with correct base64 data
        mock_solver.twocaptcha_client.normal.assert_called_once_with(base64_data)
    
    def test_solve_turnstile(self, mock_solver):
        """Test solving Cloudflare Turnstile."""
        # Mock successful response
        mock_solver.twocaptcha_client.turnstile.return_value = "SOLVED_TURNSTILE_CODE"
        
        result = mock_solver.solve_turnstile(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert result.solution == "SOLVED_TURNSTILE_CODE"
        assert result.success is True
        
        # Verify API was called with correct parameters
        mock_solver.twocaptcha_client.turnstile.assert_called_once_with(
            sitekey="test_site_key",
            url="https://example.com/login"
        )
    
    def test_detect_captcha_type(self, mock_solver):
        """Test detecting CAPTCHA type from HTML content."""
        # Test reCAPTCHA detection
        recaptcha_html = """
        <html>
            <head>
                <script src="https://www.google.com/recaptcha/api.js"></script>
            </head>
            <body>
                <div class="g-recaptcha" data-sitekey="6LdqmVQiAAAAAAObwP1S18Gw_91h5-JCFaEWFEBB"></div>
            </body>
        </html>
        """
        
        captcha_type, site_key = mock_solver.detect_captcha_type(recaptcha_html)
        
        assert captcha_type == CaptchaType.RECAPTCHA_V2
        assert site_key == "6LdqmVQiAAAAAAObwP1S18Gw_91h5-JCFaEWFEBB"
        
        # Test hCaptcha detection
        hcaptcha_html = """
        <html>
            <head>
                <script src="https://js.hcaptcha.com/1/api.js"></script>
            </head>
            <body>
                <div class="h-captcha" data-sitekey="10000000-ffff-ffff-ffff-000000000001"></div>
            </body>
        </html>
        """
        
        captcha_type, site_key = mock_solver.detect_captcha_type(hcaptcha_html)
        
        assert captcha_type == CaptchaType.HCAPTCHA
        assert site_key == "10000000-ffff-ffff-ffff-000000000001"
        
        # Test Turnstile detection
        turnstile_html = """
        <html>
            <head>
                <script src="https://challenges.cloudflare.com/turnstile/v0/api.js"></script>
            </head>
            <body>
                <div class="cf-turnstile" data-sitekey="0x4AAAAAAAAE3BxxnQVzGYzB"></div>
            </body>
        </html>
        """
        
        captcha_type, site_key = mock_solver.detect_captcha_type(turnstile_html)
        
        assert captcha_type == CaptchaType.TURNSTILE
        assert site_key == "0x4AAAAAAAAE3BxxnQVzGYzB"
        
        # Test no CAPTCHA
        no_captcha_html = "<html><body>No CAPTCHA here</body></html>"
        
        captcha_type, site_key = mock_solver.detect_captcha_type(no_captcha_html)
        
        assert captcha_type == CaptchaType.NONE
        assert site_key is None
    
    def test_solve_with_service_priority(self, mock_solver):
        """Test solving with service priority."""
        # Make first service fail
        mock_solver.twocaptcha_client.recaptcha.side_effect = Exception("2Captcha error")
        
        # Make second service succeed
        mock_solver.anticaptcha_client.solve_recaptcha_v2.return_value = "ANTICAPTCHA_SOLUTION"
        
        result = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        # Verify fallback worked
        assert result.solution == "ANTICAPTCHA_SOLUTION"
        assert result.service == "anticaptcha"
        
        # Verify all services were tried in order
        mock_solver.twocaptcha_client.recaptcha.assert_called_once()
        mock_solver.anticaptcha_client.solve_recaptcha_v2.assert_called_once()
        mock_solver.capsolver_client.solve_recaptcha_v2.assert_not_called()
    
    def test_all_services_fail(self, mock_solver):
        """Test behavior when all services fail."""
        # Make all services fail
        mock_solver.twocaptcha_client.recaptcha.side_effect = Exception("2Captcha error")
        mock_solver.anticaptcha_client.solve_recaptcha_v2.side_effect = Exception("AntiCaptcha error")
        mock_solver.capsolver_client.solve_recaptcha_v2.side_effect = Exception("CapSolver error")
        
        with pytest.raises(CaptchaError) as excinfo:
            mock_solver.solve_recaptcha_v2(
                site_key="test_site_key",
                page_url="https://example.com/login"
            )
        
        # Verify error message contains all service errors
        error_msg = str(excinfo.value)
        assert "All CAPTCHA services failed" in error_msg
        assert "2Captcha error" in error_msg
        assert "AntiCaptcha error" in error_msg
        assert "CapSolver error" in error_msg
    
    def test_solution_caching(self, mock_solver):
        """Test solution caching functionality."""
        # Mock successful response
        mock_solver.twocaptcha_client.recaptcha.return_value = "SOLVED_CAPTCHA_CODE"
        
        # First call should use the API
        result1 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert result1.solution == "SOLVED_CAPTCHA_CODE"
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 1
        
        # Second call with same parameters should use cache
        result2 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert result2.solution == "SOLVED_CAPTCHA_CODE"
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 1  # No additional calls
        
        # Different parameters should make a new API call
        result3 = mock_solver.solve_recaptcha_v2(
            site_key="different_site_key",
            page_url="https://example.com/login"
        )
        
        assert result3.solution == "SOLVED_CAPTCHA_CODE"
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 2
    
    def test_disable_caching(self, mock_solver):
        """Test disabling the solution cache."""
        # Disable caching
        mock_solver.use_cache = False
        
        # Mock successful response
        mock_solver.twocaptcha_client.recaptcha.return_value = "SOLVED_CAPTCHA_CODE"
        
        # First call
        result1 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        # Second call with same parameters should NOT use cache
        result2 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        # Verify API was called twice
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 2
    
    def test_solve_from_html(self, mock_solver):
        """Test solving CAPTCHA directly from HTML content."""
        # HTML with reCAPTCHA
        recaptcha_html = """
        <html>
            <head>
                <script src="https://www.google.com/recaptcha/api.js"></script>
            </head>
            <body>
                <div class="g-recaptcha" data-sitekey="6LdqmVQiAAAAAAObwP1S18Gw_91h5-JCFaEWFEBB"></div>
            </body>
        </html>
        """
        
        # Mock successful response
        mock_solver.twocaptcha_client.recaptcha.return_value = "SOLVED_FROM_HTML"
        
        result = mock_solver.solve_from_html(
            html_content=recaptcha_html,
            page_url="https://example.com/login"
        )
        
        assert result.solution == "SOLVED_FROM_HTML"
        assert result.success is True
        assert result.captcha_type == CaptchaType.RECAPTCHA_V2
        
        # Verify API was called with correct parameters
        mock_solver.twocaptcha_client.recaptcha.assert_called_once_with(
            sitekey="6LdqmVQiAAAAAAObwP1S18Gw_91h5-JCFaEWFEBB",
            url="https://example.com/login"
        )
    
    def test_timeout_handling(self, mock_solver):
        """Test CAPTCHA timeout handling."""
        # Configure a minimal timeout
        mock_solver.config["captcha_timeout"] = 0.1
        
        # Make the API call sleep to trigger timeout
        def slow_solve(*args, **kwargs):
            time.sleep(0.2)
            return "SOLVED_CAPTCHA_CODE"
        
        mock_solver.twocaptcha_client.recaptcha = slow_solve
        
        # Attempt to solve should raise timeout error
        with pytest.raises(CaptchaTimeoutError):
            mock_solver.solve_recaptcha_v2(
                site_key="test_site_key",
                page_url="https://example.com/login"
            )
    
    def test_custom_retries(self, mock_solver):
        """Test custom retry configuration."""
        # Configure custom retries
        mock_solver.max_retries = 2
        mock_solver.retry_delay = 0.1
        
        # Make API fail twice then succeed
        fail_count = [0]
        
        def fail_then_succeed(*args, **kwargs):
            fail_count[0] += 1
            if fail_count[0] <= 2:
                raise Exception(f"Attempt {fail_count[0]} failed")
            return "SOLVED_AFTER_RETRIES"
        
        mock_solver.twocaptcha_client.recaptcha = fail_then_succeed
        
        # Should succeed after retries
        result = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert result.solution == "SOLVED_AFTER_RETRIES"
        assert result.success is True
        assert fail_count[0] == 3  # Initial attempt + 2 retries
    
    def test_too_many_retries(self, mock_solver):
        """Test behavior when retries are exhausted."""
        # Configure minimal retries
        mock_solver.max_retries = 1
        mock_solver.retry_delay = 0.1
        
        # Make API always fail
        def always_fail(*args, **kwargs):
            raise Exception("Always failing")
        
        mock_solver.twocaptcha_client.recaptcha = always_fail
        mock_solver.anticaptcha_client.solve_recaptcha_v2 = always_fail
        mock_solver.capsolver_client.solve_recaptcha_v2 = always_fail
        
        with pytest.raises(CaptchaError) as excinfo:
            mock_solver.solve_recaptcha_v2(
                site_key="test_site_key",
                page_url="https://example.com/login"
            )
        
        # Verify error message indicates retries were attempted
        error_msg = str(excinfo.value)
        assert "All CAPTCHA services failed after retries" in error_msg
    
    def test_solution_cache_expiry(self, mock_solver):
        """Test solution cache expiration."""
        # Set a very short cache time
        mock_solver.cache_time = 0.2
        
        # Mock successful response
        mock_solver.twocaptcha_client.recaptcha.return_value = "SOLVED_CAPTCHA_CODE"
        
        # First call should use the API
        result1 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 1
        
        # Wait for cache to expire
        time.sleep(0.3)
        
        # Next call should use API again
        result2 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 2
    
    def test_clear_cache(self, mock_solver):
        """Test clearing the solution cache."""
        # Mock successful response
        mock_solver.twocaptcha_client.recaptcha.return_value = "SOLVED_CAPTCHA_CODE"
        
        # First call should use the API
        result1 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        # Cache should have one entry
        assert len(mock_solver.solution_cache) == 1
        
        # Clear the cache
        mock_solver.clear_cache()
        
        # Cache should be empty
        assert len(mock_solver.solution_cache) == 0
        
        # Next call should use API again
        result2 = mock_solver.solve_recaptcha_v2(
            site_key="test_site_key",
            page_url="https://example.com/login"
        )
        
        assert mock_solver.twocaptcha_client.recaptcha.call_count == 2
    
    def test_save_load_cache(self, mock_solver, tmpdir):
        """Test saving and loading the solution cache."""
        # Create a cache file path
        cache_file = tmpdir.join("captcha_cache.json")
        
        # Add some solutions to the cache
        mock_solver.solution_cache = {
            "key1": {
                "solution": "solution1",
                "timestamp": time.time(),
                "service": "2captcha"
            },
            "key2": {
                "solution": "solution2",
                "timestamp": time.time(),
                "service": "anticaptcha"
            }
        }
        
        # Save the cache
        mock_solver.save_cache(str(cache_file))
        
        # Clear the cache
        mock_solver.solution_cache = {}
        
        # Load the cache
        mock_solver.load_cache(str(cache_file))
        
        # Verify cache was restored
        assert "key1" in mock_solver.solution_cache
        assert "key2" in mock_solver.solution_cache
        assert mock_solver.solution_cache["key1"]["solution"] == "solution1"
        assert mock_solver.solution_cache["key2"]["solution"] == "solution2"


if __name__ == "__main__":
    pytest.main(["-v", __file__])