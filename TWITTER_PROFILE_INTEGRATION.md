# Twitter Profile Integration

This document describes the Twitter/X profile scraping functionality integrated into the trend-crawler system.

## Overview

The Twitter profile integration enhances the trend analysis system by:

1. Scraping Twitter/X profiles mentioned in trending content
2. Calculating influence scores for profiles
3. Establishing relationships between profiles and trends
4. Enhancing coolness scores with profile-based metrics

## Components

### 1. TwitterProfileScraper

The `TwitterProfileScraper` class is responsible for scraping Twitter/X profile data. It:

- Uses Selenium for browser automation
- Extracts profile metadata (name, bio, followers, etc.)
- Calculates influence scores based on multiple factors
- Stores profile data in the database

### 2. TrendAnalyzer

The `TrendAnalyzer` class integrates profile data with trend analysis. It:

- Extracts mentioned users from trend content
- Scrapes profiles for mentioned users
- Calculates influencer impact on trend scores
- Combines profile data with tweet metrics

### 3. ResourceManager

The `ResourceManager` class optimizes resource usage for scraping. It:

- Manages user agents to avoid detection
- Implements adaptive throttling
- Handles errors with exponential backoff
- Cleans up resources after use

## Database Schema

The integration adds two new tables to the database:

### twitter_profiles

Stores Twitter profile data:

```sql
CREATE TABLE IF NOT EXISTS twitter_profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    bio TEXT,
    category VARCHAR(255),
    website VARCHAR(255),
    joining_date DATE,
    followers_count INT,
    following_count INT,
    verified BOOLEAN DEFAULT FALSE,
    last_scraped DATETIME,
    influence_score FLOAT,
    trend_id INT,
    FOREIGN KEY (trend_id) REFERENCES coolness_data(id)
);
```

### profile_relationships

Tracks relationships between profiles and trends:

```sql
CREATE TABLE IF NOT EXISTS profile_relationships (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id VARCHAR(255) NOT NULL,
    related_trend_id INT NOT NULL,
    relationship_type ENUM('author', 'mention', 'influencer'),
    FOREIGN KEY (profile_id) REFERENCES twitter_profiles(profile_id),
    FOREIGN KEY (related_trend_id) REFERENCES coolness_data(id)
);
```

## Influence Score Calculation

The influence score is calculated based on multiple factors:

1. **Followers Count**: Logarithmic scale to avoid extreme values
2. **Verified Status**: Verified accounts get a boost
3. **Following Ratio**: High following/followers ratio reduces score
4. **Website Presence**: Having a website adds credibility
5. **Bio Quality**: Detailed bios increase score

The formula is:

```
influence_score = base_score + verified_boost + website_bonus + bio_bonus - ratio_penalty
```

Where:
- `base_score` is log(1+followers)/15 (logarithmic scale)
- `verified_boost` is 0.2 if verified
- `website_bonus` is 0.1 if website exists
- `bio_bonus` is proportional to bio length (max 0.1)
- `ratio_penalty` is based on following/followers ratio

## Coolness Score Enhancement

The profile data enhances the coolness score calculation:

```python
# Add Twitter profile influences if available
if profile_data:
    # Verified status boost
    if profile_data.get('verified', False):
        coolness_score += 0.15

    # Followers count boost
    followers = profile_data.get('followers_count', 0)
    coolness_score += 0.10 * min(1, followers / 1000000)

    # Website credibility boost
    if "http" in profile_data.get('website', ''):
        coolness_score += 0.05
```

## Usage

To enable Twitter profile analysis:

```bash
python trend_crawler.py --twitter-enabled --twitter-workers 5
```

The `--twitter-workers` parameter controls the number of concurrent profile scraping threads.

## Testing

To test the Twitter profile scraping functionality:

```bash
python test_profile_analysis.py --profiles elonmusk BillGates BarackObama
```

## Fallback Mechanisms

The system includes fallback mechanisms:

1. If Selenium is not available, the system skips profile scraping
2. If a profile cannot be scraped, the system continues with other profiles
3. If all profile scraping fails, the system falls back to tweet-only analysis

## Dependencies

Required dependencies:
- selenium
- webdriver-manager
- beautifulsoup4

These are included in the `requirements-twitter.txt` file.
