#!/usr/bin/env python3
"""
Vector Embedding Tasks for trend-crawler

This module contains Celery tasks for asynchronous embedding generation and storage.
"""

import os
import json
import logging
import time
from typing import Dict, List, Any, Optional, Union
import numpy as np
from datetime import datetime

from celery import Celery
from celery.exceptions import MaxRetriesExceededError
import psycopg2
from psycopg2 import pool

from vector_store_manager import VectorStoreManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration from environment variables
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_PORT = os.getenv('DB_PORT', '5432')
DB_NAME = os.getenv('DB_NAME', 'trend_crawler')
DB_USER = os.getenv('DB_USER', 'postgres')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'postgres')
MODEL_NAME = os.getenv('EMBEDDING_MODEL', 'all-MiniLM-L6-v2')
MODEL_VERSION = os.getenv('MODEL_VERSION', 'minilm_l6_v2')

# Initialize Celery app
app = Celery('vector_tasks', broker=CELERY_BROKER_URL, backend=CELERY_RESULT_BACKEND)

app.conf.update(
    task_acks_late=True,  # Acknowledge task only after completion/failure
    worker_prefetch_multiplier=1,  # Process one message at a time per worker process
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
)

# Global variables (initialized per worker process)
vector_store_manager = None

def get_vector_store_manager():
    """Get or initialize the vector store manager."""
    global vector_store_manager
    if vector_store_manager is None:
        logger.info("Initializing VectorStoreManager")
        try:
            vector_store_manager = VectorStoreManager(
                db_host=DB_HOST,
                db_port=DB_PORT,
                db_name=DB_NAME,
                db_user=DB_USER,
                db_password=DB_PASSWORD,
                model_name=MODEL_NAME,
                model_version=MODEL_VERSION
            )
            logger.info(f"VectorStoreManager initialized with model {MODEL_NAME}")
        except Exception as e:
            logger.error(f"Failed to initialize VectorStoreManager: {e}")
            raise
    return vector_store_manager

@app.task(
    bind=True,  # Gives access to `self` for retry mechanisms
    autoretry_for=(psycopg2.OperationalError, TimeoutError),  # Retry for these exceptions
    max_retries=5,  # Max number of retries
    default_retry_delay=60  # Delay before retrying (seconds)
)
def generate_embedding_for_content(
    self,
    content_id: int,
    model_version_tag: str = MODEL_VERSION
) -> Dict[str, Any]:
    """
    Generate embedding for a specific content ID and store it in the database.
    
    Args:
        content_id: ID of the content to process
        model_version_tag: Version tag for the embedding model
        
    Returns:
        Dictionary with status information
    """
    vsm = get_vector_store_manager()
    conn = None
    
    try:
        conn = vsm.get_connection()
        with conn.cursor() as cur:
            # Fetch content that needs embedding
            cur.execute(
                """
                SELECT content_text 
                FROM crawled_content 
                WHERE id = %s 
                AND (embedding IS NULL OR model_version != %s)
                AND status != 'embedding_failed'
                """,
                (content_id, model_version_tag)
            )
            result = cur.fetchone()
            
            if not result:
                logger.info(f"Content ID {content_id} not found or already processed. Skipping.")
                return {"status": "skipped", "reason": "not_found_or_processed", "content_id": content_id}
            
            content_text = result[0]
            if not content_text or not content_text.strip():
                logger.warning(f"No content text for ID: {content_id}")
                cur.execute(
                    """
                    UPDATE crawled_content 
                    SET status = 'embedding_failed_no_text' 
                    WHERE id = %s
                    """,
                    (content_id,)
                )
                conn.commit()
                return {"status": "failed", "reason": "no_text", "content_id": content_id}
            
            # Generate embedding
            start_time = time.time()
            embedding = vsm.generate_embedding(content_text)
            processing_time = time.time() - start_time
            
            # Update database with the embedding
            cur.execute(
                """
                UPDATE crawled_content
                SET embedding = %s, 
                    status = 'embedding_complete', 
                    model_version = %s, 
                    embedding_generated_at = NOW()
                WHERE id = %s
                """,
                (embedding.tolist(), model_version_tag, content_id)
            )
            conn.commit()
            
            logger.info(f"Successfully embedded content ID {content_id} in {processing_time:.2f} seconds")
            return {
                "status": "success",
                "content_id": content_id,
                "model_version": model_version_tag,
                "processing_time": processing_time
            }
    
    except psycopg2.OperationalError as e:
        logger.error(f"Database operational error for content ID {content_id}: {e}")
        if conn:
            conn.rollback()
        raise self.retry(exc=e)
        
    except Exception as e:
        logger.error(f"Error generating embedding for content ID {content_id}: {e}")
        if conn:
            conn.rollback()
            
            # Update status to indicate failure
            try:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE crawled_content
                        SET status = 'embedding_failed'
                        WHERE id = %s
                        """,
                        (content_id,)
                    )
                conn.commit()
            except Exception as update_error:
                logger.error(f"Failed to update status after error: {update_error}")
        
        try:
            raise self.retry(exc=e)
        except MaxRetriesExceededError:
            return {"status": "failed", "reason": "max_retries_exceeded", "content_id": content_id}
            
    finally:
        if conn and vsm:
            vsm.release_connection(conn)

@app.task(bind=True)
def process_pending_embeddings(self, batch_size: int = 100):
    """
    Process pending embeddings from the database.
    
    Args:
        batch_size: Number of items to process at once
        
    Returns:
        Dictionary with status information
    """
    vsm = get_vector_store_manager()
    conn = None
    
    try:
        conn = vsm.get_connection()
        with conn.cursor() as cur:
            # Get pending content IDs
            cur.execute(
                """
                SELECT id
                FROM crawled_content
                WHERE embedding IS NULL
                AND status = 'pending_embedding'
                ORDER BY scraped_at ASC
                LIMIT %s
                FOR UPDATE SKIP LOCKED
                """,
                (batch_size,)
            )
            
            pending_ids = [row[0] for row in cur.fetchall()]
            
            if not pending_ids:
                return {"status": "success", "message": "No pending embeddings found", "count": 0}
            
            # Queue individual tasks for each content ID
            for content_id in pending_ids:
                generate_embedding_for_content.apply_async(args=[content_id])
            
            return {
                "status": "success",
                "message": f"Queued {len(pending_ids)} items for embedding generation",
                "count": len(pending_ids)
            }
            
    except Exception as e:
        logger.error(f"Error processing pending embeddings: {e}")
        if conn:
            conn.rollback()
        return {"status": "failed", "reason": str(e)}
    finally:
        if conn and vsm:
            vsm.release_connection(conn)

@app.task(bind=True)
def discover_new_trends(self, min_cluster_size: int = 5, time_window: str = '1 day'):
    """
    Discover new trends by clustering recent content.
    
    Args:
        min_cluster_size: Minimum number of items to form a trend
        time_window: Time window for recent content (e.g. '1 day', '1 week')
        
    Returns:
        Dictionary with discovered trends
    """
    vsm = get_vector_store_manager()
    
    try:
        # Discover potential trend clusters
        potential_trends = vsm.discover_potential_trends(
            min_cluster_size=min_cluster_size,
            time_window=time_window
        )
        
        # Create actual trends from the potential clusters
        created_trends = []
        for trend in potential_trends:
            # Create a trend name from representative text (truncated)
            name = trend['representative_text'][:50] + '...' if len(trend['representative_text']) > 50 else trend['representative_text']
            
            trend_info = vsm.find_or_create_trend(
                content_ids=trend['content_ids'],
                name=name,
                description=f"Auto-discovered trend with {trend['size']} items. Average similarity: {trend['avg_similarity']:.2f}"
            )
            
            if trend_info:
                created_trends.append({
                    'trend_id': trend_info['id'],
                    'name': trend_info['name'],
                    'is_new': trend_info.get('is_new', False),
                    'content_count': trend['size']
                })
        
        return {
            "status": "success",
            "potential_trends": len(potential_trends),
            "created_trends": created_trends
        }
        
    except Exception as e:
        logger.error(f"Error discovering new trends: {e}")
        return {"status": "failed", "reason": str(e)}

if __name__ == "__main__":
    # For testing purposes only
    logger.info("This module should be run as a Celery worker, not directly.")
