# Twitter/X Scraping in Trend Crawler

This document explains the Twitter/X scraping functionality implemented in the Trend Crawler project.

## Overview

The Trend Crawler now includes a robust Twitter/X scraping system that uses a hybrid approach:

1. **API-based scraping** when a bearer token is available
2. **Browser automation** using <PERSON>wright for JavaScript rendering

This implementation properly handles X.com's JavaScript requirements while maintaining ethical scraping practices.

## Components

### 1. TwitterXScraper Class

The main scraper class that handles both API and browser-based scraping:

- `scrape_trends()` - Gets trending topics from Twitter/X
- `scrape_tweets(query, limit)` - Searches for tweets matching a query
- Uses context manager pattern with `async with` support

### 2. Database Integration

New database tables and columns:

- `twitter_data` - Stores individual tweets
- `twitter_metrics` - Stores aggregated metrics
- `twitter_scraping_logs` - Tracks scraping performance
- Added columns to `coolness_data`: `twitter_volume`, `twitter_sentiment`, `twitter_engagement`

### 3. Trend Analysis Integration

The `update_twitter_metrics()` function:

- Correlates website trends with Twitter trends
- Updates coolness scores based on Twitter engagement
- Stores Twitter-specific metrics in the database

## Usage

### Command Line Arguments

```bash
python trend_crawler.py --twitter-enabled --twitter-max-tweets 50
```

- `--twitter-enabled`: Enables Twitter scraping
- `--twitter-max-tweets`: Sets maximum tweets to retrieve per trend (default: 100)

### API Token Configuration

Create a `.env` file in the project root:

```
TWITTER_BEARER_TOKEN=your_api_token_here
```

If no API token is provided, the system will automatically default to using browser-based scraping.

### Dependencies

Required packages:

```bash
pip install playwright python-dotenv asyncio
playwright install
```

## Testing

You can test the Twitter scraping functionality separately:

```bash
python test_twitter_x_scraper.py
```

This will:
1. Scrape trending topics
2. Search for tweets on a test query
3. Save results to `twitter_test_results.json`
4. Test the coolness score update mechanism

## Implementation Details

### Hybrid Scraping Approach

The system tries multiple methods in sequence:

1. First attempts to use the Twitter API if a bearer token is available
2. Falls back to browser automation with Playwright if API fails

### Anti-Detection Measures

- Randomized delays between requests
- Realistic browser headers
- Proper user agent configuration
- Error handling and retry logic

### Integration with Coolness Scores

Twitter data affects coolness scores in several ways:

- Trending topics get a 20% boost
- High engagement increases impact score
- Tweet volume affects velocity score
- Sentiment analysis contributes to overall score

## Troubleshooting

If you encounter issues:

1. Check that Playwright is installed: `playwright install`
2. Verify your API token in the `.env` file
3. Try running with `--simple` flag for faster processing
4. Check the logs for specific error messages
