#!/usr/bin/env python3
"""
Twitter/X profile scraper with improved performance and reliability.
"""

import os
import logging
import asyncio
import numpy as np
from typing import Dict, Any, List, Optional, Set
import random
from datetime import datetime, timedelta
import json
import aiohttp
from bs4 import BeautifulSoup
import nest_asyncio
from textblob import TextB<PERSON>b
import re
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeout

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TwitterProfileScraper:
    """Enhanced Twitter/X profile scraper with caching and parallel processing."""

    def __init__(self, cache_dir: str = ".cache"):
        """Initialize the profile scraper with caching support."""
        self.cache_dir = cache_dir
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamp: Dict[str, datetime] = {}
        self.cache_duration = timedelta(hours=6)
        self.max_parallel_profiles = 5
        self.browser = None
        self.context = None
        self.available = False
        self._setup_cache()
        # Remove synchronous browser initialization
        # self._init_browser()

    def _setup_cache(self):
        """Set up caching directory and load existing cache."""
        try:
            os.makedirs(self.cache_dir, exist_ok=True)
            cache_file = os.path.join(self.cache_dir, "profile_cache.json")

            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached_data = json.load(f)
                    self.cache = cached_data.get('profiles', {})
                    # Convert timestamp strings to datetime objects
                    self.cache_timestamp = {
                        k: datetime.fromisoformat(v)
                        for k, v in cached_data.get('timestamps', {}).items()
                    }
                logger.info(f"Loaded {len(self.cache)} profiles from cache")
        except Exception as e:
            logger.error(f"Error setting up cache: {e}")
            self.cache = {}
            self.cache_timestamp = {}

    def _save_cache(self):
        """Save current cache to disk."""
        try:
            cache_file = os.path.join(self.cache_dir, "profile_cache.json")
            with open(cache_file, 'w') as f:
                json.dump({
                    'profiles': self.cache,
                    'timestamps': {
                        k: v.isoformat()
                        for k, v in self.cache_timestamp.items()
                    }
                }, f, indent=2)
            logger.info("Cache saved successfully")
        except Exception as e:
            logger.error(f"Error saving cache: {e}")

    async def _init_browser(self):
        """Initialize browser with enhanced anti-detection measures."""
        try:
            playwright = await async_playwright().start()

            # Use more sophisticated browser launch options for openSUSE compatibility
            self.browser = await playwright.chromium.launch(
                headless=True,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=IsolateOrigins,site-per-process',
                    '--disable-site-isolation-trials',
                    '--disable-web-security',
                    '--disable-features=BlockInsecurePrivateNetworkRequests',
                    '--window-size=1920,1080',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',  # Added for openSUSE
                    '--disable-gpu',  # Added for openSUSE
                    '--disable-software-rasterizer',  # Added for openSUSE
                ]
            )

            # Create context with enhanced anti-detection
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                locale='en-US',
                timezone_id='America/New_York',
                permissions=['geolocation'],
                java_script_enabled=True
            )

            # Add extra headers
            await self.context.set_extra_http_headers({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            })

            # Modify navigator properties
            await self.context.add_init_script('''
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
                Object.defineProperty(navigator, 'plugins', { get: () => [{ description: "PDF Viewer", filename: "internal-pdf-viewer" }] });
                Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
            ''')

            self.available = True
            logger.info("Browser initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            self.available = False
            return False

    async def scrape_profiles(self, usernames: List[str], force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Scrape multiple profiles in parallel with caching.

        Args:
            usernames: List of Twitter/X usernames
            force_refresh: Force refresh cache for all profiles

        Returns:
            List of profile data dictionaries
        """
        results = []
        pending = []

        # Filter out cached profiles unless force refresh
        if not force_refresh:
            now = datetime.now()
            pending = [
                username for username in usernames
                if username not in self.cache or
                (now - self.cache_timestamp.get(username, now)) > self.cache_duration
            ]
        else:
            pending = usernames.copy()

        # Return cached results for non-pending usernames
        results.extend([
            self.cache[username]
            for username in usernames
            if username not in pending and username in self.cache
        ])

        # Process pending profiles in parallel batches
        if pending:
            if not self.available and not await self._init_browser():
                logger.error("Browser initialization failed")
                # Return only the results we have, don't generate mock profiles
                return results

            for i in range(0, len(pending), self.max_parallel_profiles):
                batch = pending[i:i + self.max_parallel_profiles]

                # Create tasks for parallel processing
                tasks = [
                    self._scrape_profile_with_retry(username)
                    for username in batch
                ]

                # Wait for all tasks to complete
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                for username, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        logger.error(f"Failed to scrape profile {username}: {result}")
                        # Skip failed profiles instead of generating mock data
                    else:
                        self.cache[username] = result
                        self.cache_timestamp[username] = datetime.now()
                        results.append(result)

                # Add random delay between batches
                if i + self.max_parallel_profiles < len(pending):
                    await asyncio.sleep(random.uniform(2, 5))

        # Save updated cache
        self._save_cache()
        return results

    async def _scrape_profile_with_retry(self, username: str, max_retries: int = 3) -> Dict[str, Any]:
        """Scrape a profile with retry mechanism."""
        for attempt in range(max_retries):
            try:
                return await self._scrape_profile(username)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                delay = (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"Attempt {attempt + 1} failed for {username}: {e}. Retrying in {delay:.1f}s")
                await asyncio.sleep(delay)

    async def _scrape_profile(self, username: str) -> Dict[str, Any]:
        """Scrape an individual profile with enhanced extraction."""
        page = await self.context.new_page()
        try:
            # First visit Twitter homepage
            await page.goto("https://twitter.com", timeout=30000)
            await page.wait_for_timeout(random.randint(1000, 2000))

            # Navigate to profile
            await page.goto(f"https://twitter.com/{username}", timeout=30000)
            await page.wait_for_load_state('networkidle')

            # Wait for profile content with multiple selector attempts
            profile_selectors = [
                '[data-testid="UserName"]',
                '[data-testid="UserDescription"]',
                '[data-testid="UserProfileHeader-Items"]'
            ]

            profile_loaded = False
            for selector in profile_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    profile_loaded = True
                    break
                except Exception:
                    continue

            if not profile_loaded:
                raise Exception("Profile content not found")

            # Extract profile information
            profile_data = {}

            # Get follower count with improved selector and extraction
            follower_selectors = [
                'a[href$="/followers"] span span',
                '[data-testid="followersCount"]',
                'a[href*="/followers"] span',
                '[data-testid="UserProfileHeader_Items"] a[href*="/followers"]',
                '[data-testid="primaryColumn"] a[href*="/followers"]'
            ]

            for selector in follower_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element:
                        text = await element.inner_text()
                        logger.info(f"Found follower text: '{text}' for {username}")
                        count = self._parse_count(text)
                        if count > 0:
                            profile_data['followers_count'] = count
                            break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue

            # If standard selectors fail, try JavaScript extraction
            if 'followers_count' not in profile_data or profile_data['followers_count'] == 0:
                try:
                    # Use JavaScript to extract follower count from any element containing "followers"
                    count_text = await page.evaluate('''() => {
                        const elements = Array.from(document.querySelectorAll('*'));
                        for (const el of elements) {
                            if (el.textContent && el.textContent.toLowerCase().includes('follower')) {
                                return el.textContent;
                            }
                        }
                        return '';
                    }''')

                    if count_text:
                        logger.info(f"JS extraction found follower text: '{count_text}' for {username}")
                        # Extract numbers from the text
                        import re
                        numbers = re.findall(r'[\d,\.]+[KMB]?', count_text)
                        if numbers:
                            count = self._parse_count(numbers[0])
                            if count > 0:
                                profile_data['followers_count'] = count
                except Exception as e:
                    logger.warning(f"JavaScript follower extraction failed: {e}")

            if 'followers_count' not in profile_data:
                profile_data['followers_count'] = 0

            # Get following count
            following_count = 0
            following_selectors = [
                'a[href$="/following"] span span',
                '[data-testid="followingCount"]',
                'a[href*="/following"] span'
            ]

            for selector in following_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element:
                        text = await element.inner_text()
                        count = self._parse_count(text)
                        if count > 0:
                            following_count = count
                            break
                except Exception:
                    continue

            profile_data['following_count'] = following_count

            # Get profile name and bio
            try:
                name_element = await page.wait_for_selector('[data-testid="UserName"]')
                profile_data['name'] = await name_element.inner_text() if name_element else username
            except Exception as e:
                logger.warning(f"Could not get profile name: {e}")
                profile_data['name'] = username

            try:
                bio_element = await page.wait_for_selector('[data-testid="UserDescription"]')
                profile_data['bio'] = await bio_element.inner_text() if bio_element else ""
            except Exception as e:
                logger.warning(f"Could not get bio: {e}")
                profile_data['bio'] = ""

            # Get verification status
            try:
                verified_element = await page.query_selector('[aria-label="Verified account"]')
                profile_data['verified'] = bool(verified_element)
            except Exception:
                profile_data['verified'] = False

            # Get location if available
            try:
                location_element = await page.query_selector('[data-testid="UserProfileHeader-Items"] span')
                profile_data['location'] = await location_element.inner_text() if location_element else ""
            except Exception:
                profile_data['location'] = ""

            # Get website if available
            try:
                website_element = await page.query_selector('[data-testid="UserUrl"]')
                profile_data['website'] = await website_element.get_attribute('href') if website_element else ""
            except Exception:
                profile_data['website'] = ""

            # Calculate influence score
            profile_data['influence_score'] = self._calculate_influence_score(profile_data)

            # Add metadata
            profile_data.update({
                'username': username,
                'last_scraped': datetime.now().isoformat(),
                'scrape_successful': True
            })

            logger.info(f"Successfully scraped profile for {username}")
            return profile_data

        except Exception as e:
            logger.error(f"Failed to scrape profile for {username}: {e}")
            raise
        finally:
            await page.close()

    def _parse_count(self, text: str) -> int:
        """Parse follower/following count with improved handling."""
        try:
            # Remove commas and spaces
            text = text.replace(',', '').replace(' ', '')

            # Extract number and multiplier using regex
            match = re.match(r'^([\d.]+)([KMB])?$', text.upper())
            if not match:
                return 0

            number = float(match.group(1))
            multiplier = match.group(2)

            if multiplier == 'K':
                return int(number * 1000)
            elif multiplier == 'M':
                return int(number * 1000000)
            elif multiplier == 'B':
                return int(number * 1000000000)
            else:
                return int(number)

        except (ValueError, TypeError, AttributeError):
            return 0

    def _calculate_influence_score(self, profile_data: Dict[str, Any]) -> float:
        """Calculate profile influence score with improved metrics."""
        score = 0.0
        max_score = 0.0

        # Follower score (40%)
        if profile_data.get('followers_count', 0) > 0:
            follower_score = min(1.0, np.log10(profile_data['followers_count'] + 1) / np.log10(1000000))
            score += follower_score * 0.4
            max_score += 0.4

        # Verified status (20%)
        if profile_data.get('verified', False):
            score += 0.2
            max_score += 0.2

        # Location and website presence (10%)
        if profile_data.get('location'):
            score += 0.05
            max_score += 0.05
        if profile_data.get('website'):
            score += 0.05
            max_score += 0.05

        # Bio sentiment and quality (15%)
        if profile_data.get('bio'):
            try:
                # Sentiment analysis
                blob = TextBlob(profile_data['bio'])
                sentiment = (blob.sentiment.polarity + 1) / 2  # Convert to 0-1 range

                # Bio length and quality
                words = len(profile_data['bio'].split())
                length_score = min(1.0, words / 50)  # Favor bios up to 50 words

                bio_score = (sentiment * 0.5 + length_score * 0.5) * 0.15
                score += bio_score
                max_score += 0.15
            except Exception:
                pass

        # Following ratio (15%)
        if profile_data.get('followers_count', 0) > 0 and profile_data.get('following_count', 0) > 0:
            ratio = profile_data['followers_count'] / max(1, profile_data['following_count'])
            ratio_score = min(1.0, ratio / 100)  # Cap at 100:1 ratio
            score += ratio_score * 0.15
            max_score += 0.15

        # Normalize score based on available components
        return score / max_score if max_score > 0 else 0.0

    # Mock profile generation method removed

    async def cleanup(self):
        """Clean up resources."""
        try:
            if self.browser:
                await self.browser.close()
                self.browser = None
                self.context = None
                self.available = False
            self._save_cache()
            logger.info("Resources cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    async def __aenter__(self):
        """Async context manager entry."""
        await self._init_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
