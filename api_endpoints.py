#!/usr/bin/env python3
"""
API endpoints for the trend-crawler system.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import FastAPI, APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import dependencies
try:
    from coolness_classifier import CoolnessClassifier
    CLASSIFIER_AVAILABLE = True
except ImportError:
    CLASSIFIER_AVAILABLE = False
    logger.warning("CoolnessClassifier not available. Some endpoints will be limited.")

# Create FastAPI app
app = FastAPI(
    title="Trend Crawler API",
    description="API for the trend-crawler system",
    version="1.0.0"
)

# Create router
router = APIRouter(prefix="/api/v1")

# Database connection
db = None

# Classifier instance
classifier = None


# Models
class FeedbackRequest(BaseModel):
    """Feedback request model."""
    url: str = Field(..., description="URL to provide feedback for")
    rating: int = Field(..., description="Rating (1-9)", ge=1, le=9)
    user_id: Optional[str] = Field(None, description="User ID")


class PredictionRequest(BaseModel):
    """Prediction request model."""
    url: str = Field(..., description="URL to predict coolness for")
    text: str = Field(..., description="Text content to analyze")


# Dependency to get classifier
def get_classifier():
    """Get classifier instance."""
    global classifier
    if classifier is None and CLASSIFIER_AVAILABLE:
        classifier = CoolnessClassifier(db)
        classifier.initialize_model()
    return classifier


@router.post("/feedback")
async def submit_feedback(
    request: FeedbackRequest,
    classifier: CoolnessClassifier = Depends(get_classifier)
):
    """
    Submit feedback for a URL.
    
    Args:
        request: Feedback request
        
    Returns:
        Status message
    """
    if not CLASSIFIER_AVAILABLE:
        raise HTTPException(
            status_code=501,
            detail="Feedback collection not available. CoolnessClassifier not installed."
        )
    
    if not classifier or not classifier.available:
        raise HTTPException(
            status_code=503,
            detail="Classifier not available"
        )
    
    try:
        success = classifier.record_feedback(
            url=request.url,
            rating=request.rating,
            user_id=request.user_id
        )
        
        if success:
            return {"status": "feedback recorded"}
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to record feedback"
            )
    except Exception as e:
        logger.error(f"Error recording feedback: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error recording feedback: {str(e)}"
        )


@router.get("/coolness-prediction")
async def get_prediction(
    url: str,
    text: str,
    classifier: CoolnessClassifier = Depends(get_classifier)
):
    """
    Get coolness prediction for a URL.
    
    Args:
        url: URL to predict coolness for
        text: Text content to analyze
        
    Returns:
        Prediction result
    """
    if not CLASSIFIER_AVAILABLE:
        raise HTTPException(
            status_code=501,
            detail="Prediction not available. CoolnessClassifier not installed."
        )
    
    if not classifier or not classifier.available:
        raise HTTPException(
            status_code=503,
            detail="Classifier not available"
        )
    
    try:
        prediction = classifier.predict(text)
        
        # Store prediction if possible
        try:
            classifier.store_prediction(url, prediction)
        except Exception as e:
            logger.warning(f"Failed to store prediction: {e}")
        
        return prediction
    except Exception as e:
        logger.error(f"Error getting prediction: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting prediction: {str(e)}"
        )


# Add router to app
app.include_router(router)


def initialize_api(database=None):
    """
    Initialize API with database connection.
    
    Args:
        database: Database connection
        
    Returns:
        FastAPI app
    """
    global db
    db = database
    
    # Initialize classifier if available
    if CLASSIFIER_AVAILABLE:
        global classifier
        classifier = CoolnessClassifier(db)
        classifier.initialize_model()
    
    return app


if __name__ == "__main__":
    import uvicorn
    
    # Run API server
    uvicorn.run(app, host="0.0.0.0", port=8000)
