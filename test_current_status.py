#!/usr/bin/env python3
"""
Quick test to show current CAPTCHA solver status.
"""

import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_solver_availability():
    """Test which solvers are currently available."""
    print("🔍 CAPTCHA Solver System Status Check")
    print("=" * 50)
    
    # Test ML solvers import
    try:
        from ml_captcha_solvers import PYTORCH_AVAILABLE, TENSORFLOW_AVAILABLE
        print(f"📦 ML Package Import: ✅ SUCCESS")
        print(f"🔥 PyTorch Available: {'✅ YES' if PYTORCH_AVAILABLE else '❌ NO'}")
        print(f"🧠 TensorFlow Available: {'✅ YES' if TENSORFLOW_AVAILABLE else '❌ NO'}")
    except ImportError as e:
        print(f"📦 ML Package Import: ❌ FAILED - {e}")
        return
    
    print()
    
    # Test main captcha solver
    try:
        from captcha_solver import CaptchaSolver, ML_SOLVERS_AVAILABLE
        print(f"🎯 Main Solver Import: ✅ SUCCESS")
        print(f"🤖 ML Solvers Available: {'✅ YES' if ML_SOLVERS_AVAILABLE else '❌ NO'}")
        
        # Initialize solver
        solver = CaptchaSolver(use_local_ml=True)
        
        # Check which solvers are enabled
        enabled_solvers = [name for name, info in solver.solvers.items() if info['enabled']]
        print(f"🔧 Enabled Solvers: {', '.join(enabled_solvers) if enabled_solvers else 'None'}")
        
        # Show priority order
        if enabled_solvers:
            print("\n📊 Solver Priority Order:")
            sorted_solvers = sorted(enabled_solvers, key=lambda x: solver.solvers[x]['priority'])
            for i, solver_name in enumerate(sorted_solvers, 1):
                priority = solver.solvers[solver_name]['priority']
                print(f"  {i}. {solver_name} (priority: {priority})")
        
    except Exception as e:
        print(f"🎯 Main Solver Import: ❌ FAILED - {e}")
    
    print()
    print("💡 Status Summary:")
    if PYTORCH_AVAILABLE:
        print("  • PyTorch ML solver is ready for use")
    if TENSORFLOW_AVAILABLE:
        print("  • TensorFlow ML solver is available (with lazy loading)")
    if not (PYTORCH_AVAILABLE or TENSORFLOW_AVAILABLE):
        print("  • No ML solvers available, will use external services")
    print("  • System will fallback through available solvers automatically")

if __name__ == '__main__':
    test_solver_availability()
