# Security-Enhanced Docker Setup for NanoLLM
FROM pytorch/pytorch:2.1.0-cuda11.8-runtime

# Security hardening
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libssl3 \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Add PostgreSQL repository for pgvector
RUN curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /usr/share/keyrings/postgresql-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/postgresql-keyring.gpg] http://apt.postgresql.org/pub/repos/apt $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    postgresql-client-15 \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Non-root user context
RUN useradd -m -u 1000 nanollm
USER nanollm
WORKDIR /app

# Copy requirements first for better caching
COPY --chown=nanollm:nanollm requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --user -r requirements.txt

# Encrypted volume for API keys
VOLUME /secrets
ENV API_KEY_PATH=/secrets/encrypted_keys.bin

# Copy application code
COPY --chown=nanollm:nanollm . .

# Set proper permissions for security
RUN chmod -R 750 /app && \
    chmod 400 /app/security_policy.json

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app \
    MODEL_VERSION=1.0.0 \
    LOG_LEVEL=INFO \
    MAX_MEMORY_MB=500 \
    MAX_TRENDS_PER_HOUR=1000 \
    TLS_MIN_VERSION=1.3

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose necessary ports
EXPOSE 8000

# Run with security flags
CMD ["python", "-u", "secure_server.py"]
