#!/usr/bin/env python3
"""
Quick test to verify private mode changes without full application startup
"""

import os

def test_file_changes():
    """Test that the required file changes are in place"""
    print("Testing private mode file changes...")
    
    # Test 1: Check login.html changes
    try:
        with open('templates/login.html', 'r') as f:
            content = f.read()
            if "Registration is disabled" in content and "Contact an administrator" in content:
                print("✓ Login template shows private mode message")
            else:
                print("✗ Login template missing private mode message")
    except Exception as e:
        print(f"✗ Error reading login template: {e}")

    # Test 2: Check registration route is commented out
    try:
        with open('web_dashboard.py', 'r') as f:
            content = f.read()
            # Look for the commented registration route
            if '# Registration disabled for private mode' in content:
                print("✓ Registration route is properly disabled")
            else:
                print("✗ Registration route may not be disabled")
                
            # Check for admin promotion endpoint
            if '@dashboard_router.post("/api/v1/users/{username}/promote-admin")' in content:
                print("✓ Admin promotion endpoint is present")
            else:
                print("✗ Admin promotion endpoint missing")
    except Exception as e:
        print(f"✗ Error reading web_dashboard.py: {e}")

    # Test 3: Check user management template
    try:
        with open('templates/user_management.html', 'r') as f:
            content = f.read()
            if 'promoteAdmin(' in content and 'Promote to Admin' in content:
                print("✓ User management template has admin promotion features")
            else:
                print("✗ User management template missing admin promotion features")
    except Exception as e:
        print(f"✗ Error reading user management template: {e}")

    # Test 4: Check JavaScript files for removed /register paths
    js_files = [
        'static/js/dashboard.js',
        'static/js/auth-guard.js', 
        'static/js/page-auth.js'
    ]
    
    for js_file in js_files:
        try:
            with open(js_file, 'r') as f:
                content = f.read()
                if '"/register"' not in content and "'/register'" not in content:
                    print(f"✓ {js_file} has registration path removed")
                else:
                    print(f"✗ {js_file} still contains registration path")
        except Exception as e:
            print(f"✗ Error reading {js_file}: {e}")

def test_code_syntax():
    """Test that the code has no syntax errors"""
    print("\nTesting code syntax...")
    
    try:
        # Try to compile the main file
        with open('web_dashboard.py', 'r') as f:
            code = f.read()
        compile(code, 'web_dashboard.py', 'exec')
        print("✓ web_dashboard.py has no syntax errors")
    except SyntaxError as e:
        print(f"✗ Syntax error in web_dashboard.py: {e}")
    except Exception as e:
        print(f"✗ Error checking syntax: {e}")

if __name__ == "__main__":
    print("=== Quick Private Mode Test ===\n")
    test_file_changes()
    test_code_syntax()
    print("\n=== Test Complete ===")
