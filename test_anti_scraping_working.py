#!/usr/bin/env python3
"""
Test script for the anti-scraping implementation using simplified imports.
"""

import logging
import argparse
import json
import time
import asyncio
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_mock_shield():
    """Create a mock ScrapingShield for testing."""
    class MockShield:
        def __init__(self):
            self.user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            ]
        
        def rotate_user_agent(self):
            return self.user_agents[0]
        
        def get_enhanced_headers(self):
            return {
                'User-Agent': self.rotate_user_agent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
        
        def detect_honeypot(self, response):
            return False
        
        def cleanup(self):
            pass
    
    return MockShield()


def test_basic_functionality():
    """Test basic anti-scraping functionality."""
    logger.info("Testing basic anti-scraping functionality...")
    
    try:
        # Create mock shield
        shield = create_mock_shield()
        
        # Test user agent rotation
        ua = shield.rotate_user_agent()
        logger.info(f"✅ User agent rotation: {ua[:50]}...")
        
        # Test enhanced headers
        headers = shield.get_enhanced_headers()
        logger.info(f"✅ Enhanced headers: {len(headers)} headers generated")
        
        # Test honeypot detection
        class MockResponse:
            text = "<html><body>Test content</body></html>"
        
        is_honeypot = shield.detect_honeypot(MockResponse())
        logger.info(f"✅ Honeypot detection: {is_honeypot}")
        
        logger.info("✅ Basic functionality test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic functionality test FAILED: {e}")
        return False


def test_import_structure():
    """Test the import structure after directory reorganization."""
    logger.info("Testing import structure...")
    
    try:
        # Test if tradingview modules exist and can be imported
        import importlib.util
        
        # Check tradingview modules
        tradingview_modules = [
            'tradingview.ideas_scraper',
            'tradingview.chart_scraper', 
            'tradingview.technicals_scraper'
        ]
        
        for module_name in tradingview_modules:
            try:
                spec = importlib.util.find_spec(module_name)
                if spec is not None:
                    logger.info(f"✅ Module {module_name} found")
                else:
                    logger.warning(f"⚠️  Module {module_name} not found")
            except Exception as e:
                logger.warning(f"⚠️  Error checking {module_name}: {e}")
        
        logger.info("✅ Import structure test completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Import structure test FAILED: {e}")
        return False


def test_config_loading():
    """Test configuration loading."""
    logger.info("Testing configuration loading...")
    
    try:
        config_file = "proxy_config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
                logger.info(f"✅ Configuration loaded: {len(config)} settings")
        else:
            logger.info("ℹ️  No config file found, will use defaults")
        
        logger.info("✅ Configuration test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test FAILED: {e}")
        return False


async def test_async_functionality():
    """Test async functionality."""
    logger.info("Testing async functionality...")
    
    try:
        # Mock async request
        async def mock_request(url):
            await asyncio.sleep(0.1)  # Simulate network delay
            return f"Mock response for {url}"
        
        # Test async execution
        result = await mock_request("https://example.com")
        logger.info(f"✅ Async request result: {result}")
        
        logger.info("✅ Async functionality test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Async functionality test FAILED: {e}")
        return False


def test_file_structure():
    """Test that required files exist."""
    logger.info("Testing file structure...")
    
    required_files = [
        'tradingview/ideas_scraper.py',
        'tradingview/chart_scraper.py',
        'tradingview/technicals_scraper.py',
        'anti_scraping/__init__.py',
        'trend_crawler.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            logger.info(f"✅ Found: {file_path}")
        else:
            logger.warning(f"⚠️  Missing: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning(f"Missing files: {missing_files}")
    else:
        logger.info("✅ All required files found")
    
    return len(missing_files) == 0


def main():
    """Run all tests."""
    parser = argparse.ArgumentParser(description="Test anti-scraping implementation")
    parser.add_argument('--test', choices=['basic', 'imports', 'config', 'async', 'files', 'all'], 
                        default='all', help='Which test to run')
    args = parser.parse_args()
    
    logger.info("=== Anti-Scraping Implementation Test Suite ===")
    
    tests = {
        'basic': test_basic_functionality,
        'imports': test_import_structure,
        'config': test_config_loading,
        'files': test_file_structure,
    }
    
    async_tests = {
        'async': test_async_functionality,
    }
    
    passed = 0
    total = 0
    
    # Run synchronous tests
    if args.test == 'all' or args.test in tests:
        for test_name, test_func in tests.items():
            if args.test == 'all' or args.test == test_name:
                logger.info(f"\n--- Running {test_name} test ---")
                total += 1
                if test_func():
                    passed += 1
    
    # Run async tests
    if args.test == 'all' or args.test in async_tests:
        for test_name, test_func in async_tests.items():
            if args.test == 'all' or args.test == test_name:
                logger.info(f"\n--- Running {test_name} test ---")
                total += 1
                if asyncio.run(test_func()):
                    passed += 1
    
    logger.info(f"\n=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        logger.info("🎉 All tests PASSED!")
        return 0
    else:
        logger.warning(f"⚠️  {total - passed} tests FAILED")
        return 1


if __name__ == "__main__":
    sys.exit(main())
