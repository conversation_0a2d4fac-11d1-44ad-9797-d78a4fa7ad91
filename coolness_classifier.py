#!/usr/bin/env python3
"""
Enhanced coolness classifier with BERT integration.
"""
import os
import logging
from typing import Dict, Any, Optional
import numpy as np
from datetime import datetime
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch
from textblob import TextBlob

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CoolnessClassifier:
    """Coolness classifier with BERT model integration."""

    def __init__(self, use_bert: bool = True):
        """
        Initialize the classifier.
        
        Args:
            use_bert: Whether to use BERT for sentiment analysis
        """
        self.use_bert = use_bert
        self.bert_model = None
        self.tokenizer = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self._init_bert_model() if use_bert else None

    def _init_bert_model(self):
        """Initialize BERT model for sentiment analysis."""
        try:
            # Load BERT model and tokenizer
            model_name = "nlptown/bert-base-multilingual-uncased-sentiment"
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.bert_model = AutoModelForSequenceClassification.from_pretrained(model_name)
            self.bert_model.to(self.device)
            logger.info("BERT model initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize BERT model: {e}")
            self.bert_model = None
            self.tokenizer = None
            self.use_bert = False

    def analyze_sentiment_bert(self, text: str) -> float:
        """
        Analyze sentiment using BERT model.
        
        Args:
            text: Text to analyze
            
        Returns:
            Sentiment score between -1 and 1
        """
        if not self.bert_model or not self.tokenizer:
            return self._fallback_sentiment(text)

        try:
            # Tokenize and prepare input
            inputs = self.tokenizer(text, return_tensors="pt", truncation=True, max_length=512, padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Get prediction
            with torch.no_grad():
                outputs = self.bert_model(**inputs)
                scores = outputs.logits.softmax(dim=1)
                
            # Convert 1-5 score to -1 to 1 range
            score = float(torch.argmax(scores)) + 1  # 1-5 score
            normalized_score = (score - 3) / 2  # Convert to -1 to 1 range
            return normalized_score

        except Exception as e:
            logger.error(f"Error in BERT sentiment analysis: {e}")
            return self._fallback_sentiment(text)

    def _fallback_sentiment(self, text: str) -> float:
        """Fallback to TextBlob for sentiment analysis."""
        try:
            blob = TextBlob(text)
            return blob.sentiment.polarity
        except Exception:
            return 0.0

    def calculate_engagement_score(self, metrics: Dict[str, Any]) -> float:
        """
        Calculate engagement score from metrics.
        
        Args:
            metrics: Dictionary containing engagement metrics
            
        Returns:
            Engagement score between 0 and 1
        """
        try:
            # Extract metrics with defaults
            views = float(metrics.get('views', 0))
            likes = float(metrics.get('likes', 0))
            shares = float(metrics.get('shares', 0))
            comments = float(metrics.get('comments', 0))
            
            # Calculate ratios
            engagement_ratio = (likes + shares + comments) / max(views, 1)
            share_ratio = shares / max(likes, 1)
            comment_ratio = comments / max(likes, 1)
            
            # Normalize metrics
            normalized_views = np.log10(views + 1) / 6  # Assuming max 1M views
            normalized_engagement = min(1.0, engagement_ratio * 10)  # Cap at 10% engagement
            normalized_share = min(1.0, share_ratio * 2)  # Cap at 50% share ratio
            normalized_comment = min(1.0, comment_ratio * 2)  # Cap at 50% comment ratio
            
            # Weighted combination
            score = (
                normalized_views * 0.3 +
                normalized_engagement * 0.3 +
                normalized_share * 0.2 +
                normalized_comment * 0.2
            )
            
            return float(min(1.0, max(0.0, score)))
        except Exception as e:
            logger.error(f"Error calculating engagement score: {e}")
            return 0.0

    def calculate_coolness_score(self, text: str, metrics: Optional[Dict[str, Any]] = None) -> float:
        """
        Calculate overall coolness score combining sentiment and engagement.
        
        Args:
            text: Content text
            metrics: Optional engagement metrics
            
        Returns:
            Coolness score between 0 and 1
        """
        try:
            # Get sentiment score (-1 to 1)
            sentiment = self.analyze_sentiment_bert(text) if self.use_bert else self._fallback_sentiment(text)
            
            # Convert sentiment to 0-1 range
            normalized_sentiment = (sentiment + 1) / 2
            
            # Calculate engagement score if metrics provided
            engagement_score = self.calculate_engagement_score(metrics) if metrics else 0.5
            
            # Length score based on optimal content length
            words = len(text.split())
            length_score = min(1.0, words / 1000)  # Optimal length around 1000 words
            
            # Quality indicators
            has_links = 'http' in text.lower()
            has_numbers = any(c.isdigit() for c in text)
            has_formatting = any(marker in text for marker in ['#', '*', '_', '-'])
            
            quality_score = (
                0.25 +  # Base score
                (0.25 if has_links else 0) +
                (0.25 if has_numbers else 0) +
                (0.25 if has_formatting else 0)
            )
            
            # Weighted combination
            score = (
                normalized_sentiment * 0.3 +
                engagement_score * 0.3 +
                length_score * 0.2 +
                quality_score * 0.2
            )
            
            return float(min(1.0, max(0.0, score)))
        except Exception as e:
            logger.error(f"Error calculating coolness score: {e}")
            return 0.5  # Return neutral score on error

    def save_model(self, path: str):
        """Save BERT model and configuration."""
        try:
            os.makedirs(path, exist_ok=True)

            # Save BERT model if available
            if self.use_bert and self.bert_model and self.tokenizer:
                self.bert_model.save_pretrained(os.path.join(path, 'bert_model'))
                self.tokenizer.save_pretrained(os.path.join(path, 'bert_tokenizer'))

            # Save configuration
            config = {
                'timestamp': datetime.now().isoformat(),
                'use_bert': self.use_bert,
                'device': str(self.device)
            }

            import json
            with open(os.path.join(path, 'config.json'), 'w') as f:
                json.dump(config, f, indent=2)

            logger.info(f"Model saved to {path}")

        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise

    def load_model(self, path: str):
        """Load BERT model and configuration."""
        try:
            import json
            with open(os.path.join(path, 'config.json'), 'r') as f:
                config = json.load(f)

            self.use_bert = config.get('use_bert', True)

            if self.use_bert:
                model_path = os.path.join(path, 'bert_model')
                tokenizer_path = os.path.join(path, 'bert_tokenizer')
                
                if os.path.exists(model_path) and os.path.exists(tokenizer_path):
                    self.bert_model = AutoModelForSequenceClassification.from_pretrained(model_path)
                    self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
                    self.bert_model.to(self.device)
                    logger.info("BERT model loaded from saved weights")
                else:
                    logger.warning("Saved BERT model not found, initializing new model")
                    self._init_bert_model()

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

if __name__ == "__main__":
    # Test the classifier
    classifier = CoolnessClassifier(use_bert=True)
    
    test_text = """
    Revolutionary AI breakthrough: A cutting-edge innovation in machine learning
    has led to groundbreaking developments in artificial intelligence. This trending
    technology is set to disrupt multiple industries with its next-gen capabilities.
    """
    
    test_metrics = {
        'views': 50000,
        'likes': 5000,
        'shares': 500,
        'comments': 200
    }
    
    test_twitter = {
        'twitter_volume': 50000,
        'twitter_engagement': 5000,
        'twitter_sentiment': 0.7,
        'verified_ratio': 0.4,
        'follower_weighted_score': 0.8
    }
    
    score = classifier.calculate_coolness_score(test_text, test_metrics, test_twitter)
    print(f"Test coolness score: {score:.2f}")
