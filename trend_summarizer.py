#!/usr/bin/env python3
"""
TrendSummarizer - Uses NanoLLM and Secure Distillation to generate trend summaries from vector data.
Supports innovative applications mentioned in the requirements:
1. Trend Summarization with LLMs
2. Predictive Trend Analysis
3. Identifying "Sleeper" Trends
4. Detecting Semantic Shifts in Ongoing Trends
"""

import logging
import time
from typing import Dict, List, Optional, Any
import numpy as np
import psycopg2
import json
from collections import deque
from datetime import datetime

# Import from nano_neural_network.py for NanoLLM and related components
from nano_neural_network import MetaLearner, TemporalGAN
# Import from secure_services.py for SecureDistillationSystem
from secure_services import SecureDistillationSystem, SecureAPIBridge, HybridRouter
# Import from vector_store_manager.py for vector operations
from vector_store_manager import VectorStoreManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- Security and Monitoring Integration Points ---
# These are stubs/placeholders for the advanced security, meta-learning, and GAN components
# as described in the architecture blueprint. Implementations should be provided in their respective modules.

# API Security Layer: Dual teacher LLM bridges with sanitization and rate limiting
try:
    from secure_services import SecureAPIBridge, HybridRouter
    # Example: self.api_router = HybridRouter(teacher1, teacher2)
except ImportError:
    SecureAPIBridge = None
    HybridRouter = None

# Meta-Learning and Optimizer hooks
try:
    from nano_neural_network import MetaLearner
except ImportError:
    MetaLearner = None

# Temporal Prediction GAN integration
try:
    from nano_neural_network import TemporalGAN
except ImportError:
    TemporalGAN = None

# Defense matrix and monitoring (for runtime security)
# Removed unresolved import for DefenseMatrix
DefenseMatrix = None

class TrendSummarizer:
    """
    Handles summarization of trends using secure distillation and NanoLLM.
    Implements the innovative applications mentioned in requirements.
    """
    
    def __init__(self,
                 secure_distiller: SecureDistillationSystem,
                 vector_store_manager: VectorStoreManager,
                 db_host: str = None,
                 db_port: str = None,
                 db_name: str = None,
                 db_user: str = None,
                 db_password: str = None):
        """
        Initialize the Trend Summarizer.
        
        Args:
            secure_distiller: Instance of SecureDistillationSystem for LLM operations
            vector_store_manager: Instance of VectorStoreManager for vector operations
            db_host: PostgreSQL database host
            db_port: PostgreSQL database port
            db_name: PostgreSQL database name
            db_user: PostgreSQL database user
            db_password: PostgreSQL database password
        """
        self.secure_distiller = secure_distiller
        self.vector_store_manager = vector_store_manager
        
        # Database connection parameters - use vector_store_manager's if not provided
        self.db_host = db_host or vector_store_manager.db_host
        self.db_port = db_port or vector_store_manager.db_port
        self.db_name = db_name or vector_store_manager.db_name
        self.db_user = db_user or vector_store_manager.db_user
        self.db_password = db_password or vector_store_manager.db_password
        
        # Create direct connection for transactions that need it
        try:
            self.conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                dbname=self.db_name,
                user=self.db_user,
                password=self.db_password
            )
            self.conn.autocommit = False  # For explicit transaction control
            logger.info("Connected to PostgreSQL database for trend summarization")
        except Exception as e:
            logger.error("Failed to connect to PostgreSQL: %s", e)
            self.conn = None
            raise
        
        # Initialize semantic velocity tracking
        self.velocity_cache = {}  # trend_id -> list of (timestamp, embedding) tuples
        self.max_velocity_history = 100  # Maximum number of points to track per trend
        
        # Cache for trend embeddings to avoid repeated database calls
        self.trend_embedding_cache = {}  # trend_id -> (embedding, timestamp)
        self.cache_ttl = 300  # Cache TTL in seconds

        # --- Advanced Architecture Integration Points ---
        # API Security Bridge (dual teacher LLMs)
        self.api_router = None
        if SecureAPIBridge and HybridRouter:
            try:
                # Example: initialize with your API keys and rate limits
                teacher1 = SecureAPIBridge(api_key='API_KEY1', rate_limits=(15/60, 200/86400))
                teacher2 = SecureAPIBridge(api_key='API_KEY2', rate_limits=(30/60, 7000/86400))
                self.api_router = HybridRouter(teacher1, teacher2)
            except Exception as e:
                logger.warning("Could not initialize API router: %s", e)
        # Meta-Learner
        self.meta_learner = MetaLearner() if MetaLearner else None
        # Temporal Prediction GAN
        self.temporal_gan = TemporalGAN() if TemporalGAN else None
        # Defense Matrix for runtime security
        self.defense_matrix = DefenseMatrix() if DefenseMatrix else None

    def summarize_trend(self, trend_id: int, top_k: int = 10) -> Optional[str]:
        """
        Fetch the top K items for a trend, feed their content_text to NanoLLM,
        and store the summary in trends.description.
        
        Args:
            trend_id: ID of the trend to summarize
            top_k: Number of top items to include in summarization
            
        Returns:
            Generated summary or None if failed
        """
        logger.info("Summarizing trend %d using top %d items", trend_id, top_k)
        
        try:
            with self.conn.cursor() as cur:
                # Fetch top K items for the trend by similarity to trend centroid
                cur.execute("""
                SELECT cc.content_text
                FROM trend_content_map tcm
                JOIN crawled_content cc ON tcm.content_id = cc.id
                WHERE tcm.trend_id = %s
                ORDER BY tcm.similarity_to_trend DESC
                LIMIT %s
                """, (trend_id, top_k))
                
                rows = cur.fetchall()
                
                if not rows:
                    logger.warning("No content found for trend %d", trend_id)
                    return None
                
                # Extract content texts
                content_texts = [row[0] for row in rows]
                
                # Generate summary using NanoLLM via SecureDistillationSystem
                # This will handle the knowledge distillation from teacher models
                # The prompt template guides the model to summarize the trend
                prompt_template = "Summarize the following texts into a concise trend description: {text}"
                summaries = self.secure_distiller.summarize_with_nano_llm(content_texts, prompt_template)
                
                if not summaries:
                    logger.warning("Failed to generate summary for trend %d", trend_id)
                    return None
                
                combined_summary = summaries[0]  # Take the first summary for now
                # If we need to combine multiple summaries, we can implement that logic here
                
                # Store the summary in the trends table
                cur.execute("""
                UPDATE trends
                SET description = %s,
                    last_updated_at = NOW()
                WHERE id = %s
                """, (combined_summary, trend_id))
                
                self.conn.commit()
                logger.info("Updated description for trend %d", trend_id)
                
                return combined_summary
                
        except Exception as e:
            logger.error("Error summarizing trend %d: %s", trend_id, e)
            self.conn.rollback()
            return None

    def analyze_semantic_velocity(self, trend_id: int) -> Dict[str, Any]:
        """
        Analyze how quickly new semantically similar items are appearing
        for a specific trend.
        
        Args:
            trend_id: ID of the trend to analyze
            
        Returns:
            Dictionary with velocity metrics
        """
        try:
            with self.conn.cursor() as cur:
                # Get recent content for the trend with timestamps
                cur.execute("""
                SELECT cc.id, cc.embedding, cc.published_at
                FROM trend_content_map tcm
                JOIN crawled_content cc ON tcm.content_id = cc.id
                WHERE tcm.trend_id = %s
                ORDER BY cc.published_at DESC
                LIMIT 100
                """, (trend_id,))
                
                rows = cur.fetchall()
                
                if not rows:
                    logger.warning("No content found for trend %d", trend_id)
                    return {"trend_id": trend_id, "velocity": 0, "status": "insufficient_data"}
                
                # Process embeddings and timestamps
                embeddings = []
                timestamps = []
                for row in rows:
                    embeddings.append(np.array(row[1]))
                    timestamps.append(row[2])
                
                # Calculate semantic velocity
                # 1. Rate of new content
                time_span = (timestamps[0] - timestamps[-1]).total_seconds() / 3600  # hours
                if time_span < 1:  # Avoid division by zero or very small values
                    time_span = 1
                content_rate = len(rows) / time_span
                
                # 2. Semantic drift (how much the centroid is changing)
                recent_centroid = np.mean(embeddings[:10], axis=0) if len(embeddings) >= 10 else np.mean(embeddings, axis=0)
                older_centroid = np.mean(embeddings[-10:], axis=0) if len(embeddings) >= 20 else np.mean(embeddings, axis=0)
                
                # Calculate cosine similarity between centroids
                drift = 1.0 - np.dot(recent_centroid, older_centroid) / (np.linalg.norm(recent_centroid) * np.linalg.norm(older_centroid))
                
                # 3. Combined velocity metric
                velocity = content_rate * (1 + drift * 10)  # Weight drift higher
                
                # Update trend metadata
                cur.execute("""
                UPDATE trends
                SET metadata = jsonb_set(
                    COALESCE(metadata, '{}'::jsonb),
                    '{semantic_velocity}',
                    %s::jsonb,
                    true
                )
                WHERE id = %s
                """, (
                    json.dumps({
                        "velocity": velocity,
                        "content_rate": content_rate,
                        "semantic_drift": drift,
                        "measured_at": datetime.now().isoformat(),
                        "sample_size": len(rows)
                    }),
                    trend_id
                ))
                
                self.conn.commit()
                
                # Cache velocity data for tracking drift over time
                if trend_id not in self.velocity_cache:
                    self.velocity_cache[trend_id] = deque(maxlen=self.max_velocity_history)
                
                self.velocity_cache[trend_id].append((datetime.now(), recent_centroid))
                
                return {
                    "trend_id": trend_id,
                    "velocity": velocity,
                    "content_rate": content_rate,
                    "semantic_drift": drift,
                    "status": "success"
                }
                
        except Exception as e:
            logger.error("Error analyzing semantic velocity for trend %d: %s", trend_id, e)
            self.conn.rollback()
            return {"trend_id": trend_id, "velocity": 0, "status": "error", "error": str(e)}

    def detect_semantic_shifts(self, trend_id: int, window_days: int = 7) -> Dict[str, Any]:
        """
        Monitor how the trend_embedding (centroid) of a long-running trend 
        drifts over time, indicating an evolution in the topic.
        
        Args:
            trend_id: ID of the trend to analyze
            window_days: Number of days to look back for shift detection
            
        Returns:
            Dictionary with shift detection metrics
        """
        try:
            with self.conn.cursor() as cur:
                # Get current trend embedding
                cur.execute("""
                SELECT trend_embedding
                FROM trends
                WHERE id = %s
                """, (trend_id,))
                
                _ = cur.fetchone()[0]  # Remove unused variable 'current_embedding'
                
                # Get historical content grouped by day to track evolution
                cur.execute("""
                SELECT 
                    DATE_TRUNC('day', cc.published_at) AS day,
                    AVG(ARRAY[cc.embedding]) AS avg_embedding
                FROM 
                    trend_content_map tcm
                JOIN 
                    crawled_content cc ON tcm.content_id = cc.id
                WHERE 
                    tcm.trend_id = %s AND
                    cc.published_at >= NOW() - INTERVAL %s DAY
                GROUP BY 
                    DATE_TRUNC('day', cc.published_at)
                ORDER BY 
                    day
                """, (trend_id, window_days))
                
                rows = cur.fetchall()
                
                if len(rows) < 2:
                    logger.warning("Insufficient historical data for trend %d", trend_id)
                    return {"trend_id": trend_id, "shift_detected": False, "status": "insufficient_data"}
                
                # Process daily centroids
                days = []
                centroids = []
                for day, embedding in rows:
                    days.append(day)
                    centroids.append(np.array(embedding))
                
                # Calculate shifts between consecutive days
                shifts = []
                for i in range(1, len(centroids)):
                    sim = np.dot(centroids[i], centroids[i-1]) / (np.linalg.norm(centroids[i]) * np.linalg.norm(centroids[i-1]))
                    shift = 1.0 - sim
                    shifts.append(shift)
                
                # Determine if significant shift has occurred
                avg_shift = np.mean(shifts)
                max_shift = np.max(shifts)
                max_shift_day = days[np.argmax(shifts) + 1] if shifts else None
                
                # Threshold for significant shift
                significant_shift = max_shift > 0.1  # Adjustable threshold
                
                # Store shift detection results
                shift_data = {
                    "avg_shift": float(avg_shift),
                    "max_shift": float(max_shift),
                    "max_shift_day": max_shift_day.isoformat() if max_shift_day else None,
                    "significant_shift": significant_shift,
                    "analyzed_at": datetime.now().isoformat(),
                    "window_days": window_days
                }
                
                cur.execute("""
                UPDATE trends
                SET metadata = jsonb_set(
                    COALESCE(metadata, '{}'::jsonb),
                    '{semantic_shift}',
                    %s::jsonb,
                    true
                )
                WHERE id = %s
                """, (json.dumps(shift_data), trend_id))
                
                self.conn.commit()
                
                return {
                    "trend_id": trend_id,
                    "shift_detected": significant_shift,
                    "avg_shift": avg_shift,
                    "max_shift": max_shift,
                    "max_shift_day": max_shift_day,
                    "status": "success"
                }
                
        except Exception as e:
            logger.error("Error detecting semantic shifts for trend %d: %s", trend_id, e)
            self.conn.rollback()
            return {"trend_id": trend_id, "shift_detected": False, "status": "error", "error": str(e)}

    def identify_sleeper_trends(self, cohesion_threshold: float = 0.9, growth_rate_threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        Identify trends that have a small but highly cohesive semantic core
        and are slowly gaining traction.
        
        Args:
            cohesion_threshold: Minimum cohesion score to consider (0-1)
            growth_rate_threshold: Minimum growth rate to consider
            
        Returns:
            List of sleeper trends with their metrics
        """
        try:
            sleeper_trends = []
            
            with self.conn.cursor() as cur:
                # Get all active trends with their content counts and time span
                cur.execute("""
                WITH trend_stats AS (
                    SELECT
                        t.id,
                        t.name,
                        t.trend_embedding,
                        COUNT(tcm.content_id) AS content_count,
                        MAX(cc.published_at) - MIN(cc.published_at) AS time_span,
                        AVG(tcm.similarity_to_trend) AS avg_similarity
                    FROM
                        trends t
                    JOIN
                        trend_content_map tcm ON t.id = tcm.trend_id
                    JOIN
                        crawled_content cc ON tcm.content_id = cc.id
                    WHERE
                        t.status != 'archived'
                    GROUP BY
                        t.id, t.name, t.trend_embedding
                )
                SELECT
                    id,
                    name,
                    trend_embedding,
                    content_count,
                    EXTRACT(EPOCH FROM time_span) / 3600 AS hours_span,
                    avg_similarity
                FROM
                    trend_stats
                WHERE
                    content_count BETWEEN 5 AND 20 -- Small core
                    AND avg_similarity >= %s -- Highly cohesive
                ORDER BY
                    avg_similarity DESC
                """, (cohesion_threshold,))
                
                potential_sleepers = cur.fetchall()
                
                for trend_id, name, _, content_count, hours_span, avg_similarity in potential_sleepers:
                    # Calculate growth rate (content per hour)
                    growth_rate = content_count / hours_span if hours_span > 0 else 0
                    
                    if growth_rate <= growth_rate_threshold and growth_rate > 0:
                        # This is a sleeper trend - slow but steady growth with high cohesion
                        
                        # Additional query to get recent engagement growth
                        cur.execute("""
                        WITH recent_content AS (
                            SELECT
                                cc.id,
                                cc.published_at,
                                cc.engagement_metrics
                            FROM
                                trend_content_map tcm
                            JOIN
                                crawled_content cc ON tcm.content_id = cc.id
                            WHERE
                                tcm.trend_id = %s
                            ORDER BY
                                cc.published_at DESC
                            LIMIT 10
                        ),
                        older_content AS (
                            SELECT
                                cc.id,
                                cc.published_at,
                                cc.engagement_metrics
                            FROM
                                trend_content_map tcm
                            JOIN
                                crawled_content cc ON tcm.content_id = cc.id
                            WHERE
                                tcm.trend_id = %s
                            ORDER BY
                                cc.published_at ASC
                            LIMIT 10
                        )
                        SELECT
                            AVG((rc.engagement_metrics->>'total_engagement')::float) AS recent_engagement,
                            AVG((oc.engagement_metrics->>'total_engagement')::float) AS older_engagement
                        FROM
                            recent_content rc,
                            older_content oc
                        """, (trend_id, trend_id))
                        
                        engagement_row = cur.fetchone()
                        recent_engagement = engagement_row[0] or 0
                        older_engagement = engagement_row[1] or 0.001  # Avoid division by zero
                        
                        engagement_growth = recent_engagement / older_engagement
                        
                        # Mark as sleeper trend in database
                        sleeper_data = {
                            "is_sleeper": True,
                            "cohesion_score": float(avg_similarity),
                            "growth_rate": float(growth_rate),
                            "content_count": content_count,
                            "engagement_growth": float(engagement_growth),
                            "identified_at": datetime.now().isoformat()
                        }
                        
                        cur.execute("""
                        UPDATE trends
                        SET metadata = jsonb_set(
                            COALESCE(metadata, '{}'::jsonb),
                            '{sleeper_trend}',
                            %s::jsonb,
                            true
                        )
                        WHERE id = %s
                        """, (json.dumps(sleeper_data), trend_id))
                        
                        sleeper_trends.append({
                            "trend_id": trend_id,
                            "name": name,
                            "cohesion_score": avg_similarity,
                            "growth_rate": growth_rate,
                            "content_count": content_count,
                            "engagement_growth": engagement_growth
                        })
                
                self.conn.commit()
                logger.info("Identified %d sleeper trends", len(sleeper_trends))
                
                return sleeper_trends
                
        except Exception as e:
            logger.error("Error identifying sleeper trends: %s", e)
            self.conn.rollback()
            return []

    def predict_trend_significance(self, trend_id: int) -> Dict[str, Any]:
        """
        Predict if a trend is likely to become significant based on:
        - Semantic velocity
        - Engagement growth
        - Network analysis (who is talking about it)
        
        Args:
            trend_id: ID of the trend to analyze
            
        Returns:
            Dictionary with prediction metrics
        """
        try:
            # Get semantic velocity
            velocity_metrics = self.analyze_semantic_velocity(trend_id)
            
            with self.conn.cursor() as cur:
                # Get trend info
                cur.execute("""
                SELECT name, first_seen_at
                FROM trends
                WHERE id = %s
                """, (trend_id,))
                
                name, first_seen_at = cur.fetchone()
                
                # Get engagement growth
                cur.execute("""
                WITH daily_stats AS (
                    SELECT
                        DATE_TRUNC('day', cc.published_at) AS day,
                        COUNT(cc.id) AS daily_count,
                        AVG((CASE WHEN cc.engagement_metrics->>'likes' IS NOT NULL 
                             THEN (cc.engagement_metrics->>'likes')::float ELSE 0 END) + 
                            (CASE WHEN cc.engagement_metrics->>'shares' IS NOT NULL 
                             THEN (cc.engagement_metrics->>'shares')::float ELSE 0 END) + 
                            (CASE WHEN cc.engagement_metrics->>'comments' IS NOT NULL 
                             THEN (cc.engagement_metrics->>'comments')::float ELSE 0 END)) AS avg_engagement
                    FROM
                        trend_content_map tcm
                    JOIN
                        crawled_content cc ON tcm.content_id = cc.id
                    WHERE
                        tcm.trend_id = %s
                    GROUP BY
                        DATE_TRUNC('day', cc.published_at)
                    ORDER BY
                        day
                )
                SELECT
                    day,
                    daily_count,
                    avg_engagement
                FROM
                    daily_stats
                ORDER BY
                    day DESC
                LIMIT 7
                """, (trend_id,))
                
                daily_stats = cur.fetchall()
                
                # Calculate engagement growth
                engagement_values = [row[2] for row in daily_stats if row[2] is not None]
                engagement_growth = 0
                if len(engagement_values) >= 2:
                    recent_avg = np.mean(engagement_values[:len(engagement_values)//2])
                    older_avg = np.mean(engagement_values[len(engagement_values)//2:])
                    engagement_growth = recent_avg / older_avg if older_avg > 0 else 1.0
                
                # Get network analysis - who is talking about it
                cur.execute("""
                WITH author_stats AS (
                    SELECT
                        cc.author_username,
                        COUNT(cc.id) AS content_count,
                        AVG((CASE WHEN cc.engagement_metrics->>'author_followers' IS NOT NULL 
                             THEN (cc.engagement_metrics->>'author_followers')::float ELSE 0 END)) AS avg_followers
                    FROM
                        trend_content_map tcm
                    JOIN
                        crawled_content cc ON tcm.content_id = cc.id
                    WHERE
                        tcm.trend_id = %s AND
                        cc.author_username IS NOT NULL
                    GROUP BY
                        cc.author_username
                    ORDER BY
                        avg_followers DESC
                    LIMIT 10
                )
                SELECT
                    SUM(content_count) AS total_content,
                    SUM(content_count * avg_followers) AS weighted_reach,
                    COUNT(DISTINCT author_username) AS unique_authors,
                    AVG(avg_followers) AS avg_author_reach
                FROM
                    author_stats
                """, (trend_id,))
                
                network_stats = cur.fetchone()
                
                # Combine factors to predict significance
                velocity = velocity_metrics.get('velocity', 0)
                
                # Calculate a weighted score for potential significance
                significance_score = (
                    velocity * 0.4 +                               # Semantic velocity
                    min(engagement_growth, 10) * 0.3 +             # Engagement growth (capped at 10x)
                    (network_stats[3] / 10000 if network_stats[3] else 0) * 0.3  # Author reach
                )
                
                # Classification based on score
                significance_level = "low"
                if significance_score > 2.0:
                    significance_level = "high"
                elif significance_score > 1.0:
                    significance_level = "medium"
                
                # Store prediction results
                prediction_data = {
                    "significance_score": float(significance_score),
                    "significance_level": significance_level,
                    "semantic_velocity": float(velocity),
                    "engagement_growth": float(engagement_growth),
                    "author_reach": float(network_stats[3]) if network_stats[3] else 0,
                    "unique_authors": int(network_stats[2]) if network_stats[2] else 0,
                    "predicted_at": datetime.now().isoformat()
                }
                
                cur.execute("""
                UPDATE trends
                SET metadata = jsonb_set(
                    COALESCE(metadata, '{}'::jsonb),
                    '{significance_prediction}',
                    %s::jsonb,
                    true
                )
                WHERE id = %s
                """, (json.dumps(prediction_data), trend_id))
                
                self.conn.commit()
                
                return {
                    "trend_id": trend_id,
                    "name": name,
                    "significance_score": significance_score,
                    "significance_level": significance_level,
                    "semantic_velocity": velocity,
                    "engagement_growth": engagement_growth,
                    "author_reach": network_stats[3] if network_stats[3] else 0,
                    "unique_authors": network_stats[2] if network_stats[2] else 0
                }
                
        except Exception as e:
            logger.error("Error predicting trend significance: %s", e)
            self.conn.rollback()
            return {"trend_id": trend_id, "significance_level": "error", "error": str(e)}

    def process_all_trends(self, max_trends: int = 50) -> Dict[str, Any]:
        """
        Process all active trends to update summaries and analytics.
        
        Args:
            max_trends: Maximum number of trends to process
            
        Returns:
            Dictionary with processing statistics
        """
        start_time = time.time()
        processed = 0
        summarized = 0
        velocity_analyzed = 0
        shifts_detected = 0
        
        try:
            with self.conn.cursor() as cur:
                # Get active trends
                cur.execute("""
                SELECT id, name, status, metadata
                FROM trends
                WHERE status != 'archived'
                ORDER BY last_updated_at
                LIMIT %s
                """, (max_trends,))
                
                trends = cur.fetchall()
                
                for trend_id, name, status, metadata in trends:
                    logger.info("Processing trend %d: %s", trend_id, name)
                    
                    # 1. Summarize trend
                    summary = self.summarize_trend(trend_id)
                    if summary:
                        summarized += 1
                    
                    # 2. Analyze semantic velocity
                    velocity = self.analyze_semantic_velocity(trend_id)
                    if velocity.get('status') == 'success':
                        velocity_analyzed += 1
                    
                    # 3. Detect semantic shifts for older trends
                    if metadata and 'first_seen_at' in metadata:
                        first_seen = datetime.fromisoformat(metadata['first_seen_at'])
                        days_old = (datetime.now() - first_seen).days
                        
                        if days_old >= 7:  # Only check older trends for shifts
                            shift = self.detect_semantic_shifts(trend_id)
                            if shift.get('shift_detected'):
                                shifts_detected += 1
                    
                    # --- Temporal Prediction GAN integration (stub) ---
                    if self.temporal_gan:
                        try:
                            # Example: update GAN with latest trend embedding
                            cur.execute("SELECT trend_embedding FROM trends WHERE id = %s", (trend_id,))
                            emb_row = cur.fetchone()
                            if emb_row:
                                _ = np.array(emb_row[0])  # Remove unused variable 'embedding'
                                self.temporal_gan.update(np.array(emb_row[0]))
                        except Exception as e:
                            logger.warning("GAN update failed for trend %d: %s", trend_id, e)
                    
                    # --- Meta-Learning feedback (stub) ---
                    if self.meta_learner:
                        try:
                            # Example: adapt meta-learner with dummy loss values
                            self.meta_learner.adapt(distillation_loss=0.1, gan_loss=0.1)
                        except Exception as e:
                            logger.warning("Meta-learner update failed: %s", e)
                    
                    processed += 1
                    
                # 4. Identify sleeper trends (once per batch)
                sleeper_trends = self.identify_sleeper_trends()
                
                processing_stats = {
                    "processed": processed,
                    "summarized": summarized,
                    "velocity_analyzed": velocity_analyzed,
                    "shifts_detected": shifts_detected,
                    "sleepers_identified": len(sleeper_trends),
                    "execution_time": time.time() - start_time
                }
                
                logger.info("Completed trend processing: %s", json.dumps(processing_stats))
                return processing_stats
                
        except Exception as e:
            logger.error("Error in batch trend processing: %s", e)
            return {
                "error": str(e),
                "processed": processed,
                "execution_time": time.time() - start_time
            }

    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()
            logger.info("Closed database connection")
