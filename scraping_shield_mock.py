#!/usr/bin/env python3
"""
Mock ScrapingShield for testing without heavy dependencies.
"""

import random
import time
import logging
from typing import Dict, Optional, Any
import asyncio

logger = logging.getLogger(__name__)


class MockScrapingShield:
    """Mock implementation of ScrapingShield for testing."""
    
    def __init__(self, config_path: str = "proxy_config.json"):
        """Initialize mock shield."""
        self.proxy_pool = []
        self.max_retries = 3
        self.retry_delay = 2
        self.exponential_backoff = True
        
        # Mock user agents
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        
        logger.info("MockScrapingShield initialized successfully")
    
    def rotate_user_agent(self) -> str:
        """Get a random user agent string."""
        return random.choice(self.user_agents)
    
    def get_enhanced_headers(self) -> Dict[str, str]:
        """Get enhanced browser headers."""
        return {
            'User-Agent': self.rotate_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    
    def detect_honeypot(self, response) -> bool:
        """Mock honeypot detection."""
        # Always return False for testing
        return False
    
    def get_session_with_proxy(self):
        """Mock session creation."""
        try:
            import requests
            session = requests.Session()
            session.headers.update(self.get_enhanced_headers())
            return session
        except ImportError:
            # Return a mock session if requests not available
            class MockSession:
                def __init__(self):
                    self.headers = {}
                def get(self, *args, **kwargs):
                    class MockResponse:
                        status_code = 200
                        text = "<html><body>Mock response</body></html>"
                    return MockResponse()
            return MockSession()
    
    async def make_request(self, url: str, method: str = 'get', retries: int = None, **kwargs) -> Optional[Any]:
        """Mock request method."""
        logger.info(f"Mock request to {url}")
        
        # Simulate processing time
        await asyncio.sleep(0.1)
        
        # Return a mock response
        class MockResponse:
            status_code = 200
            text = f"<html><body>Mock response for {url}</body></html>"
            
            def raise_for_status(self):
                pass
        
        return MockResponse()
    
    def simulate_human_behavior(self):
        """Mock human behavior simulation."""
        time.sleep(random.uniform(0.5, 1.5))
    
    def cleanup(self):
        """Mock cleanup."""
        logger.debug("MockScrapingShield cleanup completed")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


# For testing, alias the mock as the real class
ScrapingShield = MockScrapingShield

if __name__ == "__main__":
    # Test the mock
    shield = MockScrapingShield()
    print("✅ MockScrapingShield initialized")
    print("Headers:", shield.get_enhanced_headers())
    print("User-Agent:", shield.rotate_user_agent())
    print("✅ Mock test completed")
