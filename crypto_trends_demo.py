#!/usr/bin/env python3
"""
Cryptocurrency Trends Demo

This script demonstrates how to use the integrated cryptocurrency data sources
from both CoinMarketCap and Yahoo Finance in the trend-crawler project.
"""

import json
import logging
import sys
import time
from typing import Dict, Any, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the data source manager
from data_sources import data_source_manager


def print_section(title: str) -> None:
    """Print a section header."""
    print("\n" + "=" * 80)
    print(f"  {title}")
    print("=" * 80)


def pretty_print_json(data: Dict[str, Any]) -> None:
    """Print data in a pretty JSON format."""
    print(json.dumps(data, indent=2, default=str))


def save_results(data: Dict[str, Any], filename: str) -> None:
    """Save results to a JSON file."""
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        logger.info(f"Results saved to {filename}")
    except Exception as e:
        logger.error(f"Error saving results to {filename}: {e}")


def fetch_crypto_trends(topic: Optional[str] = None, save_to_file: bool = True) -> Dict[str, Any]:
    """
    Fetch cryptocurrency trends from all available sources.
    
    Args:
        topic: Optional topic to filter trends by
        save_to_file: Whether to save results to a file
    
    Returns:
        Dictionary with cryptocurrency trend data
    """
    # Get combined crypto data from all sources
    print_section(f"Fetching combined cryptocurrency data for: {topic or 'all trends'}")
    crypto_data = data_source_manager.get_source("crypto.combined")(topic)
    
    # Display results
    print(f"Available sources: {crypto_data.get('sources_available', [])}")
    
    # Display combined insights
    if "combined_insights" in crypto_data:
        print("\nCombined Insights:")
        print(f"- Market sentiment: {crypto_data['combined_insights']['market_sentiment']}")
        print(f"- Total market cap: {crypto_data['combined_insights']['total_market_cap']:,}")
        print(f"- Positive sentiment ratio: {crypto_data['combined_insights']['positive_sentiment_ratio']:.2f}%")
        
        # Show trending coins
        print("\nTrending coins:")
        for coin in crypto_data['combined_insights']['trending_coins'][:5]:  # Show top 5
            print(f"- {coin['name']} ({coin['symbol']}): {coin['percent_change_24h']}% change (Source: {coin['source']})")
    
    # Save to file if requested
    if save_to_file:
        save_results(crypto_data, f"crypto_trends_{topic or 'all'}.json")
    
    return crypto_data


def fetch_source_specific_data() -> None:
    """Fetch and display data from specific cryptocurrency sources."""
    # 1. Get only CoinMarketCap data
    print_section("Fetching CoinMarketCap data for: ethereum")
    cmc_data = data_source_manager.get_source("crypto.coinmarketcap")("ethereum")
    
    if cmc_data and not cmc_data.get("error"):
        print("CoinMarketCap Data:")
        # Show market sentiment if available
        if "market_sentiments" in cmc_data.get("coinmarketcap", {}):
            sentiment = cmc_data["coinmarketcap"]["market_sentiments"]["market_sentiment"]
            print(f"- Market sentiment: {sentiment}")
        
        # Show trending coins related to ethereum
        if "trending_coins" in cmc_data.get("coinmarketcap", {}):
            print("\nTrending Ethereum-related coins:")
            for coin in cmc_data["coinmarketcap"]["trending_coins"][:3]:  # Show top 3
                print(f"- {coin.get('name', 'Unknown')} ({coin.get('symbol', 'Unknown')})")
    else:
        print("CoinMarketCap data not available")
    
    # Add a short delay
    time.sleep(1)
    
    # 2. Get only Yahoo Finance data
    print_section("Fetching Yahoo Finance data for: dogecoin")
    yahoo_data = data_source_manager.get_source("crypto.yahoo_finance")("dogecoin")
    
    if yahoo_data and not yahoo_data.get("error"):
        print("Yahoo Finance Data:")
        # Show market overview if available
        if "market_overview" in yahoo_data.get("yahoo_finance", {}):
            overview = yahoo_data["yahoo_finance"]["market_overview"]
            print(f"- Market sentiment: {overview.get('market_sentiment', 'unknown')}")
            print(f"- Positive coins: {overview.get('positive_coins_percent', 0):.2f}%")
        
        # Show trending coins related to dogecoin
        if "trending_cryptos" in yahoo_data.get("yahoo_finance", {}):
            print("\nTrending Dogecoin-related cryptos:")
            for crypto in yahoo_data["yahoo_finance"]["trending_cryptos"][:3]:  # Show top 3
                print(f"- {crypto.get('Name', 'Unknown')} ({crypto.get('Symbol', 'Unknown')})")
    else:
        print("Yahoo Finance data not available")


def fetch_all_trend_data() -> None:
    """Fetch and display trend data from all available sources including cryptocurrency."""
    print_section("Fetching data from ALL trend sources for: bitcoin")
    
    # Collect from all available data sources
    all_trends = data_source_manager.collect_trend_data("bitcoin")
    
    # Display available sources
    print(f"Available data sources: {list(all_trends.keys())}")
    
    # Show cryptocurrency data if available
    if "crypto.combined" in all_trends:
        print("\nCryptocurrency Insights:")
        if "combined_insights" in all_trends["crypto.combined"]:
            insights = all_trends["crypto.combined"]["combined_insights"]
            print(f"- Market sentiment: {insights.get('market_sentiment', 'unknown')}")
            print(f"- Trending coins: {len(insights.get('trending_coins', []))}")
    
    # Show Google Trends data if available
    if "google_trends" in all_trends:
        print("\nGoogle Trends Insights:")
        print(f"- Data available: {bool(all_trends['google_trends'])}")
    
    # Show social media data if available
    for source in all_trends.keys():
        if source.startswith("social_media."):
            print(f"\n{source.split('.')[1].capitalize()} Insights:")
            print(f"- Data available: {bool(all_trends[source])}")
    
    # Save complete results
    save_results(all_trends, "all_trend_sources_bitcoin.json")


def main() -> None:
    """Main function to demonstrate cryptocurrency data integration."""
    print_section("CRYPTOCURRENCY DATA SOURCES DEMONSTRATION")
    print("This script demonstrates how to use the integrated cryptocurrency data sources")
    
    try:
        # Example 1: Fetch combined crypto data for a specific topic
        fetch_crypto_trends("bitcoin")
        
        # Example 2: Fetch data from specific sources
        fetch_source_specific_data()
        
        # Example 3: Fetch data from all trend sources including crypto
        fetch_all_trend_data()
        
        print_section("DEMONSTRATION COMPLETE")
        print("Check the generated JSON files for complete results.")
    
    except Exception as e:
        logger.error(f"Error in cryptocurrency trends demo: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()