#!/usr/bin/env python3
"""
Semantic Velocity Analyzer for trend-crawler

This module implements semantic velocity analysis for trend data,
providing real-time insights into trend evolution and prediction.
"""

import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union
from datetime import datetime, timedelta
import threading
import time
import json

# Import pgvector manager if available
try:
    from pg_vector_manager import PgVectorManager, RealTimeVectorProcessor
    PGVECTOR_AVAILABLE = True
except ImportError:
    PGVECTOR_AVAILABLE = False
    logging.warning("PgVector manager not available, using fallback methods")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SemanticVelocityAnalyzer:
    """
    Analyzes semantic velocity of trends using pgvector.
    
    This class implements real-time analysis of trend evolution,
    semantic drift detection, and predictive analytics.
    """
    
    def __init__(self, 
                 pg_vector_manager: Optional['PgVectorManager'] = None,
                 embedding_dim: int = 384,
                 device: Optional[str] = None):
        """
        Initialize the semantic velocity analyzer.
        
        Args:
            pg_vector_manager: PgVector manager instance
            embedding_dim: Dimension of embeddings
            device: Device for computation
        """
        self.embedding_dim = embedding_dim
        self.device = device if device else "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize pgvector manager
        self.pg_manager = pg_vector_manager
        
        # Initialize velocity cache
        self.velocity_cache = {}
        self.cache_lock = threading.Lock()
        
        # Initialize zero vector for calculations
        self.zero_vector = np.zeros(embedding_dim)
        
        logger.info(f"SemanticVelocityAnalyzer initialized with device: {self.device}")
    
    def calculate_velocity(self, 
                          trend_id: int,
                          time_window: timedelta = timedelta(hours=24)) -> Dict[str, Any]:
        """
        Calculate semantic velocity for a trend.
        
        Args:
            trend_id: ID of the trend
            time_window: Time window for calculation
            
        Returns:
            Dictionary with velocity metrics
        """
        if not self.pg_manager:
            logger.warning("PgVector manager not available")
            return {
                "trend_id": trend_id,
                "velocity_magnitude": 0.0,
                "coherence_score": 0.0,
                "prediction_score": 0.0,
                "status": "error",
                "error": "PgVector manager not available"
            }
        
        try:
            # Get connection from manager
            conn = self.pg_manager.get_connection()
            
            with conn.cursor() as cur:
                # Get trend embedding
                cur.execute(
                    "SELECT trend_embedding FROM trends WHERE id = %s",
                    (trend_id,)
                )
                
                row = cur.fetchone()
                if not row:
                    return {
                        "trend_id": trend_id,
                        "velocity_magnitude": 0.0,
                        "coherence_score": 0.0,
                        "prediction_score": 0.0,
                        "status": "error",
                        "error": "Trend not found"
                    }
                
                trend_embedding = np.array(row[0])
                
                # Get current time
                current_time = datetime.now()
                
                # Get current centroid
                cur.execute(
                    """
                    SELECT AVG(cc.embedding::float[])
                    FROM trend_content_map tcm
                    JOIN crawled_content cc ON tcm.content_id = cc.id
                    WHERE tcm.trend_id = %s
                    AND cc.published_at >= %s
                    """,
                    (trend_id, current_time - time_window)
                )
                
                current_centroid = cur.fetchone()[0]
                if not current_centroid:
                    return {
                        "trend_id": trend_id,
                        "velocity_magnitude": 0.0,
                        "coherence_score": 0.0,
                        "prediction_score": 0.0,
                        "status": "insufficient_data"
                    }
                
                current_centroid = np.array(current_centroid)
                
                # Get previous centroid
                cur.execute(
                    """
                    SELECT AVG(cc.embedding::float[])
                    FROM trend_content_map tcm
                    JOIN crawled_content cc ON tcm.content_id = cc.id
                    WHERE tcm.trend_id = %s
                    AND cc.published_at >= %s
                    AND cc.published_at < %s
                    """,
                    (
                        trend_id, 
                        current_time - time_window * 2,
                        current_time - time_window
                    )
                )
                
                previous_centroid = cur.fetchone()[0]
                if not previous_centroid:
                    # Use trend embedding as previous if no data
                    previous_centroid = trend_embedding
                else:
                    previous_centroid = np.array(previous_centroid)
                
                # Calculate velocity vector
                velocity_vector = current_centroid - previous_centroid
                
                # Calculate velocity magnitude
                velocity_magnitude = np.linalg.norm(velocity_vector)
                
                # Calculate trend coherence
                cur.execute(
                    """
                    WITH recent_content AS (
                        SELECT cc.embedding
                        FROM trend_content_map tcm
                        JOIN crawled_content cc ON tcm.content_id = cc.id
                        WHERE tcm.trend_id = %s
                        AND cc.published_at >= %s
                        LIMIT 100
                    )
                    SELECT AVG(1 - (a.embedding <=> b.embedding))
                    FROM recent_content a
                    CROSS JOIN recent_content b
                    WHERE a.embedding IS NOT NULL AND b.embedding IS NOT NULL
                    AND a.embedding <> b.embedding
                    """,
                    (trend_id, current_time - time_window)
                )
                
                coherence_score = cur.fetchone()[0] or 0.5
                
                # Get previous velocity from metadata
                cur.execute(
                    """
                    SELECT metadata->'semantic_velocity'->>'magnitude'
                    FROM trends
                    WHERE id = %s
                    """,
                    (trend_id,)
                )
                
                prev_velocity = cur.fetchone()[0]
                if prev_velocity:
                    acceleration = velocity_magnitude - float(prev_velocity)
                else:
                    acceleration = 0.0
                
                # Calculate prediction score
                # Combines velocity, direction alignment, and coherence
                direction_alignment = np.dot(
                    current_centroid / np.linalg.norm(current_centroid),
                    trend_embedding / np.linalg.norm(trend_embedding)
                )
                
                prediction_score = (
                    (velocity_magnitude * 0.4) + 
                    (direction_alignment * 0.4) + 
                    (coherence_score * 0.2)
                )
                
                # Update trend metadata
                cur.execute(
                    """
                    UPDATE trends
                    SET metadata = jsonb_set(
                        COALESCE(metadata, '{}'::jsonb),
                        '{semantic_velocity}',
                        %s::jsonb,
                        true
                    )
                    WHERE id = %s
                    """,
                    (
                        json.dumps({
                            "magnitude": velocity_magnitude,
                            "direction": velocity_vector.tolist(),
                            "coherence": coherence_score,
                            "acceleration": acceleration,
                            "prediction_score": prediction_score,
                            "calculated_at": current_time.isoformat(),
                            "previous_velocity_magnitude": velocity_magnitude
                        }),
                        trend_id
                    )
                )
                
                conn.commit()
                
                # Return velocity metrics
                return {
                    "trend_id": trend_id,
                    "velocity_magnitude": velocity_magnitude,
                    "velocity_direction": velocity_vector.tolist(),
                    "coherence_score": coherence_score,
                    "acceleration": acceleration,
                    "prediction_score": prediction_score,
                    "status": "success"
                }
                
        except Exception as e:
            logger.error(f"Error calculating velocity for trend {trend_id}: {e}")
            return {
                "trend_id": trend_id,
                "velocity_magnitude": 0.0,
                "coherence_score": 0.0,
                "prediction_score": 0.0,
                "status": "error",
                "error": str(e)
            }
        finally:
            if 'conn' in locals():
                self.pg_manager.release_connection(conn)
