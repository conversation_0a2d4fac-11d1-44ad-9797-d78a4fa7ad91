#!/usr/bin/env python3
"""
Meta Learning Controller for trend-crawler

This module implements meta-learning and hypernetwork optimization
for the nano neural network architecture.
"""

import os
import logging
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import OrderedDict, defaultdict
import copy
import math

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MetaLearningController:
    """
    Controller for meta-learning optimization.
    
    Implements Model-Agnostic Meta-Learning (MAML) for rapid adaptation
    to new tasks with minimal data, optimizing across different trends.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 inner_lr: float = 0.01,
                 meta_lr: float = 0.001,
                 first_order: bool = True,
                 epsilon: float = 1e-8):
        """
        Initialize the meta-learning controller.
        
        Args:
            model: Base model to adapt
            inner_lr: Learning rate for task-specific adaptation
            meta_lr: Learning rate for meta-update
            first_order: Whether to use first-order approximation
            epsilon: Small constant for numerical stability
        """
        self.model = model
        self.inner_lr = inner_lr
        self.meta_lr = meta_lr
        self.first_order = first_order
        self.epsilon = epsilon
        
        # Meta-optimizer operates on the model's parameters
        self.meta_optimizer = optim.Adam(self.model.parameters(), lr=meta_lr)
        
        # Keep track of task losses
        self.task_losses = []
        
        # Store initial parameter values
        self.store_initial_parameters()
        
    def store_initial_parameters(self):
        """Store the initial parameters of the model."""
        self.initial_params = OrderedDict()
        for name, param in self.model.named_parameters():
            self.initial_params[name] = param.clone().detach()
    
    def adapt_to_task(self, 
                      loss_fn: callable, 
                      task_data: Tuple,
                      num_inner_steps: int = 1):
        """
        Adapt the model to a specific task using inner loop optimization.
        
        Args:
            loss_fn: Loss function for the task
            task_data: Tuple of (inputs, targets) for this task
            num_inner_steps: Number of inner optimization steps
            
        Returns:
            Adapted model parameters
        """
        # Create a clone of the model parameters for this task
        task_params = OrderedDict()
        for name, param in self.model.named_parameters():
            task_params[name] = param.clone()
            
        # Extract task data
        inputs, targets = task_data
        
        # Perform inner loop updates
        for _ in range(num_inner_steps):
            # Forward pass with the current task parameters
            outputs = self.forward_with_params(inputs, task_params)
            
            # Compute loss
            loss = loss_fn(outputs, targets)
            
            # Compute gradients w.r.t task parameters
            grads = torch.autograd.grad(
                loss, 
                task_params.values(),
                create_graph=not self.first_order,  # Create graph if not first-order
                allow_unused=True
            )
            
            # Update task parameters
            for (name, param), grad in zip(task_params.items(), grads):
                if grad is not None:  # Some layers might not need gradients
                    task_params[name] = param - self.inner_lr * grad
        
        return task_params
    
    def forward_with_params(self, 
                           inputs: torch.Tensor, 
                           params: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Forward pass using the given parameters instead of the model's current parameters.
        
        Args:
            inputs: Input data
            params: Model parameters to use
            
        Returns:
            Model outputs
        """
        # Store original parameters
        orig_params = OrderedDict()
        for name, param in self.model.named_parameters():
            orig_params[name] = param.data.clone()
            
        # Override with provided parameters
        for name, param in self.model.named_parameters():
            if name in params:
                param.data = params[name].data
        
        # Forward pass
        outputs = self.model(inputs)
        
        # Restore original parameters
        for name, param in self.model.named_parameters():
            if name in orig_params:
                param.data = orig_params[name]
                
        return outputs
    
    def meta_update(self, 
                   meta_loss: torch.Tensor,
                   retain_graph: bool = False):
        """
        Perform meta-update using the meta-loss.
        
        Args:
            meta_loss: Meta-learning loss
            retain_graph: Whether to retain computation graph
        """
        # Zero gradients
        self.meta_optimizer.zero_grad()
        
        # Backward pass
        meta_loss.backward(retain_graph=retain_graph)
        
        # Update parameters
        self.meta_optimizer.step()
    
    def meta_train_step(self, 
                       tasks_data: List[Tuple],
                       loss_fn: callable,
                       num_inner_steps: int = 1):
        """
        Perform one step of meta-training across multiple tasks.
        
        Args:
            tasks_data: List of (inputs, targets) tuples for different tasks
            loss_fn: Loss function
            num_inner_steps: Number of inner adaptation steps
            
        Returns:
            Meta-loss
        """
        meta_loss = 0.0
        n_tasks = len(tasks_data)
        
        # Reset task losses
        self.task_losses = []
        
        # Reset meta-optimizer
        self.meta_optimizer.zero_grad()
        
        # Iterate through tasks
        for i, task_data in enumerate(tasks_data):
            # Adapt to this task (inner loop)
            adapted_params = self.adapt_to_task(loss_fn, task_data, num_inner_steps)
            
            # Get validation data for this task
            # For simplicity, using the same data, but in practice would use a different split
            val_inputs, val_targets = task_data
            
            # Forward pass with adapted parameters
            val_outputs = self.forward_with_params(val_inputs, adapted_params)
            
            # Compute task-specific loss
            task_loss = loss_fn(val_outputs, val_targets)
            self.task_losses.append(task_loss.item())
            
            # Accumulate meta-loss
            meta_loss += task_loss
        
        # Average meta-loss
        meta_loss = meta_loss / n_tasks
        
        # Meta-update
        self.meta_update(meta_loss)
        
        return meta_loss.item()
    
    def reset_to_initial_parameters(self):
        """Reset model to initial parameters."""
        for name, param in self.model.named_parameters():
            if name in self.initial_params:
                param.data = self.initial_params[name].data.clone()


class HypernetworkOptimizer:
    """
    Hypernetwork optimizer for dynamic parameter generation.
    
    Generates weights for a target network based on conditional inputs,
    enabling rapid adaptation to new trends.
    """
    
    def __init__(self, 
                 target_model: nn.Module,
                 conditioning_dim: int = 32,
                 hidden_dim: int = 64,
                 device: torch.device = None):
        """
        Initialize the hypernetwork optimizer.
        
        Args:
            target_model: Target model to generate weights for
            conditioning_dim: Dimension of conditioning input
            hidden_dim: Hidden layer dimension
            device: Device to use
        """
        self.target_model = target_model
        self.conditioning_dim = conditioning_dim
        self.hidden_dim = hidden_dim
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Build parameter mapping
        self.param_shapes = OrderedDict()
        self.param_sizes = OrderedDict()
        total_params = 0
        
        for name, param in self.target_model.named_parameters():
            self.param_shapes[name] = param.shape
            size = param.numel()
            self.param_sizes[name] = size
            total_params += size
        
        logger.info(f"Target model has {total_params} parameters")
        
        # Build hypernetwork
        self.hypernet = self._build_hypernet(total_params)
        self.hypernet.to(self.device)
        
        # Optimizer
        self.optimizer = optim.Adam(self.hypernet.parameters(), lr=0.001)
        
        # Track parameters
        self.original_params = OrderedDict()
        for name, param in self.target_model.named_parameters():
            self.original_params[name] = param.clone().detach()
    
    def _build_hypernet(self, total_params: int) -> nn.Module:
        """
        Build the hypernetwork architecture.
        
        Args:
            total_params: Total number of parameters to generate
            
        Returns:
            Hypernetwork model
        """
        class Hypernetwork(nn.Module):
            def __init__(self, conditioning_dim, hidden_dim, output_dim):
                super().__init__()
                self.fc1 = nn.Linear(conditioning_dim, hidden_dim)
                self.fc2 = nn.Linear(hidden_dim, hidden_dim * 2)
                self.fc3 = nn.Linear(hidden_dim * 2, hidden_dim * 2)
                self.fc_out = nn.Linear(hidden_dim * 2, output_dim)
                
                # Dropout for regularization
                self.dropout = nn.Dropout(0.2)
                
                # Layer normalization for stability
                self.layer_norm1 = nn.LayerNorm(hidden_dim)
                self.layer_norm2 = nn.LayerNorm(hidden_dim * 2)
                self.layer_norm3 = nn.LayerNorm(hidden_dim * 2)
            
            def forward(self, x):
                x = self.fc1(x)
                x = self.layer_norm1(x)
                x = F.relu(x)
                
                x = self.fc2(x)
                x = self.layer_norm2(x)
                x = F.relu(x)
                x = self.dropout(x)
                
                x = self.fc3(x)
                x = self.layer_norm3(x)
                x = F.relu(x)
                x = self.dropout(x)
                
                x = self.fc_out(x)
                return x
        
        return Hypernetwork(self.conditioning_dim, self.hidden_dim, total_params)
    
    def generate_parameters(self, 
                           condition: torch.Tensor) -> OrderedDict:
        """
        Generate parameters for the target model based on a condition.
        
        Args:
            condition: Conditioning tensor
            
        Returns:
            Dictionary of generated parameters
        """
        if condition.dim() == 1:
            condition = condition.unsqueeze(0)
            
        condition = condition.to(self.device)
        
        # Generate flat parameter vector
        flat_params = self.hypernet(condition).squeeze(0)
        
        # Convert flat parameters back to shapes
        params_dict = OrderedDict()
        start_idx = 0
        
        for name, shape in self.param_shapes.items():
            size = self.param_sizes[name]
            param_flat = flat_params[start_idx:start_idx + size]
            param_reshaped = param_flat.reshape(shape)
            params_dict[name] = param_reshaped
            start_idx += size
            
        return params_dict
    
    def apply_parameters(self, params_dict: OrderedDict):
        """
        Apply generated parameters to the target model.
        
        Args:
            params_dict: Dictionary of parameters
        """
        with torch.no_grad():
            for name, param in self.target_model.named_parameters():
                if name in params_dict:
                    param.copy_(params_dict[name])
    
    def reset_parameters(self):
        """Reset target model to original parameters."""
        with torch.no_grad():
            for name, param in self.target_model.named_parameters():
                if name in self.original_params:
                    param.copy_(self.original_params[name])
    
    def train_step(self,
                  condition: torch.Tensor,
                  inputs: torch.Tensor,
                  targets: torch.Tensor,
                  loss_fn: callable) -> float:
        """
        Train the hypernetwork on a batch.
        
        Args:
            condition: Conditioning input
            inputs: Model inputs
            targets: Target outputs
            loss_fn: Loss function
            
        Returns:
            Loss value
        """
        # Reset optimizer
        self.optimizer.zero_grad()
        
        # Generate and apply parameters
        params_dict = self.generate_parameters(condition)
        
        # Store original parameters
        with torch.no_grad():
            orig_params = OrderedDict()
            for name, param in self.target_model.named_parameters():
                orig_params[name] = param.clone()
                if name in params_dict:
                    param.copy_(params_dict[name])
        
        # Forward pass with generated parameters
        outputs = self.target_model(inputs)
        
        # Calculate loss
        loss = loss_fn(outputs, targets)
        
        # Backward pass
        loss.backward()
        
        # Optimize hypernetwork
        self.optimizer.step()
        
        # Restore original parameters
        with torch.no_grad():
            for name, param in self.target_model.named_parameters():
                if name in orig_params:
                    param.copy_(orig_params[name])
        
        return loss.item()

    def forward_with_condition(self,
                              condition: torch.Tensor,
                              inputs: torch.Tensor) -> torch.Tensor:
        """
        Perform a forward pass with a specific condition.
        
        Args:
            condition: Conditioning tensor
            inputs: Input data
            
        Returns:
            Model outputs
        """
        # Generate parameters
        params_dict = self.generate_parameters(condition)
        
        # Store original parameters
        with torch.no_grad():
            orig_params = OrderedDict()
            for name, param in self.target_model.named_parameters():
                orig_params[name] = param.clone()
                if name in params_dict:
                    param.copy_(params_dict[name])
        
        # Forward pass
        outputs = self.target_model(inputs)
        
        # Restore original parameters
        with torch.no_grad():
            for name, param in self.target_model.named_parameters():
                if name in orig_params:
                    param.copy_(orig_params[name])
                    
        return outputs
    
    def save(self, path: str):
        """
        Save the hypernetwork model.
        
        Args:
            path: Path to save to
        """
        torch.save(self.hypernet.state_dict(), path)
        
    def load(self, path: str):
        """
        Load the hypernetwork model.
        
        Args:
            path: Path to load from
        """
        self.hypernet.load_state_dict(torch.load(path, map_location=self.device))


class DPGradientComputation:
    """
    Differentially private gradient computation.
    
    Implements DP-SGD (Differentially Private Stochastic Gradient Descent) for
    privacy-preserving training of neural networks.
    """
    
    def __init__(self,
                 model: nn.Module,
                 noise_multiplier: float = 1.0,
                 max_grad_norm: float = 1.0,
                 secure_rng: bool = True):
        """
        Initialize DP gradient computation.
        
        Args:
            model: The model being trained
            noise_multiplier: Noise multiplier for privacy
            max_grad_norm: Maximum gradient norm for clipping
            secure_rng: Whether to use secure random number generator
        """
        self.model = model
        self.noise_multiplier = noise_multiplier
        self.max_grad_norm = max_grad_norm
        self.secure_rng = secure_rng
        
        # Use secure random number generator if requested
        if secure_rng:
            try:
                import torchcsprng as csprng
                self.rng = csprng.create_mt19937_generator()
                self.secure_rng_available = True
                logger.info("Using secure RNG for differential privacy")
            except ImportError:
                self.secure_rng_available = False
                logger.warning("Secure RNG not available, falling back to torch.randn")
                
    def _compute_grad_sample(self, 
                            loss: torch.Tensor, 
                            params: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Compute per-sample gradients.
        
        Args:
            loss: Loss tensor with dimension (batch_size,)
            params: List of model parameters
            
        Returns:
            List of per-sample gradients
        """
        # Create a list of empty tensors for gradients
        grads = [torch.zeros_like(p) for p in params]
        
        # Compute gradient for each sample
        for i in range(loss.shape[0]):
            # Zero out existing gradients
            for p in params:
                if p.grad is not None:
                    p.grad.zero_()
            
            # Backward on individual sample
            loss[i].backward(retain_graph=True)
            
            # Collect gradients
            for j, p in enumerate(params):
                if p.grad is not None:
                    grads[j] += p.grad
                    
        return grads
                
    def clip_and_accumulate(self, 
                           loss: torch.Tensor,
                           params: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Clip per-sample gradients and accumulate.
        
        Args:
            loss: Loss tensor with dimension (batch_size,)
            params: List of model parameters
            
        Returns:
            List of clipped and accumulated gradients
        """
        # Compute per-sample gradients
        grad_samples = self._compute_grad_sample(loss, params)
        
        # Compute total gradient norm for each sample
        sample_norms = []
        for i in range(loss.shape[0]):
            norm = 0
            for g in grad_samples:
                if g is not None:
                    norm += torch.sum(g[i] ** 2)
            norm = torch.sqrt(norm)
            sample_norms.append(norm)
            
        # Clip gradients for each sample
        for i in range(loss.shape[0]):
            for g in grad_samples:
                if g is not None:
                    g[i] *= min(1.0, self.max_grad_norm / (sample_norms[i] + 1e-6))
                    
        # Accumulate gradients
        clipped_grads = [torch.mean(g, dim=0) if g is not None else None for g in grad_samples]
        
        return clipped_grads
    
    def add_noise_to_gradients(self, 
                              grads: List[torch.Tensor], 
                              batch_size: int) -> List[torch.Tensor]:
        """
        Add Gaussian noise to gradients for differential privacy.
        
        Args:
            grads: List of gradient tensors
            batch_size: Batch size used for gradient computation
            
        Returns:
            List of noisy gradient tensors
        """
        noisy_grads = []
        
        for g in grads:
            if g is None:
                noisy_grads.append(None)
                continue
                
            # Compute noise standard deviation
            noise_std = self.noise_multiplier * self.max_grad_norm / batch_size
            
            # Generate noise
            if self.secure_rng_available:
                # Use cryptographically secure RNG
                noise = torch.empty_like(g)
                noise = noise.normal_(generator=self.rng, mean=0, std=noise_std)
            else:
                # Fall back to standard PyTorch RNG
                noise = torch.normal(0, noise_std, size=g.shape, device=g.device)
                
            # Add noise to gradients
            noisy_g = g + noise
            noisy_grads.append(noisy_g)
            
        return noisy_grads
    
    def apply_gradients(self, 
                       params: List[torch.Tensor],
                       grads: List[torch.Tensor]):
        """
        Apply computed gradients to model parameters.
        
        Args:
            params: List of model parameters
            grads: List of gradient tensors
        """
        with torch.no_grad():
            for p, g in zip(params, grads):
                if g is not None:
                    p.grad = g
                    
    def dp_step(self, 
               loss: torch.Tensor,
               optimizer: torch.optim.Optimizer):
        """
        Perform one step of differentially private optimization.
        
        Args:
            loss: Loss tensor with dimension (batch_size,)
            optimizer: PyTorch optimizer
        """
        # Get model parameters
        params = [p for p in self.model.parameters() if p.requires_grad]
        
        # Zero out existing gradients
        optimizer.zero_grad()
        
        # Clip gradients
        batch_size = loss.shape[0]
        clipped_grads = self.clip_and_accumulate(loss, params)
        
        # Add noise for privacy
        noisy_grads = self.add_noise_to_gradients(clipped_grads, batch_size)
        
        # Apply gradients to parameters
        self.apply_gradients(params, noisy_grads)
        
        # Optimizer step
        optimizer.step()

if __name__ == "__main__":
    # Simple test of the meta-learning controller
    import torch.nn as nn
    
    # Create a simple model
    model = nn.Sequential(
        nn.Linear(10, 20),
        nn.ReLU(),
        nn.Linear(20, 1)
    )
    
    # Initialize meta-learning controller
    meta_controller = MetaLearningController(model)
    
    # Print model parameters
    for name, param in model.named_parameters():
        print(f"{name}: {param.shape}")
        
    print("Meta-learning controller initialized successfully")
