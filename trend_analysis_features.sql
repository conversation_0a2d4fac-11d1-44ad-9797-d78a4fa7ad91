-- trend_analysis_features.sql
-- Innovative trend analysis features for pgvector schema

-- 1. Trend Summarization with LLMs
-- Function to generate the summarization prompt for a trend
CREATE OR REPLACE FUNCTION generate_trend_summary_prompt(trend_id INTEGER)
RETURNS TEXT AS $$
DECLARE
    trend_data RECORD;
    content_sample TEXT;
    content_ids TEXT;
    prompt TEXT;
BEGIN
    -- Get trend info
    SELECT t.id, t.name, t.description, t.first_seen_at, t.last_updated_at, t.status, t.metadata
    INTO trend_data 
    FROM trends t 
    WHERE t.id = trend_id;

    -- Get a representative sample of content IDs
    SELECT string_agg(content_id::text, ',')
    INTO content_ids
    FROM (
        SELECT tcm.content_id
        FROM trend_content_map tcm
        WHERE tcm.trend_id = trend_id
        ORDER BY tcm.similarity_to_trend DESC
        LIMIT 5
    ) top_content;

    -- Construct the prompt
    prompt := 'Summarize the following trend based on top related content: ' || 
              COALESCE(trend_data.name, 'Unnamed trend') || E'\n\n';
    
    prompt := prompt || 'Key information:\n';
    prompt := prompt || '- First seen: ' || trend_data.first_seen_at || E'\n';
    prompt := prompt || '- Last updated: ' || trend_data.last_updated_at || E'\n';
    prompt := prompt || '- Status: ' || trend_data.status || E'\n\n';
    
    prompt := prompt || 'Content samples:\n';

    -- Add content samples
    FOR content_sample IN 
        SELECT cc.content_text 
        FROM crawled_content cc
        JOIN unnest(string_to_array(content_ids, ','))::bigint AS ids(id) ON cc.id = ids.id
        LIMIT 5
    LOOP
        prompt := prompt || '- ' || LEFT(content_sample, 200) || E'...\n';
    END LOOP;

    prompt := prompt || E'\nProvide a concise summary of this trend, including its key themes, potential significance, and how it has evolved.';

    RETURN prompt;
END;
$$ LANGUAGE plpgsql;

-- Function to update a trend with a LLM-generated summary
-- Note: The actual LLM call will be handled by Python
CREATE OR REPLACE FUNCTION update_trend_with_summary(trend_id INTEGER, summary TEXT)
RETURNS VOID AS $$
BEGIN
    UPDATE trends
    SET description = summary,
        metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{summary_generated_at}',
            to_jsonb(now())
        )
    WHERE id = trend_id;
END;
$$ LANGUAGE plpgsql;

-- 2. Predictive Trend Analysis using Semantic Velocity
-- Calculate the semantic velocity of a trend
CREATE OR REPLACE FUNCTION calculate_semantic_velocity(trend_id INTEGER, time_window INTERVAL DEFAULT '24 hours')
RETURNS TABLE(
    trend_id INTEGER,
    velocity_magnitude FLOAT,
    velocity_direction FLOAT[],
    coherence_score FLOAT,
    acceleration FLOAT,
    prediction_score FLOAT
) AS $$
DECLARE
    trend_embedding VECTOR;
    current_centroid VECTOR;
    previous_centroid VECTOR;
    current_time TIMESTAMPTZ := now();
    previous_time TIMESTAMPTZ;
    velocity_vec VECTOR;
    velocity_mag FLOAT;
    trend_coherence FLOAT;
    accel FLOAT;
    prediction FLOAT;
BEGIN
    -- Get the trend's embedding
    SELECT t.trend_embedding INTO trend_embedding 
    FROM trends t WHERE t.id = trend_id;
    
    -- Calculate current centroid from recent content (within time window)
    SELECT avg(cc.embedding::float[])::vector INTO current_centroid
    FROM trend_content_map tcm
    JOIN crawled_content cc ON tcm.content_id = cc.id
    WHERE tcm.trend_id = trend_id
    AND cc.published_at >= (current_time - time_window);
    
    -- Calculate previous centroid from older content (previous time window)
    previous_time := current_time - time_window;
    SELECT avg(cc.embedding::float[])::vector INTO previous_centroid
    FROM trend_content_map tcm
    JOIN crawled_content cc ON tcm.content_id = cc.id
    WHERE tcm.trend_id = trend_id
    AND cc.published_at >= (previous_time - time_window)
    AND cc.published_at < previous_time;
    
    -- If we have both centroids, calculate velocity
    IF current_centroid IS NOT NULL AND previous_centroid IS NOT NULL THEN
        -- Velocity vector (difference between current and previous centroids)
        velocity_vec := current_centroid - previous_centroid;
        
        -- Velocity magnitude (L2 norm of velocity vector)
        velocity_mag := sqrt(velocity_vec <-> zero_vector(384));
        
        -- Trend coherence (cosine similarity of content within current window)
        SELECT avg(1 - (cc1.embedding <=> cc2.embedding)) INTO trend_coherence
        FROM trend_content_map tcm1
        JOIN crawled_content cc1 ON tcm1.content_id = cc1.id
        JOIN trend_content_map tcm2 ON tcm2.trend_id = trend_id AND tcm1.content_id != tcm2.content_id
        JOIN crawled_content cc2 ON tcm2.content_id = cc2.id
        WHERE tcm1.trend_id = trend_id
        AND cc1.published_at >= (current_time - time_window)
        AND cc2.published_at >= (current_time - time_window)
        LIMIT 100; -- Limit for performance
        
        -- Acceleration (get previous velocity from metadata if available)
        SELECT COALESCE(
            (metadata->>'previous_velocity_magnitude')::float, 
            0
        ) INTO accel
        FROM trends
        WHERE id = trend_id;
        
        accel := velocity_mag - accel;
        
        -- Prediction score combines velocity, direction alignment, and coherence
        -- Direction alignment: dot product between trend and velocity
        prediction := (
            (velocity_mag * 0.4) + 
            ((current_centroid <#> trend_embedding) * 0.4) + 
            (COALESCE(trend_coherence, 0.5) * 0.2)
        );
        
        -- Store velocity data in trend metadata
        UPDATE trends
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{semantic_velocity}',
            jsonb_build_object(
                'magnitude', velocity_mag,
                'direction', (velocity_vec::float[]),
                'coherence', trend_coherence,
                'acceleration', accel,
                'prediction_score', prediction,
                'calculated_at', current_time,
                'previous_velocity_magnitude', velocity_mag
            )
        )
        WHERE id = trend_id;
        
        -- Return results
        trend_id := trend_id;
        velocity_magnitude := velocity_mag;
        velocity_direction := velocity_vec::float[];
        coherence_score := COALESCE(trend_coherence, 0.5);
        acceleration := accel;
        prediction_score := prediction;
        RETURN NEXT;
    ELSE
        -- Not enough data for velocity calculation
        trend_id := trend_id;
        velocity_magnitude := 0;
        velocity_direction := trend_embedding::float[];
        coherence_score := 0.5;
        acceleration := 0;
        prediction_score := 0;
        RETURN NEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 3. Identifying "Sleeper" Trends
-- Function to identify potential sleeper trends
CREATE OR REPLACE FUNCTION identify_sleeper_trends(
    min_age INTERVAL DEFAULT '7 days',
    max_age INTERVAL DEFAULT '90 days',
    min_coherence FLOAT DEFAULT 0.7,
    min_content_count INTEGER DEFAULT 5,
    max_content_count INTEGER DEFAULT 50
)
RETURNS TABLE(
    trend_id INTEGER,
    trend_name TEXT,
    first_seen_at TIMESTAMPTZ,
    content_count BIGINT,
    coherence_score FLOAT,
    recent_growth_rate FLOAT,
    sleeper_score FLOAT
) AS $$
DECLARE
    current_time TIMESTAMPTZ := now();
    trend_record RECORD;
    trend_content_count BIGINT;
    trend_coherence FLOAT;
    recent_content_count BIGINT;
    older_content_count BIGINT;
    growth_rate FLOAT;
    sleeper FLOAT;
BEGIN
    -- Find trends within the age range
    FOR trend_record IN
        SELECT t.id, t.name, t.first_seen_at, t.trend_embedding
        FROM trends t
        WHERE t.first_seen_at <= (current_time - min_age)
        AND t.first_seen_at >= (current_time - max_age)
        AND t.status != 'archived'
    LOOP
        -- Count content for this trend
        SELECT COUNT(*) INTO trend_content_count
        FROM trend_content_map
        WHERE trend_id = trend_record.id;
        
        -- Skip if outside content count range
        IF trend_content_count < min_content_count OR trend_content_count > max_content_count THEN
            CONTINUE;
        END IF;
        
        -- Calculate trend coherence
        SELECT avg(1 - (cc1.embedding <=> cc2.embedding)) INTO trend_coherence
        FROM trend_content_map tcm1
        JOIN crawled_content cc1 ON tcm1.content_id = cc1.id
        JOIN trend_content_map tcm2 ON tcm2.trend_id = trend_record.id AND tcm1.content_id != tcm2.content_id
        JOIN crawled_content cc2 ON tcm2.content_id = cc2.id
        WHERE tcm1.trend_id = trend_record.id
        LIMIT 100; -- Limit for performance
        
        -- Skip if coherence is too low
        IF trend_coherence < min_coherence THEN
            CONTINUE;
        END IF;
        
        -- Calculate growth rate: recent content vs older content
        SELECT COUNT(*) INTO recent_content_count
        FROM trend_content_map tcm
        JOIN crawled_content cc ON tcm.content_id = cc.id
        WHERE tcm.trend_id = trend_record.id
        AND cc.published_at >= (current_time - INTERVAL '7 days');
        
        SELECT COUNT(*) INTO older_content_count
        FROM trend_content_map tcm
        JOIN crawled_content cc ON tcm.content_id = cc.id
        WHERE tcm.trend_id = trend_record.id
        AND cc.published_at < (current_time - INTERVAL '7 days')
        AND cc.published_at >= (current_time - INTERVAL '30 days');
        
        -- Calculate growth rate
        IF older_content_count > 0 THEN
            growth_rate := (recent_content_count::float / (older_content_count::float / 3.0));
        ELSE
            growth_rate := recent_content_count;
        END IF;
        
        -- Calculate sleeper score - combines:
        -- 1. Age (older = higher score)
        -- 2. Trend coherence (more coherent = higher score)
        -- 3. Growth rate (moderate growth = highest score, follows a bell curve)
        -- 4. Content count (moderate = highest score)
        sleeper := (
            -- Age factor (0-1): older trends score higher
            (EXTRACT(EPOCH FROM (current_time - trend_record.first_seen_at)) / 
             EXTRACT(EPOCH FROM max_age))::float * 0.3 +
            
            -- Coherence factor (0-1)
            COALESCE(trend_coherence, 0.5) * 0.3 +
            
            -- Growth rate factor (0-1): bell curve centered at growth_rate=1.2
            (1.0 - ABS(growth_rate - 1.2) / 1.2) * 0.25 +
            
            -- Content count factor (0-1): bell curve centered at the middle of min/max range
            (1.0 - ABS((trend_content_count - min_content_count) / 
                  (max_content_count - min_content_count) - 0.5) * 2.0) * 0.15
        );
        
        -- Update trend metadata
        UPDATE trends
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{sleeper_analysis}',
            jsonb_build_object(
                'content_count', trend_content_count,
                'coherence_score', trend_coherence,
                'recent_growth_rate', growth_rate,
                'sleeper_score', sleeper,
                'analyzed_at', current_time
            )
        )
        WHERE id = trend_record.id;
        
        -- Return result
        trend_id := trend_record.id;
        trend_name := trend_record.name;
        first_seen_at := trend_record.first_seen_at;
        content_count := trend_content_count;
        coherence_score := COALESCE(trend_coherence, 0.5);
        recent_growth_rate := growth_rate;
        sleeper_score := sleeper;
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 4. Detecting Semantic Shifts in Ongoing Trends
-- Function to detect semantic shifts in existing trends
CREATE OR REPLACE FUNCTION detect_semantic_shifts(
    trend_id INTEGER,
    time_window INTERVAL DEFAULT '7 days',
    shift_threshold FLOAT DEFAULT 0.15
)
RETURNS TABLE(
    trend_id INTEGER,
    shift_detected BOOLEAN,
    shift_magnitude FLOAT,
    shift_direction FLOAT[],
    dominant_shift_keywords TEXT[],
    shift_clusters JSONB
) AS $$
DECLARE
    trend_embedding VECTOR;
    trend_record RECORD;
    recent_centroid VECTOR;
    original_centroid VECTOR;
    shift_vector VECTOR;
    shift_mag FLOAT;
    shift_dir FLOAT[];
    current_time TIMESTAMPTZ := now();
    detected BOOLEAN := FALSE;
    recent_content_ids BIGINT[];
    original_content_ids BIGINT[];
    recent_keywords TEXT[];
    old_keywords TEXT[];
    unique_keywords TEXT[];
BEGIN
    -- Get trend data
    SELECT t.trend_embedding, t.id, t.first_seen_at
    INTO trend_record
    FROM trends t WHERE t.id = trend_id;
    
    trend_embedding := trend_record.trend_embedding;
    
    -- Get original content centroid (from first 30% of content by time)
    WITH content_timespan AS (
        SELECT 
            min(cc.published_at) as min_time,
            max(cc.published_at) as max_time
        FROM trend_content_map tcm
        JOIN crawled_content cc ON tcm.content_id = cc.id
        WHERE tcm.trend_id = trend_id
    )
    SELECT array_agg(tcm.content_id) INTO original_content_ids
    FROM trend_content_map tcm
    JOIN crawled_content cc ON tcm.content_id = cc.id
    JOIN content_timespan ct ON 1=1
    WHERE tcm.trend_id = trend_id
    AND cc.published_at <= (ct.min_time + (ct.max_time - ct.min_time) * 0.3);
    
    -- Get recent content ids (within time window)
    SELECT array_agg(tcm.content_id) INTO recent_content_ids
    FROM trend_content_map tcm
    JOIN crawled_content cc ON tcm.content_id = cc.id
    WHERE tcm.trend_id = trend_id
    AND cc.published_at >= (current_time - time_window);
    
    -- Calculate centroids
    SELECT avg(cc.embedding::float[])::vector INTO original_centroid
    FROM crawled_content cc
    WHERE cc.id = ANY(original_content_ids);
    
    SELECT avg(cc.embedding::float[])::vector INTO recent_centroid
    FROM crawled_content cc
    WHERE cc.id = ANY(recent_content_ids);
    
    -- Check if we have enough data
    IF original_centroid IS NULL OR recent_centroid IS NULL THEN
        -- Not enough data
        trend_id := trend_record.id;
        shift_detected := FALSE;
        shift_magnitude := 0;
        shift_direction := trend_embedding::float[];
        dominant_shift_keywords := ARRAY[]::TEXT[];
        shift_clusters := '{}'::jsonb;
        RETURN NEXT;
        RETURN;
    END IF;
    
    -- Calculate shift vector and magnitude
    shift_vector := recent_centroid - original_centroid;
    shift_mag := sqrt(shift_vector <-> zero_vector(384));
    shift_dir := shift_vector::float[];
    
    -- Extract keywords from both content sets
    -- (In real implementation, you'd have a more sophisticated keyword extraction)
    WITH keywords AS (
        SELECT unnest(cc.keywords) AS keyword
        FROM crawled_content cc
        WHERE cc.id = ANY(original_content_ids)
        GROUP BY keyword
    )
    SELECT array_agg(keyword) INTO old_keywords FROM keywords;
    
    WITH keywords AS (
        SELECT unnest(cc.keywords) AS keyword
        FROM crawled_content cc
        WHERE cc.id = ANY(recent_content_ids)
        GROUP BY keyword
    )
    SELECT array_agg(keyword) INTO recent_keywords FROM keywords;
    
    -- Find unique keywords in recent content not in original
    SELECT array_agg(kw) INTO unique_keywords
    FROM (
        SELECT unnest(recent_keywords) AS kw
        EXCEPT
        SELECT unnest(old_keywords) AS kw
    ) unique_kws;
    
    -- Determine if shift detected
    detected := (shift_mag > shift_threshold);
    
    -- Update trend metadata
    UPDATE trends
    SET metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb),
        '{semantic_shift}',
        jsonb_build_object(
            'detected', detected,
            'magnitude', shift_mag,
            'direction', shift_dir,
            'original_content_count', array_length(original_content_ids, 1),
            'recent_content_count', array_length(recent_content_ids, 1),
            'unique_keywords', unique_keywords,
            'analyzed_at', current_time
        )
    )
    WHERE id = trend_record.id;
    
    -- Return result
    trend_id := trend_record.id;
    shift_detected := detected;
    shift_magnitude := shift_mag;
    shift_direction := shift_dir;
    dominant_shift_keywords := unique_keywords;
    shift_clusters := jsonb_build_object(
        'original_count', array_length(original_content_ids, 1),
        'recent_count', array_length(recent_content_ids, 1),
        'cosine_similarity', 1 - (original_centroid <=> recent_centroid)
    );
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Helper function for zero vectors
CREATE OR REPLACE FUNCTION zero_vector(dimensions INTEGER)
RETURNS VECTOR AS $$
DECLARE
    zeros float[];
BEGIN
    SELECT array_fill(0::float, ARRAY[dimensions]) INTO zeros;
    RETURN zeros::vector;
END;
$$ LANGUAGE plpgsql;
