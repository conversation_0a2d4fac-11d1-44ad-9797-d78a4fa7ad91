#!/usr/bin/env python3
"""
TwitterScraperService - A robust Twitter/X scraper using twitter_scraper_selenium.
"""

import os
import time
import random
import logging
import json
import concurrent.futures
from typing import Dict, List, Optional, Any
from monitoring import monitor, monitor_scraper  # Add monitoring import

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import pandas as pd
    from twitter_scraper_selenium import scrape_keyword
    from selenium.common.exceptions import WebDriverException
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.chrome.options import Options
    from dotenv import load_dotenv

    # Load environment variables
    load_dotenv()

    TWITTER_SCRAPER_AVAILABLE = True
except (ImportError, AttributeError) as e:
    # Handle both import errors and attribute errors (like the SSLv2_METHOD issue)
    logger.error(f"Error loading Twitter scraper dependencies: {e}")
    TWITTER_SCRAPER_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TwitterScraperService:
    """
    Service for scraping Twitter/X data using twitter_scraper_selenium.
    """

    def __init__(self, db=None, max_workers=3):
        """
        Initialize the Twitter scraper service.

        Args:
            db: Database connection
            max_workers: Maximum number of concurrent scraping workers
        """
        if not TWITTER_SCRAPER_AVAILABLE:
            logger.warning("Twitter scraper dependencies not available. Install with: pip install -r requirements-twitter.txt")
            self.available = False
            return

        self.db = db
        self.max_workers = max_workers
        self.available = True

        try:
            # Install ChromeDriver
            self.driver_path = ChromeDriverManager().install()

            # Configure Chrome options
            self.chrome_options = Options()
            self.chrome_options.add_argument("--headless")
            self.chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            self.chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            self.chrome_options.add_argument("--no-sandbox")
            self.chrome_options.add_argument("--disable-dev-shm-usage")
            self.chrome_options.add_argument(f"user-agent={self._random_user_agent()}")

            # Configure scraper options
            proxy_server = os.getenv("PROXY_SERVER")
            if not proxy_server:
                # Try to get proxy from proxy_config.json
                try:
                    with open('proxy_config.json', 'r') as f:
                        proxy_config = json.load(f)
                        if proxy_config.get('brightdata', {}).get('enabled'):
                            proxies = proxy_config['brightdata']['proxies']
                            if proxies:
                                proxy_info = proxies[0]
                                proxy_server = f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['server']}"
                                logger.info(f"Using proxy from proxy_config.json: {proxy_server}")
                except Exception as e:
                    logger.warning(f"Failed to load proxy from config: {e}")

            self.options = {
                "headless": True,
                "browser": "chrome",
                "proxy": proxy_server,
                "tweets_count": 100,
                "output_format": "json",
                "driver_path": self.driver_path,
                "chrome_options": self.chrome_options
            }

            logger.info(f"Using proxy: {proxy_server if proxy_server else 'None'}")

            logger.info("Twitter scraper service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Twitter scraper: {e}")
            self.available = False

    def _random_user_agent(self) -> str:
        """
        Generate a random user agent to avoid detection.

        Returns:
            Random user agent string
        """
        agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59"
        ]
        return random.choice(agents)

    @monitor_scraper("twitter_scraper_safe")
    def _scrape_safe(self, keyword: str) -> Optional[List[Dict[str, Any]]]:
        """
        Wrapper for scrape_keyword with error handling and anti-detection measures.

        Args:
            keyword: Keyword to search for

        Returns:
            List of tweet data or None if scraping failed
        """
        if not self.available:
            logger.warning(f"Twitter scraper not available, skipping {keyword}")
            return None

        try:
            # Add random delay between 2-8 seconds to avoid detection
            delay = random.uniform(2, 8)
            logger.info(f"Scraping tweets for '{keyword}' (delay: {delay:.2f}s)")
            time.sleep(delay)

            # Perform scraping with compatible parameters
            # Remove driver_path and chrome_options from options to avoid the error
            scrape_options = self.options.copy()
            if 'driver_path' in scrape_options:
                del scrape_options['driver_path']
            if 'chrome_options' in scrape_options:
                del scrape_options['chrome_options']

            # Check the signature of scrape_keyword to see what parameters it accepts
            import inspect
            params = inspect.signature(scrape_keyword).parameters
            logger.info(f"Available parameters for scrape_keyword: {list(params.keys())}")

            # Filter options to only include valid parameters
            valid_options = {k: v for k, v in scrape_options.items() if k in params}
            logger.info(f"Using options: {valid_options}")

            # Use the latest ChromeDriver version
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager

            # Override the browser parameter to use the latest ChromeDriver
            valid_options['browser'] = 'chrome'

            # Set up a custom browser instance with the latest ChromeDriver
            if 'browser_instance' not in params:
                # If browser_instance is not a parameter, use the default scrape_keyword
                return scrape_keyword(
                    keyword=keyword,
                    **valid_options
                )
            else:
                # If browser_instance is a parameter, create a custom browser instance
                chrome_options = webdriver.ChromeOptions()
                if valid_options.get('headless', False):
                    chrome_options.add_argument('--headless')
                if valid_options.get('proxy'):
                    chrome_options.add_argument(f'--proxy-server={valid_options["proxy"]}')
                chrome_options.add_argument('--disable-gpu')
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--disable-notifications')
                chrome_options.add_argument('--disable-extensions')
                chrome_options.add_argument('--disable-popup-blocking')
                chrome_options.add_argument('--start-maximized')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # Create a browser instance with the latest ChromeDriver
                service = Service(ChromeDriverManager().install())
                browser = webdriver.Chrome(service=service, options=chrome_options)

                # Remove browser parameter as we're providing browser_instance
                if 'browser' in valid_options:
                    del valid_options['browser']

                # Call scrape_keyword with the browser instance
                return scrape_keyword(
                    keyword=keyword,
                    browser_instance=browser,
                    **valid_options
                )
        except WebDriverException as e:
            logger.error(f"Scraping failed for '{keyword}': {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error scraping '{keyword}': {e}")
            return None

    @monitor_scraper("twitter_trend_metrics")
    def get_trend_metrics(self, trends: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Scrape metrics for multiple trends concurrently.

        Args:
            trends: List of trending topics to scrape

        Returns:
            Dictionary mapping trends to their metrics
        """
        if not self.available:
            logger.warning("Twitter scraper not available, returning empty metrics")
            return {}

        results = {}

        # Use ThreadPoolExecutor for concurrent scraping
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit scraping tasks
            futures = {
                executor.submit(self._scrape_safe, trend): trend
                for trend in trends
            }

            # Process results as they complete
            for future in concurrent.futures.as_completed(futures):
                trend = futures[future]
                try:
                    data = future.result()
                    if data:
                        results[trend] = self._process_data(data)
                        logger.info(f"Successfully scraped {len(data)} tweets for '{trend}'")
                    else:
                        logger.warning(f"No data returned for '{trend}'")
                except Exception as e:
                    logger.error(f"Processing failed for '{trend}': {e}")

        return results

    def _process_data(self, raw_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Convert raw scrape data to metrics.

        Args:
            raw_data: List of tweet data from scraper

        Returns:
            Dictionary of processed metrics
        """
        try:
            # Convert to DataFrame for easier processing
            df = pd.DataFrame(raw_data)

            # Calculate sentiment if TextBlob is available
            try:
                from textblob import TextBlob
                df['sentiment'] = df['text'].apply(lambda x: TextBlob(x).sentiment.polarity)
                avg_sentiment = df['sentiment'].mean()
            except ImportError:
                avg_sentiment = 0

            # Get top tweet (most likes)
            if not df.empty and 'likes' in df.columns:
                top_tweet_idx = df['likes'].fillna(0).astype(int).idxmax()
                top_tweet = df.iloc[top_tweet_idx].to_dict() if top_tweet_idx is not None else {}
            else:
                top_tweet = {}

            # Calculate metrics
            return {
                "volume": len(df),
                "avg_likes": df['likes'].fillna(0).astype(int).mean() if 'likes' in df.columns else 0,
                "avg_retweets": df['retweets'].fillna(0).astype(int).mean() if 'retweets' in df.columns else 0,
                "avg_replies": df['replies'].fillna(0).astype(int).mean() if 'replies' in df.columns else 0,
                "avg_sentiment": avg_sentiment,
                "top_tweet": top_tweet
            }
        except Exception as e:
            logger.error(f"Error processing tweet data: {e}")
            return {
                "volume": 0,
                "avg_likes": 0,
                "avg_retweets": 0,
                "avg_replies": 0,
                "avg_sentiment": 0,
                "top_tweet": {}
            }

    def save_to_db(self, metrics: Dict[str, Dict[str, Any]]) -> bool:
        """
        Store metrics in database.

        Args:
            metrics: Dictionary mapping trends to their metrics

        Returns:
            True if successful, False otherwise
        """
        if not self.db:
            logger.warning("No database connection available for saving Twitter metrics")
            return False

        if not metrics:
            logger.warning("No metrics to save to database")
            return False

        cursor = self.db.cursor()
        try:
            for trend, data in metrics.items():
                # Insert into twitter_metrics table
                cursor.execute("""
                    INSERT INTO twitter_metrics
                    (trending_hashtag, tweet_volume, avg_likes, avg_retweets, avg_sentiment, crawled_at)
                    VALUES (%s, %s, %s, %s, %s, NOW())
                    ON DUPLICATE KEY UPDATE
                    tweet_volume = VALUES(tweet_volume),
                    avg_likes = VALUES(avg_likes),
                    avg_retweets = VALUES(avg_retweets),
                    avg_sentiment = VALUES(avg_sentiment),
                    crawled_at = NOW()
                """, (
                    trend,
                    data['volume'],
                    data['avg_likes'],
                    data['avg_retweets'],
                    data['avg_sentiment']
                ))

                # Save top tweet if available
                if data['top_tweet'] and 'text' in data['top_tweet']:
                    cursor.execute("""
                        INSERT INTO twitter_data
                        (tweet_id, text, user_name, likes, retweets, replies, sentiment_score, crawled_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                        ON DUPLICATE KEY UPDATE
                        likes = VALUES(likes),
                        retweets = VALUES(retweets),
                        replies = VALUES(replies),
                        sentiment_score = VALUES(sentiment_score),
                        crawled_at = NOW()
                    """, (
                        data['top_tweet'].get('tweet_id', f"mock_{int(time.time())}"),
                        data['top_tweet'].get('text', '')[:65535],  # Limit text to avoid DB errors
                        data['top_tweet'].get('username', '')[:255],
                        data['top_tweet'].get('likes', 0),
                        data['top_tweet'].get('retweets', 0),
                        data['top_tweet'].get('replies', 0),
                        data['top_tweet'].get('sentiment', 0)
                    ))

            # Commit changes
            self.db.commit()
            logger.info(f"Successfully saved Twitter metrics for {len(metrics)} trends to database")
            return True

        except Exception as e:
            logger.error(f"Database save failed: {e}")
            try:
                self.db.rollback()
            except:
                pass
            return False

def calculate_coolness_score_with_twitter(base_score: float, tweet_metrics: Optional[Dict[str, Any]] = None) -> float:
    """
    Calculate coolness score with Twitter metrics boost.

    Args:
        base_score: Base coolness score
        tweet_metrics: Twitter metrics data

    Returns:
        Updated coolness score
    """
    score = base_score

    # Add Twitter metrics boost if available
    if tweet_metrics:
        # Volume boost (up to 20%)
        score += 0.2 * min(1, tweet_metrics.get('volume', 0) / 1000)

        # Engagement boost (up to 15%)
        score += 0.15 * min(1, tweet_metrics.get('avg_likes', 0) / 500)

        # Virality boost (up to 10%)
        score += 0.1 * min(1, tweet_metrics.get('avg_retweets', 0) / 200)

        # Sentiment boost (up to 5%, can be negative)
        score += 0.05 * tweet_metrics.get('avg_sentiment', 0)

    # Cap at 1.0
    return min(score, 1.0)

def test_twitter_scraper():
    """
    Test the TwitterScraperService functionality.
    """
    # Initialize the scraper
    scraper = TwitterScraperService()

    if not scraper.available:
        print("Twitter scraper not available. Please install required dependencies.")
        return

    # Test scraping for a few keywords
    test_keywords = ["python programming", "artificial intelligence", "data science"]

    print(f"Testing Twitter scraper with keywords: {test_keywords}")
    metrics = scraper.get_trend_metrics(test_keywords)

    # Print results
    for keyword, data in metrics.items():
        print(f"\nResults for '{keyword}':")
        print(f"  Tweet volume: {data['volume']}")
        print(f"  Average likes: {data['avg_likes']:.2f}")
        print(f"  Average retweets: {data['avg_retweets']:.2f}")
        print(f"  Average sentiment: {data['avg_sentiment']:.2f}")

        if data['top_tweet'] and 'text' in data['top_tweet']:
            print(f"\n  Top tweet: {data['top_tweet']['text'][:100]}...")
            print(f"    Likes: {data['top_tweet'].get('likes', 0)}")
            print(f"    Retweets: {data['top_tweet'].get('retweets', 0)}")

    # Test coolness score calculation
    base_score = 0.5
    for keyword, data in metrics.items():
        new_score = calculate_coolness_score_with_twitter(base_score, data)
        print(f"\nCoolness score for '{keyword}': {base_score:.2f} → {new_score:.2f}")

if __name__ == "__main__":
    # Run test if executed directly
    test_twitter_scraper()
