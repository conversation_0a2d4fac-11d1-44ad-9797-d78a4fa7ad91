#!/usr/bin/env python3
"""
Nano Neural Network for Trend Analysis

This module implements a custom lightweight neural network architecture
specifically designed for trend analysis with self-training capabilities.
It integrates with PostgreSQL's pgvector extension for efficient vector operations.

Features:
1. Lightweight neural architecture (< 1MB model size)
2. Knowledge distillation from larger models
3. Self-training capabilities using unlabeled data
4. Specialized for temporal trend pattern recognition
5. Integration with pgvector for database-accelerated inference
6. Secure API bridge with dual knowledge distillers
7. Rate-limited API access with token buckets
8. TLS 1.3 secure connections
9. Input/output sanitization
10. Meta-learning controller for hyperparameter optimization
"""

import os
import logging
import json
import time
import threading
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from typing import Dict, Tuple, Optional, Any, Union, List
import psycopg2
from datetime import datetime
from collections import deque
import math
from sklearn.metrics.pairwise import cosine_similarity

# Import secure components
try:
    from secure_text_processors import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Text<PERSON>orm<PERSON><PERSON>, J<PERSON>NS<PERSON><PERSON><PERSON>, InputVali<PERSON><PERSON>
    from secure_api_bridge import TokenBucket, TLSClient, RateLimitException, DynamicRouter, system_load, APIKeyManager
    from meta_learning_controller import MetaLearningController, HypernetworkOptimizer, DPGradientComputation
    from pg_vector_manager import PgVectorManager, RealTimeVectorProcessor
    SECURE_COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Some secure components not found: {e}. Running in reduced security mode.")
    SECURE_COMPONENTS_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TrendEmbeddingDataset(Dataset):
    """Dataset for trend embeddings with temporal information."""

    def __init__(self,
                 embeddings: np.ndarray,
                 timestamps: np.ndarray,
                 labels: Optional[np.ndarray] = None):
        """
        Initialize the dataset.

        Args:
            embeddings: Matrix of embeddings (n_samples, embedding_dim)
            timestamps: Array of Unix timestamps for each embedding
            labels: Optional labels for supervised training
        """
        self.embeddings = torch.tensor(embeddings, dtype=torch.float32)

        # Normalize timestamps to days from earliest
        min_timestamp = timestamps.min()
        days = (timestamps - min_timestamp) / (24 * 60 * 60)
        self.time_features = torch.tensor(days, dtype=torch.float32).unsqueeze(1)

        if labels is not None:
            self.labels = torch.tensor(labels, dtype=torch.float32)
        else:
            self.labels = None

        self.supervised = labels is not None

    def __len__(self):
        return len(self.embeddings)

    def __getitem__(self, idx):
        if self.supervised:
            return {
                'embedding': self.embeddings[idx],
                'time_feature': self.time_features[idx],
                'label': self.labels[idx]
            }
        else:
            return {
                'embedding': self.embeddings[idx],
                'time_feature': self.time_features[idx]
            }


class RateLimitedTeacher:
    """
    Teacher model with rate limiting that safely calls external APIs.

    Enforces both request rate limits and token usage limits.
    """

    def __init__(self,
                 req_limit: float,
                 token_limit: float,
                 sanitizer: Any,
                 api_key: str = None,
                 base_url: str = None,
                 model_name: str = "default"):
        """
        Initialize the rate limited teacher.

        Args:
            req_limit: Requests per second limit
            token_limit: Tokens per second limit
            sanitizer: Text sanitizer object
            api_key: API key for authentication
            base_url: Base URL for API
            model_name: Model name for logging
        """
        self.req_bucket = TokenBucket(req_limit, req_limit * 5)
        self.token_bucket = TokenBucket(token_limit, token_limit * 10)
        self.sanitizer = sanitizer
        self.api_key = api_key or os.getenv(f"{model_name.upper()}_API_KEY")
        self.encrypted_key = self.api_key  # In production, would be encrypted
        self.base_url = base_url
        self.model_name = model_name
        self.client = TLSClient()

    def query(self, text: str) -> str:
        """
        Query the teacher model with rate limiting.

        Args:
            text: Input text to query

        Returns:
            Model response

        Raises:
            RateLimitException: If rate limits exceeded
        """
        if not self.req_bucket.consume(1):
            raise RateLimitException("Request rate limit exceeded")

        # Token-based rate limiting
        if not self.token_bucket.consume(len(text)):
            raise RateLimitException("Token rate limit exceeded")

        # Apply sanitization
        sanitized = self.sanitizer.clean(text)

        # Query the API
        try:
            with TLSClient() as client:
                response = client.post(
                    f"{self.base_url}/v1/completions",
                    json={"prompt": sanitized},
                    headers={"Authorization": f"Bearer {self.encrypted_key}"}
                )
            return self._validate_response(response)
        except Exception as e:
            logger.error(f"API query failed: {e}")
            raise

    def _validate_response(self, response: Dict[str, Any]) -> str:
        """
        Validate and extract response from API.

        Args:
            response: API response

        Returns:
            Extracted text
        """
        if response.get('status') != 200:
            raise ValueError(f"API returned status {response.get('status')}")

        content = response.get('content')
        if not content:
            raise ValueError("Empty response from API")

        # Extract the response text
        if isinstance(content, dict):
            return content.get('choices', [{}])[0].get('text', '')

        return str(content)


class DualTeacherDistiller:
    """
    Knowledge distillation system that routes between two teacher models.

    Uses dynamic routing based on text characteristics, rate limits,
    and system load.
    """

    def __init__(self, api_keys: Dict[str, str] = None):
        """
        Initialize the dual teacher distiller.

        Args:
            api_keys: Dictionary of API keys for each teacher
        """
        api_keys = api_keys or {}

        # Initialize teachers with proper rate limits
        self.teachers = {
            "compound-beta": RateLimitedTeacher(
                req_limit=15/60,  # 15 requests per minute
                token_limit=200/86400,  # 200 tokens per day
                sanitizer=HTMLSanitizer(),
                api_key=api_keys.get("compound-beta"),
                base_url="https://api.compound-beta.example.com",
                model_name="compound-beta"
            ),
            "allam-2-7b": RateLimitedTeacher(
                req_limit=30/60,  # 30 requests per minute
                token_limit=7000/86400,  # 7000 tokens per day
                sanitizer=TextNormalizer(),
                api_key=api_keys.get("allam-2-7b"),
                base_url="https://api.allam-models.example.com",
                model_name="allam-2-7b"
            )
        }

        # Dynamic router for selecting teachers
        self.router = DynamicRouter(
            cost_fn=lambda t, ctx: len(ctx.get('text', '')) * 0.8 + self.teachers[t].token_bucket.level
        )

    def _encode(self, response: str) -> np.ndarray:
        """
        Encode a response into an embedding vector.

        Args:
            response: Text response from teacher

        Returns:
            Embedding vector
        """
        # Simple encoding for demonstration - in production would use a proper encoder
        tokens = response.split()
        # Create a simple bag-of-words representation
        vec = np.zeros(384)  # Match common embedding dimension
        for i, token in enumerate(tokens[:min(len(tokens), 384)]):
            vec[i % 384] += 1
        # Normalize
        norm = np.linalg.norm(vec)
        if norm > 0:
            vec = vec / norm
        return vec

    def _compute_gradients(self, teacher_responses: List[np.ndarray]) -> np.ndarray:
        """
        Compute gradients from teacher responses for knowledge distillation.

        Args:
            teacher_responses: List of teacher embeddings

        Returns:
            Gradients to apply
        """
        if not teacher_responses:
            return np.zeros(384)

        # Average teacher responses
        return np.mean(teacher_responses, axis=0)

    def distill(self, text_batch: List[str]) -> np.ndarray:
        """
        Distill knowledge from teachers for a batch of texts.

        Args:
            text_batch: Batch of input texts

        Returns:
            Distilled embedding
        """
        teacher_responses = []
        for text in text_batch:
            try:
                # Select teacher based on context
                teacher = self.router.select(
                    text,
                    candidates=self.teachers,
                    context={"text": text, "current_load": system_load()}
                )

                # Query the teacher
                response = teacher.query(text)
                teacher_responses.append(self._encode(response))
            except RateLimitException:
                logger.warning(f"Rate limit exceeded, skipping text: {text[:30]}...")
            except Exception as e:
                logger.error(f"Error querying teacher: {e}")

        # Compute gradients from responses
        return self._compute_gradients(teacher_responses)


class SpectralNorm:
    """
    Spectral Normalization for improved GAN stability.

    Normalizes the weight matrix by its spectral norm to prevent
    exploding gradients and mode collapse.
    """

    def __init__(self, module, name='weight', power_iterations=1):
        """
        Initialize spectral norm wrapper.

        Args:
            module: PyTorch module to apply spectral norm to
            name: Name of the parameter to normalize
            power_iterations: Number of power iterations for estimating spectral norm
        """
        self.module = module
        self.name = name
        self.power_iterations = power_iterations

        weight = getattr(self.module, self.name)

        # Register a parameter buffer for u vector
        with torch.no_grad():
            height = weight.shape[0]
            # Random initialization of u
            u = torch.randn(height, 1, device=weight.device)
            v = torch.randn(1, weight.shape[1], device=weight.device)
            u = u / torch.norm(u)
            v = v / torch.norm(v)

        # Register buffers for persistent state
        self.module.register_buffer('u', u)
        self.module.register_buffer('v', v)

        # Modify forward to apply spectral norm
        self.original_forward = self.module.forward
        self.module.forward = self.forward_with_spectral_norm

    def forward_with_spectral_norm(self, *args, **kwargs):
        """
        Forward pass with spectral normalization applied.

        Args:
            *args: Arguments to pass to module
            **kwargs: Keyword arguments to pass to module

        Returns:
            Module output with normalized weights
        """
        # Apply spectral norm
        weight = getattr(self.module, self.name)
        u = self.module.u
        v = self.module.v

        # Power iteration
        with torch.no_grad():
            for _ in range(self.power_iterations):
                v = F.normalize(torch.matmul(u.t(), weight), dim=1, eps=1e-12)
                u = F.normalize(torch.matmul(weight, v.t()), dim=0, eps=1e-12)

        # Update u and v
        self.module.u = u
        self.module.v = v

        # Get spectral norm
        sigma = torch.sum(u * torch.matmul(weight, v.t()))

        # Normalize weight
        weight = weight / sigma

        # Temporarily replace weight
        setattr(self.module, self.name, weight)

        # Call original forward
        result = self.original_forward(*args, **kwargs)

        # Restore original weight
        setattr(self.module, self.name, getattr(self.module, self.name) * sigma)

        return result


class DilatedTCNBlock(nn.Module):
    """
    Dilated Temporal Convolutional Network block.

    Implements causal dilated convolutions for capturing long-range temporal dependencies.
    """

    def __init__(self,
                 in_channels: int,
                 out_channels: int,
                 kernel_size: int = 3,
                 dilation: int = 1,
                 dropout: float = 0.2):
        """
        Initialize the TCN block.

        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            kernel_size: Size of convolutional kernel
            dilation: Dilation factor for convolution
            dropout: Dropout rate
        """
        super().__init__()
        self.conv1 = nn.Conv1d(
            in_channels,
            out_channels,
            kernel_size,
            padding=(kernel_size - 1) * dilation,
            dilation=dilation
        )
        self.conv2 = nn.Conv1d(
            out_channels,
            out_channels,
            kernel_size,
            padding=(kernel_size - 1) * dilation * 2,
            dilation=dilation * 2
        )
        self.norm1 = nn.BatchNorm1d(out_channels)
        self.norm2 = nn.BatchNorm1d(out_channels)
        self.dropout = nn.Dropout(dropout)

        # Residual connection if dimensions don't match
        self.residual = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the TCN block.

        Args:
            x: Input tensor of shape (batch_size, in_channels, seq_len)

        Returns:
            Output tensor of shape (batch_size, out_channels, seq_len)
        """
        # First dilated convolution
        out = self.conv1(x)
        out = self.norm1(out)
        out = F.relu(out)
        out = self.dropout(out)

        # Second dilated convolution with doubled dilation
        out = self.conv2(out)
        out = self.norm2(out)

        # Residual connection
        res = self.residual(x)
        return F.relu(out + res)


class RNNDetector(nn.Module):
    """
    RNN-based discriminator for temporal patterns.

    Uses bidirectional LSTM to capture temporal dependencies in both directions.
    """

    def __init__(self, input_dim: int, hidden_dim: int = 128):
        """
        Initialize the RNN detector.

        Args:
            input_dim: Dimension of input features
            hidden_dim: Dimension of hidden state
        """
        super().__init__()
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=2,
            batch_first=True,
            bidirectional=True,
            dropout=0.2
        )
        self.fc = nn.Linear(hidden_dim * 2, 1)  # *2 for bidirectional

        # Apply spectral normalization to the output layer
        self.fc = SpectralNorm(self.fc)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the RNN detector.

        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)

        Returns:
            Discrimination score
        """
        # Run through LSTM
        output, _ = self.lstm(x)

        # Use the final time step's output
        final_output = output[:, -1]

        # Generate score
        score = torch.sigmoid(self.fc(final_output))
        return score


class TemporalAttention(nn.Module):
    """Attention mechanism for focusing on relevant temporal patterns."""

    def __init__(self, embedding_dim: int):
        """
        Initialize temporal attention mechanism.

        Args:
            embedding_dim: Dimension of the input embeddings
        """
        super().__init__()
        self.query = nn.Linear(embedding_dim, embedding_dim)
        self.key = nn.Linear(embedding_dim, embedding_dim)
        self.value = nn.Linear(embedding_dim, embedding_dim)
        self.scale = np.sqrt(embedding_dim)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Apply temporal attention to input embeddings.

        Args:
            x: Input tensor of shape (batch_size, sequence_length, embedding_dim)

        Returns:
            Tensor with attention applied
        """
        q = self.query(x)  # (batch_size, seq_len, embedding_dim)
        k = self.key(x)    # (batch_size, seq_len, embedding_dim)
        v = self.value(x)  # (batch_size, seq_len, embedding_dim)

        # Calculate attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / self.scale

        # Apply softmax to get attention weights
        attention = F.softmax(scores, dim=-1)

        # Apply attention weights to values
        return torch.matmul(attention, v)


class NanoTrendNetwork(nn.Module):
    """
    Nano Neural Network for trend analysis with self-training capabilities.
    Total size < 1MB with focus on temporal pattern recognition.
    """

    def __init__(self,
                 input_dim: int = 384,
                 hidden_dim: int = 64,
                 output_dim: int = 32,
                 dropout: float = 0.2):
        """
        Initialize the nano neural network.

        Args:
            input_dim: Dimension of input embeddings
            hidden_dim: Dimension of hidden layer
            output_dim: Dimension of output embeddings
            dropout: Dropout probability
        """
        super().__init__()

        # Dimensionality reduction for input embeddings
        self.embedding_projection = nn.Linear(input_dim, hidden_dim)

        # Temporal feature integration
        self.time_projection = nn.Linear(1, hidden_dim // 4)

        # Hidden layers with residual connections
        self.hidden1 = nn.Linear(hidden_dim + hidden_dim // 4, hidden_dim)
        self.hidden2 = nn.Linear(hidden_dim, hidden_dim)

        # Temporal attention for sequence modeling
        self.temporal_attention = TemporalAttention(hidden_dim)

        # Output projection
        self.output = nn.Linear(hidden_dim, output_dim)

        # Regularization
        self.dropout = nn.Dropout(dropout)
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)

        # Optional prediction head (only used for supervised training)
        self.prediction_head = nn.Linear(output_dim, 1)

    def forward(self,
                embeddings: torch.Tensor,
                time_features: torch.Tensor,
                return_predictions: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """
        Forward pass through the network.

        Args:
            embeddings: Tensor of embeddings (batch_size, input_dim)
            time_features: Tensor of temporal features (batch_size, 1)
            return_predictions: Whether to return trend predictions

        Returns:
            Projected embeddings or tuple of (embeddings, predictions)
        """
        # Project input embeddings to lower dimension
        x = self.embedding_projection(embeddings)
        x = F.relu(x)

        # Project and integrate temporal features
        t = self.time_projection(time_features)
        t = F.relu(t)

        # Combine embeddings with temporal information
        combined = torch.cat([x, t], dim=1)

        # First hidden layer with residual connection
        h1 = self.hidden1(combined)
        h1 = F.relu(h1)
        h1 = self.dropout(h1)
        h1 = self.layer_norm1(h1)

        # Second hidden layer with residual connection
        h2 = self.hidden2(h1)
        h2 = F.relu(h2)
        h2 = self.dropout(h2)
        h2 = h1 + self.layer_norm2(h2)  # Residual connection

        # Output projection
        output_embeddings = self.output(h2)

        # Return output embeddings and predictions if requested
        if return_predictions:
            predictions = self.prediction_head(output_embeddings)
            return output_embeddings, predictions

        return output_embeddings

    def get_model_size_mb(self) -> float:
        """
        Calculate the size of the model parameters in MB.

        Returns:
            Size of the model in MB
        """
        param_size = 0
        for param in self.parameters():
            param_size += param.nelement() * param.element_size()

        return param_size / (1024 * 1024)  # Convert bytes to MB


class TemporalGAN(nn.Module):
    """
    Temporal GAN for predictive trend analysis.

    This GAN uses advanced TCN architecture for the generator and
    RNN-based discriminator for capturing temporal dependencies.
    Implements Wasserstein GAN with gradient penalty for stability.
    """

    def __init__(self, embedding_dim: int = 384, hidden_dim: int = 256, noise_dim: int = 20, device: str = None):
        """
        Initialize the Temporal GAN.

        Args:
            embedding_dim: Dimension of embeddings
            hidden_dim: Hidden dimension for generator and discriminator
            noise_dim: Dimension of noise vector
            device: Device to run model on
        """
        super().__init__()

        # Set device
        self.device = device if device else "cuda" if torch.cuda.is_available() else "cpu"

        # Dimensions
        self.embedding_dim = embedding_dim
        self.noise_dim = noise_dim

        # Initialize generator
        self.generator = nn.Sequential(
            nn.Conv1d(embedding_dim + noise_dim + 1, hidden_dim, 3, padding=1),
            nn.LeakyReLU(0.2),
            DilatedTCNBlock(hidden_dim, hidden_dim * 2, dilation=2),
            nn.LeakyReLU(0.2),
            DilatedTCNBlock(hidden_dim * 2, hidden_dim, dilation=4),
            nn.Conv1d(hidden_dim, embedding_dim, 1)
        ).to(self.device)

        # Initialize discriminator using RNNDetector
        self.discriminator = RNNDetector(embedding_dim * 2 + 1, hidden_dim // 2).to(self.device)

        # Optimizers
        self.generator_lr = 0.0001
        self.discriminator_lr = 0.0001
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=self.generator_lr, betas=(0.5, 0.999))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=self.discriminator_lr, betas=(0.5, 0.999))

        # Gradient penalty weight
        self.lambda_gp = 10

    def generator_forward(self, embeddings: torch.Tensor, time_deltas: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the generator.

        Args:
            embeddings: Original embeddings (batch_size, embedding_dim)
            time_deltas: Time differences (batch_size, 1)
            noise: Random noise (batch_size, noise_dim)

        Returns:
            Generated future embeddings
        """
        batch_size = embeddings.size(0)

        # Expand time_deltas and reshape for temporal convolution
        time_deltas_expanded = time_deltas.expand(batch_size, 1, 1)

        # Reshape for 1D convolution (batch, channels, sequence_length)
        embeddings = embeddings.unsqueeze(2)  # (batch, embedding_dim, 1)
        noise = noise.unsqueeze(2)  # (batch, noise_dim, 1)

        # Concatenate along channel dimension
        x = torch.cat([embeddings, noise, time_deltas_expanded], dim=1)  # (batch, emb+noise+1, 1)

        # Forward pass through generator
        generated = self.generator(x)  # (batch, embedding_dim, 1)

        # Reshape back to (batch, embedding_dim)
        return generated.squeeze(2)

    def discriminator_forward(self, original_embeddings: torch.Tensor, future_embeddings: torch.Tensor, time_deltas: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the discriminator.

        Args:
            original_embeddings: Original embeddings (batch_size, embedding_dim)
            future_embeddings: Future embeddings (batch_size, embedding_dim)
            time_deltas: Time differences (batch_size, 1)

        Returns:
            Discrimination scores
        """
        # Concatenate embeddings and time_deltas
        x = torch.cat([original_embeddings, future_embeddings, time_deltas], dim=1)

        # Reshape for RNN input (batch, sequence_length, features)
        # Here we treat the single embedding as a sequence of length 1
        x = x.unsqueeze(1)  # (batch, 1, embedding_dim*2+1)

        # Forward pass through discriminator
        return self.discriminator(x)

    def gradient_penalty(self,
                         real_embeddings: torch.Tensor,
                         fake_embeddings: torch.Tensor,
                         original_embeddings: torch.Tensor,
                         time_deltas: torch.Tensor) -> torch.Tensor:
        """
        Calculate gradient penalty for WGAN-GP.

        Args:
            real_embeddings: Real future embeddings
            fake_embeddings: Generated future embeddings
            original_embeddings: Original embeddings
            time_deltas: Time differences

        Returns:
            Gradient penalty term
        """
        batch_size = real_embeddings.size(0)

        # Generate random interpolation factor
        alpha = torch.rand(batch_size, 1, device=self.device)

        # Create interpolated samples
        interpolates = alpha * real_embeddings + (1 - alpha) * fake_embeddings
        interpolates.requires_grad_(True)

        # Calculate discrimination score for interpolated samples
        disc_interpolates = self.discriminator_forward(original_embeddings, interpolates, time_deltas)

        # Calculate gradients
        gradients = torch.autograd.grad(
            outputs=disc_interpolates,
            inputs=interpolates,
            grad_outputs=torch.ones_like(disc_interpolates),
            create_graph=True,
            retain_graph=True
        )[0]

        # Calculate gradient penalty
        gradients = gradients.view(batch_size, -1)
        gradient_norm = gradients.norm(2, dim=1)
        gradient_penalty = ((gradient_norm - 1) ** 2).mean()

        return gradient_penalty

    def train_step(self,
                   original_embeddings: torch.Tensor,
                   future_embeddings: torch.Tensor,
                   time_deltas: torch.Tensor) -> Dict[str, float]:
        """
        Perform a single training step using Wasserstein GAN with gradient penalty.

        Args:
            original_embeddings: Original embeddings (batch_size, embedding_dim)
            future_embeddings: Real future embeddings (batch_size, embedding_dim)
            time_deltas: Time differences (batch_size, 1)

        Returns:
            Dictionary with loss values
        """
        batch_size = original_embeddings.size(0)

        # Move data to device
        original_embeddings = original_embeddings.to(self.device)
        future_embeddings = future_embeddings.to(self.device)
        time_deltas = time_deltas.to(self.device)

        # ---------------------
        # Train Discriminator
        # ---------------------
        self.d_optimizer.zero_grad()

        # Generate noise and fake future embeddings
        noise = torch.randn(batch_size, self.noise_dim, device=self.device)

        # Generate fake samples
        fake_embeddings = self.generator_forward(original_embeddings, time_deltas, noise)

        # Compute real and fake scores
        real_scores = self.discriminator_forward(original_embeddings, future_embeddings, time_deltas)
        fake_scores = self.discriminator_forward(original_embeddings, fake_embeddings.detach(), time_deltas)

        # Wasserstein loss for discriminator: maximize E[D(real)] - E[D(fake)]
        d_loss = torch.mean(fake_scores) - torch.mean(real_scores)

        # Gradient penalty
        gp = self.gradient_penalty(future_embeddings, fake_embeddings.detach(), original_embeddings, time_deltas)

        # Add gradient penalty to discriminator loss
        d_loss_with_gp = d_loss + self.lambda_gp * gp

        # Optimize discriminator
        d_loss_with_gp.backward()
        self.d_optimizer.step()

        # ---------------------
        # Train Generator
        # ---------------------
        # Train generator only every n steps to balance with discriminator
        if batch_size % 5 == 0:
            self.g_optimizer.zero_grad()

            # Generate new samples with fresh noise
            noise = torch.randn(batch_size, self.noise_dim, device=self.device)
            fake_embeddings = self.generator_forward(original_embeddings, time_deltas, noise)

            # Get scores for generated samples
            fake_scores = self.discriminator_forward(original_embeddings, fake_embeddings, time_deltas)

            # Wasserstein loss for generator: minimize -E[D(fake)]
            g_loss = -torch.mean(fake_scores)

            # Optimize generator
            g_loss.backward()
            self.g_optimizer.step()
        else:
            g_loss = torch.tensor(0.0, device=self.device)

        return {
            "d_loss": d_loss.item(),
            "g_loss": g_loss.item(),
            "gradient_penalty": gp.item()
        }

    def generate_future_embedding(self,
                                  embedding: np.ndarray,
                                  time_delta_days: float,
                                  num_samples: int = 1) -> np.ndarray:
        """
        Generate future embedding predictions.

        Args:
            embedding: Original embedding vector
            time_delta_days: Days into the future to predict
            num_samples: Number of samples to generate

        Returns:
            Generated future embeddings
        """
        self.eval()

        with torch.no_grad():
            # Prepare inputs
            embedding_tensor = torch.tensor(embedding, dtype=torch.float32).unsqueeze(0)
            embedding_tensor = embedding_tensor.repeat(num_samples, 1).to(self.device)

            time_delta_tensor = torch.tensor([[time_delta_days]], dtype=torch.float32)
            time_delta_tensor = time_delta_tensor.repeat(num_samples, 1).to(self.device)

            noise = torch.randn(num_samples, self.noise_dim, device=self.device)

            # Generate future embeddings
            generated = self.generator_forward(embedding_tensor, time_delta_tensor, noise)

            return generated.cpu().numpy()

    def save(self, path: str) -> None:
        """
        Save the model to disk.

        Args:
            path: Path to save the model
        """
        torch.save({
            'generator': self.generator.state_dict(),
            'discriminator': self.discriminator.state_dict(),
            'g_optimizer': self.g_optimizer.state_dict(),
            'd_optimizer': self.d_optimizer.state_dict(),
            'embedding_dim': self.embedding_dim,
            'noise_dim': self.noise_dim,
            'lambda_gp': self.lambda_gp
        }, path)

    def load(self, path: str) -> None:
        """
        Load the model from disk.

        Args:
            path: Path to load the model from
        """
        checkpoint = torch.load(path, map_location=self.device)

        self.embedding_dim = checkpoint['embedding_dim']
        self.noise_dim = checkpoint.get('noise_dim', self.noise_dim)
        self.lambda_gp = checkpoint.get('lambda_gp', 10)

        self.generator.load_state_dict(checkpoint['generator'])
        self.discriminator.load_state_dict(checkpoint['discriminator'])

        # Ensure optimizers are reinstantiated with the loaded models
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=self.generator_lr, betas=(0.5, 0.999))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=self.discriminator_lr, betas=(0.5, 0.999))

        if 'g_optimizer' in checkpoint:
            self.g_optimizer.load_state_dict(checkpoint['g_optimizer'])

        if 'd_optimizer' in checkpoint:
            self.d_optimizer.load_state_dict(checkpoint['d_optimizer'])


class NanoNetworkManager:
    """
    Manager class for the Nano Neural Network and Temporal GAN.

    Handles training, prediction, and integration with PostgreSQL.
    """

    def __init__(self,
                 db_host: str = None,
                 db_port: str = None,
                 db_name: str = None,
                 db_user: str = None,
                 db_password: str = None,
                 input_dim: int = 384,
                 device: str = None):
        """
        Initialize the Nano Network Manager.

        Args:
            db_host: PostgreSQL database host
            db_port: PostgreSQL database port
            db_name: PostgreSQL database name
            db_user: PostgreSQL database user
            db_password: PostgreSQL database password
            input_dim: Dimension of input embeddings
            device: Device to run the model on
        """
        # Load configuration from environment variables if not provided
        self.db_host = db_host or os.getenv('DB_HOST', 'localhost')
        self.db_port = db_port or os.getenv('DB_PORT', '5432')
        self.db_name = db_name or os.getenv('DB_NAME', 'trend_crawler')
        self.db_user = db_user or os.getenv('DB_USER', 'postgres')
        self.db_password = db_password or os.getenv('DB_PASSWORD', 'postgres')

        # Set device
        self.device = device if device else "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")

        # Initialize models
        self.nano_network = NanoTrendNetwork(input_dim=input_dim).to(self.device)
        self.temporal_gan = TemporalGAN(embedding_dim=input_dim, device=self.device)

        # Model paths
        self.models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "models")
        os.makedirs(self.models_dir, exist_ok=True)

        # Database connection
        self.conn = None
        try:
            self.conn = psycopg2.connect(
                host=self.db_host,
                port=self.db_port,
                dbname=self.db_name,
                user=self.db_user,
                password=self.db_password
            )
            logger.info(f"Connected to PostgreSQL database {self.db_name}")

            # Ensure trend_nano_models table exists
            self._ensure_model_table_exists()

        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            self.conn = None

    def _ensure_model_table_exists(self) -> None:
        """Ensure the necessary database table for model tracking exists."""
        if not self.conn:
            logger.error("No database connection")
            return

        try:
            with self.conn.cursor() as cur:
                cur.execute("""
                CREATE TABLE IF NOT EXISTS trend_nano_models (
                    id SERIAL PRIMARY KEY,
                    model_type VARCHAR(50) NOT NULL,  -- 'nano_network' or 'temporal_gan'
                    model_version VARCHAR(50) NOT NULL,
                    trained_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    model_path TEXT NOT NULL,
                    parameters JSONB NOT NULL,
                    metrics JSONB,
                    is_active BOOLEAN DEFAULT TRUE,
                    embedding_dim INTEGER NOT NULL,
                    hidden_dim INTEGER NOT NULL,
                    training_samples INTEGER NOT NULL
                )
                """)

                self.conn.commit()
                logger.info("Ensured trend_nano_models table exists")

        except Exception as e:
            logger.error(f"Failed to create model table: {e}")
            self.conn.rollback()

    def fetch_training_data(self,
                           days_back: int = 30,
                           max_samples: int = 10000) -> Dict[str, np.ndarray]:
        """
        Fetch training data from the database.

        Args:
            days_back: Days of data to fetch
            max_samples: Maximum number of samples

        Returns:
            Dictionary with training data arrays
        """
        if not self.conn:
            logger.error("No database connection")
            return None

        try:
            with self.conn.cursor() as cur:
                # Fetch recent content with embeddings
                cur.execute("""
                SELECT
                    id,
                    embedding,
                    published_at,
                    source_platform,
                    sentiment_score
                FROM
                    crawled_content
                WHERE
                    embedding IS NOT NULL AND
                    published_at >= NOW() - INTERVAL %s DAY AND
                    status = 'embedding_complete'
                ORDER BY
                    published_at DESC
                LIMIT %s
                """, (days_back, max_samples))

                rows = cur.fetchall()

                if not rows:
                    logger.warning("No training data found")
                    return None

                # Process the data
                content_ids = []
                embeddings = []
                timestamps = []
                sources = []
                sentiments = []

                for row in rows:
                    content_id, embedding, published_at, source, sentiment = row
                    content_ids.append(content_id)
                    embeddings.append(embedding)
                    timestamps.append(published_at.timestamp())
                    sources.append(source)
                    sentiments.append(sentiment if sentiment is not None else 0.0)

                return {
                    'content_ids': np.array(content_ids),
                    'embeddings': np.array(embeddings),
                    'timestamps': np.array(timestamps),
                    'sources': np.array(sources),
                    'sentiments': np.array(sentiments)
                }

        except Exception as e:
            logger.error(f"Error fetching training data: {e}")
            return None

    def fetch_temporal_pairs(self,
                            trend_id: int,
                            min_pairs: int = 100,
                            time_window_days: int = 7) -> Optional[Dict[str, np.ndarray]]:
        """
        Fetch temporal embedding pairs for GAN training.

        Args:
            trend_id: ID of the trend to analyze
            min_pairs: Minimum number of pairs to fetch
            time_window_days: Maximum time window in days

        Returns:
            Dictionary with temporal embedding pairs
        """
        if not self.conn:
            logger.error("No database connection")
            return None

        try:
            with self.conn.cursor() as cur:
                # Fetch content in the trend ordered by timestamp
                cur.execute("""
                SELECT
                    cc.id,
                    cc.embedding,
                    cc.published_at
                FROM
                    trend_content_map tcm
                JOIN
                    crawled_content cc ON tcm.content_id = cc.id
                WHERE
                    tcm.trend_id = %s AND
                    cc.embedding IS NOT NULL
                ORDER BY
                    cc.published_at
                """, (trend_id,))

                rows = cur.fetchall()

                if len(rows) < min_pairs:
                    logger.warning(f"Not enough data for trend {trend_id}: {len(rows)} < {min_pairs}")
                    return None

                # Process data into pairs
                content_ids = []
                original_embeddings = []
                future_embeddings = []
                time_deltas = []

                all_embeddings = [(row[0], np.array(row[1]), row[2]) for row in rows]

                # Create pairs with different time windows
                for i, (id1, emb1, ts1) in enumerate(all_embeddings[:-1]):
                    for j in range(i + 1, len(all_embeddings)):
                        id2, emb2, ts2 = all_embeddings[j]

                        # Calculate time delta in days
                        delta = (ts2 - ts1).total_seconds() / (24 * 60 * 60)

                        if delta <= time_window_days:
                            content_ids.append((id1, id2))
                            original_embeddings.append(emb1)
                            future_embeddings.append(emb2)
                            time_deltas.append(delta)

                if len(time_deltas) < min_pairs:
                    logger.warning(f"Not enough temporal pairs for trend {trend_id}: {len(time_deltas)} < {min_pairs}")
                    return None

                return {
                    'content_ids': np.array(content_ids),
                    'original_embeddings': np.array(original_embeddings),
                    'future_embeddings': np.array(future_embeddings),
                    'time_deltas': np.array(time_deltas).reshape(-1, 1)
                }

        except Exception as e:
            logger.error(f"Error fetching temporal pairs: {e}")
            return None

    def _distill_knowledge(self,
                          source_embeddings: np.ndarray,
                          target_model: nn.Module,
                          temperature: float = 2.0,
                          batch_size: int = 32,
                          epochs: int = 5) -> nn.Module:
        """
        Distill knowledge from pre-trained embeddings to the nano network.

        Args:
            source_embeddings: Source embeddings from larger model
            target_model: Target nano model to train
            temperature: Temperature for soft targets
            batch_size: Batch size for training
            epochs: Number of epochs

        Returns:
            Trained model
        """
        target_model.train()
        optimizer = optim.Adam(target_model.parameters(), lr=0.001)

        # Create synthetic timestamps (not important for distillation)
        timestamps = np.zeros((len(source_embeddings), 1))

        # Create dataset and dataloader
        dataset = TrendEmbeddingDataset(source_embeddings, timestamps)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # Train with MSE loss to mimic original embeddings
        for epoch in range(epochs):
            total_loss = 0

            for batch in dataloader:
                embeddings = batch['embedding'].to(self.device)
                time_features = batch['time_feature'].to(self.device)

                # Forward pass through target model
                projected_embeddings = target_model(embeddings, time_features)

                # Calculate MSE loss between original and projected embeddings
                loss = F.mse_loss(
                    F.normalize(projected_embeddings, p=2, dim=1),
                    F.normalize(embeddings, p=2, dim=1)
                )

                # Backpropagate and update
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            logger.info(f"Distillation Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")

        return target_model

    def train_nano_network(self,
                          days_back: int = 30,
                          batch_size: int = 64,
                          epochs: int = 10,
                          hidden_dim: int = 64,
                          output_dim: int = 32) -> Optional[str]:
        """
        Train the nano neural network on recent embeddings.

        Args:
            days_back: Days of data to use for training
            batch_size: Batch size for training
            epochs: Number of training epochs
            hidden_dim: Hidden dimension size
            output_dim: Output dimension size

        Returns:
            Path to saved model or None if failed
        """
        # Get training data
        data = self.fetch_training_data(days_back=days_back)
        if data is None:
            return None

        embeddings = data['embeddings']
        timestamps = data['timestamps']
        sentiments = data['sentiments']

        # Initialize new nano network for training
        input_dim = embeddings.shape[1]
        nano_net = NanoTrendNetwork(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            output_dim=output_dim
        ).to(self.device)

        # Knowledge distillation from original embeddings
        logger.info("Performing knowledge distillation...")
        nano_net = self._distill_knowledge(
            embeddings,
            nano_net,
            batch_size=batch_size,
            epochs=max(3, epochs // 2)  # Shorter distillation phase
        )

        # Create dataset for supervised fine-tuning
        dataset = TrendEmbeddingDataset(
            embeddings,
            timestamps,
            labels=sentiments.reshape(-1, 1)
        )
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # Fine-tune with sentiment supervision
        logger.info("Fine-tuning with sentiment supervision...")
        criterion = nn.MSELoss()
        optimizer = optim.Adam(nano_net.parameters(), lr=0.0005)

        for epoch in range(epochs):
            total_loss = 0

            for batch in dataloader:
                embeddings_batch = batch['embedding'].to(self.device)
                time_features = batch['time_feature'].to(self.device)
                labels = batch['label'].to(self.device)

                # Forward pass
                _, predictions = nano_net(embeddings_batch, time_features, return_predictions=True)

                # Calculate loss
                loss = criterion(predictions, labels)

                # Backpropagate and update
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()

                total_loss += loss.item()

            avg_loss = total_loss / len(dataloader)
            logger.info(f"Training Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")

        # Generate model version
        model_version = f"nano_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        model_path = os.path.join(self.models_dir, f"nano_network_{model_version}.pt")

        # Save the model
        torch.save({
            'model_state_dict': nano_net.state_dict(),
            'input_dim': input_dim,
            'hidden_dim': hidden_dim,
            'output_dim': output_dim,
            'version': model_version
        }, model_path)

        # Log in database
        if self.conn:
            try:
                with self.conn.cursor() as cur:
                    cur.execute("""
                    INSERT INTO trend_nano_models
                    (model_type, model_version, model_path, parameters, metrics, embedding_dim, hidden_dim, training_samples)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        'nano_network',
                        model_version,
                        model_path,
                        json.dumps({
                            'epochs': epochs,
                            'batch_size': batch_size,
                            'days_back': days_back
                        }),
                        json.dumps({
                            'final_loss': avg_loss,
                            'model_size_mb': nano_net.get_model_size_mb()
                        }),
                        input_dim,
                        hidden_dim,
                        len(timestamps)
                    ))

                    self.conn.commit()
                    logger.info(f"Saved model metadata to database: {model_version}")
            except Exception as e:
                logger.error(f"Failed to log model in database: {e}")
                self.conn.rollback()

        logger.info(f"Nano network trained and saved: {model_path}")
        return model_path

    def train_temporal_gan(self,
                          trend_id: int,
                          batch_size: int = 32,
                          epochs: int = 50) -> Optional[str]:
        """
        Train the temporal GAN for a specific trend.

        Args:
            trend_id: ID of the trend to train on
            batch_size: Batch size for training
            epochs: Number of training epochs

        Returns:
            Path to saved model or None if failed
        """
        # Get temporal pairs for training
        data = self.fetch_temporal_pairs(trend_id)
        if data is None:
            return None

        original_embeddings = data['original_embeddings']
        future_embeddings = data['future_embeddings']
        time_deltas = data['time_deltas']

        # Initialize GAN
        input_dim = original_embeddings.shape[1]
        self.temporal_gan = TemporalGAN(
            embedding_dim=input_dim,
            device=self.device
        )

        # Create dataset and dataloader
        dataset = torch.utils.data.TensorDataset(
            torch.tensor(original_embeddings, dtype=torch.float32),
            torch.tensor(future_embeddings, dtype=torch.float32),
            torch.tensor(time_deltas, dtype=torch.float32)
        )
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # Training loop
        logger.info(f"Training Temporal GAN for trend {trend_id}...")

        g_losses = []
        d_losses = []

        for epoch in range(epochs):
            epoch_g_loss = 0
            epoch_d_loss = 0

            for batch_orig, batch_future, batch_deltas in dataloader:
                losses = self.temporal_gan.train_step(batch_orig, batch_future, batch_deltas)

                epoch_g_loss += losses['g_loss']
                epoch_d_loss += losses['d_loss']

            avg_g_loss = epoch_g_loss / len(dataloader)
            avg_d_loss = epoch_d_loss / len(dataloader)

            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)

            if (epoch + 1) % 5 == 0:
                logger.info(f"Epoch {epoch+1}/{epochs}, G Loss: {avg_g_loss:.4f}, D Loss: {avg_d_loss:.4f}")

        # Generate model version
        model_version = f"gan_{trend_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        model_path = os.path.join(self.models_dir, f"temporal_gan_{model_version}.pt")

        # Save the model
        self.temporal_gan.save(model_path)

        # Log in database
        if self.conn:
            try:
                with self.conn.cursor() as cur:
                    # Get trend name
                    cur.execute("SELECT name FROM trends WHERE id = %s", (trend_id,))
                    trend_name = cur.fetchone()[0] if cur.rowcount > 0 else f"Trend {trend_id}"

                    cur.execute("""
                    INSERT INTO trend_nano_models
                    (model_type, model_version, model_path, parameters, metrics, embedding_dim, hidden_dim, training_samples)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        'temporal_gan',
                        model_version,
                        model_path,
                        json.dumps({
                            'trend_id': trend_id,
                            'trend_name': trend_name,
                            'epochs': epochs,
                            'batch_size': batch_size
                        }),
                        json.dumps({
                            'final_g_loss': g_losses[-1],
                            'final_d_loss': d_losses[-1],
                            'g_losses': g_losses,
                            'd_losses': d_losses
                        }),
                        input_dim,
                        64,  # Default hidden dim
                        len(time_deltas)
                    ))

                    self.conn.commit()
                    logger.info(f"Saved GAN model metadata to database: {model_version}")
            except Exception as e:
                logger.error(f"Failed to log GAN model in database: {e}")
                self.conn.rollback()

        logger.info(f"Temporal GAN trained and saved: {model_path}")
        return model_path

    def predict_trend_evolution(self,
                               trend_id: int,
                               days_ahead: int = 7,
                               num_samples: int = 5) -> Dict[str, Any]:
        """
        Predict how a trend will evolve using the Temporal GAN.

        Args:
            trend_id: ID of the trend to predict
            days_ahead: Days into the future to predict
            num_samples: Number of samples to generate

        Returns:
            Dictionary with prediction results
        """
        if not self.conn:
            logger.error("No database connection")
            return None

        try:
            # Get latest GAN model for this trend
            with self.conn.cursor() as cur:
                cur.execute("""
                SELECT model_path
                FROM trend_nano_models
                WHERE model_type = 'temporal_gan' AND
                      parameters->>'trend_id' = %s AND
                      is_active = TRUE
                ORDER BY trained_at DESC
                LIMIT 1
                """, (str(trend_id),))

                row = cur.fetchone()

                if not row:
                    logger.warning(f"No GAN model found for trend {trend_id}")
                    return None

                model_path = row[0]

                # Load the GAN model
                self.temporal_gan.load(model_path)

                # Get trend embedding (centroid)
                cur.execute("""
                SELECT trend_embedding
                FROM trends
                WHERE id = %s
                """, (trend_id,))

                trend_embedding = np.array(cur.fetchone()[0])

                # Generate future embeddings
                future_embeddings = self.temporal_gan.generate_future_embedding(
                    trend_embedding,
                    days_ahead,
                    num_samples=num_samples
                )

                # Calculate cosine similarity with original
                similarities = cosine_similarity(trend_embedding.reshape(1, -1), future_embeddings)

                # Calculate average magnitude change
                magnitudes = np.linalg.norm(future_embeddings, axis=1)
                orig_magnitude = np.linalg.norm(trend_embedding)
                magnitude_changes = magnitudes / orig_magnitude

                # Find nearest content for each predicted embedding
                nearest_content = []

                for i, future_emb in enumerate(future_embeddings):
                    # Query for nearest content
                    cur.execute("""
                    SELECT id, content_text, similarity(embedding, %s::vector) as sim
                    FROM crawled_content
                    ORDER BY embedding <=> %s::vector
                    LIMIT 3
                    """, (
                        future_emb.tolist(),
                        future_emb.tolist()
                    ))

                    similar_content = [
                        {
                            "content_id": row[0],
                            "content_text": row[1][:100] + "..." if len(row[1]) > 100 else row[1],
                            "similarity": row[2]
                        }
                        for row in cur.fetchall()
                    ]

                    nearest_content.append(similar_content)

                # Return prediction results
                return {
                    "trend_id": trend_id,
                    "days_ahead": days_ahead,
                    "num_samples": num_samples,
                    "original_embedding": trend_embedding.tolist(),
                    "future_embeddings": [emb.tolist() for emb in future_embeddings],
                    "similarities": similarities.tolist()[0],
                    "magnitude_changes": magnitude_changes.tolist(),
                    "nearest_content": nearest_content,
                    "avg_similarity": float(np.mean(similarities)),
                    "avg_magnitude_change": float(np.mean(magnitude_changes)),
                    "generated_at": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Error in predicting trend evolution: {e}")
            return None

    def add_real_time_hooks(self) -> bool:
        """
        Add database hooks for real-time updates.

        Returns:
            True if successful, False otherwise
        """
        if not self.conn:
            logger.error("No database connection")
            return False

        try:
            with self.conn.cursor() as cur:
                # Create function to update trend predictions when new content is added
                cur.execute("""
                CREATE OR REPLACE FUNCTION update_trend_predictions()
                RETURNS TRIGGER AS $$
                DECLARE
                    affected_trends INTEGER[];
                BEGIN
                    -- Get trends related to this content
                    SELECT array_agg(trend_id)
                    INTO affected_trends
                    FROM trend_content_map
                    WHERE content_id = NEW.id;

                    -- Log the update for processing by the external service
                    INSERT INTO trend_prediction_updates (trend_ids, content_id, update_type)
                    VALUES (affected_trends, NEW.id, 'new_content');

                    RETURN NEW;
                END;
                $$ LANGUAGE plpgsql;
                """)

                # Create table for tracking updates
                cur.execute("""
                CREATE TABLE IF NOT EXISTS trend_prediction_updates (
                    id SERIAL PRIMARY KEY,
                    trend_ids INTEGER[],
                    content_id BIGINT,
                    update_type VARCHAR(50),
                    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                    processed BOOLEAN DEFAULT FALSE,
                    processed_at TIMESTAMPTZ
                )
                """)

                # Create trigger on crawled_content for inserts/updates
                cur.execute("""
                DROP TRIGGER IF EXISTS trg_update_trend_predictions ON crawled_content;
                """)

                cur.execute("""
                CREATE TRIGGER trg_update_trend_predictions
                AFTER INSERT OR UPDATE OF embedding
                ON crawled_content
                FOR EACH ROW
                WHEN (NEW.embedding IS NOT NULL)
                EXECUTE FUNCTION update_trend_predictions();
                """)

                self.conn.commit()
                logger.info("Added real-time database hooks for trend predictions")
                return True

        except Exception as e:
            logger.error(f"Failed to add real-time hooks: {e}")
            self.conn.rollback()
            return False

    def process_prediction_updates(self, max_updates: int = 10) -> int:
        """
        Process pending trend prediction updates.

        Args:
            max_updates: Maximum number of updates to process

        Returns:
            Number of updates processed
        """
        if not self.conn:
            logger.error("No database connection")
            return 0

        processed_count = 0

        try:
            with self.conn.cursor() as cur:
                # Get pending updates
                cur.execute("""
                SELECT id, trend_ids, content_id, update_type
                FROM trend_prediction_updates
                WHERE NOT processed
                ORDER BY created_at
                LIMIT %s
                """, (max_updates,))

                updates = cur.fetchall()

                for update_id, trend_ids, content_id, update_type in updates:
                    if not trend_ids:
                        # Mark as processed if no trends to update
                        cur.execute("""
                        UPDATE trend_prediction_updates
                        SET processed = TRUE, processed_at = NOW()
                        WHERE id = %s
                        """, (update_id,))
                        continue

                    # Process each trend
                    for trend_id in trend_ids:
                        # Generate updated predictions
                        prediction = self.predict_trend_evolution(trend_id)

                        if prediction:
                            # Store updated prediction
                            cur.execute("""
                            UPDATE trends
                            SET metadata = jsonb_set(
                                COALESCE(metadata, '{}'::jsonb),
                                '{nano_predictions}',
                                %s::jsonb,
                                true
                            )
                            WHERE id = %s
                            """, (
                                json.dumps(prediction),
                                trend_id
                            ))

                    # Mark update as processed
                    cur.execute("""
                    UPDATE trend_prediction_updates
                    SET processed = TRUE, processed_at = NOW()
                    WHERE id = %s
                    """, (update_id,))

                    processed_count += 1

                self.conn.commit()

                if processed_count > 0:
                    logger.info(f"Processed {processed_count} trend prediction updates")

                return processed_count

        except Exception as e:
            logger.error(f"Error processing prediction updates: {e}")
            self.conn.rollback()
            return 0

    def close(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Closed database connection")


class NanoLLM(nn.Module):
    """
    A compact Language Model.
    This is a simplified transformer-based model.
    Actual implementation would require more sophisticated tokenization,
    embedding layers, and potentially more transformer blocks.
    """
    def __init__(self, vocab_size=32000, d_model=64, nhead=4, num_encoder_layers=2, dim_feedforward=128, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.embedding = nn.Embedding(vocab_size, d_model)
        encoder_layer = nn.TransformerEncoderLayer(d_model, nhead, dim_feedforward, dropout, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_encoder_layers)
        self.fc_out = nn.Linear(d_model, vocab_size) # Output layer for generating token logits

        # Placeholder for tokenizer - in a real scenario, this would be a proper tokenizer (e.g., SentencePiece, BPE)
        self.tokenizer = self._SimpleTokenizer(vocab_size)
        self.max_seq_len = 128 # Max sequence length for this simple model

    def _SimpleTokenizer(self, vocab_size):
        # Extremely basic tokenizer for demonstration.
        # Maps characters to integers.
        class Tokenizer:
            def __init__(self, vocab_size):
                self.vocab = {chr(i): i for i in range(min(vocab_size, 256))} # Basic ASCII
                self.pad_token_id = 0
                self.eos_token_id = 1
                self.bos_token_id = 2
                self.unk_token_id = 3
                self.vocab_size = vocab_size

            def encode(self, text, max_length=128):
                tokens = [self.vocab.get(char, self.unk_token_id) for char in text]
                tokens = [self.bos_token_id] + tokens[:max_length-2] + [self.eos_token_id]
                padding = [self.pad_token_id] * (max_length - len(tokens))
                return tokens + padding

            def decode(self, token_ids):
                # Basic decoding, remove special tokens
                chars = [key for token_id in token_ids
                         for key, value in self.vocab.items()
                         if value == token_id and token_id not in [self.pad_token_id, self.eos_token_id, self.bos_token_id, self.unk_token_id]]
                return "".join(chars)

            def batch_encode(self, texts: list[str], max_length=128):
                return torch.tensor([self.encode(text, max_length) for text in texts], dtype=torch.long)

        return Tokenizer(vocab_size)

    def forward(self, src_tokens: torch.Tensor): # src_tokens: (batch_size, seq_len)
        # src_tokens should be token IDs from the tokenizer
        embedded = self.embedding(src_tokens) * math.sqrt(self.d_model) # (batch_size, seq_len, d_model)
        # Transformer expects src_mask or padding_key_mask.
        # Create a padding mask for the transformer
        src_key_padding_mask = (src_tokens == self.tokenizer.pad_token_id) # (batch_size, seq_len)

        encoded_output = self.transformer_encoder(embedded, src_key_padding_mask=src_key_padding_mask) # (batch_size, seq_len, d_model)

        # For tasks like summarization, we might take the output of the first token (e.g., [CLS])
        # or average pool the sequence. For generation, we use all token outputs.
        # This output is logits over the vocabulary for each position.
        logits = self.fc_out(encoded_output) # (batch_size, seq_len, vocab_size)
        return logits

    # Duplicate method removed

    def summarize_to_embedding_batch(self, texts: List[str]) -> torch.Tensor:
        # Generates embeddings for a batch of texts, suitable for comparison with teacher embeddings.
        # This is a simplified version. A more robust approach would involve generating a summary
        # and then embedding it, or using a specific CLS token representation.
        # For now, it averages the token embeddings from the encoder.
        # Output shape: (batch_size, d_model)
        if not texts:
            return torch.empty(0, self.d_model)

        tokenized_batch = self.tokenizer.batch_encode(texts, max_length=self.max_seq_len)
        # Ensure tokenized_batch is on the same device as the model parameters
        device = next(self.parameters()).device
        tokenized_batch = tokenized_batch.to(device)

        embedded = self.embedding(tokenized_batch) * math.sqrt(self.d_model)
        src_key_padding_mask = (tokenized_batch == self.tokenizer.pad_token_id)

        encoded_output = self.transformer_encoder(embedded, src_key_padding_mask=src_key_padding_mask) # (B, S, D)

        # Average pooling over sequence length, ignoring padding
        # Create a mask to exclude padding tokens from averaging
        mask = ~src_key_padding_mask.unsqueeze(-1).expand_as(encoded_output) # (B, S, D)
        masked_output = encoded_output * mask
        sum_embeddings = masked_output.sum(dim=1) # (B, D)
        num_non_padding_tokens = mask.sum(dim=1) # (B, D), sum over S
        num_non_padding_tokens = num_non_padding_tokens[:, 0].unsqueeze(1) # (B, 1), take count from one feature dim

        # Avoid division by zero if a sequence is all padding (should not happen with BOS/EOS)
        num_non_padding_tokens = torch.clamp(num_non_padding_tokens, min=1)

        avg_embeddings = sum_embeddings / num_non_padding_tokens # (B, D)
        return avg_embeddings

    def generate_summary(self, text_content: str, prompt_template: str = "Summarize: {text}", max_length=50) -> str:
        # Generates a summary for a single text content.
        # This is a very basic generative placeholder.
        # A real implementation would involve more complex decoding strategies (beam search, etc.).
        self.eval() # Set model to evaluation mode
        # prompt = prompt_template.format(text=text_content) # Not directly using prompt for this basic version
        input_ids = self.tokenizer.batch_encode([text_content], max_length=self.max_seq_len)

        device = next(self.parameters()).device
        input_ids = input_ids.to(device)

        generated_token_ids = []
        # Start with BOS token, or use input_ids directly if model is conditioned that way
        # For simplicity, let's try to extend the input or generate from scratch.
        # This basic version will just take the input, get logits, and greedily decode a few steps.

        current_input_ids = input_ids[:, :1] # Start with BOS token if tokenizer adds it, or first token.
                                            # For this simple tokenizer, it's the BOS token.

        with torch.no_grad():
            for _ in range(max_length):
                logits = self.forward(current_input_ids) # (1, current_seq_len, vocab_size)
                next_token_logits = logits[:, -1, :] # Get logits for the last token: (1, vocab_size)
                next_token_id = torch.argmax(next_token_logits, dim=-1).unsqueeze(0) # (1,1)

                if next_token_id.item() == self.tokenizer.eos_token_id:
                    break
                generated_token_ids.append(next_token_id.item())
                current_input_ids = torch.cat([current_input_ids, next_token_id], dim=1)
                if current_input_ids.size(1) >= self.max_seq_len: # Stop if max_seq_len for input is reached
                    break

        self.train() # Set model back to training mode
        return self.tokenizer.decode(generated_token_ids)


class NeuralOptimizer(optim.AdamW): # Inherit from a standard optimizer
    # Custom optimizer wrapper for meta-learning capabilities (e.g., MAML)
    # and adaptive temperature for distillation.
    def __init__(self, params, lr=1e-3, initial_temp=2.0, temp_decay_rate=0.999, privatize_gradients_flag=False):
        super().__init__(params, lr=lr)
        self.temperature = initial_temp
        self.temp_decay_rate = temp_decay_rate
        self.meta_lr = 0.01 # Example meta-learning rate
        self.privatize_gradients_flag = privatize_gradients_flag # From original architect LLM

    def adapt(self, loss): # For meta-learning, e.g., update based on a meta-loss
        # This is a simplified adaptation step.
        # A full MAML would involve inner/outer loops and gradient calculations on meta-parameters.
        # For now, just decay temperature as a form of adaptation/scheduling.
        self.temperature *= self.temp_decay_rate
        # print(f"Optimizer: Temperature adapted to {self.temperature:.3f}")

    def current_temperature(self):
        return self.temperature

    # privatize_gradients method would be called by SecureDistillationSystem if flag is true


class ActiveMemory:
    # Stores (prompt, response, score) tuples for active learning.
    # Prioritizes samples where models performed poorly or where teacher models disagreed.
    def __init__(self, capacity=10000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity) # Stores (text_content, teacher_target_representation, student_loss_or_disagreement_score)
        # For simplicity, score is higher for worse performance / more disagreement

    def add(self, text_content: Union[str, List[str]], teacher_target, score: float):
        # Adds an experience to the memory
        if isinstance(text_content, list): # If a batch was added
            # For simplicity, if it's a list, we assume teacher_target and score are for the batch
            # or we'd need to iterate and add individually.
            # This simplified version will just store the first item if it's a list.
            # A more robust version would handle batch additions properly.
            if text_content:
                self.buffer.append((text_content[0], teacher_target, score))
        else:
            self.buffer.append((text_content, teacher_target, score))

    def sample(self, batch_size):
        # Samples a batch from memory, prioritizing high scores
        if not self.buffer:
            return []

        # Sort by score (descending) and take top N
        # This is inefficient for large buffers; a priority queue (heap) would be better.
        sorted_buffer = sorted(self.buffer, key=lambda x: x[2], reverse=True)

        num_to_sample = min(batch_size, len(sorted_buffer))
        return sorted_buffer[:num_to_sample]

    def __len__(self):
        return len(self.buffer)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("Testing Nano Neural Network for Trend Analysis")

    # Simple test of network architecture
    input_dim = 384  # Default for all-MiniLM-L6-v2
    batch_size = 8

    # Create random test data
    test_embeddings = np.random.randn(batch_size, input_dim).astype(np.float32)
    test_time_features = np.random.randn(batch_size, 1).astype(np.float32)

    # Convert to tensors
    test_embeddings_tensor = torch.tensor(test_embeddings)
    test_time_features_tensor = torch.tensor(test_time_features)

    # Test the network
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    nano_net = NanoTrendNetwork(input_dim=input_dim).to(device)

    # Report model size
    model_size_mb = nano_net.get_model_size_mb()
    logger.info(f"NanoTrendNetwork model size: {model_size_mb:.2f} MB")

    # Forward pass
    output_embeddings = nano_net(
        test_embeddings_tensor.to(device),
        test_time_features_tensor.to(device)
    )

    logger.info(f"Output embedding shape: {output_embeddings.size()}")
