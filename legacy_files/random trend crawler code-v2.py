import requests
import numpy as np
from transformers import BertTokenizer, TFBertForSequenceClassification
from sklearn.feature_extraction.text import TfidfVectorizer
from keras.optimizers import Adam
import tensorflow as tf
import psycopg2
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier


# Constants for coolness levels
COOLNESS_LEVELS = ["Red", "Orange", "Yellow", "Green", "Teal", "Blue", "Indigo", "Violet", "Pink"]


# Initialize a TF-IDF Vectorizer for feature extraction
vectorizer = TfidfVectorizer(stop_words='english')

# Initialize a BERT tokenizer
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')


# Function to create a Transformer model
def create_model(input_shape):
    model = TFBertForSequenceClassification.from_pretrained('bert-base-uncased', num_labels=len(COOLNESS_LEVELS))
    model.compile(optimizer=Adam(learning_rate=3e-5), loss='sparse_categorical_crossentropy', metrics=['accuracy'])
    return model


# Function to extract features from a website
def extract_features(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        webpage = response.text
        return tokenizer.encode_plus(webpage, max_length=512, truncation=True, padding='max_length', return_tensors='tf')
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None


# Function to calculate the 'coolness' of a website
def calculate_coolness(url, model):
    features = extract_features(url)
    if features is not None:
        prediction = model.predict(features)
        coolness_level_index = np.argmax(prediction)
        return COOLNESS_LEVELS[coolness_level_index]
    return None


# Define a function to find and open the coolest website
def find_coolest_website(urls, model):
    coolest_website = None
    highest_coolness = -1

    for url in urls:
        coolness = calculate_coolness(url, model)
        if coolness:
            print(f"Coolness of {url}: {coolness}")
            coolness_index = COOLNESS_LEVELS.index(coolness)

            if coolness_index > highest_coolness:
                highest_coolness = coolness_index
                coolest_website = url

    if coolest_website:
        print(f"Coolest website: {coolest_website}")


# Main execution
websites = ["http://www.yahoo.com", "http://www.google.com", "http://www.bing.com",
            "http://www.duckduckgo.com", "http://www.twitter.com", "http://www.reddit.com"]

# Fit the TF-IDF Vectorizer on the content of the websites
all_text = [requests.get(url).text for url in websites]
vectorizer.fit(all_text)

# Train the Transformer model
model = create_model(len(vectorizer.get_feature_names()))
features = vectorizer.transform([requests.get(url).text for url in websites]).toarray()

# Connect to the PostgreSQL database
conn = psycopg2.connect(
    host="localhost",
    database="trend_crawler",
    user="postgres",
    password="postgres"
)

# Load the user labels from the database
df = pd.read_sql_query("SELECT * from user_labels", conn)

# Close the connection
conn.close()

# Separate features and labels
X = df['id']
y = df['label']

# Split data into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train the Random Forest classifier
forest = RandomForestClassifier(n_estimators=100, random_state=42)
forest.fit(X_train, y_train)

# Get predictions from the Random Forest model
predictions = forest.predict(X_test)

# Update the user labels database with predictions
predictions_df = pd.DataFrame({'id': X_test, 'predicted_label': predictions})

# Connect to PostgreSQL database
conn = psycopg2.connect(
    host="localhost",
    database="trend_crawler",
    user="postgres",
    password="postgres"
)

# Write the predictions to the database
cursor = conn.cursor()
# Create table if it doesn't exist
cursor.execute('''
    CREATE TABLE IF NOT EXISTS predicted_labels (
        id INTEGER,
        predicted_label INTEGER
    )
''')
conn.commit()

# Clear existing data
cursor.execute("DELETE FROM predicted_labels")
conn.commit()

# Insert new predictions
for index, row in predictions_df.iterrows():
    cursor.execute(
        "INSERT INTO predicted_labels (id, predicted_label) VALUES (%s, %s)",
        (row['id'], row['predicted_label'])
    )
conn.commit()

# Close the connection
cursor.close()
conn.close()

# Train the Transformer model with user feedback
user_feedback = []
for url in websites:
    coolness = calculate_coolness(url, model)
    if coolness:
        feedback = input(f"Please rate the coolness of {url} (1-9): ")
        user_feedback.append({"url": url, "rating": int(feedback)})

# Update features and labels based on user feedback
labels = []  # Initialize labels list
for feedback in user_feedback:
    features.append(vectorizer.transform([requests.get(feedback["url"]).text]).toarray()[0])
    labels.append(feedback["rating"] - 1)

# Train the model with combined data
model.fit(features, labels, epochs=10, verbose=1)

# Find the coolest website again
find_coolest_website(websites, model)

# Use the trained model for other tasks like predicting coolness of new website
new_website = "https://www.example.com"

features = extract_features(new_website)
if features is not None:
    prediction = model.predict(features)
    coolness_level_index = np.argmax(prediction)
    print(f"Predicted coolness of {new_website}: {COOLNESS_LEVELS[coolness_level_index]}")
else:
    print("Error fetching website content.")
