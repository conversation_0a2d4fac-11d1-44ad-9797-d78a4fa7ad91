#!/usr/bin/env python3
"""
Database Fix Script for Trend-Crawler

This script fixes database connectivity issues and creates missing tables.
"""

import os
import logging
import psycopg2
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_admin_user():
    """Create the admin user if it doesn't exist already."""
    try:
        from web_dashboard import pwd_context
        
        # Connect to database
        conn = psycopg2.connect(
            host="localhost", 
            port=5432,
            database="scraper_metrics",
            user="postgres",
            password="postgres",
            connect_timeout=5
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Create users table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            username VARCHAR(50) PRIMARY KEY,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name <PERSON><PERSON><PERSON><PERSON>(100),
            disabled BOOLEAN DEFAULT FALSE,
            is_admin BOOLEAN DEFAULT FALSE,
            password_hash VARCHAR(200) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # Check if admin user exists
        cursor.execute("SELECT * FROM users WHERE username = %s", ("admin",))
        if cursor.fetchone() is None:
            # Create admin user
            cursor.execute('''
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash)
            VALUES (%s, %s, %s, %s, %s, %s)
            ''', (
                "admin",
                "<EMAIL>",
                "Admin User",
                False,
                True,
                pwd_context.hash("admin")  # Default password
            ))
            logger.info("Admin user created successfully")
        else:
            logger.info("Admin user already exists")
            
        # Close connection
        cursor.close()
        conn.close()
        return True
    except ImportError:
        logger.error("Could not import pwd_context from web_dashboard module")
        return False
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        return False

def fix_monitoring_db():
    """Fix the database issues in the monitoring module."""
    try:
        # First try to update the config file
        logger.info("Updating database configuration in proxy_config.json...")
        
        from monitoring import monitor
        
        # Reconnect with correct password
        if hasattr(monitor, 'connection') and monitor.connection is None:
            try:
                logger.info("Attempting to reconnect to database...")
                monitor.connection = psycopg2.connect(
                    host="localhost", 
                    port=5432,
                    database="scraper_metrics",
                    user="postgres",
                    password="postgres",  # Use correct password
                    connect_timeout=5
                )
                monitor.connection.autocommit = True
                monitor.initialized = True
                logger.info("Successfully reconnected to PostgreSQL database")
            except Exception as e:
                logger.error(f"Failed to reconnect to database: {e}")
                return False
        
        # Create missing tables
        if monitor.connection:
            try:
                cursor = monitor.connection.cursor()
                
                # Create system_metrics table for system resource tracking
                logger.info("Creating system_metrics table...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        cpu_usage FLOAT,
                        memory_usage FLOAT,
                        disk_usage FLOAT,
                        load_avg_1m FLOAT,
                        load_avg_5m FLOAT,
                        load_avg_15m FLOAT
                    )
                ''')
                
                # Create api_metrics table for API monitoring
                logger.info("Creating api_metrics table...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_metrics (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        endpoint TEXT NOT NULL,
                        status_code INTEGER,
                        response_time FLOAT,
                        success BOOLEAN NOT NULL,
                        error_message TEXT
                    )
                ''')
                
                # Create db_performance table for database metrics
                logger.info("Creating db_performance table...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS db_performance (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        query_type TEXT NOT NULL,
                        query_duration FLOAT,
                        rows_affected INTEGER,
                        success BOOLEAN NOT NULL,
                        error_message TEXT
                    )
                ''')
                
                # Create system_events table for system-level events
                logger.info("Creating system_events table...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_events (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        event_type TEXT NOT NULL,
                        severity TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT
                    )
                ''')
                
                # Create proxies table if it doesn't exist
                logger.info("Creating proxies table...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS proxies (
                        id SERIAL PRIMARY KEY,
                        ip_address TEXT NOT NULL,
                        port INTEGER NOT NULL,
                        proxy_type TEXT NOT NULL,
                        username TEXT,
                        password TEXT,
                        proxy_group TEXT,
                        location TEXT,
                        status TEXT DEFAULT 'active',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_used TIMESTAMP,
                        UNIQUE(ip_address, port)
                    )
                ''')
                
                cursor.close()
                logger.info("All tables created successfully!")
                
                # Populate some initial test data
                populate_test_data(monitor.connection)
                
                return True
            except Exception as e:
                logger.error(f"Error creating database tables: {e}")
                return False
        else:
            logger.error("No database connection available")
            return False
    except ImportError:
        logger.error("Could not import monitor from monitoring module")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def populate_test_data(conn):
    """Populate some initial test data for the dashboard."""
    try:
        cursor = conn.cursor()
        
        # Add some system metrics data
        logger.info("Adding sample system metrics data...")
        for i in range(10):
            cpu = 20 + (i * 5) % 40  # Values between 20-60%
            memory = 30 + (i * 3) % 30  # Values between 30-60%
            disk = 50 + (i % 10)  # Values between 50-60%
            
            cursor.execute('''
                INSERT INTO system_metrics 
                (timestamp, cpu_usage, memory_usage, disk_usage, 
                load_avg_1m, load_avg_5m, load_avg_15m)
                VALUES 
                (NOW() - INTERVAL '%s minutes', %s, %s, %s, %s, %s, %s)
            ''', (
                i * 10,  # Every 10 minutes
                cpu,
                memory,
                disk,
                cpu / 20,  # Load averages based on CPU
                cpu / 30,
                cpu / 40
            ))
        
        # Add some API metrics
        logger.info("Adding sample API metrics data...")
        endpoints = ['/api/stats', '/api/proxies', '/api/system/info', '/api/scraper']
        for i in range(20):
            endpoint = endpoints[i % len(endpoints)]
            success = (i % 4 != 0)  # 75% success rate
            response_time = 0.1 + (i % 10) / 10  # Between 0.1-1.0s
            
            cursor.execute('''
                INSERT INTO api_metrics 
                (timestamp, endpoint, status_code, response_time, success, error_message)
                VALUES 
                (NOW() - INTERVAL '%s minutes', %s, %s, %s, %s, %s)
            ''', (
                i * 5,  # Every 5 minutes
                endpoint,
                200 if success else 500,
                response_time,
                success,
                None if success else "Sample error message"
            ))
        
        # Add some DB performance metrics
        logger.info("Adding sample DB performance metrics...")
        query_types = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
        for i in range(15):
            query_type = query_types[i % len(query_types)]
            success = (i % 5 != 0)  # 80% success rate
            duration = 0.05 + (i % 20) / 100  # Between 0.05-0.25s
            
            cursor.execute('''
                INSERT INTO db_performance 
                (timestamp, query_type, query_duration, rows_affected, success, error_message)
                VALUES 
                (NOW() - INTERVAL '%s minutes', %s, %s, %s, %s, %s)
            ''', (
                i * 7,  # Every 7 minutes
                query_type,
                duration,
                (i % 10) + 1,
                success,
                None if success else "Sample DB error"
            ))
        
        # Add some system events
        logger.info("Adding sample system events...")
        event_types = ['system_startup', 'alert', 'scraper_error', 'proxy_change']
        severities = ['info', 'warning', 'error', 'critical']
        for i in range(8):
            event_type = event_types[i % len(event_types)]
            severity = severities[i % len(severities)]
            
            cursor.execute('''
                INSERT INTO system_events 
                (timestamp, event_type, severity, message, details)
                VALUES 
                (NOW() - INTERVAL '%s hours', %s, %s, %s, %s)
            ''', (
                i * 3,  # Every 3 hours
                event_type,
                severity,
                f"Sample {severity} event of type {event_type}",
                '{"additional": "details", "sample": true}'
            ))
        
        # Add some proxy entries
        logger.info("Adding sample proxy entries...")
        proxy_types = ['residential', 'datacenter']
        groups = ['us', 'eu', 'asia']
        for i in range(5):
            group = groups[i % len(groups)]
            proxy_type = proxy_types[i % len(proxy_types)]
            
            cursor.execute('''
                INSERT INTO proxies 
                (ip_address, port, proxy_type, username, password, proxy_group, location, status)
                VALUES 
                (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (ip_address, port) DO NOTHING
            ''', (
                f"192.168.1.{100 + i}",
                8080 + i,
                proxy_type,
                f"user{i}" if i % 2 == 0 else None,
                f"pass{i}" if i % 2 == 0 else None,
                group,
                f"{group.upper()} Region",
                'active' if i % 3 != 0 else 'inactive'
            ))
        
        conn.commit()
        cursor.close()
        logger.info("Sample data added successfully")
        
    except Exception as e:
        logger.error(f"Error adding sample data: {e}")

if __name__ == "__main__":
    if fix_monitoring_db():
        logger.info("Database fixes applied successfully!")
    else:
        logger.error("Failed to apply database fixes!")
    
    if create_admin_user():
        logger.info("Admin user created or verified successfully!")
    else:
        logger.error("Failed to create admin user!")
