-- Users table for web dashboard authentication
CREATE TABLE IF NOT EXISTS users (
    username VA<PERSON>HA<PERSON>(50) PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100),
    disabled BOOLEAN DEFAULT FALSE,
    is_admin B<PERSON>OLEAN DEFAULT FALSE,
    password_hash VARCHAR(200) NOT NULL,
    needs_password_change BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add admin user if it doesn't exist (should be changed in production)
-- Using 'admin' as the password (bcrypt hash)
INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
VALUES ('admin', '<EMAIL>', 'Admin User', FALSE, TRUE, '$2a$12$1InE4NxfXhKg5LdSNzKP2.JkMJ1HKb4a1UdEJcA3Yd/X5pNGbR4Uy', TRUE)
ON CONFLICT (username) DO NOTHING;

-- Create an index on the username for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
