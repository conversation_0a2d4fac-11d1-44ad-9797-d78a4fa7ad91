# Twitter/X Integration for Trend Crawler

This document provides an overview of the Twitter/X integration in the Trend Crawler project.

## Overview

The Twitter/X integration enhances the Trend Crawler by incorporating social media data into the trend analysis process. This provides a more comprehensive view of trending topics and improves the accuracy of coolness scores.

## Components

### 1. TwitterScraperService Class

The `TwitterScraperService` class is the primary Twitter scraper that uses `twitter_scraper_selenium` to collect data from Twitter/X using:

- **Browser automation** with Selenium and ChromeDriver
- **Parallel processing** for efficient data collection
- **Anti-detection measures** to avoid being blocked

The class provides methods for:
- Scraping tweets related to specific keywords
- Processing and analyzing tweet engagement metrics
- Calculating coolness score boosts based on Twitter data

### 2. TwitterXScraper Class (Fallback)

The `TwitterXScraper` class is a hybrid scraper that can collect data from Twitter/X using:

- **API-based scraping** when a bearer token is available
- **Browser automation** using Playwright for JavaScript rendering

This class serves as a fallback if the primary TwitterScraperService is unavailable.

### 3. Database Integration

The integration adds new database tables:

- `twitter_data` - Stores individual tweets
- `twitter_metrics` - Stores aggregated Twitter metrics
- `twitter_scraping_logs` - Tracks Twitter scraping performance

### 4. Coolness Score Enhancement

Twitter data is used to enhance coolness scores in several ways:

- Tweet volume provides up to a 20% boost (based on volume/1000)
- Tweet engagement (likes) provides up to a 15% boost (based on avg_likes/500)
- Tweet virality (retweets) provides up to a 10% boost (based on avg_retweets/200)
- Tweet sentiment contributes up to a 5% boost (can be negative for negative sentiment)

## Usage

To enable Twitter/X scraping, use the following command-line options:

```bash
python trend_crawler.py --twitter-enabled --twitter-max-tweets 50 --twitter-workers 5
```

- `--twitter-enabled`: Enables Twitter scraping
- `--twitter-max-tweets`: Sets maximum tweets to retrieve per trend (default: 100)
- `--twitter-workers`: Sets number of parallel scraping workers (default: 3)

## Dependencies

The Twitter/X integration requires additional dependencies:

```bash
pip install -r requirements-twitter.txt
```

This will install:
- `twitter_scraper_selenium` for the primary scraper
- `selenium` for browser automation
- `webdriver-manager` for automatic ChromeDriver management
- `pandas` for data processing

For the fallback scraper, you'll also need to run:

```bash
playwright install
```

## API Token (Optional)

For API-based scraping, create a `.env` file in the project root:

```
TWITTER_BEARER_TOKEN=your_api_token_here
```

The system automatically verifies if a valid API token is available:
- If a token is found, it will attempt to validate it with a test API call
- If the token is valid, it will use API-based scraping
- If no token is found or the token is invalid, it will automatically default to browser-based scraping
- If browser scraping fails (e.g., due to Twitter's anti-scraping measures), it will return an empty result set

## Implementation Details

### Hybrid Scraping Approach

The system tries multiple methods in sequence:

1. First attempts to use the TwitterScraperService with Selenium/ChromeDriver
2. If that fails, tries to use the Twitter API if a bearer token is available
3. Falls back to browser automation with Playwright if API fails

### Parallel and Asynchronous Processing

The system uses multiple processing techniques for efficient data collection:

- **TwitterScraperService**:
  - Thread-based parallel processing with ThreadPoolExecutor
  - Configurable number of workers for optimal performance
  - Proper synchronization and error isolation

- **TwitterXScraper** (fallback):
  - Async/await pattern for browser automation
  - Thread-based execution for integration with the main application
  - Proper error handling and fallback mechanisms

### Anti-Detection Measures

To avoid being blocked by Twitter/X:

- **TwitterScraperService**:
  - Randomized user agents from a pool of realistic browser signatures
  - Disabled automation flags in Chrome options
  - Random delays between 2-8 seconds between requests
  - Headless browser operation

- **TwitterXScraper** (fallback):
  - Randomized delays between requests
  - Realistic browser headers and user agent
  - Proper error handling and retry logic

## Future Improvements

Potential enhancements for the Twitter/X integration:

1. Implement OAuth authentication for higher API rate limits
2. Add more sophisticated sentiment analysis using specialized NLP models
3. Incorporate user influence metrics and account verification status
4. Implement real-time streaming for immediate trend detection
5. Add geographic filtering for regional trend analysis
6. Implement proxy rotation for improved scraping reliability
7. Add caching layer to reduce duplicate requests
8. Develop custom browser fingerprinting evasion techniques
9. Implement adaptive scraping rate based on Twitter's response patterns
