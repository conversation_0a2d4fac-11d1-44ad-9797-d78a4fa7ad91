#!/usr/bin/env python3
"""
Test script for Twitter profile analysis integration.
"""

import argparse
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to test Twitter profile analysis."""
    parser = argparse.ArgumentParser(description="Test Twitter profile analysis")
    parser.add_argument("--profiles", nargs="+", default=["elonmusk", "BillGates", "BarackObama"],
                       help="Twitter profiles to analyze")
    parser.add_argument("--trend", type=str, default="artificial intelligence",
                       help="Trend to analyze")
    parser.add_argument("--workers", type=int, default=3,
                       help="Number of worker threads")
    args = parser.parse_args()
    
    # Test profile scraper
    test_profile_scraper(args.profiles)
    
    # Test trend analyzer
    test_trend_analyzer(args.trend, args.profiles)

def test_profile_scraper(profiles: List[str]):
    """
    Test the TwitterProfileScraper.
    
    Args:
        profiles: List of Twitter profiles to scrape
    """
    try:
        from twitter_profile_scraper import TwitterProfileScraper
        
        print("\n=== Testing TwitterProfileScraper ===")
        
        # Initialize the scraper
        scraper = TwitterProfileScraper()
        
        if not scraper.available:
            print("TwitterProfileScraper not available. Please install required dependencies.")
            return
        
        # Scrape profiles
        for username in profiles:
            print(f"\nScraping profile for '{username}'...")
            profile_data = scraper.scrape_profile(username)
            
            # Print profile data
            print(f"Name: {profile_data.get('name', 'N/A')}")
            print(f"Handle: {profile_data.get('handle', 'N/A')}")
            print(f"Bio: {profile_data.get('bio', 'N/A')[:100]}...")
            print(f"Followers: {profile_data.get('followers_count', 0):,}")
            print(f"Following: {profile_data.get('following_count', 0):,}")
            print(f"Verified: {profile_data.get('verified', False)}")
            print(f"Influence Score: {profile_data.get('influence_score', 0.0):.2f}")
        
        # Clean up
        scraper.close()
        
    except ImportError:
        print("TwitterProfileScraper not available. Please install required dependencies.")

def test_trend_analyzer(trend: str, profiles: List[str]):
    """
    Test the TrendAnalyzer.
    
    Args:
        trend: Trend to analyze
        profiles: List of Twitter profiles to include in the trend
    """
    try:
        from trend_analyzer import TrendAnalyzer
        
        print("\n=== Testing TrendAnalyzer ===")
        
        # Create sample trend data
        trend_data = {
            'url': 'https://example.com/article',
            'text': f"Breaking: @{profiles[0]} announces new {trend} initiative with @{profiles[1]}! "
                   f"The future of technology looks promising according to @{profiles[2]}. "
                   f"#{trend.replace(' ', '')} #Innovation",
            'keywords': [trend, "innovation", "technology"],
            'coolness_score': 0.5
        }
        
        # Initialize the analyzer
        analyzer = TrendAnalyzer()
        
        # Analyze trend
        print(f"Analyzing trend: {trend}")
        print(f"Text: {trend_data['text']}")
        print(f"Original Score: {trend_data['coolness_score']:.2f}")
        
        # Perform analysis
        updated_data = analyzer.analyze_trend(trend_data)
        
        # Print results
        print(f"\nResults:")
        print(f"Final Score: {updated_data['coolness_score']:.2f}")
        
        if 'influencers' in updated_data:
            print(f"Influencers: {', '.join(updated_data['influencers'])}")
            print(f"Influencer Score: {updated_data.get('influencer_score', 0):.2f}")
        
        if 'twitter_metrics' in updated_data:
            metrics = updated_data['twitter_metrics']
            print(f"\nTwitter Metrics:")
            print(f"Tweet Volume: {metrics.get('volume', 0)}")
            print(f"Avg Likes: {metrics.get('avg_likes', 0):.1f}")
            print(f"Avg Retweets: {metrics.get('avg_retweets', 0):.1f}")
            print(f"Avg Sentiment: {metrics.get('avg_sentiment', 0):.2f}")
        
        if 'influencer_profiles' in updated_data:
            print("\nInfluencer Profiles:")
            for profile in updated_data['influencer_profiles']:
                print(f"  {profile.get('name', 'Unknown')} (@{profile.get('username', 'unknown')})")
                print(f"  Followers: {profile.get('followers_count', 0):,}")
                print(f"  Influence Score: {profile.get('influence_score', 0):.2f}")
                print()
        
        # Clean up
        analyzer.close()
        
    except ImportError:
        print("TrendAnalyzer not available. Please install required dependencies.")

if __name__ == "__main__":
    main()
