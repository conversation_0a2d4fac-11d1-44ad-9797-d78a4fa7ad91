#!/usr/bin/env python3
"""
Trend Crawler - A tool to discover trending topics across websites.

This script crawls specified websites, analyzes their content using
topic modeling, and calculates "coolness scores" for different topics.
Results can be stored in a PostgreSQL database with pgvector extension.
"""

import requests
from bs4 import BeautifulSoup
import re
import sys
import collections
import argparse
import psycopg2
from psycopg2.extras import execute_values, DictCursor
import logging
import time
from datetime import datetime
import json
import os
import uuid
from typing import Dict, List, Tuple, Optional, Any, Union, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import monitoring system
try:
    from monitoring import monitor
    MONITORING_AVAILABLE = True
    logger.info("Monitoring system available")
except ImportError:
    MONITORING_AVAILABLE = False
    logger.warning("Monitoring system not available")

# Import proxy manager
try:
    from proxy_manager import ProxyManager
    PROXY_MANAGER_AVAILABLE = True
    logger.info("Proxy manager available")
except ImportError:
    PROXY_MANAGER_AVAILABLE = False
    logger.warning("Proxy manager not available")

# Import vector embeddings functionality
try:
    from vector_embeddings import (
        generate_embeddings,
        cluster_embeddings,
        extract_cluster_keywords,
        analyze_vector_similarities,
        find_similar_items
    )
    VECTOR_EMBEDDINGS_AVAILABLE = True
    logger.info("Vector embeddings functionality available")
except ImportError:
    VECTOR_EMBEDDINGS_AVAILABLE = False
    logger.warning("Vector embeddings module not available")

# Import advanced trend analytics functionality
try:
    from trend_analytics import TrendAnalytics
    TREND_ANALYTICS_AVAILABLE = True
    logger.info("Advanced trend analytics functionality available")
except ImportError:
    TREND_ANALYTICS_AVAILABLE = False
    logger.warning("Advanced trend analytics module not available")

# Optional imports - will be used if available
try:
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import mlflow
    import mlflow.pytorch
    MLFLOW_AVAILABLE = True
except ImportError:
    MLFLOW_AVAILABLE = False

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False

# Optional imports - will be used if available
try:
    from bertopic import BERTopic
    from sentence_transformers import SentenceTransformer
    import torch
    import optuna
    ADVANCED_FEATURES = True
except ImportError:
    ADVANCED_FEATURES = False

# Check if all required components are available
ALL_ADVANCED_FEATURES = ADVANCED_FEATURES and SKLEARN_AVAILABLE and MLFLOW_AVAILABLE

# This section was moved to the top of the file

# Import the TwitterXScraper
try:
    from twitter_x_scraper import TwitterXScraper, update_twitter_metrics
    TWITTER_X_SCRAPER_AVAILABLE = True
    logger.info("TwitterXScraper available")
except ImportError:
    TWITTER_X_SCRAPER_AVAILABLE = False
    logger.warning("TwitterXScraper not available. Twitter features will be limited.")

# Import the TwitterScraperService
try:
    from twitter_scraper_service import TwitterScraperService, calculate_coolness_score_with_twitter
    TWITTER_SCRAPER_SERVICE_AVAILABLE = True
    logger.info("TwitterScraperService available")
except ImportError:
    TWITTER_SCRAPER_SERVICE_AVAILABLE = False
    logger.warning("TwitterScraperService not available. Install with: pip install -r requirements-twitter.txt")

# Import ScrapingShield for advanced anti-scraping capabilities
try:
    from scraping_shield import ScrapingShield
    SCRAPING_SHIELD_AVAILABLE = True
    logger.info("ScrapingShield available - advanced anti-scraping enabled")
except ImportError:
    SCRAPING_SHIELD_AVAILABLE = False
    logger.warning("ScrapingShield not available. Anti-scraping features disabled.")

# Enhanced stopwords list
STOPWORDS = {
    'a', 'an', 'the', 'and', 'or', 'but', 'if', 'because', 'as', 'what',
    'when', 'where', 'how', 'who', 'which', 'this', 'that', 'these', 'those',
    'then', 'just', 'so', 'than', 'such', 'both', 'through', 'about', 'for',
    'is', 'of', 'while', 'during', 'to', 'from', 'in', 'on', 'by', 'with',
    'at', 'be', 'am', 'are', 'was', 'were', 'has', 'have', 'had', 'do', 'does',
    'did', 'doing', 'can', 'could', 'would', 'should', 'will', 'shall', 'may',
    'might', 'must', 'here', 'there', 'now', 'then', 'always', 'often',
    'very', 'quite', 'too', 'enough', 'even', 'also', 'back', 'down', 'ever',
    'few', 'further', 'forward', 'hard', 'high', 'later', 'many', 'near',
    'nearby', 'almost', 'mostly', 'nearly', 'roughly', 'approximately',
    'same', 'similar', 'such', 'still', 'rather', 'quite'
}

def find_similar_trends(
    db,
    url: str = None,
    text: str = None,
    embedding: List[float] = None,
    model_name: str = "all-MiniLM-L6-v2",
    limit: int = 5,
    similarity_threshold: float = 0.7
) -> List[Dict[str, Any]]:
    """
    Find similar trends using vector similarity search.

    Args:
        db: Database connection
        url: URL of trend to find similar trends for (will look up embedding)
        text: Text to encode and find similar trends for
        embedding: Pre-computed embedding to search with
        model_name: Model name for encoding text (if provided)
        limit: Maximum number of results to return
        similarity_threshold: Minimum similarity score (0-1)

    Returns:
        List of similar trends with similarity scores
    """
    if not db:
        logger.error("Database connection required for similar trends search")
        return []

    if not url and not text and not embedding:
        logger.error("Either url, text or embedding must be provided")
        return []

    try:
        cursor = db.cursor(cursor_factory=DictCursor)

        # Get the embedding to search with
        search_embedding = None

        if embedding:
            # Use provided embedding
            search_embedding = embedding
        elif url:
            # Look up embedding for URL
            cursor.execute(
                "SELECT embedding FROM coolness_data WHERE url = %s "
                "ORDER BY crawl_timestamp DESC LIMIT 1",
                (url,)
            )
            result = cursor.fetchone()
            if result and result['embedding']:
                search_embedding = result['embedding']

        if not search_embedding and text:
            # Generate embedding for text
            try:
                embeddings = generate_embeddings([text], model_name=model_name)
                if embeddings is not None and len(embeddings) > 0:
                    search_embedding = embeddings[0].tolist()
            except Exception as e:
                logger.error(f"Error generating embedding for text: {e}")

        if not search_embedding:
            logger.error("Could not obtain embedding for similarity search")
            return []

        # Perform vector similarity search
        cursor.execute("""
            SELECT
                cd.url,
                cd.coolness_score,
                c.category_name,
                1 - (cd.embedding <=> %s::vector) AS similarity_score
            FROM
                coolness_data cd
            LEFT JOIN
                categories c ON cd.category_id = c.id
            WHERE
                1 - (cd.embedding <=> %s::vector) > %s
            ORDER BY
                similarity_score DESC
            LIMIT %s
        """, (
            str(search_embedding),
            str(search_embedding),
            similarity_threshold,
            limit
        ))

        # Process results
        results = []
        for row in cursor:
            results.append({
                'url': row['url'],
                'coolness_score': row['coolness_score'],
                'category': row['category_name'],
                'similarity_score': row['similarity_score']
            })

        return results

    except Exception as e:
        logger.error(f"Error in similar trends search: {e}")
        return []


def analyze_trends_with_vectors(
    coolness_data: Dict[str, Dict[str, Any]],
    embeddings: np.ndarray,
    cluster_algorithm: str = "hdbscan",
    n_clusters: int = 5
) -> Dict[str, Dict[str, Any]]:
    """
    Analyze trends using vector embeddings and clustering.

    Args:
        coolness_data: Dictionary of coolness data
        embeddings: Document embeddings for vector analysis
        cluster_algorithm: Clustering algorithm to use
        n_clusters: Number of clusters for algorithms that need it

    Returns:
        Updated coolness_data dictionary with cluster information
    """
    if not VECTOR_EMBEDDINGS_AVAILABLE or not SKLEARN_AVAILABLE:
        logger.warning("Vector embeddings or sklearn not available. Can't perform vector trend analysis.")
        return coolness_data

    if embeddings is None or len(embeddings) == 0:
        logger.warning("No embeddings provided for vector trend analysis")
        return coolness_data

    try:
        # Get URLs in the same order as we have embeddings
        urls = [url for url in coolness_data.keys() if url != "__metadata__"]
        texts = [coolness_data[url].get('text', '') for url in urls]

        # Perform clustering on the embeddings
        logger.info(f"Clustering {len(embeddings)} documents using {cluster_algorithm}")
        cluster_assignments, cluster_metadata = cluster_embeddings(
            embeddings,
            algorithm=cluster_algorithm,
            n_clusters=n_clusters
        )

        if not cluster_assignments:
            logger.error("Clustering failed")
            return coolness_data

        # Extract keywords for each cluster
        cluster_keywords = {}
        for cluster_id in set(cluster_assignments):
            if cluster_id == -1:  # Skip noise cluster
                continue
            keywords = extract_cluster_keywords(texts, cluster_assignments, cluster_id, stopwords=STOPWORDS)
            cluster_keywords[cluster_id] = keywords

        # Calculate coherence (similarity) within each cluster
        cluster_similarities = analyze_vector_similarities(embeddings, cluster_assignments)

        # Update coolness_data with cluster information
        for i, url in enumerate(urls):
            if i < len(cluster_assignments):
                # Assign cluster ID
                cluster_id = cluster_assignments[i]
                coolness_data[url]['vector_cluster_id'] = cluster_id

                # Add cluster keywords if not a noise point
                if cluster_id != -1:
                    coolness_data[url]['vector_cluster_keywords'] = cluster_keywords.get(cluster_id, [])
                    coolness_data[url]['vector_cluster_coherence'] = cluster_similarities.get(cluster_id, 0.0)
                else:
                    coolness_data[url]['vector_cluster_keywords'] = []
                    coolness_data[url]['vector_cluster_coherence'] = 0.0

        # Add global metadata for the vectors and clusters
        metadata = {
            'vector_analysis': {
                'algorithm': cluster_algorithm,
                'n_clusters': len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0),
                'n_noise': list(cluster_assignments).count(-1),
                'cluster_keywords': cluster_keywords,
                'cluster_similarities': cluster_similarities,
                'cluster_metadata': cluster_metadata
            }
        }

        # Store the metadata in a special key
        coolness_data['__metadata__'] = metadata

        logger.info(f"Vector-based trend analysis complete: {len(cluster_keywords)} clusters identified")
        return coolness_data
    except Exception as e:
        logger.error(f"Error in vector-based trend analysis: {e}")
        return coolness_data


def connect_to_database(host: str, user: str, password: str, db_name: str, db_port: str = '5432') -> Any:
    """
    Connect to PostgreSQL database with pgvector extension.

    Args:
        host: Database host
        user: Database username
        password: Database password
        db_name: Database name
        db_port: Database port

    Returns:
        Database connection object

    Raises:
        Exception: If database connection fails
    """
    try:
        db = psycopg2.connect(
            host=host,
            port=db_port,
            dbname=db_name,
            user=user,
            password=password
        )

        # Verify pgvector extension is available
        cursor = db.cursor()
        try:
            cursor.execute("SELECT COUNT(*) FROM pg_available_extensions WHERE name = 'vector' AND installed_version IS NOT NULL")
            if cursor.fetchone()[0] == 0:
                logger.warning("pgvector extension is not installed. Vector similarity search will not work.")
            else:
                logger.info("pgvector extension is installed and available.")
        except Exception as ext_e:
            logger.warning(f"Could not verify pgvector extension: {ext_e}")
        finally:
            cursor.close()

        logger.info("Connected to PostgreSQL database")
        return db
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise


def crawl_website(
    url: str, 
    proxy_manager: Optional['ProxyManager'] = None, 
    use_scraping_shield: bool = True
) -> Optional[str]:
    """
    Crawl a website and extract its text content.

    Args:
        url: URL to crawl
        proxy_manager: Optional proxy manager for proxy rotation
        use_scraping_shield: Whether to use ScrapingShield for anti-scraping

    Returns:
        Cleaned text content or None if crawling fails
    """
    retries = 3
    start_time = time.time()
    proxy_id = None
    proxy_config = None
    scraping_shield = None

    # Fix for Twitter/X.com URL handling
    if url.lower() == "https://www.x.com" or url.lower() == "https://x.com":
        # Use the correct URL for Twitter/X
        url = "https://twitter.com"
        logger.info(f"Redirecting x.com to twitter.com for compatibility")

    # Initialize ScrapingShield if available and requested
    if use_scraping_shield and SCRAPING_SHIELD_AVAILABLE:
        try:
            scraping_shield = ScrapingShield()
            logger.info("ScrapingShield initialized for enhanced anti-scraping protection")
        except Exception as e:
            logger.warning(f"Failed to initialize ScrapingShield: {e}. Falling back to standard crawling.")
            scraping_shield = None

    # Set up headers to mimic a browser (enhanced if ScrapingShield is available)
    if scraping_shield:
        headers = scraping_shield.create_enhanced_headers()
        logger.debug("Using ScrapingShield enhanced headers")
    else:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }

    # Get proxy configuration if proxy manager is available
    if proxy_manager and PROXY_MANAGER_AVAILABLE:
        try:
            proxy_config = proxy_manager.get_proxy()
            if proxy_config:
                proxy_id = proxy_config.get('id')
                logger.info(f"Using proxy {proxy_id} for {url}")
        except Exception as e:
            logger.warning(f"Failed to get proxy: {e}")

    for attempt in range(retries):
        try:
            # Set up proxy configuration for requests
            proxies = None
            if proxy_config:
                proxy_url = f"http://{proxy_config['host']}:{proxy_config['port']}"
                if proxy_config.get('username') and proxy_config.get('password'):
                    proxy_url = f"http://{proxy_config['username']}:{proxy_config['password']}@{proxy_config['host']}:{proxy_config['port']}"
                proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }

            # Use ScrapingShield if available, otherwise fall back to standard requests
            if scraping_shield:
                try:
                    # Use ScrapingShield's make_request method which includes anti-detection
                    response = scraping_shield.make_request(url, headers=headers, proxies=proxies, timeout=15)
                    logger.debug("Used ScrapingShield for request")
                except Exception as shield_error:
                    logger.warning(f"ScrapingShield request failed: {shield_error}. Falling back to standard request.")
                    response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
            else:
                response = requests.get(url, headers=headers, proxies=proxies, timeout=15)  # Increased timeout
            
            response_time = time.time() - start_time

            # Check for CAPTCHA or other anti-scraping measures if ScrapingShield is available
            if scraping_shield and hasattr(response, 'text'):
                captcha_detected = scraping_shield.detect_captcha(response.text)
                honeypot_detected = scraping_shield.detect_honeypot(response)
                
                if captcha_detected:
                    logger.warning(f"CAPTCHA detected on {url}")
                    # Try to handle CAPTCHA if possible
                    try:
                        if hasattr(scraping_shield, 'handle_advanced_captcha'):
                            solved = scraping_shield.handle_advanced_captcha(response.text)
                            if solved:
                                logger.info("CAPTCHA solved successfully")
                            else:
                                logger.warning("CAPTCHA solving failed")
                    except Exception as captcha_error:
                        logger.warning(f"CAPTCHA handling error: {captcha_error}")
                
                if honeypot_detected:
                    logger.warning(f"Honeypot detected on {url}")
                    # Simulate human behavior to avoid detection
                    if hasattr(scraping_shield, 'simulate_human_behavior'):
                        scraping_shield.simulate_human_behavior()
            else:
                captcha_detected = False
                honeypot_detected = False

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                # Remove scripts and styles
                for script in soup(["script", "style"]):
                    script.extract()
                text = soup.get_text()
                # Clean text: remove excessive whitespace
                text = re.sub(r'\s+', ' ', text).strip()
                logger.info(f"Successfully crawled {url}")

                # Record successful metrics to monitoring system
                if MONITORING_AVAILABLE:
                    metrics = {
                        'url': url,
                        'status_code': response.status_code,
                        'success': True,
                        'response_time': response_time,
                        'captcha_detected': False,
                        'error_message': None
                    }
                    monitor.record_request('trend_crawler', metrics)

                # Record successful proxy metrics if proxy was used
                if proxy_manager and proxy_id and MONITORING_AVAILABLE:
                    proxy_metrics = {
                        'proxy_id': proxy_id,
                        'success': True,
                        'response_time': response_time,
                        'status_code': response.status_code,
                        'error_message': None
                    }
                    monitor.record_proxy_request(proxy_id, proxy_metrics)
                    # Update proxy manager stats
                    proxy_manager.update_proxy_stats(proxy_id, True, response_time)

                return text
            else:
                logger.error(
                    f"Failed to crawl {url} (Status: {response.status_code})"
                )

                # Record failed metrics to monitoring system
                if MONITORING_AVAILABLE:
                    metrics = {
                        'url': url,
                        'status_code': response.status_code,
                        'success': False,
                        'response_time': response_time,
                        'captcha_detected': False,
                        'error_message': f"HTTP {response.status_code}"
                    }
                    monitor.record_request('trend_crawler', metrics)

                # Record failed proxy metrics if proxy was used
                if proxy_manager and proxy_id and MONITORING_AVAILABLE:
                    proxy_metrics = {
                        'proxy_id': proxy_id,
                        'success': False,
                        'response_time': response_time,
                        'status_code': response.status_code,
                        'error_message': f"HTTP {response.status_code}"
                    }
                    monitor.record_proxy_request(proxy_id, proxy_metrics)
                    # Update proxy manager stats
                    proxy_manager.update_proxy_stats(proxy_id, False, response_time, f"HTTP {response.status_code}")

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            logger.error(f"Error crawling {url}: {e}")

            # Record failed metrics to monitoring system
            if MONITORING_AVAILABLE:
                metrics = {
                    'url': url,
                    'status_code': None,
                    'success': False,
                    'response_time': response_time,
                    'captcha_detected': False,
                    'error_message': str(e)
                }
                monitor.record_request('trend_crawler', metrics)

            # Record failed proxy metrics if proxy was used
            if proxy_manager and proxy_id and MONITORING_AVAILABLE:
                proxy_metrics = {
                    'proxy_id': proxy_id,
                    'success': False,
                    'response_time': response_time,
                    'status_code': None,
                    'error_message': str(e)
                }
                monitor.record_proxy_request(proxy_id, proxy_metrics)
                # Update proxy manager stats
                proxy_manager.update_proxy_stats(proxy_id, False, response_time, str(e))

            time.sleep(2 ** attempt)  # Exponential backoff

    logger.error(f"Failed to crawl {url} after {retries} attempts")

    # Record final failure metrics
    if MONITORING_AVAILABLE:
        final_response_time = time.time() - start_time
        metrics = {
            'url': url,
            'status_code': None,
            'success': False,
            'response_time': final_response_time,
            'captcha_detected': False,
            'error_message': f"Failed after {retries} attempts"
        }
        monitor.record_request('trend_crawler', metrics)

    # Record final proxy failure if proxy was used
    if proxy_manager and proxy_id and MONITORING_AVAILABLE:
        final_response_time = time.time() - start_time
        proxy_metrics = {
            'proxy_id': proxy_id,
            'success': False,
            'response_time': final_response_time,
            'status_code': None,
            'error_message': f"Failed after {retries} attempts"
        }
        monitor.record_proxy_request(proxy_id, proxy_metrics)
        # Update proxy manager stats
        proxy_manager.update_proxy_stats(proxy_id, False, final_response_time, f"Failed after {retries} attempts")

    return None


def create_pgvector_indexes(db: Any, index_type: str = 'hnsw', recreate: bool = False) -> None:
    """
    Create or recreate pgvector indexes for vector similarity search.

    Args:
        db: Database connection object
        index_type: Type of index to create ('hnsw', 'ivfflat', or 'exact')
        recreate: Whether to drop and recreate existing indexes
    """
    cursor = db.cursor()

    try:
        # Check if pgvector extension is available
        cursor.execute("SELECT COUNT(*) FROM pg_available_extensions WHERE name = 'vector' AND installed_version IS NOT NULL")
        if cursor.fetchone()[0] == 0:
            logger.error("pgvector extension is not installed. Cannot create vector indexes.")
            return

        # Drop existing indexes if recreate is True
        if recreate:
            logger.info("Dropping existing pgvector indexes...")
            cursor.execute("DROP INDEX IF EXISTS idx_coolness_data_embedding_hnsw")
            cursor.execute("DROP INDEX IF EXISTS idx_coolness_data_embedding_ivfflat")
            cursor.execute("DROP INDEX IF EXISTS idx_coolness_data_embedding_exact")
            cursor.execute("DROP INDEX IF EXISTS idx_twitter_data_embedding_hnsw")
            cursor.execute("DROP INDEX IF EXISTS idx_twitter_data_embedding_ivfflat")
            cursor.execute("DROP INDEX IF EXISTS idx_twitter_data_embedding_exact")

        # Create the appropriate index type
        if index_type == 'hnsw':
            # HNSW index (Hierarchical Navigable Small World) - fastest search
            logger.info("Creating HNSW indexes for vector similarity search...")
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_coolness_data_embedding_hnsw
            ON coolness_data USING hnsw (embedding vector_cosine_ops)
            WITH (m=16, ef_construction=64)
            """)

            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_twitter_data_embedding_hnsw
            ON twitter_data USING hnsw (embedding vector_cosine_ops)
            WITH (m=16, ef_construction=64)
            """)

        elif index_type == 'ivfflat':
            # IVFFlat index - balanced performance
            logger.info("Creating IVFFlat indexes for vector similarity search...")
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_coolness_data_embedding_ivfflat
            ON coolness_data USING ivfflat (embedding vector_cosine_ops)
            WITH (lists=100)
            """)

            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_twitter_data_embedding_ivfflat
            ON twitter_data USING ivfflat (embedding vector_cosine_ops)
            WITH (lists=100)
            """)

        elif index_type == 'exact':
            # Exact index - most accurate but slowest
            logger.info("Creating exact indexes for vector similarity search...")
            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_coolness_data_embedding_exact
            ON coolness_data USING btree (embedding)
            """)

            cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_twitter_data_embedding_exact
            ON twitter_data USING btree (embedding)
            """)

        db.commit()
        logger.info(f"Successfully created {index_type} indexes for vector similarity search")

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating pgvector indexes: {e}")
    finally:
        cursor.close()


def create_tables_if_not_exist(db: Any) -> None:
    """
    Create database tables if they don't exist.

    Args:
        db: Database connection object
    """
    cursor = db.cursor()

    # Enable the pgvector extension
    cursor.execute("CREATE EXTENSION IF NOT EXISTS vector")

    # Create model_versions table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS model_versions (
        id SERIAL PRIMARY KEY,
        model_name VARCHAR(255) NOT NULL,
        version VARCHAR(50) NOT NULL,
        parameters TEXT,
        metrics TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT FALSE,
        UNIQUE (model_name, version)
    )
    """)

    # Create categories table with model_version_id
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        category_name VARCHAR(255) NOT NULL,
        topic_label VARCHAR(255),
        model_version_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (category_name, model_version_id),
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create coolness_data table with vector embedding support
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS coolness_data (
        id SERIAL PRIMARY KEY,
        url VARCHAR(255) NOT NULL,
        coolness_score FLOAT NOT NULL,
        velocity_score FLOAT,
        impact_score FLOAT,
        novelty_score FLOAT,
        category_id INT,
        crawl_timestamp TIMESTAMP,
        embedding VECTOR(384),
        UNIQUE (url, crawl_timestamp),
        FOREIGN KEY (category_id) REFERENCES categories(id)
    )
    """)

    # Create training_logs table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS training_logs (
        id SERIAL PRIMARY KEY,
        model_version_id INT,
        training_start TIMESTAMP,
        training_end TIMESTAMP,
        dataset_size INT,
        loss FLOAT,
        accuracy FLOAT,
        parameters TEXT,
        metrics TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create distributed_training_nodes table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS distributed_training_nodes (
        id SERIAL PRIMARY KEY,
        training_log_id INT,
        node_id VARCHAR(50),
        node_rank INT,
        device_info TEXT,
        performance_metrics TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (training_log_id) REFERENCES training_logs(id)
    )
    """)

    # Create twitter_data table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS twitter_data (
        id SERIAL PRIMARY KEY,
        tweet_id VARCHAR(255) NOT NULL UNIQUE,
        text TEXT NOT NULL,
        user_name VARCHAR(255),
        likes INT,
        retweets INT,
        replies INT,
        quoted_tweets INT,
        sentiment_score FLOAT,
        crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        trend_id INT,
        embedding VECTOR(384),
        FOREIGN KEY (trend_id) REFERENCES coolness_data(id)
    )
    """)

    # Create twitter_metrics table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS twitter_metrics (
        id SERIAL PRIMARY KEY,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        trending_hashtag VARCHAR(255),
        tweet_volume INT,
        coolness_score FLOAT,
        model_version_id INT,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create twitter_scraping_logs table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS twitter_scraping_logs (
        id SERIAL PRIMARY KEY,
        scrape_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        search_term VARCHAR(512) NOT NULL,
        tweets_found INT,
        success_rate FLOAT,
        error_message TEXT,
        model_version_id INT,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create twitter_profiles table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS twitter_profiles (
        id SERIAL PRIMARY KEY,
        profile_id VARCHAR(255) NOT NULL UNIQUE,
        username VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        bio TEXT,
        category VARCHAR(255),
        website VARCHAR(255),
        joining_date DATE,
        followers_count INT,
        following_count INT,
        verified BOOLEAN DEFAULT FALSE,
        last_scraped TIMESTAMP,
        influence_score FLOAT,
        trend_id INT,
        FOREIGN KEY (trend_id) REFERENCES coolness_data(id)
    )
    """)

    # Create profile_relationships table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS profile_relationships (
        id SERIAL PRIMARY KEY,
        profile_id VARCHAR(255) NOT NULL,
        related_trend_id INT NOT NULL,
        relationship_type VARCHAR(50) CHECK (relationship_type IN ('author', 'mention', 'influencer')),
        FOREIGN KEY (profile_id) REFERENCES twitter_profiles(profile_id),
        FOREIGN KEY (related_trend_id) REFERENCES coolness_data(id)
    )
    """)

    # Create user_feedback table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS user_feedback (
        id SERIAL PRIMARY KEY,
        url VARCHAR(255) NOT NULL,
        rating INT CHECK (rating BETWEEN 1 AND 9),
        user_id VARCHAR(255),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        model_version_id INT,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create model_predictions table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS model_predictions (
        id SERIAL PRIMARY KEY,
        url VARCHAR(255) NOT NULL,
        prediction JSONB NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        model_version_id INT,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Alter coolness_data table to add Twitter-related columns
    cursor.execute("""
    ALTER TABLE coolness_data
    ADD COLUMN IF NOT EXISTS twitter_volume INT DEFAULT 0,
    ADD COLUMN IF NOT EXISTS twitter_sentiment FLOAT DEFAULT 0.0,
    ADD COLUMN IF NOT EXISTS twitter_engagement FLOAT DEFAULT 0.0
    """)

    # Create active_learning_decisions table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS active_learning_decisions (
        id SERIAL PRIMARY KEY,
        url VARCHAR(255) NOT NULL,
        uncertainty_score FLOAT,
        diversity_score FLOAT,
        selected_for_labeling BOOLEAN DEFAULT FALSE,
        human_feedback TEXT,
        feedback_timestamp TIMESTAMP,
        model_version_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create concept_drift_logs table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS concept_drift_logs (
        id SERIAL PRIMARY KEY,
        detection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        drift_score FLOAT,
        distribution_before TEXT,
        distribution_after TEXT,
        action_taken VARCHAR(255),
        model_version_id INT,
        FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
    )
    """)

    # Create index for vector similarity search
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS idx_coolness_data_embedding_hnsw ON coolness_data
    USING hnsw (embedding vector_cosine_ops);
    """)

    # Create index for vector similarity search for twitter data
    cursor.execute("""
    CREATE INDEX IF NOT EXISTS idx_twitter_data_embedding_hnsw ON twitter_data
    USING hnsw (embedding vector_cosine_ops);
    """)

    db.commit()


def get_or_create_category_id(
    db: Any, category_name: str, topic_label: str
) -> int:
    """
    Get or create a category ID.

    Args:
        db: Database connection object
        category_name: Category name
        topic_label: Topic label

    Returns:
        Category ID
    """
    cursor = db.cursor()
    cursor.execute(
        "SELECT id FROM categories WHERE category_name = %s",
        (category_name,)
    )
    result = cursor.fetchone()
    if result:
        return result[0]

    cursor.execute(
        "INSERT INTO categories (category_name, topic_label) VALUES (%s, %s) RETURNING id",
        (category_name, topic_label)
    )
    result = cursor.fetchone()
    db.commit()
    return result[0]


def extract_keywords(text: str) -> List[str]:
    """
    Extract keywords from text using simple frequency analysis.

    Args:
        text: Text to analyze

    Returns:
        List of keywords
    """
    # Simple tokenization using regex
    words = re.findall(r'\b\w+\b', text.lower())

    # Filter out stopwords and count word frequencies
    filtered_words = [
        word for word in words
        if word not in STOPWORDS and len(word) > 2
    ]
    word_counts = collections.Counter(filtered_words)

    # Get the most common words
    keywords = [word for word, _ in word_counts.most_common(10)]
    return keywords


def simple_topic_modeling(
    texts: List[str], num_topics: int = 5
) -> Tuple[List[int], List[str]]:
    """
    Simple topic modeling using word frequency.

    Args:
        texts: List of texts to analyze
        num_topics: Number of topics to extract

    Returns:
        Tuple of (topic assignments, topic labels)
    """
    # Process all texts
    all_words = []
    for text in texts:
        words = re.findall(r'\b\w+\b', text.lower())
        filtered_words = [
            word for word in words
            if word not in STOPWORDS and len(word) > 2
        ]
        all_words.extend(filtered_words)

    # Count word frequencies
    word_counts = collections.Counter(all_words)

    # Create topics based on most common words
    topics = []
    for i in range(min(num_topics, len(word_counts))):
        if i < len(word_counts.most_common(num_topics)):
            word, _ = word_counts.most_common(num_topics)[i]
            topics.append(f"category_{i+1}_{word}")
        else:
            topics.append(f"category_{i+1}")

    # Assign topics to texts based on word overlap
    topic_assignments = []
    for text in texts:
        words = set(re.findall(r'\b\w+\b', text.lower()))
        filtered_words = {
            word for word in words
            if word not in STOPWORDS and len(word) > 2
        }

        # Find topic with most word overlap
        best_topic = 0
        best_score = 0
        for i, topic in enumerate(topics):
            topic_word = topic.split('_')[-1]
            score = 1 if topic_word in filtered_words else 0
            if score > best_score:
                best_score = score
                best_topic = i

        topic_assignments.append(best_topic)

    return topic_assignments, topics


class ActiveLearningController:
    """
    Controller for active learning functionality.

    This class handles uncertainty sampling, diversity-aware selection,
    and human feedback integration for active learning.
    """

    def __init__(self, embedding_model=None, db=None, model_version_id=None):
        """
        Initialize the active learning controller.

        Args:
            embedding_model: Model to generate embeddings
            db: Database connection
            model_version_id: Current model version ID
        """
        self.embedding_model = embedding_model
        self.db = db
        self.model_version_id = model_version_id
        self.memory_buffer = []
        self.labeled_samples = set()

    def calculate_uncertainty(self, probs: np.ndarray) -> np.ndarray:
        """
        Calculate uncertainty scores based on prediction probabilities.

        Args:
            probs: Probability distribution over topics

        Returns:
            Array of uncertainty scores
        """
        # Entropy-based uncertainty
        # Higher entropy = higher uncertainty
        entropy = -np.sum(probs * np.log2(probs + 1e-10), axis=1)
        return entropy / np.log2(probs.shape[1])  # Normalize

    def calculate_diversity(self, embeddings: np.ndarray,
                           selected_indices: Set[int] = None) -> np.ndarray:
        """
        Calculate diversity scores for samples.

        Args:
            embeddings: Document embeddings
            selected_indices: Indices of already selected samples

        Returns:
            Array of diversity scores
        """
        if selected_indices is None:
            selected_indices = set()

        n_samples = embeddings.shape[0]
        diversity_scores = np.ones(n_samples)

        if not selected_indices:
            # If no samples selected yet, use K-means to find diverse points
            n_clusters = min(5, n_samples)
            if n_samples > n_clusters:
                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                clusters = kmeans.fit_predict(embeddings)

                # Count samples in each cluster
                cluster_counts = np.bincount(clusters, minlength=n_clusters)

                # Samples in smaller clusters are more diverse
                for i in range(n_samples):
                    diversity_scores[i] = 1.0 / max(cluster_counts[clusters[i]], 1)
        else:
            # Calculate average similarity to already selected samples
            selected_embeddings = embeddings[list(selected_indices)]
            similarities = cosine_similarity(embeddings, selected_embeddings)
            avg_similarities = np.mean(similarities, axis=1)

            # Convert similarity to diversity (lower similarity = higher diversity)
            diversity_scores = 1.0 - avg_similarities

        return diversity_scores

    def query_samples(self, texts: List[str], embeddings: np.ndarray,
                     topic_probs: np.ndarray, k: int = 5) -> List[int]:
        """
        Select samples for human labeling using uncertainty and diversity.

        Args:
            texts: List of text samples
            embeddings: Document embeddings
            topic_probs: Topic probability distributions
            k: Number of samples to select

        Returns:
            Indices of selected samples
        """
        # Calculate uncertainty scores
        uncertainty_scores = self.calculate_uncertainty(topic_probs)

        # Initialize with empty set of selected indices
        selected_indices = set()

        # Iteratively select diverse and uncertain samples
        for _ in range(min(k, len(texts))):
            # Calculate diversity based on current selections
            diversity_scores = self.calculate_diversity(embeddings, selected_indices)

            # Combine uncertainty and diversity (weighted sum)
            combined_scores = 0.7 * uncertainty_scores + 0.3 * diversity_scores

            # Mask already selected indices
            mask = np.ones(len(texts), dtype=bool)
            mask[list(selected_indices)] = False
            masked_scores = np.copy(combined_scores)
            masked_scores[~mask] = -np.inf

            # Select the highest scoring sample
            next_idx = np.argmax(masked_scores)
            selected_indices.add(next_idx)

            # Store decision in database if available
            if self.db and self.model_version_id:
                self._log_selection_to_db(
                    texts[next_idx],
                    uncertainty_scores[next_idx],
                    diversity_scores[next_idx]
                )

        return list(selected_indices)

    def _log_selection_to_db(self, text: str, uncertainty: float,
                           diversity: float) -> None:
        """
        Log active learning selection to database.

        Args:
            text: Selected text
            uncertainty: Uncertainty score
            diversity: Diversity score
        """
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO active_learning_decisions
                (url, uncertainty_score, diversity_score,
                selected_for_labeling, model_version_id)
                VALUES (%s, %s, %s, %s, %s)
            """, (text[:255], uncertainty, diversity, True, self.model_version_id))
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to log active learning decision: {e}")

    def incorporate_feedback(self, sample_idx: int, feedback: str) -> None:
        """
        Incorporate human feedback for a sample.

        Args:
            sample_idx: Index of the sample
            feedback: Human feedback text
        """
        self.labeled_samples.add(sample_idx)

        # Store feedback in memory buffer for later training
        self.memory_buffer.append({
            'sample_idx': sample_idx,
            'feedback': feedback,
            'timestamp': datetime.now()
        })

        # Update database if available
        if self.db and self.model_version_id:
            try:
                cursor = self.db.cursor()
                cursor.execute("""
                    UPDATE active_learning_decisions
                    SET human_feedback = %s, feedback_timestamp = %s
                    WHERE model_version_id = %s AND id = %s
                """, (feedback, datetime.now(), self.model_version_id, sample_idx))
                self.db.commit()
            except Exception as e:
                logger.error(f"Failed to update feedback: {e}")


class MetaTrainer:
    """
    Meta-learning system for continuous model improvement.

    This class handles experience replay, continuous learning,
    and model version tracking.
    """

    def __init__(self, db=None, model_name="BERTopicModel"):
        """
        Initialize the meta-trainer.

        Args:
            db: Database connection
            model_name: Name of the model
        """
        self.db = db
        self.model_name = model_name
        self.current_version = None
        self.current_model = None
        self.memory = []
        self.update_interval = 100  # Steps between updates
        self.step = 0
        self.drift_threshold = 0.3
        self.auto_retrain = True
        self.distributed = False
        self.world_size = 1
        self.rank = 0

        # Initialize MLflow tracking if available
        if MLFLOW_AVAILABLE:
            mlflow.set_tracking_uri("./mlruns")
            mlflow.set_experiment(f"trend-crawler-{model_name}")

        # Try to import distributed training libraries
        try:
            import torch.distributed as dist
            import torch.multiprocessing as mp
            from torch.nn.parallel import DistributedDataParallel as DDP
            self.dist = dist
            self.mp = mp
            self.DDP = DDP
            self.distributed_available = True
        except ImportError:
            self.distributed_available = False

    def register_model_version(self, model, params: Dict[str, Any],
                             metrics: Dict[str, float]) -> int:
        """
        Register a new model version.

        Args:
            model: The model to register
            params: Model parameters
            metrics: Model metrics

        Returns:
            Model version ID
        """
        version = str(uuid.uuid4())[:8]
        self.current_version = version
        self.current_model = model

        # Log to MLflow if available
        if MLFLOW_AVAILABLE:
            with mlflow.start_run():
                mlflow.log_params(params)
                mlflow.log_metrics(metrics)
                if hasattr(model, 'embedding_model'):
                    # For BERTopic models
                    mlflow.pytorch.log_model(model.embedding_model, "embedding_model")

        # Store in database if available
        if self.db:
            try:
                cursor = self.db.cursor()
                cursor.execute("""
                    INSERT INTO model_versions
                    (model_name, version, parameters, metrics, is_active)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    self.model_name,
                    version,
                    json.dumps(params),
                    json.dumps(metrics),
                    True
                ))
                self.db.commit()

                # Get the ID of the inserted row
                cursor.execute("""
                    SELECT id FROM model_versions
                    WHERE model_name = %s AND version = %s
                """, (self.model_name, version))
                result = cursor.fetchone()
                if result:
                    return result[0]
            except Exception as e:
                logger.error(f"Failed to register model version: {e}")

        return None

    def add_to_memory(self, data: Dict[str, Any]) -> None:
        """
        Add data to experience replay buffer.

        Args:
            data: Data to add to memory
        """
        self.memory.append(data)
        self.step += 1

        # Limit buffer size
        if len(self.memory) > 1000:
            self.memory = self.memory[-1000:]

        # Check if we should update the model
        if self.should_update() and self.auto_retrain:
            self.train_on_buffer()

    def should_update(self) -> bool:
        """
        Check if model should be updated.

        Returns:
            True if update is needed, False otherwise
        """
        return self.step % self.update_interval == 0 and len(self.memory) > 10

    def train_on_buffer(self) -> None:
        """
        Train model on experience replay buffer.

        This implements the continuous learning loop using the memory buffer.
        """
        if not self.memory or not self.current_model:
            logger.warning("Cannot train: empty memory buffer or no model")
            return

        logger.info(f"Training on memory buffer with {len(self.memory)} samples")

        # Extract texts from memory buffer
        texts = []
        labels = []
        for item in self.memory:
            if 'text' in item:
                texts.append(item['text'])
            if 'label' in item:
                labels.append(item['label'])

        if not texts:
            logger.warning("No texts found in memory buffer")
            return

        # Prepare for training
        start_time = datetime.now()
        training_params = {}

        # Check if we're using BERTopic
        if hasattr(self.current_model, 'fit_transform'):
            # For BERTopic models
            try:
                # Use distributed training if available
                if self.distributed and self.distributed_available and self.world_size > 1:
                    self._train_distributed(texts, labels)
                else:
                    # Regular training with mixed precision if available
                    if ALL_ADVANCED_FEATURES and torch.cuda.is_available():
                        # Enable gradient checkpointing if available
                        if hasattr(self.current_model, 'embedding_model') and \
                           hasattr(self.current_model.embedding_model, 'gradient_checkpointing_enable'):
                            self.current_model.embedding_model.gradient_checkpointing_enable()
                            logger.info("Gradient checkpointing enabled")

                        # Use mixed precision training
                        scaler = torch.cuda.amp.GradScaler()
                        with torch.cuda.amp.autocast():
                            topics, probs = self.current_model.fit_transform(texts)
                    else:
                        # Regular training without mixed precision
                        topics, probs = self.current_model.fit_transform(texts)

                # Calculate metrics
                unique_topics = len(set(topics)) - (1 if -1 in topics else 0)
                training_params = {
                    'num_topics': unique_topics,
                    'dataset_size': len(texts)
                }

                logger.info(f"Training completed with {unique_topics} topics identified")
            except Exception as e:
                logger.error(f"Error during training: {e}")
                return

        # Log training run
        end_time = datetime.now()
        self._log_training_run(start_time, end_time, len(texts), training_params)

    def _train_distributed(self, texts, labels=None):
        """
        Train model using distributed training.

        Args:
            texts: List of texts to train on
            labels: Optional list of labels
        """
        if not self.distributed_available:
            logger.warning("Distributed training requested but not available")
            return

        # Initialize process group if not already initialized
        if not self.dist.is_initialized():
            try:
                self.dist.init_process_group(backend="nccl" if torch.cuda.is_available() else "gloo")
                self.world_size = self.dist.get_world_size()
                self.rank = self.dist.get_rank()
            except Exception as e:
                logger.error(f"Failed to initialize distributed training: {e}")
                return

        # Split data across processes
        chunk_size = len(texts) // self.world_size
        start_idx = self.rank * chunk_size
        end_idx = start_idx + chunk_size if self.rank < self.world_size - 1 else len(texts)
        process_texts = texts[start_idx:end_idx]

        # Train on this process's chunk with mixed precision
        if torch.cuda.is_available():
            # Enable gradient checkpointing
            if hasattr(self.current_model, 'embedding_model') and \
               hasattr(self.current_model.embedding_model, 'gradient_checkpointing_enable'):
                self.current_model.embedding_model.gradient_checkpointing_enable()

            # Use mixed precision training
            scaler = torch.cuda.amp.GradScaler()
            with torch.cuda.amp.autocast():
                local_topics, local_probs = self.current_model.fit_transform(process_texts)
        else:
            local_topics, local_probs = self.current_model.fit_transform(process_texts)

        # Synchronize results across processes
        self.dist.barrier()

        # Gather results from all processes if this is the main process
        if self.rank == 0:
            # In a real implementation, we would gather and combine results
            # This is a simplified version
            logger.info(f"Distributed training completed across {self.world_size} processes")

    def _log_training_run(self, start_time, end_time, dataset_size, params):
        """
        Log training run to database and MLflow.

        Args:
            start_time: Training start time
            end_time: Training end time
            dataset_size: Size of the dataset
            params: Training parameters
        """
        # Log to MLflow if available
        if MLFLOW_AVAILABLE:
            with mlflow.start_run():
                mlflow.log_params(params)
                mlflow.log_metric("training_duration_seconds",
                                 (end_time - start_time).total_seconds())
                mlflow.log_metric("dataset_size", dataset_size)

        # Log to database if available
        if self.db and self.current_version:
            try:
                cursor = self.db.cursor()
                cursor.execute("""
                    INSERT INTO training_logs
                    (model_version_id, training_start, training_end,
                    dataset_size, parameters)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    self.current_version,
                    start_time,
                    end_time,
                    dataset_size,
                    json.dumps(params)
                ))
                self.db.commit()
            except Exception as e:
                logger.error(f"Failed to log training run: {e}")

    def detect_concept_drift(self, old_dist: np.ndarray,
                           new_dist: np.ndarray) -> Tuple[bool, float]:
        """
        Detect concept drift using KL divergence.

        Args:
            old_dist: Old probability distribution
            new_dist: New probability distribution

        Returns:
            Tuple of (drift_detected, drift_score)
        """
        # Smooth distributions to avoid division by zero
        old_dist = np.maximum(old_dist, 1e-10)
        new_dist = np.maximum(new_dist, 1e-10)

        # Normalize
        old_dist = old_dist / np.sum(old_dist)
        new_dist = new_dist / np.sum(new_dist)

        # Calculate KL divergence
        kl_div = np.sum(new_dist * np.log(new_dist / old_dist))

        # Log drift if above threshold
        drift_detected = kl_div > self.drift_threshold

        if drift_detected:
            action = "model_update_triggered" if self.auto_retrain else "drift_detected"
            logger.info(f"Concept drift detected (score: {kl_div:.4f}). Action: {action}")

            # Log to database if available
            if self.db and self.current_version:
                try:
                    cursor = self.db.cursor()
                    cursor.execute("""
                        INSERT INTO concept_drift_logs
                        (drift_score, distribution_before, distribution_after,
                        action_taken, model_version_id)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (
                        float(kl_div),
                        json.dumps(old_dist.tolist()),
                        json.dumps(new_dist.tolist()),
                        action,
                        self.current_version
                    ))
                    self.db.commit()
                except Exception as e:
                    logger.error(f"Failed to log concept drift: {e}")

            # Trigger retraining if auto_retrain is enabled
            if self.auto_retrain and self.memory:
                logger.info("Auto-retraining triggered by concept drift")
                self.train_on_buffer()

        return drift_detected, kl_div

    def enable_distributed_training(self, world_size=None):
        """
        Enable distributed training across multiple GPUs or nodes.

        Args:
            world_size: Number of processes to use (default: auto-detect)
        """
        if not self.distributed_available:
            logger.warning("Distributed training not available. Required packages missing.")
            return False

        self.distributed = True

        # Auto-detect world size if not specified
        if world_size is None:
            if torch.cuda.is_available():
                self.world_size = torch.cuda.device_count()
            else:
                self.world_size = 1
        else:
            self.world_size = world_size

        logger.info(f"Distributed training enabled with world_size={self.world_size}")
        return True


class TwitterScraper:
    """
    Scrapes Twitter/X data using ntscraper.

    This class handles Twitter data collection, processing,
    and storage for trend analysis.
    """

    def __init__(self, db=None, model_version_id=None):
        """
        Initialize the Twitter scraper.

        Args:
            db: Database connection
            model_version_id: Current model version ID
        """
        self.scraper = None
        self.db = db
        self.model_version_id = model_version_id

        # Load environment variables from .env file
        try:
            from dotenv import load_dotenv
            load_dotenv()
            logger.info("Loaded environment variables from .env file")

            # Log if Twitter credentials are available (without showing the actual values)
            if os.environ.get("TWITTER_USERNAME") and os.environ.get("TWITTER_PASSWORD"):
                logger.info(f"Twitter credentials found for user: {os.environ.get('TWITTER_USERNAME')}")
            else:
                logger.warning("Twitter credentials not found in environment variables")

            # Log if proxy is available
            if os.environ.get("PROXY_SERVER"):
                proxy_server = os.environ.get("PROXY_SERVER")
                # Only show the host part for security
                if '@' in proxy_server:
                    proxy_host = proxy_server.split('@')[-1]
                    logger.info(f"Using proxy server: {proxy_host}")
                else:
                    logger.info("Proxy server configured")
            else:
                logger.warning("Proxy server not configured in environment variables")
        except ImportError:
            logger.warning("python-dotenv not installed. Environment variables may not be loaded.")

        try:
            from ntscraper import Nitter
            # Initialize with a specific Nitter instance to avoid the "Cannot choose from an empty sequence" error
            self.scraper = Nitter(skip_instance_check=True)

            # Add some known working Nitter instances
            self.scraper.working_instances = [
                "https://nitter.net",
                "https://nitter.lacontrevoie.fr",
                "https://nitter.1d4.us",
                "https://nitter.kavin.rocks",
                "https://nitter.unixfox.eu",
                "https://nitter.domain.glass",
                "https://nitter.privacydev.net"
            ]

            # Test if we can get a working instance
            if self.scraper.working_instances:
                self.available = True
                logger.info(f"Twitter scraper initialized successfully with {len(self.scraper.working_instances)} instances")
            else:
                self.available = False
                logger.warning("No working Nitter instances found. Twitter features disabled.")
        except ImportError:
            self.available = False
            logger.warning("ntscraper not installed. Twitter features disabled.")

    # Mock tweet generation method removed

    def scrape_tweets(self, target: str, mode: str = 'term', number: int = 100):
        """
        Scrape tweets by search term or user.

        Args:
            target: Search term or username
            mode: 'term' for keyword search, 'user' for user timeline
            number: Maximum number of tweets to retrieve

        Returns:
            List of processed tweets
        """
        if not self.available:
            logger.warning("Twitter scraper not available")
            return []

        # Make sure we have a valid search term
        if not target or len(target.strip()) < 3:
            logger.warning(f"Search term '{target}' is too short or empty. Using 'technology' instead.")
            target = "technology"

        # Proceed with real scraping

        # Real scraping (currently not working reliably)
        try:
            logger.info(f"Scraping Twitter for {mode}: {target}")

            # Add a delay to avoid rate limiting
            time.sleep(1)

            # Try with a more specific search term if it's a generic category
            if target.startswith('category_'):
                # Extract the actual keyword from the category name
                parts = target.split('_')
                if len(parts) >= 3:
                    target = parts[2]  # Use the keyword part
                    logger.info(f"Using extracted keyword '{target}' from category")

                    # If the keyword is too generic, add some context
                    if target in ['all', 'you', 'see', 'our']:
                        target = f"trending {target}"
                        logger.info(f"Modified generic term to '{target}'")

            # Try each instance until one works
            results = None
            errors = []

            # If we have no instances, use a default one
            if not self.scraper.working_instances:
                self.scraper.working_instances = ["https://nitter.net"]

            # Try each instance
            for instance in self.scraper.working_instances:
                try:
                    logger.info(f"Trying Twitter scraping with instance: {instance}")
                    results = self.scraper.get_tweets(
                        target,
                        mode=mode,
                        number=number,
                        language='en',
                        instance=instance
                    )

                    # If we got results, break the loop
                    if results and 'tweets' in results and results['tweets']:
                        logger.info(f"Successfully retrieved tweets from {instance}")
                        break
                    else:
                        logger.warning(f"No tweets found on {instance}")
                except Exception as e:
                    errors.append(f"{instance}: {str(e)}")
                    logger.warning(f"Failed to scrape from {instance}: {e}")
                    continue

            # If we didn't get any results, log the errors
            if not results or not results.get('tweets'):
                logger.warning(f"Failed to scrape tweets from all instances. Errors: {errors}")
            processed_tweets = self._process_tweets(results)
            logger.info(f"Retrieved {len(processed_tweets)} tweets for {target}")
            return processed_tweets
        except Exception as e:
            logger.error(f"Twitter scraping failed: {e}")
            # Try with a fallback search term
            if target != "technology":
                logger.info("Trying fallback search term 'technology'")
                try:
                    time.sleep(2)  # Add a delay before retry
                    # Try each instance until one works
                    fallback_results = None
                    fallback_errors = []

                    # If we have no instances, use a default one
                    if not self.scraper.working_instances:
                        self.scraper.working_instances = ["https://nitter.net"]

                    # Try each instance
                    for instance in self.scraper.working_instances:
                        try:
                            logger.info(f"Trying fallback Twitter scraping with instance: {instance}")
                            fallback_results = self.scraper.get_tweets(
                                "technology",
                                mode=mode,
                                number=number,
                                language='en',
                                instance=instance
                            )

                            # If we got results, break the loop
                            if fallback_results and 'tweets' in fallback_results and fallback_results['tweets']:
                                logger.info(f"Successfully retrieved fallback tweets from {instance}")
                                break
                            else:
                                logger.warning(f"No fallback tweets found on {instance}")
                        except Exception as e:
                            fallback_errors.append(f"{instance}: {str(e)}")
                            logger.warning(f"Failed to scrape fallback from {instance}: {e}")
                            continue

                    # If we didn't get any results, log the errors
                    if not fallback_results or not fallback_results.get('tweets'):
                        logger.warning(f"Failed to scrape fallback tweets from all instances. Errors: {fallback_errors}")

                    results = fallback_results
                    processed_tweets = self._process_tweets(results)
                    logger.info(f"Retrieved {len(processed_tweets)} tweets for fallback term 'technology'")
                    return processed_tweets
                except Exception as e2:
                    logger.error(f"Fallback Twitter scraping failed: {e2}")

            # If all real scraping fails, return empty list
            logger.error(f"All Twitter scraping attempts failed for: {target}")
            return []

    def _process_tweets(self, raw_tweets):
        """
        Process raw tweet data into structured format.

        Args:
            raw_tweets: Raw tweet data from scraper

        Returns:
            List of processed tweet dictionaries
        """
        # Check if raw_tweets is None or empty
        if raw_tweets is None:
            logger.warning("Received None for raw_tweets")
            return []

        # Check if 'tweets' key exists
        if not isinstance(raw_tweets, dict) or 'tweets' not in raw_tweets:
            logger.warning(f"Invalid raw_tweets format: {type(raw_tweets)}")
            return []

        # Check if tweets list is empty
        if not raw_tweets.get('tweets'):
            logger.warning("Empty tweets list in raw_tweets")
            return []

        processed = []
        for tweet in raw_tweets.get('tweets', []):
            # Extract tweet data
            tweet_data = {
                'tweet_id': tweet.get('id', ''),
                'text': tweet.get('text', ''),
                'user': tweet.get('user', {}).get('name', ''),
                'likes': tweet.get('stats', {}).get('likes', 0),
                'retweets': tweet.get('stats', {}).get('retweets', 0),
                'replies': tweet.get('stats', {}).get('comments', 0),
                'quoted': tweet.get('stats', {}).get('quotes', 0),
                'timestamp': tweet.get('date', ''),
                'url': tweet.get('link', '')
            }

            # Calculate sentiment if TextBlob is available
            if TEXTBLOB_AVAILABLE and tweet_data['text']:
                try:
                    blob = TextBlob(tweet_data['text'])
                    tweet_data['sentiment_score'] = blob.sentiment.polarity
                except Exception as e:
                    logger.warning(f"Sentiment analysis failed: {e}")
                    tweet_data['sentiment_score'] = 0.0
            else:
                tweet_data['sentiment_score'] = 0.0

            processed.append(tweet_data)

        return processed

    def save_to_db(self, tweets, trend_id=None):
        """
        Save tweets to database.

        Args:
            tweets: List of processed tweets
            trend_id: ID of the related trend (optional)
        """
        if not self.db:
            logger.warning("Database connection not available for saving tweets")
            return

        cursor = self.db.cursor()
        try:
            for tweet in tweets:
                cursor.execute("""
                    INSERT INTO twitter_data
                    (tweet_id, text, user_name, likes, retweets, replies,
                    quoted_tweets, sentiment_score, crawled_at, trend_id)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    likes = VALUES(likes),
                    retweets = VALUES(retweets),
                    replies = VALUES(replies),
                    sentiment_score = VALUES(sentiment_score)
                """, (
                    tweet['tweet_id'],
                    tweet['text'][:65535],  # Limit text to avoid DB errors
                    tweet['user'][:255],
                    tweet['likes'],
                    tweet['retweets'],
                    tweet['replies'],
                    tweet['quoted'],
                    tweet['sentiment_score'],
                    datetime.now(),
                    trend_id
                ))
            self.db.commit()
            logger.info(f"Saved {len(tweets)} tweets to database")
        except Exception as e:
            logger.error(f"Failed to save tweets: {e}")
            self.db.rollback()

    def save_metrics(self, hashtag, tweet_volume, coolness_score):
        """
        Save Twitter metrics to database.

        Args:
            hashtag: Trending hashtag
            tweet_volume: Volume of tweets
            coolness_score: Calculated coolness score
        """
        if not self.db or not self.model_version_id:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO twitter_metrics
                (timestamp, trending_hashtag, tweet_volume, coolness_score, model_version_id)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                datetime.now(),
                hashtag[:255],
                tweet_volume,
                coolness_score,
                self.model_version_id
            ))
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to save Twitter metrics: {e}")
            self.db.rollback()


class ResourceManager:
    """
    Manages computational resources for optimal performance.

    This class handles GPU/CPU optimization, batch sizing,
    mixed precision training, and gradient checkpointing.
    """

    def __init__(self):
        """
        Initialize the resource manager.
        """
        # Check for GPU availability
        self.gpu_available = False
        self.mixed_precision_available = False
        self.distributed_available = False

        if ADVANCED_FEATURES:
            try:
                self.gpu_available = torch.cuda.is_available()
                if self.gpu_available:
                    # Check for mixed precision support
                    self.mixed_precision_available = hasattr(torch.cuda, 'amp')

                    # Check for distributed training support
                    try:
                        import torch.distributed as dist
                        self.distributed_available = True
                    except ImportError:
                        self.distributed_available = False
            except Exception as e:
                logger.warning(f"Error checking GPU availability: {e}")

        # Set device
        self.device = torch.device("cuda" if self.gpu_available else "cpu")

        # Initialize mixed precision tools if available
        if self.mixed_precision_available:
            try:
                self.scaler = torch.cuda.amp.GradScaler()
                logger.info("Mixed precision training enabled")
            except Exception as e:
                logger.warning(f"Failed to initialize mixed precision: {e}")
                self.mixed_precision_available = False
                self.scaler = None
        else:
            self.scaler = None

        # Determine optimal batch size based on available memory
        if self.gpu_available:
            try:
                gpu_mem = torch.cuda.get_device_properties(0).total_memory
                # Heuristic: 4GB can handle batch size of 16 for most transformer models
                # Reduce batch size for mixed precision to account for optimizer states
                mem_factor = 16 if not self.mixed_precision_available else 24
                self.batch_size = max(1, int(gpu_mem / (4 * 1024 * 1024 * 1024) * mem_factor))
            except Exception as e:
                logger.warning(f"Error determining batch size: {e}")
                self.batch_size = 8  # Default fallback
        else:
            self.batch_size = 4  # Smaller batch size for CPU

    def optimize_for_inference(self, model):
        """
        Optimize model for inference based on available resources.

        Args:
            model: The model to optimize

        Returns:
            Optimized model
        """
        if not ADVANCED_FEATURES:
            return model

        # Enable gradient checkpointing if available (saves memory during inference)
        if self.gpu_available and hasattr(model, 'embedding_model'):
            try:
                if hasattr(model.embedding_model, 'gradient_checkpointing_enable'):
                    model.embedding_model.gradient_checkpointing_enable()
                    logger.info("Gradient checkpointing enabled for inference")
            except Exception as e:
                logger.warning(f"Failed to enable gradient checkpointing: {e}")

        # For CPU: Use quantization if available
        if not self.gpu_available and hasattr(model, 'embedding_model'):
            try:
                import torch.quantization

                # Move to CPU for quantization
                model.embedding_model = model.embedding_model.cpu()

                # Prepare for quantization
                model.embedding_model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
                torch.quantization.prepare(model.embedding_model, inplace=True)

                # Convert to quantized model
                torch.quantization.convert(model.embedding_model, inplace=True)

                logger.info("Model quantized for CPU inference")
            except Exception as e:
                logger.warning(f"Quantization failed: {e}")

        return model

    def allocate_batch(self, data, batch_idx=0):
        """
        Allocate an optimized batch based on available resources.

        Args:
            data: Full dataset
            batch_idx: Batch index

        Returns:
            Optimized batch of data
        """
        start_idx = batch_idx * self.batch_size
        end_idx = min(start_idx + self.batch_size, len(data))
        batch = data[start_idx:end_idx]

        if self.gpu_available and ADVANCED_FEATURES:
            # Move batch to GPU if available
            if isinstance(batch, torch.Tensor):
                batch = batch.to(self.device)
            elif isinstance(batch, list) and all(isinstance(x, torch.Tensor) for x in batch):
                batch = [x.to(self.device) for x in batch]

        return batch

    def mixed_precision_context(self):
        """
        Get a context manager for mixed precision operations.

        Returns:
            Context manager for mixed precision or a dummy context manager
        """
        if self.mixed_precision_available:
            return torch.cuda.amp.autocast()
        else:
            # Return a dummy context manager when mixed precision is not available
            from contextlib import nullcontext
            return nullcontext()

    def scale_gradients(self, loss, optimizer):
        """
        Scale gradients for mixed precision training.

        Args:
            loss: Loss tensor
            optimizer: Optimizer

        Returns:
            Boolean indicating whether optimizer step was taken
        """
        if self.mixed_precision_available and self.scaler is not None:
            # Scale loss and compute gradients
            self.scaler.scale(loss).backward()

            # Unscale gradients and check for infs/NaNs
            self.scaler.unscale_(optimizer)

            # Update weights if no infs/NaNs
            self.scaler.step(optimizer)

            # Update scaler for next iteration
            self.scaler.update()
            return True
        else:
            # Regular backward pass and optimizer step
            loss.backward()
            optimizer.step()
            return True


def tune_bertopic_params(texts: List[str]) -> Dict[str, Any]:
    """
    Tune BERTopic hyperparameters.

    Args:
        texts: List of texts to analyze

    Returns:
        Dictionary of best hyperparameters
    """
    if not ADVANCED_FEATURES:
        return {"min_topic_size": 10}

    def objective(trial):
        min_topic_size = trial.suggest_int("min_topic_size", 10, 50)
        model = BERTopic(min_topic_size=min_topic_size)
        topics, _ = model.fit_transform(texts)
        # Placeholder for coherence score
        coherence = len(set(topics))  # Simple metric: number of unique topics
        return coherence

    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=5)  # Adjust n_trials as needed
    return study.best_params


def calculate_coolness_score(
    text: str,
    embeddings: Optional[np.ndarray] = None,
    topic_popularity: Optional[float] = None,
    historical_data: Optional[Dict[str, Any]] = None,
    tweet_metrics: Optional[Dict[str, int]] = None,
    profile_data: Optional[Dict[str, Any]] = None,
    classifier: Optional[Any] = None
) -> Dict[str, float]:
    """
    Calculate multi-factor coolness score.

    Args:
        text: Text content
        embeddings: Text embeddings if available
        topic_popularity: Topic popularity score if available
        historical_data: Historical data for trend analysis

    Returns:
        Dictionary with component scores and total score
    """
    # Initialize scores
    velocity_score = 0.0
    impact_score = 0.0
    novelty_score = 0.0

    # Calculate velocity (trend momentum)
    if historical_data and 'previous_mentions' in historical_data:
        # Simple velocity: rate of increase in mentions
        prev = historical_data.get('previous_mentions', 1)
        curr = historical_data.get('current_mentions', 1)
        velocity_score = min(1.0, max(0.0, (curr - prev) / max(1, prev)))
    else:
        # Default velocity based on text length and keyword density
        words = text.split()
        keyword_count = len([w for w in words if len(w) > 5])
        velocity_score = min(1.0, keyword_count / max(1, len(words)) * 3)

    # Calculate impact (potential significance)
    # Use topic popularity if available, otherwise estimate from text
    if topic_popularity is not None:
        impact_score = min(1.0, topic_popularity * 2)  # Scale up for better distribution
    else:
        # Estimate impact from text features
        # Count specific impact signals: numbers, entities, action words
        num_count = len(re.findall(r'\d+', text))
        entity_count = len(re.findall(r'[A-Z][a-z]+', text))
        action_words = ['launch', 'announce', 'release', 'discover', 'breakthrough',
                        'reveal', 'introduce', 'update', 'transform', 'revolutionize']
        action_count = sum(1 for word in action_words if word in text.lower())

        # Combine signals
        impact_signals = num_count + entity_count + action_count * 3
        impact_score = min(1.0, impact_signals / 50)  # Normalize

    # Enhance impact score with Twitter metrics if available
    if tweet_metrics:
        # Calculate social engagement score
        engagement = (
            0.4 * tweet_metrics.get('likes', 0) +
            0.3 * tweet_metrics.get('retweets', 0) +
            0.2 * tweet_metrics.get('replies', 0) +
            0.1 * tweet_metrics.get('quoted', 0)
        ) / 1000  # Normalize

        # Update impact score with social proof
        impact_score = min(1.0, impact_score * (1.0 + min(engagement, 2.0)))  # Cap at 3x boost

        # Update velocity score if historical data available
        if historical_data and 'prev_engagement' in historical_data:
            prev_engagement = historical_data['prev_engagement']
            if prev_engagement > 0:
                # Calculate growth rate
                growth = (engagement - prev_engagement) / prev_engagement
                # Add to velocity score (capped)
                velocity_score = min(1.0, velocity_score + max(0, growth))

    # Calculate novelty (uniqueness)
    if embeddings is not None:
        # If we have embeddings, novelty could be calculated from vector properties
        # This is a placeholder - in a real system, compare to corpus centroid
        novelty_score = 0.5  # Default mid-value
    else:
        # Estimate novelty from text features
        # Count rare words (longer words tend to be more specific/novel)
        rare_word_count = len([w for w in text.split() if len(w) > 7])
        novelty_score = min(1.0, rare_word_count / 20)  # Normalize

    # Calculate weighted coolness score
    # Weights: velocity (30%), impact (50%), novelty (20%)
    base_score = 0.3 * velocity_score + 0.5 * impact_score + 0.2 * novelty_score

    # Add BERT classification component if available
    ml_score = None
    feedback_score = None
    if classifier and classifier.available:
        try:
            # Get prediction from classifier
            prediction = classifier.predict(text)

            # Extract ML score
            if 'confidence' in prediction:
                ml_score = prediction['confidence']
                # Combine with base score (70% base, 30% ML)
                base_score = 0.7 * base_score + 0.3 * ml_score

            # Add feedback adjustment if available
            if 'feedback_score' in prediction:
                feedback_score = prediction['feedback_score'] / 9.0  # Normalize to 0-1
                # Apply feedback adjustment (80% base, 20% feedback)
                base_score = base_score * (0.8 + 0.2 * feedback_score)

            # Store prediction if possible
            if hasattr(classifier, 'store_prediction'):
                classifier.store_prediction(text[:100], prediction)  # Use text snippet as URL

        except Exception as e:
            logger.error(f"Error using classifier: {e}")

    # Final coolness score after ML and feedback adjustments
    coolness_score = base_score

    # Apply BERT-based coolness classification if available
    try:
        from coolness_classifier import CoolnessClassifier
        BERT_CLASSIFIER_AVAILABLE = True
    except ImportError:
        BERT_CLASSIFIER_AVAILABLE = False

    if BERT_CLASSIFIER_AVAILABLE and classifier:
        try:
            # Create metrics dictionary for BERT classifier
            bert_metrics = {
                'views': 1000,  # Default values
                'likes': velocity_score * 100,
                'shares': impact_score * 100,
                'comments': novelty_score * 100
            }

            # Add Twitter metrics if available
            bert_twitter_data = None
            if tweet_metrics:
                bert_twitter_data = {
                    'twitter_volume': tweet_metrics.get('volume', 0),
                    'twitter_engagement': tweet_metrics.get('avg_likes', 0) + tweet_metrics.get('avg_retweets', 0),
                    'twitter_sentiment': tweet_metrics.get('avg_sentiment', 0),
                    'verified_ratio': 0.1,  # Default value
                    'follower_weighted_score': 0.5  # Default value
                }

            # Calculate BERT-enhanced score
            bert_score = classifier.calculate_coolness_score(text, bert_metrics, bert_twitter_data)

            # Combine with original score (weighted average)
            # Higher weight to BERT score as it's more sophisticated
            coolness_score = (coolness_score * 0.4) + (bert_score * 0.6)

            logger.info(f"BERT enhanced coolness score: {base_score:.2f} → {coolness_score:.2f}")
        except Exception as e:
            logger.warning(f"BERT classification failed: {e}")
            # Keep original score if BERT fails

    # Add Twitter profile influences if available
    if profile_data:
        # Verified status boost
        if profile_data.get('verified', False):
            coolness_score += 0.15

        # Followers count boost
        followers = profile_data.get('followers_count', 0)
        coolness_score += 0.10 * min(1, followers / 1000000)

        # Website credibility boost
        if "http" in profile_data.get('website', ''):
            coolness_score += 0.05

        # Cap at 1.0
        coolness_score = min(coolness_score, 1.0)

    # Return all scores
    result = {
        'coolness_score': coolness_score,
        'velocity_score': velocity_score,
        'impact_score': impact_score,
        'novelty_score': novelty_score
    }

    # Add ML and feedback scores if available
    if ml_score is not None:
        result['ml_score'] = ml_score
    if feedback_score is not None:
        result['feedback_score'] = feedback_score

    return result


def generate_embeddings(
    texts: List[str],
    model_name: str = "all-MiniLM-L6-v2",
    batch_size: int = 32,
    device: str = None
) -> np.ndarray:
    """
    Generate embeddings for a list of texts using a sentence transformer model.

    Args:
        texts: List of text strings to encode
        model_name: Name of the SentenceTransformer model to use
        batch_size: Batch size for encoding
        device: Device to use for encoding (None for auto-selection)

    Returns:
        Array of embeddings
    """
    try:
        from sentence_transformers import SentenceTransformer

        # If device is not specified, use CUDA if available
        if device is None:
            if torch.cuda.is_available():
                device = "cuda"
            else:
                device = "cpu"

        # Initialize the model
        model = SentenceTransformer(model_name, device=device)

        # Generate embeddings
        embeddings = model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=True,
            convert_to_numpy=True
        )

        logger.info(f"Generated {len(embeddings)} embeddings of size {embeddings.shape[1]} using {model_name}")
        return embeddings

    except ImportError:
        logger.error("SentenceTransformer not available. Install with: pip install sentence-transformers")
        return None
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        return None


def cluster_embeddings(
    embeddings: np.ndarray,
    algorithm: str = "hdbscan",
    n_clusters: int = 5,
    **kwargs
) -> Tuple[List[int], Dict[str, Any]]:
    """
    Cluster embeddings using advanced algorithms with optimized parameters.

    Args:
        embeddings: Numpy array of embeddings to cluster
        algorithm: Clustering algorithm to use
        n_clusters: Number of clusters for algorithms that require it
        **kwargs: Additional parameters for clustering algorithms

    Returns:
        Tuple of (cluster assignments, clustering metadata)
    """
    if embeddings is None or len(embeddings) == 0:
        logger.error("No embeddings provided for clustering")
        return [], {}

    # Normalize embeddings for better clustering results
    from sklearn.preprocessing import normalize
    normalized_embeddings = normalize(embeddings)

    try:
        if algorithm == "kmeans":
            from sklearn.cluster import KMeans
            from sklearn.metrics import silhouette_score

            # If n_clusters is auto, use silhouette analysis to find optimal number
            if kwargs.get('auto_clusters', False) and len(embeddings) > 10:
                # Try different numbers of clusters and pick the best
                best_score = -1
                best_n = n_clusters
                max_clusters = min(20, len(embeddings) - 1)

                for n in range(2, max_clusters + 1):
                    kmeans = KMeans(n_clusters=n, random_state=42, n_init=10)
                    labels = kmeans.fit_predict(normalized_embeddings)

                    # Skip if only one cluster was found
                    if len(set(labels)) < 2:
                        continue

                    # Calculate silhouette score
                    score = silhouette_score(normalized_embeddings, labels)
                    logger.info(f"KMeans with {n} clusters: silhouette score = {score:.3f}")

                    if score > best_score:
                        best_score = score
                        best_n = n

                logger.info(f"Selected optimal number of clusters: {best_n} (score: {best_score:.3f})")
                n_clusters = best_n

            # Use k-means++ initialization for better results
            clustering = KMeans(
                n_clusters=n_clusters,
                random_state=42,
                init='k-means++',
                n_init=10,
                **kwargs
            )
            cluster_assignments = clustering.fit_predict(normalized_embeddings)
            centers = clustering.cluster_centers_
            inertia = clustering.inertia_

            # Calculate additional metrics
            if len(set(cluster_assignments)) > 1:
                silhouette = silhouette_score(normalized_embeddings, cluster_assignments)
            else:
                silhouette = 0

            metadata = {
                "algorithm": "kmeans",
                "n_clusters": n_clusters,
                "inertia": float(inertia),
                "silhouette_score": float(silhouette),
                "parameters": kwargs
            }

        elif algorithm == "dbscan":
            from sklearn.cluster import DBSCAN
            from sklearn.neighbors import NearestNeighbors

            # Auto-determine eps parameter if requested
            if kwargs.get('auto_eps', False):
                # Use nearest neighbors to find optimal eps
                k = min(10, len(normalized_embeddings) - 1)
                nn = NearestNeighbors(n_neighbors=k)
                nn.fit(normalized_embeddings)
                distances, _ = nn.kneighbors(normalized_embeddings)

                # Sort distances to kth neighbor
                distances = np.sort(distances[:, k-1])

                # Find the "elbow" point
                from scipy.signal import argrelextrema
                from scipy.ndimage import gaussian_filter1d

                # Smooth the curve
                smoothed = gaussian_filter1d(distances, sigma=1)

                # Find local maxima of the second derivative (inflection points)
                d2 = np.gradient(np.gradient(smoothed))
                inflection_points = argrelextrema(d2, np.greater)[0]

                if len(inflection_points) > 0:
                    # Use the first inflection point as the elbow
                    eps = distances[inflection_points[0]]
                else:
                    # Fallback: use the mean of distances
                    eps = np.mean(distances)

                logger.info(f"Auto-determined DBSCAN eps: {eps:.4f}")
            else:
                eps = kwargs.get('eps', 0.5)

            min_samples = kwargs.get('min_samples', 5)
            clustering = DBSCAN(eps=eps, min_samples=min_samples, **kwargs)
            cluster_assignments = clustering.fit_predict(normalized_embeddings)
            n_clusters = len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0)

            # Calculate silhouette score if more than one cluster
            silhouette = 0
            if n_clusters > 1 and -1 not in cluster_assignments:
                from sklearn.metrics import silhouette_score
                silhouette = silhouette_score(normalized_embeddings, cluster_assignments)
            elif n_clusters > 1:
                # Calculate silhouette only for non-noise points
                non_noise_mask = cluster_assignments != -1
                if np.sum(non_noise_mask) > 1:
                    from sklearn.metrics import silhouette_score
                    silhouette = silhouette_score(
                        normalized_embeddings[non_noise_mask],
                        cluster_assignments[non_noise_mask]
                    )

            metadata = {
                "algorithm": "dbscan",
                "eps": eps,
                "min_samples": min_samples,
                "n_clusters": n_clusters,
                "n_noise": list(cluster_assignments).count(-1),
                "silhouette_score": float(silhouette),
                "parameters": kwargs
            }

        elif algorithm == "agglomerative":
            from sklearn.cluster import AgglomerativeClustering
            from sklearn.metrics import silhouette_score

            # Determine optimal number of clusters if requested
            if kwargs.get('auto_clusters', False) and len(embeddings) > 10:
                from scipy.cluster.hierarchy import dendrogram, linkage

                # Compute linkage matrix
                link_method = kwargs.get('linkage', 'ward')
                Z = linkage(normalized_embeddings, method=link_method)

                # Try different numbers and evaluate
                best_score = -1
                best_n = n_clusters
                max_clusters = min(20, len(embeddings) - 1)

                for n in range(2, max_clusters + 1):
                    from scipy.cluster.hierarchy import fcluster
                    labels = fcluster(Z, n, criterion='maxclust') - 1  # 0-based indexing

                    # Skip if only one cluster was found
                    if len(set(labels)) < 2:
                        continue

                    # Calculate silhouette score
                    score = silhouette_score(normalized_embeddings, labels)

                    if score > best_score:
                        best_score = score
                        best_n = n

                logger.info(f"Selected optimal number of clusters for Agglomerative: {best_n}")
                n_clusters = best_n

            linkage = kwargs.get('linkage', 'ward')
            affinity = kwargs.get('affinity', 'euclidean')

            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                linkage=linkage,
                affinity=affinity,
                **kwargs
            )
            cluster_assignments = clustering.fit_predict(normalized_embeddings)

            # Calculate silhouette score
            silhouette = 0
            if len(set(cluster_assignments)) > 1:
                silhouette = silhouette_score(normalized_embeddings, cluster_assignments)

            metadata = {
                "algorithm": "agglomerative",
                "linkage": linkage,
                "affinity": affinity,
                "n_clusters": n_clusters,
                "silhouette_score": float(silhouette),
                "parameters": kwargs
            }

        elif algorithm == "hdbscan":
            try:
                import hdbscan
                from sklearn.metrics import silhouette_score

                # Set parameters
                min_cluster_size = kwargs.get('min_cluster_size', 5)
                min_samples = kwargs.get('min_samples', None)
                cluster_selection_method = kwargs.get('cluster_selection_method', 'eom')

                # Create HDBSCAN clusterer with optimized parameters
                clustering = hdbscan.HDBSCAN(
                    min_cluster_size=min_cluster_size,
                    min_samples=min_samples,
                    cluster_selection_method=cluster_selection_method,
                    prediction_data=True,  # Enable prediction for new points
                    **kwargs
                )

                # Fit the model
                cluster_assignments = clustering.fit_predict(normalized_embeddings)
                n_clusters = len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0)

                # Calculate silhouette score if more than one cluster and non-noise points
                silhouette = 0
                if n_clusters > 1:
                    # Calculate silhouette only for non-noise points
                    non_noise_mask = cluster_assignments != -1
                    if np.sum(non_noise_mask) > 1:
                        silhouette = silhouette_score(
                            normalized_embeddings[non_noise_mask],
                            cluster_assignments[non_noise_mask]
                        )

                # Get cluster probabilities and persistence
                cluster_probabilities = clustering.probabilities_
                cluster_persistence = getattr(clustering, 'cluster_persistence_', None)

                metadata = {
                    "algorithm": "hdbscan",
                    "n_clusters": n_clusters,
                    "min_cluster_size": min_cluster_size,
                    "min_samples": min_samples,
                    "cluster_selection_method": cluster_selection_method,
                    "n_noise": list(cluster_assignments).count(-1),
                    "silhouette_score": float(silhouette),
                    "parameters": kwargs
                }

                # Add cluster persistence if available
                if cluster_persistence is not None:
                    metadata["cluster_persistence"] = [float(p) for p in cluster_persistence]

            except ImportError:
                logger.warning("HDBSCAN not available. Falling back to KMeans. Install with: pip install hdbscan")
                from sklearn.cluster import KMeans
                from sklearn.metrics import silhouette_score

                clustering = KMeans(n_clusters=n_clusters, random_state=42, init='k-means++')
                cluster_assignments = clustering.fit_predict(normalized_embeddings)

                # Calculate silhouette score
                silhouette = 0
                if len(set(cluster_assignments)) > 1:
                    silhouette = silhouette_score(normalized_embeddings, cluster_assignments)

                metadata = {
                    "algorithm": "kmeans (fallback from hdbscan)",
                    "n_clusters": n_clusters,
                    "silhouette_score": float(silhouette),
                    "parameters": {}
                }

        elif algorithm == "optics":
            from sklearn.cluster import OPTICS
            from sklearn.metrics import silhouette_score

            # Set parameters
            min_samples = kwargs.get('min_samples', 5)
            xi = kwargs.get('xi', 0.05)
            min_cluster_size = kwargs.get('min_cluster_size', 0.05)

            clustering = OPTICS(
                min_samples=min_samples,
                xi=xi,
                min_cluster_size=min_cluster_size,
                **kwargs
            )
            cluster_assignments = clustering.fit_predict(normalized_embeddings)
            n_clusters = len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0)

            # Calculate silhouette score if more than one cluster and non-noise points
            silhouette = 0
            if n_clusters > 1:
                # Calculate silhouette only for non-noise points
                non_noise_mask = cluster_assignments != -1
                if np.sum(non_noise_mask) > 1:
                    silhouette = silhouette_score(
                        normalized_embeddings[non_noise_mask],
                        cluster_assignments[non_noise_mask]
                    )

            metadata = {
                "algorithm": "optics",
                "min_samples": min_samples,
                "xi": xi,
                "min_cluster_size": min_cluster_size,
                "n_clusters": n_clusters,
                "n_noise": list(cluster_assignments).count(-1),
                "silhouette_score": float(silhouette),
                "parameters": kwargs
            }

        else:
            logger.warning(f"Unknown clustering algorithm: {algorithm}. Using KMeans.")
            from sklearn.cluster import KMeans
            from sklearn.metrics import silhouette_score

            clustering = KMeans(n_clusters=n_clusters, random_state=42, init='k-means++')
            cluster_assignments = clustering.fit_predict(normalized_embeddings)

            # Calculate silhouette score
            silhouette = 0
            if len(set(cluster_assignments)) > 1:
                silhouette = silhouette_score(normalized_embeddings, cluster_assignments)

            metadata = {
                "algorithm": "kmeans (fallback)",
                "n_clusters": n_clusters,
                "silhouette_score": float(silhouette),
                "parameters": {}
            }

        # Generate cluster labels
        cluster_counts = collections.Counter(cluster_assignments)
        logger.info(f"Clustering with {algorithm} created {len(cluster_counts)} clusters")
        logger.info(f"Cluster distribution: {dict(cluster_counts)}")

        return list(cluster_assignments), metadata

    except ImportError:
        logger.error(f"Required packages for {algorithm} clustering not available")
        return [], {}
    except Exception as e:
        logger.error(f"Error clustering with {algorithm}: {e}")
        # Fallback to simple clustering
        try:
            from sklearn.cluster import KMeans
            clustering = KMeans(n_clusters=min(n_clusters, len(embeddings)), random_state=42)
            cluster_assignments = clustering.fit_predict(normalized_embeddings)
            metadata = {
                "algorithm": "kmeans (error fallback)",
                "n_clusters": n_clusters,
                "error": str(e),
                "parameters": {}
            }
            return list(cluster_assignments), metadata
        except Exception as e2:
            logger.error(f"Fallback clustering also failed: {e2}")
            return [0] * len(embeddings), {"algorithm": "none", "error": str(e2)}


def extract_cluster_keywords(
    texts: List[str],
    cluster_assignments: List[int],
    cluster_id: int,
    num_keywords: int = 10
) -> List[str]:
    """
    Extract representative keywords for a specific cluster.

    Args:
        texts: List of text documents
        cluster_assignments: Cluster assignment for each document
        cluster_id: The cluster ID to extract keywords for
        num_keywords: Number of keywords to extract

    Returns:
        List of keywords for the cluster
    """
    # Get texts for this cluster
    cluster_texts = [
        texts[i] for i in range(len(texts))
        if i < len(cluster_assignments) and cluster_assignments[i] == cluster_id
    ]

    if not cluster_texts:
        return []

    # Join all texts in the cluster
    combined_text = " ".join(cluster_texts)

    # Extract keywords using frequency analysis
    words = re.findall(r'\b\w+\b', combined_text.lower())
    filtered_words = [word for word in words if word not in STOPWORDS and len(word) > 2]
    word_counts = collections.Counter(filtered_words)

    # Get the most common words
    keywords = [word for word, _ in word_counts.most_common(num_keywords)]
    return keywords


def analyze_trends_with_vectors(
    coolness_data: Dict[str, Dict[str, Any]],
    embeddings: np.ndarray,
    cluster_algorithm: str = "hdbscan",
    n_clusters: int = 5,
    embedding_field_name: str = 'embedding',
    auto_clusters: bool = False,
    auto_eps: bool = False
) -> Dict[str, Dict[str, Any]]:
    """
    Analyze trends using vector embeddings and advanced clustering techniques.

    Args:
        coolness_data: Dictionary of coolness data
        embeddings: Numpy array of embeddings
        cluster_algorithm: Clustering algorithm to use
        n_clusters: Number of clusters for algorithms that need it
        embedding_field_name: Field name to store embeddings in coolness_data
        auto_clusters: Whether to automatically determine optimal number of clusters
        auto_eps: Whether to automatically determine optimal eps parameter for DBSCAN

    Returns:
        Updated coolness_data with vector analysis results
    """
    if embeddings is None or len(embeddings) == 0:
        logger.error("No embeddings provided for trend analysis")
        return coolness_data

    try:
        # Get URLs in the same order as embeddings
        urls = list(coolness_data.keys())
        texts = [coolness_data[url].get('text', '') for url in urls]

        # Prepare clustering parameters with advanced options
        clustering_kwargs = {}
        if auto_clusters:
            clustering_kwargs['auto_clusters'] = True
            logger.info("Using automatic cluster count determination")
        if auto_eps and cluster_algorithm == 'dbscan':
            clustering_kwargs['auto_eps'] = True
            logger.info("Using automatic eps parameter determination for DBSCAN")

        # Perform clustering with advanced options
        cluster_assignments, cluster_metadata = cluster_embeddings(
            embeddings,
            algorithm=cluster_algorithm,
            n_clusters=n_clusters,
            **clustering_kwargs
        )

        if not cluster_assignments:
            logger.error("Clustering failed")
            return coolness_data

        # Extract cluster keywords
        cluster_keywords = {}
        for cluster_id in set(cluster_assignments):
            if cluster_id == -1:  # Skip noise points
                continue
            keywords = extract_cluster_keywords(texts, cluster_assignments, cluster_id)
            cluster_keywords[cluster_id] = keywords

        # Calculate within-cluster similarities
        from sklearn.metrics.pairwise import cosine_similarity
        cluster_similarities = {}
        for cluster_id in set(cluster_assignments):
            if cluster_id == -1:  # Skip noise points
                continue

            # Get embeddings for this cluster
            cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
            cluster_embeddings = embeddings[cluster_indices]

            # Calculate cosine similarity matrix
            if len(cluster_embeddings) > 1:
                sim_matrix = cosine_similarity(cluster_embeddings)
                # Average similarity (excluding self-similarity)
                np.fill_diagonal(sim_matrix, 0)  # Zero out the diagonal
                avg_sim = np.sum(sim_matrix) / (sim_matrix.size - len(sim_matrix))
            else:
                avg_sim = 1.0  # A cluster of size 1 has perfect similarity with itself

            cluster_similarities[cluster_id] = float(avg_sim)

        # Update coolness_data with cluster information
        for i, url in enumerate(urls):
            if i < len(cluster_assignments):
                cluster_id = cluster_assignments[i]
                coolness_data[url]['vector_cluster_id'] = int(cluster_id)

                if cluster_id != -1:
                    # Add cluster keywords
                    coolness_data[url]['vector_cluster_keywords'] = cluster_keywords.get(cluster_id, [])

                    # Add cluster coherence score (similarity)
                    coolness_data[url]['vector_cluster_coherence'] = cluster_similarities.get(cluster_id, 0.0)

                    # Add silhouette score if available
                    if 'silhouette_score' in cluster_metadata:
                        coolness_data[url]['silhouette_score'] = float(cluster_metadata['silhouette_score'])
                else:
                    coolness_data[url]['vector_cluster_keywords'] = []
                    coolness_data[url]['vector_cluster_coherence'] = 0.0

                # Add the embedding to coolness_data as a list (for JSON serialization)
                if embedding_field_name:
                    coolness_data[url][embedding_field_name] = embeddings[i].tolist() if embeddings[i] is not None else None

        # Add global cluster metadata
        global_metadata = {
            'vector_analysis': {
                'algorithm': cluster_algorithm,
                'n_clusters': len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0),
                'n_noise': cluster_assignments.count(-1) if -1 in cluster_assignments else 0,
                'cluster_metadata': cluster_metadata,
                'cluster_keywords': cluster_keywords,
                'cluster_similarities': cluster_similarities
            }
        }

        # Store the global metadata in a special key
        if '__metadata__' not in coolness_data:
            coolness_data['__metadata__'] = global_metadata
        else:
            coolness_data['__metadata__'].update(global_metadata)

        return coolness_data

    except Exception as e:
        logger.error(f"Error in trend analysis with vectors: {e}")
        return coolness_data


def run_legacy_twitter_scraper(twitter_scraper, coolness_data, db, max_tweets):
    """
    Run the legacy TwitterScraper to collect Twitter data.

    Args:
        twitter_scraper: TwitterScraper instance
        coolness_data: Dictionary of coolness data
        db: Database connection
        max_tweets: Maximum number of tweets to retrieve
    """
    # Get top trending categories for Twitter scraping
    trending_topics = []
    for url, data in coolness_data.items():
        if data.get('category'):
            trending_topics.append({
                'category': data['category'],
                'coolness_score': data.get('coolness_score', 0),
                'url': url
            })

    # Sort by coolness score and get top categories
    top_categories = sorted(
        trending_topics,
        key=lambda x: x['coolness_score'],
        reverse=True
    )[:3]  # Get top 3 categories

    if top_categories:
        logger.info(f"Scraping tweets for {len(top_categories)} trending categories")

        # Scrape tweets for each trending category
        for category_data in top_categories:
            category = category_data['category']
            logger.info(f"Scraping tweets for trend: {category}")

            # Get tweets for this category
            tweets = twitter_scraper.scrape_tweets(
                target=category,
                mode='term',
                number=max_tweets
            )

            if tweets:
                logger.info(f"Retrieved {len(tweets)} tweets for '{category}'")

                # Save tweets to database
                trend_id = None
                if db:
                    # Try to get the trend ID from the database
                    try:
                        cursor = db.cursor()
                        cursor.execute(
                            "SELECT id FROM coolness_data WHERE url = %s",
                            (category_data['url'],)
                        )
                        result = cursor.fetchone()
                        if result:
                            trend_id = result[0]
                    except Exception as e:
                        logger.error(f"Error getting trend ID: {e}")

                # Save tweets
                twitter_scraper.save_to_db(tweets, trend_id)

                # Calculate tweet metrics for coolness score update
                tweet_metrics = {
                    'likes': sum(t['likes'] for t in tweets),
                    'retweets': sum(t['retweets'] for t in tweets),
                    'replies': sum(t['replies'] for t in tweets),
                    'quoted': sum(t['quoted'] for t in tweets)
                }

                # Update coolness score with Twitter data
                url = category_data['url']
                if url in coolness_data:
                    # Get current scores
                    current_scores = {
                        'coolness_score': coolness_data[url].get('coolness_score', 0),
                        'velocity_score': coolness_data[url].get('velocity_score', 0),
                        'impact_score': coolness_data[url].get('impact_score', 0),
                        'novelty_score': coolness_data[url].get('novelty_score', 0)
                    }

                    # Recalculate with Twitter metrics
                    updated_scores = calculate_coolness_score(
                        text=coolness_data[url]['text'],
                        embeddings=None,  # We don't need embeddings for this update
                        tweet_metrics=tweet_metrics
                    )

                    # Update scores in coolness_data
                    coolness_data[url].update(updated_scores)

                    # Save Twitter metrics
                    if db and trend_id:
                        twitter_scraper.save_metrics(
                            hashtag=category,
                            tweet_volume=len(tweets),
                            coolness_score=updated_scores['coolness_score']
                        )

                    # Log the score change
                    logger.info(
                        f"Twitter data updated coolness score for '{category}': "
                        f"{current_scores['coolness_score']:.2f} → "
                        f"{updated_scores['coolness_score']:.2f}"
                    )


def save_results_to_json(
    coolness_data: Dict[str, Dict[str, Any]], filename: str = "results.json"
) -> None:
    """
    Save results to a JSON file.

    Args:
        coolness_data: Dictionary of coolness data
        filename: Output filename
    """
    # Convert datetime objects to strings for JSON serialization
    serializable_data = {}
    for url, data in coolness_data.items():
        serializable_data[url] = {}
        for k, v in data.items():
            # Handle date and datetime objects
            if isinstance(v, datetime) or (hasattr(v, '__class__') and v.__class__.__name__ == 'date'):
                serializable_data[url][k] = str(v)
            # Handle nested dictionaries with date objects
            elif isinstance(v, dict):
                serializable_data[url][k] = {}
                for sub_k, sub_v in v.items():
                    if isinstance(sub_v, datetime) or (hasattr(sub_v, '__class__') and sub_v.__class__.__name__ == 'date'):
                        serializable_data[url][k][sub_k] = str(sub_v)
                    else:
                        serializable_data[url][k][sub_k] = sub_v
            # Handle lists with date objects
            elif isinstance(v, list) and len(v) > 0 and isinstance(v[0], dict):
                serializable_data[url][k] = []
                for item in v:
                    if isinstance(item, dict):
                        serialized_item = {}
                        for item_k, item_v in item.items():
                            if isinstance(item_v, datetime) or (hasattr(item_v, '__class__') and item_v.__class__.__name__ == 'date'):
                                serialized_item[item_k] = str(item_v)
                            else:
                                serialized_item[item_k] = item_v
                        serializable_data[url][k].append(serialized_item)
                    else:
                        serializable_data[url][k].append(item)
            # Handle other types
            else:
                serializable_data[url][k] = v

    with open(filename, 'w') as f:
        json.dump(serializable_data, f, indent=2)

    logger.info(f"Results saved to {filename}")


def collect_data_from_all_sources(topic: str = None) -> Dict[str, Any]:
    """
    Collect data from all integrated sources using the data_source_manager.

    Args:
        topic: Optional topic to search for across all data sources

    Returns:
        Dictionary with results from all available sources
    """
    try:
        from data_sources import data_source_manager
        logger.info(f"Collecting data for topic: {topic or 'all trending topics'}")

        # Collect trend data from all available sources
        results = data_source_manager.collect_trend_data(topic)

        available_sources = data_source_manager.available_sources
        logger.info(f"Data collected from {len(available_sources)} sources: {', '.join(available_sources)}")

        return results
    except ImportError as e:
        logger.error(f"Error importing data_source_manager: {e}")
        return {}
    except Exception as e:
        logger.error(f"Error collecting data from sources: {e}")
        return {}


def main():
    """Main function."""
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(
            description='Enhanced Trend Crawler'
        )
        parser.add_argument(
            '--no-db',
            action='store_true',
            help='Skip database connection'
        )
        parser.add_argument(
            '--urls-file',
            type=str,
            default='urls.txt',
            help='Path to file containing URLs to crawl (one per line)'
        )
        parser.add_argument(
            '--url',
            action='append',
            help='URL to crawl (can be specified multiple times)'
        )
        parser.add_argument(
            '--output',
            type=str,
            default='results.json',
            help='Output JSON file for results'
        )
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
            default='INFO',
            help='Set the logging level'
        )
        parser.add_argument(
            '--simple',
            action='store_true',
            help='Use simple topic modeling instead of BERTopic'
        )
        parser.add_argument(
            '--active-learning',
            action='store_true',
            help='Enable active learning for sample selection'
        )
        parser.add_argument(
            '--meta-learning',
            action='store_true',
            help='Enable meta-learning for continuous improvement'
        )
        parser.add_argument(
            '--optimize-resources',
            action='store_true',
            help='Enable resource optimization for better performance'
        )
        parser.add_argument(
            '--distributed',
            action='store_true',
            help='Enable distributed training across multiple GPUs'
        )
        parser.add_argument(
            '--mixed-precision',
            action='store_true',
            help='Enable mixed precision operations for faster training'
        )
        parser.add_argument(
            '--auto-retrain',
            action='store_true',
            help='Enable automated retraining on concept drift detection'
        )
        parser.add_argument(
            '--twitter-enabled',
            action='store_true',
            help='Enable Twitter data scraping'
        )
        parser.add_argument(
            '--twitter-max-tweets',
            type=int,
            default=100,
            help='Maximum tweets to scrape per trend'
        )
        parser.add_argument(
            '--twitter-workers',
            type=int,
            default=3,
            help='Number of parallel Twitter scraping workers'
        )
        parser.add_argument(
            '--use-integrated-data-sources',
            action='store_true',
            help='Use integrated data sources (MacroTrends, Google Trends, etc.)'
        )
        parser.add_argument(
            '--data-topic',
            type=str,
            default=None,
            help='Topic to search for in the integrated data sources'
        )

        # Vector embedding and clustering options
        parser.add_argument(
            '--vector-model',
            type=str,
            default='all-MiniLM-L6-v2',
            help='Sentence transformer model for vector embeddings'
        )
        parser.add_argument(
            '--clustering-algorithm',
            choices=['kmeans', 'dbscan', 'agglomerative', 'affinity', 'hdbscan', 'optics'],
            default='hdbscan',
            help='Clustering algorithm for vector-based trend analysis'
        )
        parser.add_argument(
            '--cluster-count',
            type=int,
            default=5,
            help='Number of clusters for fixed-size clustering algorithms (like KMeans)'
        )
        # Vector embedding and clustering options (already defined above)
        # PostgreSQL with pgvector options
        parser.add_argument(
            '--pg-host',
            type=str,
            default=os.getenv('DB_HOST', 'localhost'),
            help='PostgreSQL database host'
        )
        parser.add_argument(
            '--pg-port',
            type=str,
            default=os.getenv('DB_PORT', '5432'),
            help='PostgreSQL database port'
        )
        parser.add_argument(
            '--pg-name',
            type=str,
            default=os.getenv('DB_NAME', 'trend_crawler'),
            help='PostgreSQL database name'
        )
        parser.add_argument(
            '--pg-user',
            type=str,
            default=os.getenv('DB_USER', 'postgres'),
            help='PostgreSQL database user'
        )
        parser.add_argument(
            '--pg-password',
            type=str,
            default=os.getenv('DB_PASSWORD', ''),
            help='PostgreSQL database password'
        )
        parser.add_argument(
            '--pg-vector-dim',
            type=int,
            default=384,
            help='Vector dimension for pgvector embeddings'
        )
        parser.add_argument(
            '--pg-index-type',
            type=str,
            choices=['hnsw', 'ivfflat', 'exact'],
            default='hnsw',
            help='pgvector index type (hnsw is fastest, exact is most accurate)'
        )
        parser.add_argument(
            '--pg-similarity-metric',
            type=str,
            choices=['cosine', 'l2', 'inner'],
            default='cosine',
            help='Similarity metric for pgvector (cosine, L2 distance, or inner product)'
        )
        parser.add_argument(
            '--pg-create-index',
            action='store_true',
            help='Create or recreate pgvector indexes'
        )
        # Vector embedding and clustering options (already defined above)
        # Clustering algorithm options (already defined above)
        parser.add_argument(
            '--auto-clusters',
            action='store_true',
            help='Automatically determine optimal number of clusters'
        )
        parser.add_argument(
            '--auto-eps',
            action='store_true',
            help='Automatically determine optimal eps parameter for DBSCAN'
        )
        # Cluster count option (already defined above)
        args = parser.parse_args()

        # Set logging level
        logging.getLogger().setLevel(getattr(logging, args.log_level))

        # Initialize monitoring system if available
        if MONITORING_AVAILABLE:
            try:
                # Start the monitoring system
                monitor.start_monitoring()
                logger.info("Monitoring system started successfully")
            except Exception as e:
                logger.warning(f"Failed to start monitoring system: {e}")

        # Check if advanced features are available
        if not ALL_ADVANCED_FEATURES:
            if not args.simple:
                logger.warning(
                    "BERTopic and related packages not available. "
                    "Using simple topic modeling instead."
                )
                args.simple = True

            if args.active_learning or args.meta_learning or args.optimize_resources:
                logger.warning(
                    "Advanced features requested but dependencies not available. "
                    "Some functionality will be limited."
                )

        # Initialize components based on arguments
        resource_manager = None
        active_learning_controller = None
        meta_trainer = None
        classifier = None
        training_orchestrator = None
        db = None

        # Use integrated data sources if requested
        integrated_data = {}
        if args.use_integrated_data_sources:
            print("\nCollecting data from integrated data sources...")
            integrated_data = collect_data_from_all_sources(args.data_topic)

            if integrated_data:
                print(f"Successfully collected data from integrated sources.")

                # Print some sample information from each source type
                source_types = {'google_trends': 'Google Trends',
                              'macrotrends': 'MacroTrends',
                              'social_media.reddit': 'Reddit',
                              'social_media.hackernews': 'Hacker News',
                              'social_media.github': 'GitHub',
                              'tradingview': 'TradingView'}

                print("\nSample data from integrated sources:")
                for source_key, source_name in source_types.items():
                    if source_key in integrated_data:
                        print(f"\n- {source_name} data:")
                        print(f"  {'*'*50}")
                        # Print a sample of the data
                        sample_data = integrated_data[source_key]
                        if isinstance(sample_data, list) and sample_data:
                            print(f"  Found {len(sample_data)} items")
                            if len(sample_data) > 0:
                                print(f"  First item sample: {str(sample_data[0])[:150]}...")
                        elif isinstance(sample_data, dict):
                            print(f"  Keys: {list(sample_data.keys())}")

                # Option to save the integrated data to a separate JSON file
                integrated_output = args.output.replace('.json', '_integrated.json')
                with open(integrated_output, 'w') as f:
                    json.dump({k: v for k, v in integrated_data.items()}, f, indent=2, default=str)
                print(f"\nIntegrated data saved to {integrated_output}")
            else:
                print("No data returned from integrated sources.")

        # Connect to database if needed
        if not args.no_db:
            # Use environment variables or defaults - no interactive prompts when using --no-db
            host = os.getenv('DB_HOST', 'localhost')
            user = os.getenv('DB_USER', 'postgres')
            password = os.getenv('DB_PASSWORD', 'postgres123')  # Default password
            db_name = os.getenv('DB_NAME', 'trend_crawler')

            # Ensure we have required values - use defaults if missing
            if not host:
                host = 'localhost'
            if not user:
                user = 'postgres'
            if not password:
                password = 'postgres123'
            if not db_name:
                db_name = 'trend_crawler'

            print(f"Database connection configured: {host}:{db_name} (user: {user})")

            print("Connecting to database...")
            try:
                db = connect_to_database(host, user, password, db_name)
                print("Database connection successful!")
                create_tables_if_not_exist(db)
                print("Database tables created/verified!")

                # Create pgvector indexes if requested
                if args.pg_create_index:
                    print(f"Creating {args.pg_index_type} indexes for vector similarity search...")
                    create_pgvector_indexes(db, index_type=args.pg_index_type, recreate=True)
                    print("Vector indexes created successfully!")

                # Initialize meta-trainer if requested
                if args.meta_learning and ALL_ADVANCED_FEATURES:
                    meta_trainer = MetaTrainer(db=db)
                    # Enable auto-retrain if requested
                    if args.auto_retrain:
                        meta_trainer.auto_retrain = True
                        logger.info("Automated retraining on concept drift enabled")
                    # Enable distributed training if requested
                    if args.distributed:
                        meta_trainer.enable_distributed_training()
                        logger.info("Distributed training enabled")
                    logger.info("Meta-learning enabled for continuous model improvement")

                    # Initialize classifier and training orchestrator
                    try:
                        from coolness_classifier import CoolnessClassifier
                        from training_orchestrator import TrainingOrchestrator

                        # Initialize classifier
                        classifier = CoolnessClassifier(db)
                        if classifier.available:
                            classifier.initialize_model()
                            logger.info("CoolnessClassifier initialized successfully")

                            # Initialize training orchestrator
                            training_orchestrator = TrainingOrchestrator(db)
                            training_orchestrator.start()
                            logger.info("Training orchestration started")
                    except ImportError as e:
                        logger.warning(f"CoolnessClassifier or TrainingOrchestrator not available: {e}")
            except psycopg2.Error as e:
                logger.error(f"Database error: {e}")
                print("Continuing without database functionality...")
        else:
            print("Skipping database connection.")

        # Initialize resource manager if requested
        if args.optimize_resources and ALL_ADVANCED_FEATURES:
            resource_manager = ResourceManager()
            # Enable mixed precision if requested
            if args.mixed_precision:
                resource_manager.mixed_precision_available = True
                resource_manager.scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
                logger.info("Mixed precision operations enabled")
            logger.info(f"Resource optimization enabled. Using device: {resource_manager.device}")

        # Load URLs from command line or file
        urls_to_crawl = []

        # First check if URLs were provided via command line
        if args.url:
            urls_to_crawl.extend(args.url)
            print(
                f"Using {len(args.url)} URLs provided via command line"
            )

        # If no URLs from command line or not enough, try loading from file
        if not urls_to_crawl:
            try:
                with open(args.urls_file, 'r') as f:
                    file_urls = [line.strip() for line in f if line.strip()]
                    urls_to_crawl.extend(file_urls)
                    print(
                        f"Loaded {len(file_urls)} URLs from {args.urls_file}"
                    )
            except (IOError, OSError) as e:
                print(
                    f"Warning: Could not read URLs file {args.urls_file}: {e}"
                )
                # Fall back to default URLs if no file
                default_urls = [
                    "https://www.google.com",
                    "https://www.yahoo.com",
                    "https://www.reddit.com",
                    "https://www.x.com",
                    "https://www.tiktok.com",
                    "https://www.tradingview.com"
                ]
                urls_to_crawl.extend(default_urls)
                print(f"Using {len(default_urls)} default URLs")

        # Initialize proxy manager if available
        proxy_manager = None
        if PROXY_MANAGER_AVAILABLE:
            try:
                proxy_manager = ProxyManager()
                logger.info("Proxy manager initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize proxy manager: {e}")

        # Crawl websites
        print("\nStarting web crawling...")
        all_texts = []
        coolness_data = {}

        for url in urls_to_crawl:
            print(f"Crawling {url}...")
            text = crawl_website(url, proxy_manager)
            if text:
                all_texts.append(text)
                coolness_data[url] = {'text': text}

                # Extract keywords for each URL
                keywords = extract_keywords(text)
                coolness_data[url]['keywords'] = keywords
                print(f"Top keywords: {', '.join(keywords[:5])}...")
            else:
                print(f"Failed to crawl {url}")

        if all_texts:
            # Get document embeddings if advanced features available
            document_embeddings = None
            if ALL_ADVANCED_FEATURES and not args.simple:
                try:
                    # Use resource manager if available
                    if resource_manager:
                        device = resource_manager.device
                    else:
                        device = torch.device(
                            "cuda" if torch.cuda.is_available() else "cpu"
                        )

                    # Create embedding model and generate embeddings using our new function
                    document_embeddings = generate_embeddings(
                        all_texts,
                        model_name=args.vector_model,
                        device=device
                    )

                    # Perform vector-based trend analysis with specified clustering algorithm
                    if document_embeddings is not None:
                        logger.info(f"Performing trend analysis with {args.clustering_algorithm} clustering")
                        coolness_data = analyze_trends_with_vectors(
                            coolness_data,
                            document_embeddings,
                            cluster_algorithm=args.clustering_algorithm,
                            n_clusters=args.cluster_count,
                            auto_clusters=args.auto_clusters,
                            auto_eps=args.auto_eps
                        )

                        # Print some vector analysis results
                        print("\n=== Vector-Based Trend Analysis Results ===")
                        if "__metadata__" in coolness_data:
                            metadata = coolness_data["__metadata__"]["vector_analysis"]
                            print(f"Clustering Algorithm: {metadata['algorithm']}")
                            print(f"Number of clusters: {metadata['n_clusters']}")

                            # Print clusters and their keywords
                            for cluster_id, keywords in metadata["cluster_keywords"].items():
                                if cluster_id != -1:  # Skip noise cluster
                                    coherence = metadata["cluster_similarities"].get(str(cluster_id) if isinstance(cluster_id, int) else cluster_id, 0)
                                    print(f"\nCluster {cluster_id} (coherence: {coherence:.3f}):")
                                    print(f"  Keywords: {', '.join(keywords[:10])}")

                                    # Print URLs in this cluster
                                    cluster_urls = [url for url, data in coolness_data.items()
                                                if url != "__metadata__" and
                                                data.get("vector_cluster_id") == cluster_id]
                                    if cluster_urls:
                                        print(f"  URLs ({len(cluster_urls)}):")
                                        for url in cluster_urls[:3]:  # Show just first 3
                                            print(f"    - {url}")
                                        if len(cluster_urls) > 3:
                                            print(f"    - ...and {len(cluster_urls) - 3} more")

                    # Initialize active learning controller if requested
                    if args.active_learning:
                        model_version_id = None
                        if meta_trainer and db:
                            # Register embedding model as a version
                            model_version_id = meta_trainer.register_model_version(
                                embedding_model,
                                {"model": "SentenceTransformer", "name": args.vector_model},
                                {"status": "initialized"}
                            )
                except Exception as e:
                    logger.error(f"Error creating embeddings: {e}")
                    document_embeddings = None

            # Topic modeling
            if args.simple or not ADVANCED_FEATURES:
                # Simple topic modeling
                topics, topic_labels = simple_topic_modeling(all_texts)
                topic_probs = None  # No probability distribution for simple modeling

                # Assign topics and calculate coolness scores
                for i, url in enumerate(urls_to_crawl):
                    if url in coolness_data and i < len(topics):
                        topic_i = topics[i]
                        coolness_data[url]['topic'] = topic_i
                        coolness_data[url]['category'] = topic_labels[topic_i]

                        # Calculate multi-factor coolness score
                        text = coolness_data[url]['text']
                        scores = calculate_coolness_score(
                            text=text,
                            embeddings=document_embeddings[i] if document_embeddings is not None else None,
                            classifier=classifier
                        )

                        # Store all scores
                        coolness_data[url].update(scores)

                        print(
                            f"{url} - Category: {topic_labels[topic_i][:30]}... - "
                            f"Score: {scores['coolness_score']:.2f} "
                            f"(V:{scores['velocity_score']:.2f}, "
                            f"I:{scores['impact_score']:.2f}, "
                            f"N:{scores['novelty_score']:.2f})"
                        )
            else:
                # Advanced topic modeling with BERTopic
                # Use resource manager if available
                if resource_manager:
                    device = resource_manager.device
                else:
                    device = torch.device(
                        "cuda" if torch.cuda.is_available() else "cpu"
                    )

                # Create and optimize embedding model
                embedding_model = SentenceTransformer(
                    "all-MiniLM-L6-v2", device=device
                )

                # Tune hyperparameters
                best_params = tune_bertopic_params(all_texts)

                # Create BERTopic model
                model = BERTopic(
                    embedding_model=embedding_model,
                    min_topic_size=best_params['min_topic_size']
                )

                # Optimize model if resource manager available
                if resource_manager:
                    model = resource_manager.optimize_for_inference(model)

                # Fit model and transform documents
                topics, probs = model.fit_transform(all_texts)

                # Register model version if meta-trainer available
                model_version_id = None
                if meta_trainer and db:
                    model_version_id = meta_trainer.register_model_version(
                        model,
                        {"min_topic_size": best_params['min_topic_size']},
                        {"num_topics": len(set(topics)) - (1 if -1 in topics else 0)}
                    )

                    # Initialize active learning controller if requested
                    if args.active_learning and active_learning_controller is None:
                        active_learning_controller = ActiveLearningController(
                            embedding_model=embedding_model,
                            db=db,
                            model_version_id=model_version_id
                        )
                        logger.info("Active learning controller initialized")

                # Calculate topic popularity
                topic_info = model.get_topic_info()
                total_docs = len(all_texts)
                topic_popularity = (
                    topic_info.set_index('Topic')['Count'] / total_docs
                )

                # Assign topics and calculate coolness scores
                for i, url in enumerate(urls_to_crawl):
                    if url in coolness_data and i < len(topics):
                        topic_i = topics[i]
                        coolness_data[url]['topic'] = topic_i

                        if topic_i == -1:
                            coolness_data[url]['category'] = None
                            # Calculate coolness without topic popularity
                            scores = calculate_coolness_score(
                                text=coolness_data[url]['text'],
                                embeddings=document_embeddings[i] if document_embeddings is not None else None,
                                classifier=classifier
                            )
                        else:
                            # Get topic label
                            topic_label = model.get_topic(topic_i)
                            category_name = " ".join(
                                [word for word, _ in topic_label[:4]]
                            )
                            coolness_data[url]['category'] = category_name

                            # Calculate coolness with topic popularity
                            scores = calculate_coolness_score(
                                text=coolness_data[url]['text'],
                                embeddings=document_embeddings[i] if document_embeddings is not None else None,
                                topic_popularity=topic_popularity.get(topic_i, 0),
                                classifier=classifier
                            )

                        # Store all scores
                        coolness_data[url].update(scores)

                        # Print results
                        category_display = "(No category)" if coolness_data[url]['category'] is None else coolness_data[url]['category'][:30]
                        print(
                            f"{url} - Category: {category_display}... - "
                            f"Score: {scores['coolness_score']:.2f} "
                            f"(V:{scores['velocity_score']:.2f}, "
                            f"I:{scores['impact_score']:.2f}, "
                            f"N:{scores['novelty_score']:.2f})"
                        )

                # Perform active learning if enabled
                if args.active_learning and active_learning_controller and document_embeddings is not None and probs is not None:
                    # Select samples for human feedback
                    selected_indices = active_learning_controller.query_samples(
                        texts=all_texts,
                        embeddings=document_embeddings,
                        topic_probs=probs,
                        k=3  # Select top 3 samples
                    )

                    print("\nSelected samples for human feedback:")
                    for idx in selected_indices:
                        if idx < len(urls_to_crawl):
                            url = urls_to_crawl[idx]
                            print(f"Sample {idx+1}: {url}")
                            print(f"Text snippet: {all_texts[idx][:100]}...")
                            print(f"Suggested category: {coolness_data.get(url, {}).get('category', 'Unknown')}")
                            print("")

                # Check for concept drift if meta-trainer available
                if meta_trainer and hasattr(model, 'topic_embeddings_'):
                    # This would be implemented in a real system with historical data
                    logger.info("Concept drift detection would run here in a production system")

            # Initialize Twitter scraping and profile analysis if enabled
            if args.twitter_enabled:
                model_version_id = None
                if meta_trainer and db:
                    model_version_id = meta_trainer.current_version

                # Try to use TrendAnalyzer first (includes profile analysis)
                try:
                    from trend_analyzer import TrendAnalyzer
                    TREND_ANALYZER_AVAILABLE = True
                except ImportError:
                    TREND_ANALYZER_AVAILABLE = False
                    logger.warning("TrendAnalyzer not available. Using legacy scrapers.")

                if TREND_ANALYZER_AVAILABLE:
                    logger.info("Using TrendAnalyzer for comprehensive trend analysis")
                    try:
                        # Initialize the trend analyzer
                        trend_analyzer = TrendAnalyzer(
                            db=db,
                            max_workers=args.twitter_workers
                        )

                        # Analyze top trends
                        analyzed_count = 0
                        for url, data in sorted(
                            coolness_data.items(),
                            key=lambda x: x[1].get('coolness_score', 0),
                            reverse=True
                        )[:5]:  # Analyze top 5 trends
                            if 'text' in data:
                                # Perform comprehensive analysis
                                logger.info(f"Analyzing trend: {url}")
                                updated_data = trend_analyzer.analyze_trend(data)

                                # Update coolness data
                                coolness_data[url].update(updated_data)
                                analyzed_count += 1

                                # Log the score change
                                logger.info(
                                    f"Comprehensive analysis updated coolness score for '{url}': "
                                    f"{data.get('coolness_score', 0):.2f} → {updated_data['coolness_score']:.2f}"
                                )

                                # Log influencer information if available
                                if 'influencers' in updated_data and updated_data['influencers']:
                                    logger.info(f"Influencers for '{url}': {', '.join(updated_data['influencers'])}")

                        logger.info(f"Successfully analyzed {analyzed_count} trends with TrendAnalyzer")

                    except Exception as e:
                        logger.error(f"Error using TrendAnalyzer: {e}")
                        logger.info("Falling back to legacy scrapers")
                        # Fall back to legacy scrapers
                elif TWITTER_SCRAPER_SERVICE_AVAILABLE:
                    logger.info("Using TwitterScraperService for Twitter data collection")
                    try:
                        # Initialize the Twitter scraper service
                        twitter_service = TwitterScraperService(
                            db=db,
                            max_workers=args.twitter_workers
                        )

                        if twitter_service.available:
                            # Get trending topics from coolness data
                            trending_topics = []
                            for url, data in coolness_data.items():
                                if data.get('category') and data.get('keywords'):
                                    trending_topics.append(data['keywords'][0])  # Use first keyword

                            # Limit to top 5 trends
                            trending_topics = trending_topics[:5]

                            # Get Twitter metrics
                            logger.info(f"Scraping Twitter metrics for {len(trending_topics)} trending topics")
                            twitter_metrics = twitter_service.get_trend_metrics(trending_topics)

                            # Update coolness scores
                            for url, data in coolness_data.items():
                                if data.get('keywords') and data['keywords'][0] in twitter_metrics:
                                    # Get the metrics for this trend
                                    metrics = twitter_metrics[data['keywords'][0]]

                                    # Store Twitter metrics in coolness data
                                    data['twitter_metrics'] = metrics
                                    data['twitter_volume'] = metrics.get('volume', 0)
                                    data['twitter_engagement'] = metrics.get('avg_likes', 0) + metrics.get('avg_retweets', 0)
                                    data['twitter_sentiment'] = metrics.get('avg_sentiment', 0)

                                    # Update coolness score
                                    old_score = data.get('coolness_score', 0)
                                    data['coolness_score'] = calculate_coolness_score_with_twitter(
                                        base_score=old_score,
                                        tweet_metrics=metrics
                                    )

                                    logger.info(
                                        f"Twitter data updated coolness score for '{data['keywords'][0]}': "
                                        f"{old_score:.2f} → {data['coolness_score']:.2f}"
                                    )

                            # Save to database
                            twitter_service.save_to_db(twitter_metrics)
                            logger.info("Twitter metrics updated successfully")
                        else:
                            logger.warning("TwitterScraperService not available, falling back to alternatives")
                            raise ImportError("TwitterScraperService not available")

                    except Exception as e:
                        logger.error(f"Error using TwitterScraperService: {e}")

                        # Try to use the TwitterXScraper next
                        if TWITTER_X_SCRAPER_AVAILABLE:
                            logger.info("Falling back to TwitterXScraper")
                            try:
                                # Create a new event loop for the async function
                                import asyncio
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)

                                # Run the async function in the new loop
                                loop.run_until_complete(update_twitter_metrics(
                                    db, coolness_data, model_version_id
                                ))
                                loop.close()

                                logger.info("Twitter metrics updated successfully with TwitterXScraper")
                            except Exception as e2:
                                logger.error(f"Error using TwitterXScraper: {e2}")
                                logger.info("Falling back to legacy TwitterScraper")
                                # Fall back to the legacy scraper
                                twitter_scraper = TwitterScraper(db=db, model_version_id=model_version_id)
                                if twitter_scraper.available:
                                    run_legacy_twitter_scraper(
                                        twitter_scraper, coolness_data, db, args.twitter_max_tweets
                                    )
                                else:
                                    logger.warning("Legacy Twitter scraper not available.")
                        else:
                            # Use the legacy TwitterScraper
                            logger.info("Falling back to legacy TwitterScraper")
                            twitter_scraper = TwitterScraper(db=db, model_version_id=model_version_id)
                            if twitter_scraper.available:
                                run_legacy_twitter_scraper(
                                    twitter_scraper, coolness_data, db, args.twitter_max_tweets
                                )
                            else:
                                logger.warning("Twitter scraper not available.")
                elif TWITTER_X_SCRAPER_AVAILABLE:
                    # Try TwitterXScraper if TwitterScraperService is not available
                    logger.info("Using TwitterXScraper for Twitter data collection")
                    try:
                        # Create a new event loop for the async function
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        # Run the async function in the new loop
                        loop.run_until_complete(update_twitter_metrics(
                            db, coolness_data, model_version_id
                        ))
                        loop.close()

                        logger.info("Twitter metrics updated successfully")
                    except Exception as e:
                        logger.error(f"Error using TwitterXScraper: {e}")
                        logger.info("Falling back to legacy TwitterScraper")
                        # Fall back to the legacy scraper
                        twitter_scraper = TwitterScraper(db=db, model_version_id=model_version_id)
                        if twitter_scraper.available:
                            run_legacy_twitter_scraper(
                                twitter_scraper, coolness_data, db, args.twitter_max_tweets
                            )
                        else:
                            logger.warning("Legacy Twitter scraper not available.")
                else:
                    # Use the legacy TwitterScraper
                    logger.info("Using legacy TwitterScraper for Twitter data collection")
                    twitter_scraper = TwitterScraper(db=db, model_version_id=model_version_id)
                    if twitter_scraper.available:
                        run_legacy_twitter_scraper(
                            twitter_scraper, coolness_data, db, args.twitter_max_tweets
                        )
                    else:
                        logger.warning("Twitter scraper not available.")

            # Save results to JSON
            save_results_to_json(coolness_data, args.output)

            # Save to database
            if db:
                print("\nSaving data to database...")
                cursor = db.cursor()
                try:
                    for url, data in coolness_data.items():
                        category_id = None
                        if data.get('category'):
                            category_id = get_or_create_category_id(
                                db, data['category'], data['category']
                            )

                        # Get model version ID if available
                        model_version_id = None
                        if meta_trainer:
                            model_version_id = meta_trainer.current_version

                        # Add vector embedding when available
                        embedding_array = None
                        i = 0  # Initialize index counter for accessing document_embeddings

                        # Get all URLs to match with embeddings
                        urls = [u for u in coolness_data.keys() if u != "__metadata__"]

                        # Find the index of the current URL in the URLs list
                        if url in urls:
                            i = urls.index(url)

                        # Get embedding for this document if available
                        if document_embeddings is not None and i < len(document_embeddings):
                            embedding_array = document_embeddings[i].tolist()

                        # Prepare SQL with pgvector embedding
                        if embedding_array:
                            # Use pgvector syntax for the embedding array
                            cursor.execute("""
                                INSERT INTO coolness_data
                                (url, coolness_score, velocity_score, impact_score,
                                novelty_score, category_id, crawl_timestamp, embedding)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s::vector)
                                ON CONFLICT (url, crawl_timestamp) DO UPDATE SET
                                coolness_score = EXCLUDED.coolness_score,
                                velocity_score = EXCLUDED.velocity_score,
                                impact_score = EXCLUDED.impact_score,
                                novelty_score = EXCLUDED.novelty_score,
                                category_id = EXCLUDED.category_id,
                                embedding = EXCLUDED.embedding
                            """, (
                                url,
                                data.get('coolness_score', 0),
                                data.get('velocity_score', 0),
                                data.get('impact_score', 0),
                                data.get('novelty_score', 0),
                                category_id,
                                datetime.now(),
                                str(embedding_array)  # Convert embedding array to string for pgvector
                            ))
                        else:
                            # Regular insertion without embeddings
                            cursor.execute("""
                                INSERT INTO coolness_data
                                (url, coolness_score, velocity_score, impact_score,
                                novelty_score, category_id, crawl_timestamp)
                                VALUES (%s, %s, %s, %s, %s, %s, %s)
                                ON CONFLICT (url, crawl_timestamp) DO UPDATE SET
                                coolness_score = EXCLUDED.coolness_score,
                                velocity_score = EXCLUDED.velocity_score,
                                impact_score = EXCLUDED.impact_score,
                                novelty_score = EXCLUDED.novelty_score,
                                category_id = EXCLUDED.category_id
                            """, (
                                url,
                                data.get('coolness_score', 0),
                                data.get('velocity_score', 0),
                                data.get('impact_score', 0),
                                data.get('novelty_score', 0),
                                category_id,
                                datetime.now()
                            ))
                    db.commit()
                    print("Data successfully saved to database!")
                except psycopg2.Error as e:
                    db.rollback()
                    print(f"Database operation failed: {e}")
                finally:
                    cursor.close()

            # Show trend similarities based on vector embeddings with pgvector
            if db and document_embeddings is not None and VECTOR_EMBEDDINGS_AVAILABLE:
                print("\n=== Similar Trends Analysis (using pgvector) ===")

                # Get top trends by coolness score
                top_trends = sorted(
                    [(url, data.get('coolness_score', 0))
                     for url, data in coolness_data.items()
                     if url != "__metadata__"],
                    key=lambda x: x[1],
                    reverse=True
                )[:3]  # Just look at top 3 trends

                # For each top trend, find similar trends
                for url, score in top_trends:
                    print(f"\nSimilar trends to {url} (coolness: {score:.2f}):")

                    # Find similar trends using pgvector similarity search with the specified metric
                    similar_trends = find_similar_trends(
                        db=db,
                        url=url,
                        model_name=args.vector_model,
                        limit=5,
                        similarity_threshold=0.6,
                        similarity_metric=args.pg_similarity_metric,
                        include_content=False,
                        search_tables=["coolness_data", "twitter_data"]
                    )

                    if similar_trends:
                        for i, trend in enumerate(similar_trends):
                            if trend['url'] != url:  # Don't show the trend itself
                                source_label = f"({trend['source']})" if 'source' in trend else ""
                                category_label = trend['category'] or 'unknown'
                                print(f"  {i+1}. {trend['url']} {source_label}")
                                print(f"     Category: {category_label}")
                                print(f"     Similarity: {trend['similarity_score']:.3f}")
                                print(f"     Coolness: {trend['coolness_score']:.2f}")
                    else:
                        print("  No similar trends found")

                # Demonstrate cross-modal search (text query to find similar trends)
                print("\n=== Cross-Modal Vector Search ===")
                print("Searching for trends similar to query: 'latest technology innovations'")

                text_query_results = find_similar_trends(
                    db=db,
                    text="latest technology innovations",
                    model_name=args.vector_model,
                    limit=3,
                    similarity_threshold=0.5,
                    similarity_metric=args.pg_similarity_metric
                )

                if text_query_results:
                    for i, trend in enumerate(text_query_results):
                        print(f"  {i+1}. {trend['url']} ({trend['category'] or 'unknown'})")
                        print(f"     Similarity: {trend['similarity_score']:.3f}")
                        print(f"     Coolness: {trend['coolness_score']:.2f}")
                else:
                    print("  No matching trends found for text query")

                # Show vector cluster information if available
                if "__metadata__" in coolness_data and "vector_analysis" in coolness_data["__metadata__"]:
                    print("\n=== Vector Cluster Analysis ===")

                    # Get cluster metadata
                    vector_metadata = coolness_data["__metadata__"]["vector_analysis"]
                    print(f"Clustering Algorithm: {vector_metadata['algorithm']}")
                    print(f"Number of clusters: {vector_metadata['n_clusters']}")
                    print(f"Noise points: {vector_metadata.get('n_noise', 0)}")

                    # Show cluster keywords
                    cluster_keywords = vector_metadata.get("cluster_keywords", {})
                    for cluster_id, keywords in cluster_keywords.items():
                        print(f"\nCluster {cluster_id} keywords: {', '.join(keywords[:5])}")

        else:
            print("No data collected from websites.")

        if db:
            db.close()
            print("Database connection closed.")

        print("\nCrawling completed successfully!")

    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
        sys.exit(0)
    except Exception as e:
        logger.exception("An unexpected error occurred")
        print(f"\nAn error occurred: {e}")
        sys.exit(1)


async def main_async():
    """Async version of the main function."""
    # Call the regular main function for now
    # In the future, this could be refactored to be fully async
    main()


if __name__ == "__main__":
    # Check if we need to run in async mode
    if '--twitter-enabled' in sys.argv:
        import asyncio
        asyncio.run(main_async())
    else:
        main()


def find_similar_trends(
    db,
    url: str = None,
    text: str = None,
    embedding: List[float] = None,
    model_name: str = "all-MiniLM-L6-v2",
    limit: int = 5,
    similarity_threshold: float = 0.7,
    similarity_metric: str = "cosine",
    include_content: bool = False,
    search_tables: List[str] = None
) -> List[Dict[str, Any]]:
    """
    Find similar trends using pgvector similarity search.

    Args:
        db: Database connection
        url: URL of trend to find similar trends for (will look up embedding)
        text: Text to encode and find similar trends for
        embedding: Pre-computed embedding to search with
        model_name: Model name for encoding text (if provided)
        limit: Maximum number of results to return
        similarity_threshold: Minimum similarity score (0-1)
        similarity_metric: Similarity metric to use ('cosine', 'l2', or 'inner')
        include_content: Whether to include text content in results
        search_tables: List of tables to search (defaults to coolness_data only)

    Returns:
        List of similar trends with similarity scores
    """
    if not db:
        logger.error("Database connection required for similar trends search")
        return []

    if not url and not text and not embedding:
        logger.error("Either url, text or embedding must be provided")
        return []

    # Default to searching only coolness_data if not specified
    if not search_tables:
        search_tables = ["coolness_data"]

    try:
        cursor = db.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get the embedding to search with
        search_embedding = None

        if embedding:
            # Use provided embedding
            search_embedding = embedding
        elif url:
            # Look up embedding for URL
            cursor.execute(
                "SELECT embedding FROM coolness_data WHERE url = %s "
                "ORDER BY crawl_timestamp DESC LIMIT 1",
                (url,)
            )
            result = cursor.fetchone()
            if result and result['embedding']:
                search_embedding = result['embedding']

        if not search_embedding and text:
            # Generate embedding for text
            try:
                embeddings = generate_embeddings([text], model_name=model_name)
                if embeddings is not None and len(embeddings) > 0:
                    search_embedding = embeddings[0].tolist()
            except Exception as e:
                logger.error(f"Error generating embedding for text: {e}")

        if not search_embedding:
            logger.error("Could not obtain embedding for similarity search")
            return []

        # Choose the appropriate similarity operator based on the metric
        if similarity_metric == "cosine":
            # Cosine similarity: 1 - (a <=> b)
            # Higher values (closer to 1) indicate more similar vectors
            similarity_op = "1 - (embedding <=> %s::vector)"
        elif similarity_metric == "l2":
            # L2 distance (Euclidean): -(embedding <-> %s::vector)
            # Negative because smaller L2 distances mean more similar vectors
            # We negate to make higher values mean more similar
            similarity_op = "-(embedding <-> %s::vector)"
        elif similarity_metric == "inner":
            # Inner product: (embedding <#> %s::vector)
            # Higher values indicate more similar vectors
            similarity_op = "(embedding <#> %s::vector)"
        else:
            # Default to cosine similarity
            similarity_op = "1 - (embedding <=> %s::vector)"

        all_results = []

        # Search each table
        for table in search_tables:
            # Determine the appropriate join and fields based on the table
            if table == "coolness_data":
                join_clause = "LEFT JOIN categories c ON cd.category_id = c.id"
                category_field = "c.category_name"
                content_field = "text" if include_content else "NULL"
            elif table == "twitter_data":
                join_clause = "LEFT JOIN coolness_data cd ON td.trend_id = cd.id"
                category_field = "NULL"
                content_field = "text" if include_content else "NULL"
            else:
                # Skip unknown tables
                logger.warning(f"Unknown table for vector search: {table}")
                continue

            # Build the query based on the table
            if table == "coolness_data":
                query = f"""
                    SELECT
                        cd.url,
                        cd.coolness_score,
                        {category_field} AS category_name,
                        {similarity_op} AS similarity_score,
                        {content_field} AS content
                    FROM
                        coolness_data cd
                    {join_clause}
                    WHERE
                        cd.embedding IS NOT NULL AND
                        {similarity_op} > %s
                    ORDER BY
                        similarity_score DESC
                    LIMIT %s
                """
            elif table == "twitter_data":
                query = f"""
                    SELECT
                        td.tweet_id AS url,
                        td.sentiment_score AS coolness_score,
                        {category_field} AS category_name,
                        {similarity_op} AS similarity_score,
                        {content_field} AS content
                    FROM
                        twitter_data td
                    {join_clause}
                    WHERE
                        td.embedding IS NOT NULL AND
                        {similarity_op} > %s
                    ORDER BY
                        similarity_score DESC
                    LIMIT %s
                """

            # Execute the query with the appropriate parameters
            cursor.execute(
                query,
                (
                    str(search_embedding),  # For the similarity_op
                    str(search_embedding) if similarity_op.count("%s") > 1 else None,  # For WHERE clause
                    similarity_threshold,
                    limit
                )
            )

            # Process results for this table
            table_results = []
            for row in cursor:
                result = {
                    'url': row['url'],
                    'coolness_score': row['coolness_score'],
                    'category': row['category_name'],
                    'similarity_score': row['similarity_score'],
                    'source': table
                }

                # Include content if requested
                if include_content and row['content']:
                    result['content'] = row['content']

                table_results.append(result)

            # Add results from this table to the overall results
            all_results.extend(table_results)

        # Sort all results by similarity score and limit to the requested number
        all_results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return all_results[:limit]

    except Exception as e:
        logger.error(f"Error in similar trends search: {e}")
        return []
