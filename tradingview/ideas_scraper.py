"""
Enhanced TradingView ideas scraper with anti-scraping capabilities.
"""
import logging
import time
import random
from typing import Dict, Any, List, Optional, Union
import json

import requests
from bs4 import BeautifulSoup

# Import original Ideas scraper as base
from tradingview_scraper.symbols.ideas import Ideas

# Import our anti-scraping components
from scraping_shield import ScrapingShield

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedIdeasScraper:
    """
    Enhanced TradingView ideas scraper with advanced anti-detection capabilities.
    Extends the original Ideas scraper with browser fingerprinting, proxy rotation,
    and other anti-scraping techniques.
    """

    def __init__(self, export_result=False, export_type='json'):
        """Initialize the enhanced ideas scraper with anti-scraping capabilities."""
        # Initialize the base scraper
        self.base_scraper = Ideas(export_result, export_type)

        # Initialize the anti-scraping shield
        self.shield = ScrapingShield()

        # Set advanced headers using the shield
        self.session = requests.Session()

    def _random_delay(self, min_delay: float = 2.0, max_delay: float = 8.0) -> None:
        """Add a random delay between requests to simulate human behavior."""
        delay = random.uniform(min_delay, max_delay)
        logger.debug(f"Adding random delay of {delay:.2f} seconds")
        time.sleep(delay)

    def scrape(self, symbol: str = "BTCUSD", startPage: int = 1, endPage: int = 1, sort: str = "popular") -> List[Dict[str, Any]]:
        """
        Enhanced scraping of trading ideas with anti-detection measures.

        Args:
            symbol: Trading symbol (e.g., "BTCUSD")
            startPage: First page to scrape
            endPage: Last page to scrape
            sort: Sorting criteria ("popular" or "recent")

        Returns:
            List of dictionaries containing trading ideas
        """
        pageList = range(startPage, endPage + 1)
        articles = []

        # Use our anti-scraping techniques
        for page in pageList:
            try:
                # Apply WAF bypass headers
                url = f"https://www.tradingview.com/symbols/{symbol}/ideas/"
                headers = self.shield.waf_bypass_headers(url)
                self.session.headers.update(headers)

                # Add TLS fingerprint spoofing
                ssl_context = self.shield.spoof_tls_fingerprint("chrome")

                # Randomize delays between requests
                self._random_delay()

                # Detect and handle rate limiting
                if sort == "popular":
                    page_articles = self._scrape_popular_ideas_enhanced(symbol, page)
                elif sort == "recent":
                    page_articles = self._scrape_recent_ideas_enhanced(symbol, page)
                else:
                    logger.error(f"Invalid sort option: {sort}")
                    raise ValueError("Sort argument must be 'popular' or 'recent'")

                # Check for legal honeypots in the content
                honeypot_detected = False
                for article in page_articles:
                    if article.get('title') and article.get('paragraph'):
                        content = article['title'] + ' ' + article['paragraph']
                        if self.shield.check_for_legal_honeypots(content, url):
                            logger.warning(f"Legal honeypot detected in idea: {article['title']}")
                            honeypot_detected = True
                            break

                if not honeypot_detected:
                    articles.extend(page_articles)
                    logger.info(f"Page {page} scraped successfully")

                # Wait with random delay before going to the next page
                if len(pageList) > 1 and page < endPage:
                    self._random_delay(3.0, 8.0)

                    # Rotate proxy randomly
                    if random.random() < 0.3:  # 30% chance to rotate proxy on each page
                        logger.info("Rotating proxy between pages")
                        self.shield.get_next_proxy()

            except Exception as e:
                logger.error(f"Error scraping page {page}: {e}")

                # If we encounter an error, try rotating the proxy
                try:
                    logger.info("Rotating proxy after error")
                    self.shield.get_next_proxy()

                    # Retry after a longer delay
                    self._random_delay(5.0, 10.0)

                    # If we're using the last retry, skip this page
                    if page < endPage:
                        continue
                except Exception as proxy_error:
                    logger.error(f"Error rotating proxy: {proxy_error}")

        # Save results if requested
        if self.base_scraper.export_result:
            self.base_scraper._export(data=articles, symbol=symbol)

        return articles

    def _scrape_popular_ideas_enhanced(self, symbol: str, page: int) -> List[Dict[str, Any]]:
        """Enhanced version of scrape_popular_ideas with anti-detection measures."""
        # If no symbol is provided check the front page
        if symbol:
            symbol_payload = f"/{symbol}/"
        else:
            symbol_payload = "/"

        url = f"https://www.tradingview.com/symbols{symbol_payload}ideas/page-{page}/?component-data-only=1&sort=recent"

        try:
            # Apply WAF bypass and device fingerprinting
            headers = self.shield.waf_bypass_headers(url)

            # Add random request patterns
            jitter = str(random.randint(1, 100))
            headers.update({
                "Cache-Control": f"max-age=0, no-cache, no-store, must-revalidate, {jitter}",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5"
            })

            # Make the request with retry mechanism
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    response = self.session.get(
                        url,
                        headers=headers,
                        timeout=15
                    )

                    if response.status_code == 200:
                        break

                    retry_count += 1
                    logger.warning(f"Retry {retry_count}/{max_retries} - Status code: {response.status_code}")
                    self._random_delay(retry_count + 2, retry_count + 5)

                except Exception as e:
                    retry_count += 1
                    logger.warning(f"Request error on retry {retry_count}/{max_retries}: {e}")
                    self._random_delay(retry_count + 2, retry_count + 5)

            if retry_count == max_retries:
                logger.error(f"Failed to retrieve data after {max_retries} attempts")
                return []

            # Use BeautifulSoup to parse the HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # Each div contains a single idea
            content = soup.find("div", class_="listContainer-rqOoE_3Q")

            if content is None:
                logger.warning("No ideas container found. TradingView structure may have changed.")
                return []

            articles_tag = content.find_all("article")
            if not articles_tag:
                logger.warning("No articles found in the ideas container.")
                return []

            return [self.base_scraper.parse_article(tag) for tag in articles_tag]

        except Exception as e:
            logger.error(f"Error in _scrape_popular_ideas_enhanced: {e}")
            return []

    def _scrape_recent_ideas_enhanced(self, symbol: str, page: int) -> List[Dict[str, Any]]:
        """Enhanced version of scrape_recent_ideas with anti-detection measures."""
        if not symbol:
            raise ValueError("Symbol cannot be null when getting recent ideas")

        symbol_payload = f"/{symbol}/"

        if page == 1:
            url = f"https://www.tradingview.com/symbols{symbol_payload}ideas/?component-data-only=1&sort=recent"
        else:
            url = f"https://www.tradingview.com/symbols{symbol_payload}ideas/page-{page}/?sort=recent&component-data-only=1&sort=recent"

        try:
            # Apply WAF bypass and device fingerprinting
            headers = self.shield.waf_bypass_headers(url)

            # Add JSON-specific headers
            headers.update({
                "Accept": "application/json, text/plain, */*",
                "X-Requested-With": "XMLHttpRequest",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty"
            })

            # Make the request with retry mechanism
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    response = self.session.get(
                        url,
                        headers=headers,
                        timeout=15
                    )

                    if response.status_code == 200:
                        break

                    retry_count += 1
                    logger.warning(f"Retry {retry_count}/{max_retries} - Status code: {response.status_code}")
                    self._random_delay(retry_count + 2, retry_count + 5)

                except Exception as e:
                    retry_count += 1
                    logger.warning(f"Request error on retry {retry_count}/{max_retries}: {e}")
                    self._random_delay(retry_count + 2, retry_count + 5)

            if retry_count == max_retries or response.status_code != 200:
                logger.error(f"Failed to retrieve data after {max_retries} attempts")
                return []

            try:
                response_json = response.json()
                items = response_json.get("data", {}).get("ideas", {}).get("data", {}).get("items", [])

                # Filter out items without a symbol
                return [item for item in items if item.pop("symbol", None) is not None]

            except json.JSONDecodeError:
                logger.error("Failed to parse JSON response")
                return []

        except Exception as e:
            logger.error(f"Error in _scrape_recent_ideas_enhanced: {e}")
            return []

    def get_sentiment_analysis(self, ideas: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze sentiment from collected trading ideas.

        Args:
            ideas: List of trading ideas

        Returns:
            Dictionary with sentiment analysis results
        """
        if not ideas:
            return {
                "positive": 0,
                "positive_percent": 0,
                "negative": 0,
                "negative_percent": 0,
                "neutral": 0,
                "neutral_percent": 0,
                "overall": "neutral",
                "total_ideas": 0
            }

        positive_count = 0
        negative_count = 0
        neutral_count = 0

        for idea in ideas:
            # Use idea_strategy as a basic sentiment indicator
            strategy = idea.get("idea_strategy")

            # Handle None or empty strategy
            if not strategy:
                neutral_count += 1
                continue

            strategy = strategy.lower()

            if "long" in strategy:
                positive_count += 1
            elif "short" in strategy:
                negative_count += 1
            else:
                neutral_count += 1

        total = len(ideas)

        # Calculate overall sentiment
        if positive_count > negative_count and positive_count > neutral_count:
            overall = "positive"
        elif negative_count > positive_count and negative_count > neutral_count:
            overall = "negative"
        else:
            overall = "neutral"

        return {
            "positive": positive_count,
            "positive_percent": (positive_count / total) * 100 if total > 0 else 0,
            "negative": negative_count,
            "negative_percent": (negative_count / total) * 100 if total > 0 else 0,
            "neutral": neutral_count,
            "neutral_percent": (neutral_count / total) * 100 if total > 0 else 0,
            "overall": overall,
            "total_ideas": total
        }

    def cleanup(self):
        """Clean up resources."""
        if self.shield:
            self.shield.cleanup()
        if self.session:
            self.session.close()