"""
Enhanced TradingView chart scraper with anti-scraping capabilities.
"""
import asyncio
import json
import logging
import re
from typing import Dict, Any, Optional, List, Union
import moment
from bs4 import BeautifulSoup
import pyppeteer
import nest_asyncio

# Import our anti-scraping components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from scraping_shield import ScrapingShield

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedChartScraper:
    """
    Enhanced TradingView chart scraper with advanced anti-detection capabilities.
    Uses the ScrapingShield to implement browser fingerprinting, proxy rotation,
    and other anti-scraping techniques.
    """

    def __init__(self):
        """Initialize the enhanced chart scraper with anti-scraping capabilities."""
        self.shield = ScrapingShield()
        self.browser = None
        self.page = None

        # Apply nest_asyncio to enable nested asyncio loops (required for integration with Flask)
        nest_asyncio.apply()

    async def setup_browser(self):
        """Set up a protected browser with anti-fingerprinting measures."""
        try:
            # Use ScrapingShield to initialize a browser with anti-detection measures
            await self.shield.init_browser()
            self.browser = self.shield.browser
            self.page = self.shield.page

            # Apply additional anti-detection measures
            await self.shield.avoid_detection("https://www.tradingview.com/")

            logger.info("Enhanced browser setup complete with anti-detection measures")
        except Exception as e:
            logger.error(f"Failed to set up enhanced browser: {e}")
            raise

    async def fetch_chart_data(self, url: str) -> str:
        """
        Fetch chart data from TradingView with anti-detection measures.

        Args:
            url: TradingView chart URL

        Returns:
            HTML content of the page
        """
        if not self.browser or not self.page:
            await self.setup_browser()

        try:
            # Use WAF bypass headers
            headers = self.shield.waf_bypass_headers(url)
            await self.page.setExtraHTTPHeaders(headers)

            # Set a random viewport size
            await self.page.setViewport({
                'width': 1024 + (hash(url) % 200),  # Add some randomization
                'height': 768 + (hash(url) % 150)
            })

            # Navigate to the URL with retry mechanism
            max_retries = 3
            retry_count = 0

            while retry_count < max_retries:
                try:
                    # Navigate to the page
                    response = await self.page.goto(url, {
                        'waitUntil': 'networkidle2',
                        'timeout': 60000
                    })

                    # Check if we need to solve a CAPTCHA
                    if await self._check_for_captcha():
                        await self._solve_captcha()

                    # Wait for critical element
                    await self.page.waitForSelector('.pane-legend-title__container', {'timeout': 30000})

                    # Add random delays and scrolling to simulate human behavior
                    await self.shield.simulate_human_behavior()

                    # Get the content
                    content = await self.page.content()
                    return content

                except Exception as e:
                    retry_count += 1
                    logger.warning(f"Retry {retry_count}/{max_retries} due to error: {e}")

                    # Rotate proxy if needed
                    if retry_count < max_retries:
                        await self.shield.rotate_proxy()
                        await self.setup_browser()

            raise Exception(f"Failed to fetch chart data after {max_retries} retries")

        except Exception as e:
            logger.error(f"Error fetching chart data: {e}")
            # Cleanup and re-raise
            await self.cleanup()
            raise

    async def _check_for_captcha(self) -> bool:
        """Check if a CAPTCHA is present on the page."""
        try:
            # Check for common CAPTCHA elements
            captcha_selectors = [
                '.g-recaptcha',
                'iframe[src*="recaptcha"]',
                'iframe[src*="captcha"]',
                '#captcha',
                '.captcha-container'
            ]

            for selector in captcha_selectors:
                if await self.page.querySelector(selector):
                    logger.warning("CAPTCHA detected on TradingView")
                    return True

            return False
        except Exception as e:
            logger.error(f"Error checking for CAPTCHA: {e}")
            return False

    async def _solve_captcha(self) -> bool:
        """Solve CAPTCHA if present using the ScrapingShield's CAPTCHA solver."""
        try:
            # Find the CAPTCHA site key
            site_key = await self.page.evaluate("""
                () => {
                    const recaptchaDiv = document.querySelector('.g-recaptcha');
                    return recaptchaDiv ? recaptchaDiv.getAttribute('data-sitekey') : null;
                }
            """)

            if not site_key:
                logger.warning("Could not find reCAPTCHA site key")
                return False

            # Use the ScrapingShield's advanced CAPTCHA solver
            solution = await self.shield.handle_advanced_captcha(
                site_key=site_key,
                url=self.page.url,
                captcha_type="recaptcha"
            )

            if solution:
                # Apply the CAPTCHA solution
                await self.page.evaluate(f"""
                    document.getElementById('g-recaptcha-response').innerHTML = '{solution}';
                    document.querySelector('form').submit();
                """)

                # Wait for navigation after CAPTCHA solution
                await self.page.waitForNavigation({'waitUntil': 'networkidle2'})
                return True
            else:
                logger.error("Failed to solve TradingView CAPTCHA")
                return False

        except Exception as e:
            logger.error(f"Error solving CAPTCHA: {e}")
            return False

    def extract_chart_data(self, content: str) -> Dict[str, Any]:
        """
        Extract chart data from HTML content.

        Args:
            content: HTML content of the TradingView chart page

        Returns:
            Dictionary with extracted chart data
        """
        try:
            soup = BeautifulSoup(content, 'lxml')

            # Check for legal honeypots
            if self.shield.check_for_legal_honeypots(content, "tradingview.com"):
                logger.warning("Legal honeypot detected in TradingView chart data")
                return {'error': 'Legal honeypot detected'}

            # Extract indicators
            ind_titles = soup.findAll(attrs={"class": "pane-legend-line"})
            indicator_titles = []
            indicator_values = []

            for ind in ind_titles:
                name = ind.find(attrs={"class": "pane-legend-title__description"})
                values = ind.findAll(attrs={"class": "pane-legend-item-value-wrap"})
                loc_values = []

                for val in values:
                    loc_values.append(val.get_text())

                indicator_values.append(' '.join(map(str, loc_values)))
                indicator_titles.append(name.get_text() if name else "Unknown")

            # Extract chart options data
            json_string = soup.find(attrs={"class": "js-chart-view"})
            if not json_string or not json_string.get('data-options'):
                return {'error': 'Could not find chart data'}

            parsed_string = json.loads(json_string['data-options'])
            parsed_string = json.loads(parsed_string['content'])['panes']

            # Extract main series and indicators
            main = None
            indicators = []

            for item in parsed_string:
                for item2 in item['sources']:
                    if item2['type'] == 'MainSeries':
                        main = item2
                    elif item2['type'] == 'Study':
                        indicators.append(item2)

            if not main:
                return {'error': 'Could not find main series data'}

            # Extract chart title
            title_element = soup.find(attrs={"class": "pane-legend-title__container"})
            title = title_element.get_text() if title_element else 'Untitled'

            # Process OHLC data
            bars_data = main['bars']['data']
            processed_data = []

            close_price = None
            for i, bar in enumerate(bars_data):
                ohlc = bar['value'][0:6]  # time, open, high, low, close, volume

                # Calculate percentage change
                percent_change = None
                if close_price is not None:
                    percent_change = round((ohlc[4] - close_price) / (close_price / 100), 2)
                close_price = ohlc[4]

                # Get indicator values for this bar
                indicator_data = []
                for indicator in indicators:
                    for data_point in indicator['data']['data']:
                        if data_point['value'][0] == ohlc[0]:  # Match on timestamp
                            indicator_data.append({
                                'name': indicator['metaInfo']['shortDescription'],
                                'values': data_point['value'][1:]
                            })
                            break

                # Format timestamp
                timestamp = moment.unix(ohlc[0] * 1000, utc=True).format("YYYY-MM-DD HH:mm:ss")

                processed_data.append({
                    'time': ohlc[0],
                    'open': ohlc[1],
                    'high': ohlc[2],
                    'low': ohlc[3],
                    'close': ohlc[4],
                    'volume': ohlc[5],
                    'percent_change': percent_change,
                    'timestamp': timestamp,
                    'indicator_values': indicator_data
                })

            return {
                'title': title,
                'symbol': main.get('metaInfo', {}).get('symbol', ''),
                'indicators': [{
                    'name': ind['metaInfo']['shortDescription'],
                    'inputs': ind['state'].get('inputs', {})
                } for ind in indicators],
                'data': processed_data
            }

        except Exception as e:
            logger.error(f"Error extracting chart data: {e}")
            return {'error': str(e)}

    def convert_to_csv(self, data: Dict[str, Any]) -> str:
        """
        Convert extracted data to CSV format.

        Args:
            data: Dictionary with extracted chart data

        Returns:
            CSV string with chart data
        """
        if 'error' in data:
            return f"Error: {data['error']}"

        try:
            # Create header row
            columns = ['time', 'open', 'high', 'low', 'close', 'volume', '%', 'id', 'timestamp']

            # Add indicator columns
            for indicator in data['indicators']:
                columns.append(f'"{indicator["name"]}"')

            csv_rows = [','.join(columns)]

            # Add data rows
            for i, bar in enumerate(data['data']):
                row = [
                    str(bar['time']),
                    str(bar['open']),
                    str(bar['high']),
                    str(bar['low']),
                    str(bar['close']),
                    str(bar['volume']),
                    str(bar['percent_change'] or ''),
                    str(i),
                    bar['timestamp']
                ]

                # Add indicator values
                indicator_values = []
                for ind in bar['indicator_values']:
                    indicator_values.extend(map(str, ind['values']))

                row.extend(indicator_values)
                csv_rows.append(','.join(row))

            return '\n'.join(csv_rows)

        except Exception as e:
            logger.error(f"Error converting to CSV: {e}")
            return f"Error: {e}"

    async def get_chart_data(self, url: str, output_format: str = 'json') -> Union[Dict[str, Any], str]:
        """
        Fetch and process TradingView chart data with anti-detection measures.

        Args:
            url: TradingView chart URL
            output_format: Output format ('json' or 'csv')

        Returns:
            Chart data in the requested format
        """
        try:
            content = await self.fetch_chart_data(url)
            data = self.extract_chart_data(content)

            if output_format.lower() == 'csv':
                return self.convert_to_csv(data)
            else:
                return data

        except Exception as e:
            logger.error(f"Error getting chart data: {e}")
            if output_format.lower() == 'csv':
                return f"Error: {e}"
            else:
                return {'error': str(e)}
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Clean up resources."""
        if self.shield:
            await self.shield.cleanup()