#!/usr/bin/env python3
"""
Isolated test for ultra-lite TensorFlow solver.
"""

def test_ultra_lite_import():
    """Test ultra-lite TensorFlow solver import."""
    print("Testing ultra-lite TensorFlow solver import...")
    
    try:
        # Test direct import of the ultra-lite solver
        import sys
        import os
        
        # Add current directory to path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Try to import the ultra-lite solver directly
        from ml_captcha_solvers.tensorflow_solver_ultra_lite import TensorFlowCaptchaSolverUltraLite
        print("✅ Ultra-lite TensorFlow solver imported successfully")
        
        # Test initialization
        solver = TensorFlowCaptchaSolverUltraLite()
        print("✅ Ultra-lite solver initialized successfully")
        
        # Test that model is not loaded yet
        assert not solver._model_loaded, "Model should not be loaded during initialization"
        print("✅ Lazy loading state verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Ultra-lite solver test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_package_import():
    """Test package-level import."""
    print("Testing package-level import...")
    
    try:
        from ml_captcha_solvers import TENSORFLOW_AVAILABLE, TensorFlowCaptchaSolver
        print(f"✅ Package import successful, TensorFlow available: {TENSORFLOW_AVAILABLE}")
        
        if TENSORFLOW_AVAILABLE:
            solver = TensorFlowCaptchaSolver()
            print("✅ Package-level solver initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Package import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting ultra-lite TensorFlow solver tests...")
    
    # Test 1: Direct import
    success1 = test_ultra_lite_import()
    
    # Test 2: Package import
    success2 = test_package_import()
    
    if success1 and success2:
        print("🎉 All tests passed!")
        exit(0)
    else:
        print("❌ Some tests failed!")
        exit(1)
