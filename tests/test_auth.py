import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from app.main import app # Assuming the FastAPI app instance is in app/main.py
from app.database import Base, get_db # Assuming these are in app/database.py
from app.models import User  # Assuming User model is in app/models.py
# Assuming ProfileUpdate schema is used by the endpoint, not directly in test, but good for context
# from app.schemas import ProfileUpdate 
from app.core.security import create_access_token # For generating tokens

# Use a file-based SQLite database for testing.
# Using a unique name related to the test file.
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_auth_profile.db" 
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Create database tables once per test session and drop them after."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session() -> Session:
    """
    Provides a clean database session for each test function using transactions.
    Data is rolled back after each test to ensure isolation.
    """
    connection = engine.connect()
    transaction = connection.begin()
    db = TestingSessionLocal(bind=connection)

    try:
        yield db
    finally:
        db.close() # Close the session
        transaction.rollback() # Rollback any changes
        connection.close() # Close the connection


@pytest.fixture(scope="function")
def client(db_session: Session):
    """Provides a TestClient instance with overridden DB dependency for each test."""
    app.dependency_overrides[get_db] = lambda: db_session
    with TestClient(app) as c:
        yield c
    app.dependency_overrides.clear() # Clear overrides after test


@pytest.fixture(scope="function")
def test_user(db_session: Session) -> User:
    """Creates and returns a test user in the database."""
    user_data = {"username": "testuser", "email": "<EMAIL>", "full_name": "Test User"}
    # Assuming User model has a 'hashed_password' field and it's handled appropriately.
    user = User(**user_data, hashed_password="fakehashedpassword1")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture(scope="function")
def test_user_token(test_user: User) -> str:
    """Generates an access token for the test user."""
    # Ensure the subject ('sub') matches what your token verification expects (e.g., username or id)
    return create_access_token(data={"sub": test_user.username})


@pytest.fixture(scope="function")
def another_user(db_session: Session) -> User:
    """Creates and returns a second test user for specific test cases."""
    user_data = {"username": "anotheruser", "email": "<EMAIL>", "full_name": "Another User"}
    user = User(**user_data, hashed_password="fakehashedpassword2")
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

# --- Test Cases ---

def test_successful_profile_update(client: TestClient, test_user_token: str, test_user: User, db_session: Session):
    """Test successful profile update with valid data and token."""
    new_email = "<EMAIL>"
    new_full_name = "New Valid Full Name"
    headers = {"Authorization": f"Bearer {test_user_token}"}
    payload = {"email": new_email, "full_name": new_full_name}

    response = client.post("/api/v1/users/profile", headers=headers, json=payload)

    assert response.status_code == 200, response.text
    response_data = response.json()
    
    assert "message" in response_data
    assert response_data["message"] == "Profile updated successfully" 
    
    assert "user" in response_data
    returned_user = response_data["user"]
    assert returned_user["email"] == new_email
    assert returned_user["full_name"] == new_full_name
    assert returned_user["username"] == test_user.username # Username should not change

    # Verify in database by refreshing the original test_user object
    db_session.refresh(test_user) 
    assert test_user.email == new_email
    assert test_user.full_name == new_full_name
    assert test_user.username == test_user.username 


def test_profile_update_invalid_email_format(client: TestClient, test_user_token: str):
    """Test profile update with an invalid email format."""
    headers = {"Authorization": f"Bearer {test_user_token}"}
    payload = {"email": "not-a-valid-email", "full_name": "Any Name"}

    response = client.post("/api/v1/users/profile", headers=headers, json=payload)
    
    assert response.status_code == 422, response.text # Unprocessable Entity for Pydantic validation error


def test_profile_update_email_already_in_use(client: TestClient, test_user_token: str, test_user: User, another_user: User, db_session: Session):
    """Test profile update attempting to use an email already taken by another user."""
    original_test_user_email = test_user.email
    headers = {"Authorization": f"Bearer {test_user_token}"}
    payload = {"email": another_user.email, "full_name": "Updated Name for test_user"}

    response = client.post("/api/v1/users/profile", headers=headers, json=payload)

    assert response.status_code == 400, response.text # Bad Request
    response_data = response.json()
    assert "detail" in response_data
    assert "Email address is already in use" in response_data["detail"]

    # Verify in database that the email was not changed
    db_session.refresh(test_user)
    assert test_user.email == original_test_user_email 


def test_profile_update_without_authentication(client: TestClient):
    """Test profile update request without providing an authentication token."""
    payload = {"email": "<EMAIL>", "full_name": "Some Name"}

    response = client.post("/api/v1/users/profile", json=payload)

    assert response.status_code == 401, response.text # Unauthorized


def test_username_cannot_be_changed(client: TestClient, test_user_token: str, test_user: User, db_session: Session):
    """
    Test that 'username' field in payload is ignored and username does not change.
    The ProfileUpdate Pydantic model used by the endpoint should not include 'username'.
    """
    original_username = test_user.username
    original_email = test_user.email # Store original email to ensure it can be changed
    new_email_for_this_test = "<EMAIL>"
    
    headers = {"Authorization": f"Bearer {test_user_token}"}
    # Payload includes 'username', which should be ignored by the Pydantic model 
    # if ProfileUpdate schema does not define it.
    payload = {
        "email": new_email_for_this_test,
        "full_name": "Specific Full Name",
        "username": "attempt_to_change_username" 
    }

    response = client.post("/api/v1/users/profile", headers=headers, json=payload)

    assert response.status_code == 200, response.text # Update of email/full_name should succeed
    
    response_data = response.json()
    assert "user" in response_data
    returned_user = response_data["user"]
    assert returned_user["username"] == original_username # Username in response should be unchanged
    assert returned_user["email"] == new_email_for_this_test

    # Verify in database
    db_session.refresh(test_user)
    assert test_user.username == original_username # Username in DB should be unchanged
    assert test_user.email == new_email_for_this_test # Email should be updated
    assert test_user.full_name == "Specific Full Name" # Full name should be updated
    assert test_user.email != original_email # Ensure email did change from original
