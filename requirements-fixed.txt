# Core libraries
requests==2.31.0
aiohttp==3.9.1
beautifulsoup4==4.12.2
selenium==4.15.2
undetected-chromedriver==3.5.4
playwright==1.40.0
python-dotenv==1.0.0
psycopg2-binary==2.9.9
numpy==1.26.2
pandas==2.1.3
pillow==9.2.0
opencv-python==4.6.0.66
tensorflow==2.9.1
cryptography==41.0.5

# Anti-scraping specific
fake-useragent==1.3.0
selenium-stealth==1.0.6
curl-cffi==0.5.5
fp-fetch==0.1.6
nest-asyncio==1.6.0
python-json-logger==2.0.4

# CAPTCHA solvers
2captcha-python==1.2.2
anticaptchaofficial==1.0.56
capsolver==1.0.5

# WebSockets with proper version for compatibility
websockets==10.4

# Trend analysis
scikit-learn==1.3.2
nltk==3.8.1
textblob==0.17.1
transformers==4.35.2
sentence-transformers==2.2.2
bertopic==0.12.0
hdbscan==0.8.28
umap-learn==0.5.3

# Twitter/Social scraping (compatible versions)
twitter-scraper-selenium==2.0.4
ntscraper==0.6.0
tweepy==4.10.1
pytrends==4.8.0
snscrape==0.7.0.20230622

# TradingView scraping
tradingview-scraper==1.1.0
moment==0.12.1

# Distributed computing
distributed==2023.11.0
dask==2023.11.0

# Testing
pytest==8.3.5
pytest-asyncio==0.23.5

# Documentation
sphinx==5.1.1
sphinx-rtd-theme==1.0.0

# Additional dependencies
pyppeteer==1.0.2
tqdm==4.66.1
bs4==0.0.1
lxml==5.0.1
pyopenssl==23.2.0
rotating-proxy-py==1.0.0
proxybroker==0.3.2
python-socks==2.3.0
requests[socks]==2.31.0
proxy-checker==0.6
ProxyPool==1.2.8
torch==2.1.1
prometheus-client==0.17.1
psutil==5.9.6
flask==3.0.0
sqlalchemy==2.0.23
pymysql==1.1.0
retry==0.9.2
tenacity==8.2.3