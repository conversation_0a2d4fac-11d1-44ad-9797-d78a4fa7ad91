#!/usr/bin/env python3
"""
Test script to identify the exact SQL type mismatch issue
"""

import psycopg2
import os
from datetime import datetime, timedelta

def test_sql_issue():
    """Test the specific SQL query that's causing issues"""
    try:
        # Database connection
        conn = psycopg2.connect(
            host="localhost",
            database="trend_crawler",
            user="trendc",
            password="HJn2Bs1k$1s3krPO2"
        )
        cursor = conn.cursor()
        
        print("✅ Database connection successful")
        
        # Test 1: Check proxy_metrics table structure
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'proxy_metrics' 
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print("\n📋 proxy_metrics table structure:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} (nullable: {col[2]})")
        
        # Test 2: Check actual data types in proxy_metrics
        cursor.execute("SELECT proxy_id, timestamp FROM proxy_metrics LIMIT 5;")
        sample_data = cursor.fetchall()
        print(f"\n📊 Sample data from proxy_metrics:")
        for row in sample_data:
            print(f"  proxy_id: {row[0]} (type: {type(row[0])})")
            print(f"  timestamp: {row[1]} (type: {type(row[1])})")
            break
        
        # Test 3: Try the problematic query with different approaches
        print(f"\n🧪 Testing different query approaches:")
        
        # Current time minus 1 hour for testing
        time_limit = datetime.now() - timedelta(hours=1)
        
        # Get a sample proxy_id
        cursor.execute("SELECT DISTINCT proxy_id FROM proxy_metrics LIMIT 1;")
        result = cursor.fetchone()
        if result:
            proxy_id = result[0]
            print(f"Testing with proxy_id: {proxy_id} (type: {type(proxy_id)})")
            
            # Test approach 1: Original query that might be failing
            try:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successful_requests,
                        SUM(CASE WHEN success = false THEN 1 ELSE 0 END) as failed_requests,
                        AVG(response_time) as avg_response_time,
                        MIN(response_time) as min_response_time,
                        MAX(response_time) as max_response_time
                    FROM proxy_metrics
                    WHERE proxy_id = %s::text AND timestamp > %s
                ''', (proxy_id, time_limit))
                result = cursor.fetchone()
                print("  ❌ Query with ::text casting failed as expected")
            except Exception as e:
                print(f"  ❌ Query with ::text casting failed: {e}")
            
            # Test approach 2: Fixed query without casting
            try:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successful_requests,
                        SUM(CASE WHEN success = false THEN 1 ELSE 0 END) as failed_requests,
                        AVG(response_time) as avg_response_time,
                        MIN(response_time) as min_response_time,
                        MAX(response_time) as max_response_time
                    FROM proxy_metrics
                    WHERE proxy_id = %s AND timestamp > %s
                ''', (proxy_id, time_limit))
                result = cursor.fetchone()
                print(f"  ✅ Query without casting successful: {result}")
            except Exception as e:
                print(f"  ❌ Query without casting failed: {e}")
                
            # Test approach 3: Ensure proper type conversion
            try:
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as successful_requests,
                        SUM(CASE WHEN success = false THEN 1 ELSE 0 END) as failed_requests,
                        AVG(response_time) as avg_response_time,
                        MIN(response_time) as min_response_time,
                        MAX(response_time) as max_response_time
                    FROM proxy_metrics
                    WHERE proxy_id = %s AND timestamp > %s
                ''', (str(proxy_id), time_limit))
                result = cursor.fetchone()
                print(f"  ✅ Query with str() conversion successful: {result}")
            except Exception as e:
                print(f"  ❌ Query with str() conversion failed: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sql_issue()
