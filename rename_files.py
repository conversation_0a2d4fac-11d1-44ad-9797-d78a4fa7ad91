#!/usr/bin/env python3
"""
Script to rename files in the dataSets directory to a more standardized format.
- Replaces spaces with underscores
- Removes author names (after " -- ")
- Removes website references like "( WeLib.org )" and "( PDFDrive )"
"""

import os
import re
import sys
from pathlib import Path

def standardize_name(name, is_directory=False):
    """
    Standardize a filename or directory name by:
    1. Removing author names (after " -- ")
    2. Removing website references like "( WeLib.org )" and "( PDFDrive )"
    3. Replacing spaces with underscores
    4. Removing punctuation marks (commas, apostrophes, brackets, parentheses)
    """
    if is_directory:
        # For directories, there's no extension
        base = name
        ext = ""
    else:
        # For files, split into base and extension
        base, ext = os.path.splitext(name)

    # Remove author names (typically after " -- ")
    base = re.sub(r'\s+--\s+.*?(?=\s+\(|\s*$)', '', base)

    # Remove website references
    base = re.sub(r'\s*\(\s*WeLib\.org\s*\)\s*', '', base)
    base = re.sub(r'\s*\(\s*PDFDrive\s*\)\s*', '', base)

    # Remove punctuation marks
    # First remove website references in parentheses like (PDFDrive.com)
    base = re.sub(r'\s*\([^)]*(?:PDFDrive|WeLib)[^)]*\)', '', base)

    # Remove version numbers in parentheses like (2) or (v2)
    base = re.sub(r'\s*\(\d+\)', '', base)
    base = re.sub(r'\s*\(v\d+\)', '', base)

    # Remove common annotations in brackets like [auth.] or [Solution Manual]
    base = re.sub(r'\s*\[[^\]]*(?:auth|solution|manual|writer)[^\]]*\]', '', base, flags=re.IGNORECASE)

    # Now remove remaining punctuation marks
    base = re.sub(r'[,\'"\(\)\[\]\{\}]', '', base)

    # Replace spaces with underscores
    base = base.replace(' ', '_')

    # Remove any consecutive underscores
    base = re.sub(r'_{2,}', '_', base)

    # Remove any trailing underscores
    base = base.rstrip('_')

    # Return the standardized name
    return f"{base}{ext}"

def standardize_filename(filename):
    """Wrapper for standardize_name for files"""
    return standardize_name(filename, is_directory=False)

def rename_directories(directory):
    """
    Rename directories to standardized format.
    This needs to be done bottom-up to avoid renaming parent directories
    before their children.
    """
    dir_count = 0
    dir_skipped = 0
    dir_errors = 0

    print(f"Scanning for directories to rename...")

    # Get all directories and sort them by depth (descending)
    all_dirs = []
    for root, dirs, _ in os.walk(directory):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            # Calculate depth by counting path separators
            depth = dir_path.count(os.path.sep)
            all_dirs.append((dir_path, depth))

    # Sort by depth in descending order (deepest first)
    all_dirs.sort(key=lambda x: x[1], reverse=True)

    print(f"Found {len(all_dirs)} directories to process")

    # Process directories from deepest to shallowest
    for dir_path, _ in all_dirs:
        dir_name = os.path.basename(dir_path)
        parent_dir = os.path.dirname(dir_path)

        new_dir_name = standardize_name(dir_name, is_directory=True)

        # Skip if the directory name is already standardized
        if new_dir_name == dir_name:
            dir_skipped += 1
            if dir_skipped % 20 == 0:
                print(f"Skipped {dir_skipped} directories (already standardized)")
            continue

        new_dir_path = os.path.join(parent_dir, new_dir_name)

        # Check if the new path already exists
        if os.path.exists(new_dir_path) and dir_path != new_dir_path:
            print(f"Skipping directory {dir_path} - {new_dir_path} already exists")
            dir_skipped += 1
            continue

        try:
            os.rename(dir_path, new_dir_path)
            print(f"Renamed directory: {dir_name} -> {new_dir_name}")
            dir_count += 1
        except Exception as e:
            print(f"Error renaming directory {dir_path}: {e}")
            dir_errors += 1

    print(f"\nDirectory Renaming Summary:")
    print(f"  - {dir_count} directories renamed")
    print(f"  - {dir_skipped} directories skipped")
    print(f"  - {dir_errors} errors encountered")

    return dir_count

def rename_files_in_directory(directory):
    """
    Rename all files in the given directory and its subdirectories.
    """
    count = 0
    skipped = 0
    errors = 0

    print(f"Scanning directory for files: {directory}")

    for root, _, files in os.walk(directory):
        print(f"Processing folder: {root} ({len(files)} files)")

        for filename in files:
            old_path = os.path.join(root, filename)
            new_filename = standardize_filename(filename)

            # Skip if the filename is already standardized
            if new_filename == filename:
                skipped += 1
                if skipped % 100 == 0:
                    print(f"Skipped {skipped} files (already standardized)")
                continue

            new_path = os.path.join(root, new_filename)

            # Check if the new path already exists
            if os.path.exists(new_path) and old_path != new_path:
                print(f"Skipping {old_path} - {new_path} already exists")
                skipped += 1
                continue

            try:
                os.rename(old_path, new_path)
                print(f"Renamed file: {filename} -> {new_filename}")
                count += 1

                # Print progress every 100 files
                if count % 100 == 0:
                    print(f"Progress: {count} files renamed")

            except Exception as e:
                print(f"Error renaming {old_path}: {e}")
                errors += 1

    print(f"\nFile Renaming Summary:")
    print(f"  - {count} files renamed")
    print(f"  - {skipped} files skipped")
    print(f"  - {errors} errors encountered")

    return count

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = "/home/<USER>/appsUndCode/dataSets/"

    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)

    print(f"Starting standardization process for {directory}...")

    # First rename directories (bottom-up)
    dir_count = rename_directories(directory)

    # Then rename files
    file_count = rename_files_in_directory(directory)

    print(f"\nStandardization complete:")
    print(f"  - {dir_count} directories renamed")
    print(f"  - {file_count} files renamed")
