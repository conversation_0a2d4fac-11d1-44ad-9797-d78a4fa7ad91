#!/usr/bin/env python3
"""
Secure API bridge for trend-crawler

This module implements secure API connections with rate limiting, TLS 1.3,
token bucket algorithms, and encrypted API key management.
"""

import os
import time
import logging
import json
import ssl
import threading
import http.client
import urllib.request
import urllib.error
import urllib.parse
from typing import Dict, Any, Optional, Union, Tuple, Callable
from contextlib import contextmanager
from cryptography.fernet import Fernet
import secrets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TokenBucket:
    """
    Implements the token bucket algorithm for rate limiting.
    
    This enforces rate limits for API requests and token usage.
    """
    
    def __init__(self, 
                 rate: float, 
                 capacity: float, 
                 initial_tokens: Optional[float] = None):
        """
        Initialize the token bucket.
        
        Args:
            rate: Token refill rate per second
            capacity: Maximum bucket capacity
            initial_tokens: Initial number of tokens in the bucket
        """
        self.rate = float(rate)
        self.capacity = float(capacity)
        self.tokens = float(initial_tokens if initial_tokens is not None else capacity)
        self.last_refill = time.time()
        self.lock = threading.RLock()
    
    def consume(self, tokens: float = 1.0) -> bool:
        """
        Consume tokens from the bucket.
        
        Args:
            tokens: Number of tokens to consume
            
        Returns:
            True if tokens were consumed, False if not enough tokens
        """
        with self.lock:
            self._refill()
            if tokens <= self.tokens:
                self.tokens -= tokens
                return True
            return False
    
    def _refill(self) -> None:
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill
        
        if elapsed > 0:
            new_tokens = elapsed * self.rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
    
    @property
    def level(self) -> float:
        """
        Get current token level.
        
        Returns:
            Current number of tokens in bucket
        """
        with self.lock:
            self._refill()
            return self.tokens
    
    @property
    def level_percent(self) -> float:
        """
        Get current token level as percentage.
        
        Returns:
            Percentage of capacity (0-100)
        """
        with self.lock:
            self._refill()
            return (self.tokens / self.capacity) * 100


class RateLimitException(Exception):
    """Raised when rate limits are exceeded"""
    pass


class APIKeyManager:
    """
    Securely manages API keys with encryption.
    """
    
    def __init__(self, key_file: str = None):
        """
        Initialize the API key manager.
        
        Args:
            key_file: Path to encrypted key file
        """
        self.key_file = key_file or os.getenv('API_KEY_PATH', 'encrypted_keys.bin')
        self.encryption_key = os.getenv('ENCRYPTION_KEY')
        
        # Generate encryption key if not present
        if not self.encryption_key:
            self.encryption_key = Fernet.generate_key().decode('utf-8')
            logger.warning("Generated new encryption key. Store this securely.")
        
        self.cipher = Fernet(self.encryption_key.encode('utf-8'))
        self.keys = {}
        
        # Load keys if file exists
        if os.path.exists(self.key_file):
            self._load_keys()
    
    def _load_keys(self) -> None:
        """Load encrypted keys from file"""
        try:
            with open(self.key_file, 'rb') as f:
                encrypted_data = f.read()
            
            data = self.cipher.decrypt(encrypted_data)
            self.keys = json.loads(data.decode('utf-8'))
            logger.info(f"Loaded {len(self.keys)} API keys")
        except Exception as e:
            logger.error(f"Failed to load API keys: {e}")
            self.keys = {}
    
    def _save_keys(self) -> None:
        """Save encrypted keys to file"""
        try:
            data = json.dumps(self.keys).encode('utf-8')
            encrypted_data = self.cipher.encrypt(data)
            
            with open(self.key_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set secure permissions
            os.chmod(self.key_file, 0o600)
            logger.info(f"Saved {len(self.keys)} API keys")
        except Exception as e:
            logger.error(f"Failed to save API keys: {e}")
    
    def add_key(self, service: str, key: str) -> None:
        """
        Add or update an API key.
        
        Args:
            service: Service name
            key: API key
        """
        self.keys[service] = key
        self._save_keys()
    
    def get_key(self, service: str) -> Optional[str]:
        """
        Get an API key.
        
        Args:
            service: Service name
            
        Returns:
            API key or None if not found
        """
        return self.keys.get(service)
    
    def rotate_key(self, service: str, new_key: str) -> None:
        """
        Rotate an API key.
        
        Args:
            service: Service name
            new_key: New API key
        """
        if service in self.keys:
            old_key = self.keys[service]
            self.keys[service] = new_key
            self._save_keys()
            return old_key
        return None


class TLSClient:
    """
    HTTP client with TLS 1.3 security and connection pooling.
    """
    
    def __init__(self, 
                 base_url: str = None, 
                 timeout: int = 30,
                 verify_ssl: bool = True):
        """
        Initialize the TLS client.
        
        Args:
            base_url: Base URL for requests
            timeout: Connection timeout in seconds
            verify_ssl: Whether to verify SSL certificates
        """
        self.base_url = base_url
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        
        # Create SSL context with secure TLS 1.3
        self.context = ssl.create_default_context()
        self.context.minimum_version = ssl.TLSVersion.TLSv1_3
        
        if not verify_ssl:
            self.context.check_hostname = False
            self.context.verify_mode = ssl.CERT_NONE
            logger.warning("SSL certificate verification is disabled")
    
    def _parse_url(self, url: str) -> Tuple[str, str, int, str]:
        """Parse URL into components"""
        parsed = urllib.parse.urlparse(url)
        scheme = parsed.scheme
        hostname = parsed.hostname
        port = parsed.port or (443 if scheme == 'https' else 80)
        path = parsed.path
        if parsed.query:
            path = f"{path}?{parsed.query}"
        
        return scheme, hostname, port, path
    
    @contextmanager
    def _get_connection(self, url: str):
        """Get a connection from the pool"""
        scheme, hostname, port, path = self._parse_url(url)
        
        if scheme == 'https':
            conn = http.client.HTTPSConnection(
                hostname, 
                port=port,
                context=self.context,
                timeout=self.timeout
            )
        else:
            conn = http.client.HTTPConnection(
                hostname,
                port=port,
                timeout=self.timeout
            )
        
        try:
            yield conn, path
        finally:
            conn.close()
    
    def request(self, 
                method: str, 
                url: str, 
                body: Any = None, 
                headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Make an HTTP request.
        
        Args:
            method: HTTP method
            url: URL to request
            body: Request body
            headers: Request headers
            
        Returns:
            Response dict with status, headers, and body
        """
        full_url = url
        if self.base_url and not url.startswith(('http://', 'https://')):
            full_url = f"{self.base_url.rstrip('/')}/{url.lstrip('/')}"
        
        headers = headers or {}
        
        # Convert body to JSON if dict
        if isinstance(body, dict) or isinstance(body, list):
            body = json.dumps(body).encode('utf-8')
            if 'Content-Type' not in headers:
                headers['Content-Type'] = 'application/json'
        
        with self._get_connection(full_url) as (conn, path):
            try:
                conn.request(method, path, body=body, headers=headers)
                response = conn.getresponse()
                
                # Read response data
                response_data = response.read()
                
                # Parse JSON if appropriate
                content = response_data
                content_type = response.getheader('Content-Type', '')
                if 'application/json' in content_type:
                    try:
                        content = json.loads(response_data.decode('utf-8'))
                    except json.JSONDecodeError:
                        pass
                
                return {
                    'status': response.status,
                    'headers': dict(response.getheaders()),
                    'content': content
                }
            except Exception as e:
                logger.error(f"Request failed: {e}")
                raise
    
    def get(self, url: str, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Make a GET request"""
        return self.request('GET', url, headers=headers)
    
    def post(self, url: str, json: Dict[str, Any] = None, headers: Dict[str, str] = None) -> Dict[str, Any]:
        """Make a POST request with JSON body"""
        return self.request('POST', url, body=json, headers=headers)


class DynamicRouter:
    """
    Routes requests to appropriate endpoints based on context.
    """
    
    def __init__(self, cost_fn: Callable[[str, Dict[str, Any]], float]):
        """
        Initialize the router.
        
        Args:
            cost_fn: Function to calculate cost of using a particular candidate
        """
        self.cost_fn = cost_fn
    
    def select(self, 
               text: str, 
               candidates: Dict[str, Any],
               context: Dict[str, Any] = None) -> Any:
        """
        Select the best candidate for the given text.
        
        Args:
            text: Input text
            candidates: Dictionary of candidates
            context: Additional context for cost function
            
        Returns:
            Selected candidate
        """
        if not candidates:
            raise ValueError("No candidates provided")
        
        context = context or {}
        
        # Calculate costs
        costs = {}
        for name, candidate in candidates.items():
            costs[name] = self.cost_fn(name, context)
        
        # Select candidate with lowest cost
        best_candidate = min(costs, key=costs.get)
        return candidates[best_candidate]


def system_load() -> float:
    """
    Get current system load (1-minute average).
    
    Returns:
        System load as float (0-1)
    """
    try:
        load = os.getloadavg()[0] / os.cpu_count()
        return min(load, 1.0)  # Cap at 1.0
    except:
        return 0.5  # Default to middle value if unknown
