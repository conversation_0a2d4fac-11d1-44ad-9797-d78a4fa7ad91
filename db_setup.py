#!/usr/bin/env python3
"""
Database Setup Script for Trend-Crawler

This script ensures all required tables exist in the PostgreSQL database.
It should be run once during initial setup or after schema changes.
"""

import logging
import psycopg2
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database connection settings
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "database": "trend_crawler",
    "user": "trendc",
    "password": "x1XmQ8o6UrOXRxzgZHHz0"
}

def create_required_tables():
    """Create all required tables if they don't exist."""
    conn = None
    cursor = None
    try:
        logger.info("Connecting to database...")
        conn = psycopg2.connect(**DB_CONFIG)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Create tables
        logger.info("Creating tables...")
        
        # Create system_metrics table for system resource tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                cpu_usage FLOAT,
                memory_usage FLOAT,
                disk_usage FLOAT,
                load_avg_1m FLOAT,
                load_avg_5m FLOAT,
                load_avg_15m FLOAT
            )
        ''')
        
        # Create api_metrics table for API monitoring
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_metrics (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                endpoint TEXT NOT NULL,
                status_code INTEGER,
                response_time FLOAT,
                success BOOLEAN NOT NULL,
                error_message TEXT
            )
        ''')
        
        # Create db_performance table for database metrics
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS db_performance (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                query_type TEXT NOT NULL,
                query_duration FLOAT,
                rows_affected INTEGER,
                success BOOLEAN NOT NULL,
                error_message TEXT
            )
        ''')
        
        # Create system_events table for system-level events
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_events (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT
            )
        ''')
        
        # Create proxies table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS proxies (
                id SERIAL PRIMARY KEY,
                ip_address TEXT NOT NULL,
                port INTEGER NOT NULL,
                proxy_type TEXT NOT NULL,
                username TEXT,
                password TEXT,
                proxy_group TEXT,
                location TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP,
                UNIQUE(ip_address, port)
            )
        ''')
        
        logger.info("All tables created successfully!")
        
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    create_required_tables()
