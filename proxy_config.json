{"db": {"enabled": true, "type": "postgres", "host": "localhost", "port": 5432, "name": "trend_crawler", "user": "trendc", "password": "x1XmQ8o6UrOXRxzgZHHz0"}, "brightdata": {"enabled": true, "api_token": "5b4f64bce0299cd3a647c175106e9c0e642a8805f39859d27116fdec0eb832a1", "proxies": [{"server": "brd.superproxy.io:33335", "username": "brd-customer-hl_caaa2374-zone-residential_proxy1-country-us", "password": "grh8h9v2fmib", "country": "us"}, {"server": "brd.superproxy.io:33335", "username": "brd-customer-hl_caaa2374-zone-residential_proxy1-country-gb", "password": "grh8h9v2fmib", "country": "gb"}, {"server": "brd.superproxy.io:33335", "username": "brd-customer-hl_caaa2374-zone-residential_proxy1-country-ca", "password": "grh8h9v2fmib", "country": "ca"}, {"server": "brd.superproxy.io:33335", "username": "brd-customer-hl_caaa2374-zone-residential_proxy1-country-de", "password": "grh8h9v2fmib", "country": "de"}, {"server": "brd.superproxy.io:33335", "username": "brd-customer-hl_caaa2374-zone-residential_proxy1-country-fr", "password": "grh8h9v2fmib", "country": "fr"}]}, "decodo": {"enabled": true, "proxies": [{"server": "gate.decodo.com:7000", "username": "sp27lq3bep", "password": "l3Se+1na7Rghy8QhKj", "type": "residential"}]}, "tor": {"enabled": true, "control_port": 9051, "password": ""}}