#!/usr/bin/env python3
"""
PostgreSQL with pgvector integration for trend-crawler

This module adds support for PostgreSQL with pgvector extension
to the trend-crawler application.
"""

import os
import logging
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

def connect_to_postgres(host: str, port: str, dbname: str, user: str, password: str):
    """
    Connect to PostgreSQL database with pgvector extension.
    
    Args:
        host: Database host
        port: Database port
        dbname: Database name
        user: Database username
        password: Database password
        
    Returns:
        PostgreSQL connection object
    """
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            dbname=dbname,
            user=user,
            password=password
        )
        logger.info(f"Connected to PostgreSQL database {dbname} at {host}:{port}")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to PostgreSQL: {e}")
        raise

def setup_pgvector_schema(conn):
    """
    Set up the PostgreSQL schema with pgvector extension.
    
    Args:
        conn: PostgreSQL connection object
    """
    try:
        with conn.cursor() as cur:
            # Enable pgvector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Read the schema SQL file
            schema_file = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                'pgvector_schema.sql'
            )
            
            if os.path.exists(schema_file):
                with open(schema_file, 'r') as f:
                    schema_sql = f.read()
                cur.execute(schema_sql)
                conn.commit()
                logger.info("Successfully set up pgvector schema")
            else:
                logger.error(f"Schema file not found: {schema_file}")
                raise FileNotFoundError(f"Schema file not found: {schema_file}")
            
    except Exception as e:
        logger.error(f"Failed to set up pgvector schema: {e}")
        conn.rollback()
        raise

def store_content_with_embedding(conn, content_data):
    """
    Store content with its embedding in the PostgreSQL database.
    
    Args:
        conn: PostgreSQL connection object
        content_data: Dictionary containing content data and embedding
    """
    try:
        with conn.cursor() as cur:
            query = """
            INSERT INTO crawled_content (
                source_platform, source_item_id, content_text, url, full_content,
                author_id, author_username, published_at, scraped_at, language,
                embedding, engagement_metrics, keywords, sentiment_score, sentiment_label,
                status, model_version
            ) VALUES (
                %(source_platform)s, %(source_item_id)s, %(content_text)s, %(url)s, %(full_content)s,
                %(author_id)s, %(author_username)s, %(published_at)s, NOW(), %(language)s,
                %(embedding)s, %(engagement_metrics)s, %(keywords)s, %(sentiment_score)s, %(sentiment_label)s,
                %(status)s, %(model_version)s
            )
            ON CONFLICT (source_platform, source_item_id) DO UPDATE SET
                content_text = EXCLUDED.content_text,
                url = EXCLUDED.url,
                full_content = EXCLUDED.full_content,
                embedding = EXCLUDED.embedding,
                engagement_metrics = EXCLUDED.engagement_metrics,
                status = EXCLUDED.status,
                model_version = EXCLUDED.model_version,
                scraped_at = NOW()
            RETURNING id;
            """
            
            cur.execute(query, content_data)
            content_id = cur.fetchone()[0]
            conn.commit()
            
            return content_id
            
    except Exception as e:
        logger.error(f"Failed to store content with embedding: {e}")
        conn.rollback()
        raise

def find_similar_content(conn, query_embedding, limit=10, similarity_threshold=0.7):
    """
    Find content similar to the query embedding.
    
    Args:
        conn: PostgreSQL connection object
        query_embedding: Query embedding vector
        limit: Maximum number of results
        similarity_threshold: Minimum similarity score
        
    Returns:
        List of dictionaries containing similar content items
    """
    try:
        with conn.cursor() as cur:
            query = """
            SELECT
                id,
                source_platform,
                source_item_id,
                content_text,
                url,
                1 - (embedding <=> %s) AS similarity
            FROM crawled_content
            WHERE embedding IS NOT NULL
            AND status = 'embedding_complete'
            AND 1 - (embedding <=> %s) > %s
            ORDER BY similarity DESC
            LIMIT %s;
            """
            
            cur.execute(query, (
                query_embedding,
                query_embedding,
                similarity_threshold,
                limit
            ))
            
            columns = [desc[0] for desc in cur.description]
            results = []
            
            for row in cur.fetchall():
                results.append(dict(zip(columns, row)))
                
            return results
            
    except Exception as e:
        logger.error(f"Failed to find similar content: {e}")
        raise

# Example usage
if __name__ == "__main__":
    # Load environment variables
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'trend_crawler')
    db_user = os.getenv('DB_USER', 'postgres')
    db_password = os.getenv('DB_PASSWORD', 'postgres')
    
    try:
        # Connect to PostgreSQL
        conn = connect_to_postgres(db_host, db_port, db_name, db_user, db_password)
        
        # Set up schema
        setup_pgvector_schema(conn)
        
        print("PostgreSQL with pgvector integration test complete.")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'conn' in locals() and conn:
            conn.close()
