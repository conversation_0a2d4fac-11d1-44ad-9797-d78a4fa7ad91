#!/usr/bin/env python3
"""
Final test of TensorFlow solver fix - verifying that the main CaptchaSolver 
no longer hangs during initialization.
"""

import os
import sys
import time
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_main_captcha_solver_initialization():
    """Test that the main CaptchaSolver initializes without hanging."""
    print("=== Final TensorFlow Fix Test ===")
    print("Testing main CaptchaSolver initialization...")
    
    start_time = time.time()
    
    try:
        # Import the main solver
        from captcha_solver import CaptchaSolver
        init_time = time.time() - start_time
        print(f"✓ Import successful in {init_time:.2f} seconds")
        
        # Test initialization
        solver_start = time.time()
        solver = CaptchaSolver(use_local_ml=True, service='auto')
        solver_init_time = time.time() - solver_start
        print(f"✓ CaptchaSolver initialization successful in {solver_init_time:.2f} seconds")
        
        # Check what solvers are enabled
        enabled_solvers = [name for name, info in solver.solvers.items() if info['enabled']]
        print(f"✓ Enabled solvers: {enabled_solvers}")
        
        # Check TensorFlow solver specifically
        if 'tensorflow' in solver.solvers and solver.solvers['tensorflow']['enabled']:
            tf_service = solver.solvers['tensorflow']['service']
            print(f"✓ TensorFlow solver service type: {type(tf_service)}")
            if isinstance(tf_service, dict):
                print(f"✓ TensorFlow solver using lazy initialization")
                print(f"  - Class: {tf_service['class']}")
                print(f"  - Model path: {tf_service['model_path']}")
                print(f"  - Instance created: {tf_service['instance'] is not None}")
            else:
                print(f"✓ TensorFlow solver direct instance: {tf_service}")
        
        total_time = time.time() - start_time
        print(f"✓ Total test completed in {total_time:.2f} seconds")
        
        return True
        
    except Exception as e:
        error_time = time.time() - start_time
        print(f"✗ Error after {error_time:.2f} seconds: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tensorflow_solver_usage():
    """Test that the TensorFlow solver can be used without hanging."""
    print("\n=== TensorFlow Solver Usage Test ===")
    
    try:
        from captcha_solver import CaptchaSolver
        
        # Create solver
        solver = CaptchaSolver(use_local_ml=True, service='tensorflow')
        
        # Create a test image file
        test_image_path = Path('test_captcha.png')
        
        # Create a simple test image (just some bytes)
        test_image_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x96\x00\x00\x002\x08\x02\x00\x00\x00\xc4\x1e\xb4\x80\x00\x00\x00\x01sRGB\x00\xae\xce\x1c\xe9\x00\x00\x00\x04gAMA\x00\x00\xb1\x8f\x0b\xfca\x05\x00\x00\x00\tpHYs\x00\x00\x0e\xc3\x00\x00\x0e\xc3\x01\xc7o\xa8d\x00\x00\x00\x18tEXtSoftware\x00paint.net 4.0.6\x7f\xa6\xfc\x8c\x00\x00\x00\x12IDAT\x18\x19c\xf8\x0f\x00\x01\x01\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82'
        
        print("Testing TensorFlow solver with test image...")
        start_time = time.time()
        
        try:
            # This should trigger lazy initialization if not already done
            solution = solver.solve_image_captcha(image_data=test_image_data)
            elapsed = time.time() - start_time
            
            print(f"✓ TensorFlow solver completed in {elapsed:.2f} seconds")
            print(f"  Solution: {solution}")
            
            # Check if TensorFlow instance was created
            tf_service = solver.solvers['tensorflow']['service']
            if isinstance(tf_service, dict):
                print(f"  Instance now created: {tf_service['instance'] is not None}")
            
            return True
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"✗ TensorFlow solver failed after {elapsed:.2f} seconds: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Test setup failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing final TensorFlow solver fix...")
    print("This test verifies that the hanging issue is resolved.\n")
    
    # Test 1: Basic initialization
    test1_passed = test_main_captcha_solver_initialization()
    
    # Test 2: TensorFlow solver usage (if available)
    test2_passed = True
    try:
        from ml_captcha_solvers import TENSORFLOW_AVAILABLE
        if TENSORFLOW_AVAILABLE:
            test2_passed = test_tensorflow_solver_usage()
        else:
            print("\n=== TensorFlow Solver Usage Test ===")
            print("⚠ TensorFlow not available - skipping usage test")
    except ImportError:
        print("\n=== TensorFlow Solver Usage Test ===")
        print("⚠ ML solvers not available - skipping usage test")
    
    # Summary
    print(f"\n=== Test Summary ===")
    print(f"Initialization test: {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"Usage test: {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! TensorFlow hanging issue appears to be resolved.")
        return 0
    else:
        print("\n❌ Some tests failed. TensorFlow hanging issue may still exist.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
