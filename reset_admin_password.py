#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset the admin password in the trend-crawler application.
This script will create or update the admin user with the password 'admin'.
"""

import os
import sys
import bcrypt
import sqlite3
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Default admin credentials
DEFAULT_ADMIN_USERNAME = "admin"
DEFAULT_ADMIN_EMAIL = "<EMAIL>"
DEFAULT_ADMIN_FULLNAME = "Admin User"
DEFAULT_ADMIN_PASSWORD = "admin"

def hash_password(password):
    """Hash a password using bcrypt."""
    password_bytes = password.encode('utf-8')
    salt = bcrypt.gensalt(rounds=12)
    hashed = bcrypt.hashpw(password_bytes, salt)
    return hashed.decode('utf-8')

def reset_postgres_admin():
    """Reset admin password in PostgreSQL database."""
    try:
        # Get database connection details from environment variables
        db_host = os.environ.get("DB_HOST", "localhost")
        db_port = os.environ.get("DB_PORT", "5432")
        db_name = os.environ.get("DB_NAME", "trend_crawler")
        db_user = os.environ.get("DB_USER", "trendc")
        db_password = os.environ.get("DB_PASSWORD", "x1XmQ8o6UrOXRxzgZHHz0")

        # Try to connect to the database
        print(f"Connecting to PostgreSQL database: {db_name} on {db_host}:{db_port} as {db_user}")
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            dbname=db_name,
            user=db_user,
            password=db_password
        )

        cursor = conn.cursor()

        # Check if users table exists
        cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'users'
        );
        """)

        table_exists = cursor.fetchone()[0]

        if not table_exists:
            print("Creating users table...")
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                username VARCHAR(50) PRIMARY KEY,
                email VARCHAR(100) UNIQUE NOT NULL,
                full_name VARCHAR(100),
                disabled BOOLEAN DEFAULT FALSE,
                is_admin BOOLEAN DEFAULT FALSE,
                password_hash VARCHAR(200) NOT NULL,
                needs_password_change BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """)

        # Check if admin user exists
        cursor.execute("SELECT * FROM users WHERE username = %s", (DEFAULT_ADMIN_USERNAME,))
        admin_exists = cursor.fetchone()

        password_hash = hash_password(DEFAULT_ADMIN_PASSWORD)

        if admin_exists:
            print(f"Updating existing admin user: {DEFAULT_ADMIN_USERNAME}")
            cursor.execute("""
            UPDATE users
            SET password_hash = %s, needs_password_change = TRUE
            WHERE username = %s
            """, (password_hash, DEFAULT_ADMIN_USERNAME))
        else:
            print(f"Creating new admin user: {DEFAULT_ADMIN_USERNAME}")
            cursor.execute("""
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                DEFAULT_ADMIN_FULLNAME,
                False,
                True,
                password_hash,
                True
            ))

        conn.commit()
        cursor.close()
        conn.close()

        print(f"Successfully reset admin password to '{DEFAULT_ADMIN_PASSWORD}'")
        return True

    except Exception as e:
        print(f"Error resetting PostgreSQL admin password: {e}")
        return False

def reset_sqlite_admin():
    """Reset admin password in SQLite database."""
    try:
        # Try to find the SQLite database file
        db_file = "scraper_metrics.db"
        if not os.path.exists(db_file):
            print(f"SQLite database file not found: {db_file}")
            return False

        print(f"Connecting to SQLite database: {db_file}")
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        # Check if users table exists
        cursor.execute("""
        SELECT name FROM sqlite_master WHERE type='table' AND name='users';
        """)

        table_exists = cursor.fetchone() is not None

        if not table_exists:
            print("Creating users table...")
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                username TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT,
                disabled INTEGER DEFAULT 0,
                is_admin INTEGER DEFAULT 0,
                password_hash TEXT NOT NULL,
                needs_password_change INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """)

        # Check if admin user exists
        cursor.execute("SELECT * FROM users WHERE username = ?", (DEFAULT_ADMIN_USERNAME,))
        admin_exists = cursor.fetchone()

        password_hash = hash_password(DEFAULT_ADMIN_PASSWORD)

        if admin_exists:
            print(f"Updating existing admin user: {DEFAULT_ADMIN_USERNAME}")
            cursor.execute("""
            UPDATE users
            SET password_hash = ?, needs_password_change = 1
            WHERE username = ?
            """, (password_hash, DEFAULT_ADMIN_USERNAME))
        else:
            print(f"Creating new admin user: {DEFAULT_ADMIN_USERNAME}")
            cursor.execute("""
            INSERT INTO users (username, email, full_name, disabled, is_admin, password_hash, needs_password_change)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                DEFAULT_ADMIN_USERNAME,
                DEFAULT_ADMIN_EMAIL,
                DEFAULT_ADMIN_FULLNAME,
                0,
                1,
                password_hash,
                1
            ))

        conn.commit()
        cursor.close()
        conn.close()

        print(f"Successfully reset admin password to '{DEFAULT_ADMIN_PASSWORD}'")
        return True

    except Exception as e:
        print(f"Error resetting SQLite admin password: {e}")
        return False

def main():
    """Main function to reset admin password."""
    print("Trend Crawler Admin Password Reset Tool")
    print("======================================")

    # Try PostgreSQL first
    postgres_success = reset_postgres_admin()

    # If PostgreSQL fails, try SQLite
    if not postgres_success:
        sqlite_success = reset_sqlite_admin()

        if not sqlite_success:
            print("Failed to reset admin password in any database.")
            sys.exit(1)

    print("\nAdmin password reset complete!")
    print(f"Username: {DEFAULT_ADMIN_USERNAME}")
    print(f"Password: {DEFAULT_ADMIN_PASSWORD}")
    print("\nYou will be prompted to change this password on first login.")

if __name__ == "__main__":
    main()
