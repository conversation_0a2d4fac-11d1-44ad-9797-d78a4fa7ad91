# BERT-based Coolness Classification System

This document describes the BERT-based coolness classification system integrated into the trend-crawler system.

## Overview

The BERT-based coolness classification system enhances the trend analysis system by:

1. Using a pre-trained BERT model to classify content coolness
2. Incorporating user feedback to improve predictions over time
3. Providing confidence scores and probability distributions
4. Enhancing the traditional scoring mechanism with ML-based insights

## Components

### 1. CoolnessClassifier

The `CoolnessClassifier` class is responsible for classifying content coolness. It:

- Uses a pre-trained BERT model for sequence classification
- Converts text to BERT features
- Calculates coolness scores based on model predictions
- Incorporates user feedback to improve predictions

### 2. TrainingOrchestrator

The `TrainingOrchestrator` class manages the training and retraining of models. It:

- Schedules periodic retraining based on new feedback
- Manages model versions and metrics
- Optimizes resource usage during training
- Evaluates model performance

### 3. API Endpoints

The system includes API endpoints for feedback collection and prediction:

- `POST /api/v1/feedback` - Submit feedback for a URL
- `GET /api/v1/coolness-prediction` - Get coolness prediction for a URL

## Database Schema

The integration adds three new tables to the database:

### user_feedback

Stores user feedback for coolness ratings:

```sql
CREATE TABLE IF NOT EXISTS user_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL,
    rating INT CHECK (rating BETWEEN 1 AND 9),
    user_id VARCHAR(255),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    model_version_id INT,
    FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
);
```

### model_predictions

Caches model predictions:

```sql
CREATE TABLE IF NOT EXISTS model_predictions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL,
    prediction JSON NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    model_version_id INT,
    FOREIGN KEY (model_version_id) REFERENCES model_versions(id)
);
```

### model_versions

Tracks model versions and metrics:

```sql
CREATE TABLE IF NOT EXISTS model_versions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL,
    version VARCHAR(255) NOT NULL,
    parameters JSON,
    metrics JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Coolness Levels

The system uses a 9-level coolness scale:

1. **Red** - Not cool at all
2. **Orange** - Slightly cool
3. **Yellow** - Somewhat cool
4. **Green** - Moderately cool
5. **Teal** - Cool
6. **Blue** - Very cool
7. **Indigo** - Extremely cool
8. **Violet** - Exceptionally cool
9. **Pink** - Transcendently cool

## Coolness Score Calculation

The coolness score is calculated using a hybrid approach:

```python
# Base score from traditional metrics
base_score = 0.3 * velocity_score + 0.5 * impact_score + 0.2 * novelty_score

# Add BERT classification component
if classifier:
    prediction = classifier.predict(text)
    ml_score = prediction['confidence']
    base_score = 0.7 * base_score + 0.3 * ml_score

# Add feedback adjustment
if feedback_score:
    base_score = base_score * (0.8 + 0.2 * feedback_score)

# Final coolness score
coolness_score = min(base_score, 1.0)
```

## Training Pipeline

The training pipeline includes:

1. **Initial Training**
   - Fine-tuning BERT on a labeled dataset
   - Registering the model version

2. **Feedback-Based Retraining**
   - Collecting user feedback
   - Training a feedback model
   - Combining BERT predictions with feedback

3. **Periodic Retraining**
   - Scheduled retraining based on new data
   - Concept drift detection
   - Model version management

## Usage

### Testing the Classifier

```bash
# Test with default texts
python test_coolness_classifier.py

# Test with specific texts
python test_coolness_classifier.py --texts "Revolutionary new AI technology" "Boring article about mundane topics"

# Test feedback training
python test_coolness_classifier.py --test-feedback
```

### Using the API

```bash
# Start the API server
python api_endpoints.py

# Submit feedback
curl -X POST "http://localhost:8000/api/v1/feedback" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com", "rating": 8, "user_id": "user123"}'

# Get prediction
curl -X GET "http://localhost:8000/api/v1/coolness-prediction?url=https://example.com&text=Revolutionary%20new%20AI%20technology"
```

## Dependencies

Required dependencies:
- tensorflow
- transformers
- scikit-learn
- pandas
- numpy
- fastapi
- uvicorn
- pydantic

These are included in the `requirements-bert.txt` file.
