# Core dependencies
requests>=2.25.0
pillow>=8.0.0
numpy>=1.19.0
opencv-python-headless>=4.5.0
tqdm>=4.50.0

# External CAPTCHA solving services
2captcha-python>=1.1.0
anticaptchaofficial>=1.0.0
capsolver>=1.0.0

# PyTorch (optional, for PyTorch-based solver)
# torch>=1.9.0
# torchvision>=0.10.0

# TensorFlow (optional, for TensorFlow-based solver)
# tensorflow>=2.5.0

# OCR (optional, for local text recognition)
# pytesseract>=0.3.8
