#!/usr/bin/env python3
"""
TrainingOrchestrator - Manages training and retraining of models.
"""

import time
import logging
import threading
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import dependencies
try:
    from coolness_classifier import CoolnessClassifier
    CLASSIFIER_AVAILABLE = True
except ImportError:
    CLASSIFIER_AVAILABLE = False
    logger.warning("CoolnessClassifier not available. Install required dependencies.")

try:
    from trend_crawler import MetaTrainer
    META_TRAINER_AVAILABLE = True
except ImportError:
    META_TRAINER_AVAILABLE = False
    logger.warning("MetaTrainer not available. Some features will be limited.")


class TrainingOrchestrator:
    """
    Orchestrates training and retraining of models.
    """

    def __init__(self, db=None):
        """
        Initialize the training orchestrator.

        Args:
            db: Database connection
        """
        self.db = db
        self.classifier = None
        self.meta_trainer = None
        self.batch_size = 32
        self.running = False
        self.thread = None
        self.last_feedback_count = 0
        self.last_full_training = datetime.now()
        self.retraining_interval = timedelta(days=1)  # Full retraining every day
        self.feedback_threshold = 10  # Retrain after 10 new feedback entries

        # Initialize components if available
        if CLASSIFIER_AVAILABLE:
            self.classifier = CoolnessClassifier(db)

        if META_TRAINER_AVAILABLE and db:
            self.meta_trainer = MetaTrainer(db=db)

        logger.info(f"TrainingOrchestrator initialized with classifier: {CLASSIFIER_AVAILABLE}, meta_trainer: {META_TRAINER_AVAILABLE}")

    def start(self):
        """
        Start the training orchestration thread.

        Returns:
            True if started, False otherwise
        """
        if self.running:
            logger.warning("Training orchestration already running")
            return False

        if not CLASSIFIER_AVAILABLE:
            logger.warning("CoolnessClassifier not available, cannot start training orchestration")
            return False

        # Initialize classifier model
        if not self.classifier.initialize_model():
            logger.error("Failed to initialize classifier model")
            return False

        # Start thread
        self.running = True
        self.thread = threading.Thread(target=self.periodic_retraining)
        self.thread.daemon = True
        self.thread.start()

        logger.info("Training orchestration started")
        return True

    def stop(self):
        """
        Stop the training orchestration thread.

        Returns:
            True if stopped, False otherwise
        """
        if not self.running:
            logger.warning("Training orchestration not running")
            return False

        self.running = False
        if self.thread:
            self.thread.join(timeout=5.0)

        logger.info("Training orchestration stopped")
        return True

    def periodic_retraining(self):
        """
        Combine scheduled and feedback-based retraining.
        """
        logger.info("Starting periodic retraining loop")

        # Initial training
        self._full_retraining_cycle()

        # Continuous learning loop
        while self.running:
            try:
                # Check for new feedback
                new_feedback = self._check_for_new_feedback()
                if new_feedback:
                    logger.info(f"Found {new_feedback} new feedback entries, retraining")
                    self.classifier.train_with_feedback()

                    # Register model version if meta_trainer available
                    if self.meta_trainer:
                        self.meta_trainer.register_model_version(
                            self.classifier.model,
                            {"training_type": "feedback_update"},
                            {"accuracy": self._evaluate_model()}
                        )

                # Full retraining schedule
                if self._should_retrain():
                    logger.info("Scheduled full retraining")
                    self._full_retraining_cycle()

                # Sleep for a while
                for _ in range(60):  # Check every minute if we should stop
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"Training loop failed: {e}")
                time.sleep(60)  # Wait a minute before retrying

    def _check_for_new_feedback(self):
        """
        Check for new feedback entries.

        Returns:
            Number of new feedback entries
        """
        if not self.db or not self.classifier.current_version:
            return 0

        try:
            # Count feedback entries
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM user_feedback
                WHERE model_version_id = %s
            """, (self.classifier.current_version,))

            current_count = cursor.fetchone()[0]
            new_feedback = current_count - self.last_feedback_count

            if new_feedback >= self.feedback_threshold:
                self.last_feedback_count = current_count
                return new_feedback

            return 0

        except Exception as e:
            logger.error(f"Failed to check for new feedback: {e}")
            return 0

    def _should_retrain(self):
        """
        Check if we should perform a full retraining.

        Returns:
            True if we should retrain, False otherwise
        """
        now = datetime.now()
        time_since_last_training = now - self.last_full_training

        return time_since_last_training >= self.retraining_interval

    def _full_retraining_cycle(self):
        """
        Perform a full retraining cycle.

        Returns:
            True if successful, False otherwise
        """
        if not self.db or not self.classifier:
            return False

        try:
            # Initialize a new model
            if not self.classifier.initialize_model():
                logger.error("Failed to initialize model for full retraining")
                return False

            # Train with all available data
            self.classifier.train_with_feedback()

            # Update last training timestamp
            self.last_full_training = datetime.now()

            # Update last feedback count
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM user_feedback
                WHERE model_version_id = %s
            """, (self.classifier.current_version,))

            self.last_feedback_count = cursor.fetchone()[0]

            logger.info(f"Completed full retraining cycle at {self.last_full_training}")
            return True

        except Exception as e:
            logger.error(f"Full retraining cycle failed: {e}")
            return False

    def _evaluate_model(self):
        """
        Evaluate the current model.

        Returns:
            Dictionary with evaluation metrics
        """
        if not self.db or not self.classifier:
            return {"accuracy": 0.0}

        try:
            # Get test data
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT url, text FROM coolness_data
                ORDER BY RANDOM()
                LIMIT 20
            """)

            test_data = cursor.fetchall()
            if not test_data:
                return {"accuracy": 0.0}

            # Make predictions
            correct = 0
            total = 0

            for url, text in test_data:
                # Get ground truth if available
                cursor.execute("""
                    SELECT rating FROM user_feedback
                    WHERE url = %s
                    ORDER BY timestamp DESC
                    LIMIT 1
                """, (url,))

                result = cursor.fetchone()
                if not result:
                    continue

                ground_truth = result[0]

                # Make prediction
                prediction = self.classifier.predict(text)
                predicted_rating = prediction.get('coolness_level_index', 0) + 1

                # Check if correct (within 1 level)
                if abs(predicted_rating - ground_truth) <= 1:
                    correct += 1

                total += 1

            # Calculate accuracy
            accuracy = correct / total if total > 0 else 0.0

            return {
                "accuracy": accuracy,
                "test_samples": total
            }

        except Exception as e:
            logger.error(f"Model evaluation failed: {e}")
            return {"accuracy": 0.0}

    def close(self):
        """
        Close resources.
        """
        self.stop()

        if self.classifier:
            self.classifier.close()

        logger.info("TrainingOrchestrator resources closed")


def test_training_orchestrator():
    """
    Test the TrainingOrchestrator functionality.
    """
    import psycopg2

    # Connect to PostgreSQL database for testing
    db = psycopg2.connect(
        host="localhost",
        database="trend_crawler",
        user="postgres",
        password="postgres"
    )
    cursor = db.cursor()

    # Create required tables
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS model_versions (
        id SERIAL PRIMARY KEY,
        model_name TEXT,
        version TEXT,
        parameters TEXT,
        metrics TEXT
    )
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS user_feedback (
        id SERIAL PRIMARY KEY,
        url TEXT,
        rating INTEGER,
        user_id TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        model_version_id INTEGER
    )
    """)

    cursor.execute("""
    CREATE TABLE IF NOT EXISTS coolness_data (
        id SERIAL PRIMARY KEY,
        url TEXT,
        text TEXT
    )
    """)

    # Insert test data
    cursor.execute("""
    INSERT INTO coolness_data (url, text)
    VALUES (?, ?)
    """, ("https://example.com", "This is a test website with cool content"))

    db.commit()

    # Initialize orchestrator
    orchestrator = TrainingOrchestrator(db)

    # Test start/stop
    print("Starting training orchestration...")
    if orchestrator.start():
        print("Training orchestration started")

        # Wait a bit
        time.sleep(2)

        # Stop
        print("Stopping training orchestration...")
        orchestrator.stop()
        print("Training orchestration stopped")
    else:
        print("Failed to start training orchestration")

    # Clean up
    orchestrator.close()
    db.close()


if __name__ == "__main__":
    # Run test if executed directly
    test_training_orchestrator()
