#!/usr/bin/env python3
"""
Script to find files larger than a specified size in a directory and its subdirectories,
and add them to a .gitignore file.
"""

import os
import sys
from pathlib import Path

def find_large_files(directory, size_limit_mb=99, gitignore_path=None, dry_run=True):
    """
    Find files larger than size_limit_mb in the given directory and its subdirectories.
    If gitignore_path is provided, add these files to the .gitignore file.
    If dry_run is True, only print what would be done without actually modifying .gitignore.

    Returns a list of large files (relative paths).
    """
    large_files = []
    size_limit_bytes = size_limit_mb * 1024 * 1024  # Convert MB to bytes

    # Get the absolute path of the directory
    abs_directory = os.path.abspath(directory)

    print(f"Scanning for files larger than {size_limit_mb} MB in {abs_directory}...")

    # Walk through the directory and its subdirectories
    for root, _, files in os.walk(abs_directory):
        for filename in files:
            file_path = os.path.join(root, filename)

            # Skip if the file is a symbolic link
            if os.path.islink(file_path):
                continue

            # Get the file size
            try:
                file_size = os.path.getsize(file_path)
            except (OSError, FileNotFoundError) as e:
                print(f"Error getting size of {file_path}: {e}")
                continue

            # Check if the file is larger than the size limit
            if file_size > size_limit_bytes:
                # Get the relative path from the directory
                rel_path = os.path.relpath(file_path, directory)
                large_files.append((rel_path, file_size))
                print(f"Found large file: {rel_path} ({file_size / (1024*1024):.2f} MB)")

    # Sort large files by size (largest first)
    large_files.sort(key=lambda x: x[1], reverse=True)

    # Print summary
    total_size = sum(size for _, size in large_files)
    print(f"\nFound {len(large_files)} files larger than {size_limit_mb} MB")
    print(f"Total size: {total_size / (1024*1024*1024):.2f} GB")

    # Add to .gitignore if specified
    if gitignore_path and large_files:
        add_to_gitignore(gitignore_path, [path for path, _ in large_files], dry_run)

    return [path for path, _ in large_files]

def add_to_gitignore(gitignore_path, file_paths, dry_run=True):
    """
    Add file paths to the .gitignore file.
    If dry_run is True, only print what would be done without actually modifying .gitignore.
    """
    # Create .gitignore file if it doesn't exist
    if not os.path.exists(gitignore_path):
        if dry_run:
            print(f"\nWould create new .gitignore file at {gitignore_path}")
        else:
            with open(gitignore_path, 'w') as f:
                f.write("# Large files automatically added by find_large_files.py\n")
            print(f"\nCreated new .gitignore file at {gitignore_path}")

    # Read existing .gitignore content
    existing_content = []
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r') as f:
            existing_content = [line.strip() for line in f.readlines()]

    # Add header if not already present
    header = "# Large files automatically added by find_large_files.py"
    if header not in existing_content and not dry_run:
        with open(gitignore_path, 'a') as f:
            if existing_content and existing_content[-1] != "":
                f.write("\n")
            f.write(f"{header}\n")

    # Add new file paths
    new_paths = []
    for path in file_paths:
        if path not in existing_content:
            new_paths.append(path)

    if new_paths:
        if dry_run:
            print(f"\nWould add {len(new_paths)} new entries to {gitignore_path}:")
            for path in new_paths:
                print(f"  {path}")
        else:
            with open(gitignore_path, 'a') as f:
                for path in new_paths:
                    f.write(f"{path}\n")
            print(f"\nAdded {len(new_paths)} new entries to {gitignore_path}")
    else:
        print(f"\nNo new entries to add to {gitignore_path}")

if __name__ == "__main__":
    # Parse command line arguments
    directory = "/home/<USER>/appsUndCode/dataSets/"
    size_limit_mb = 99
    gitignore_path = ".gitignore"  # Use the .gitignore in the current directory
    dry_run = True

    # Parse command line arguments
    if len(sys.argv) > 1:
        directory = sys.argv[1]

    if len(sys.argv) > 2:
        try:
            size_limit_mb = int(sys.argv[2])
        except ValueError:
            print(f"Error: Size limit must be an integer. Using default: {size_limit_mb} MB")

    if len(sys.argv) > 3:
        gitignore_path = sys.argv[3]

    if len(sys.argv) > 4 and sys.argv[4].lower() == "--apply":
        dry_run = False

    # Check if directory exists
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)

    # Find large files and add to .gitignore
    find_large_files(directory, size_limit_mb, gitignore_path, dry_run)

    if dry_run:
        print("\nThis was a dry run. No changes were made to .gitignore.")
        print("To actually update .gitignore, run with the --apply flag:")
        print(f"python {sys.argv[0]} {directory} {size_limit_mb} {gitignore_path} --apply")
