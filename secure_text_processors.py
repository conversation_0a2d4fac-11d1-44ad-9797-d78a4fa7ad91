#!/usr/bin/env python3
"""
Secure text processing utilities for trend-crawler

This module provides sanitization and security features for processing text
before sending to external APIs or processing with local models.
"""

import re
import html
import logging
import json
from typing import Dict, List, Any, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BaseSanitizer:
    """Base class for text sanitization"""
    
    def __init__(self, max_length: int = 4096):
        """
        Initialize the sanitizer.
        
        Args:
            max_length: Maximum allowed text length
        """
        self.max_length = max_length
    
    def clean(self, text: str) -> str:
        """
        Clean the input text.
        
        Args:
            text: Input text to clean
            
        Returns:
            Sanitized text
        """
        if not text:
            return ""
        
        # Basic sanitization
        text = self._remove_null_bytes(text)
        text = self._trim_to_max_length(text)
        return text
    
    def _remove_null_bytes(self, text: str) -> str:
        """Remove null bytes which can cause issues with C-based systems"""
        return text.replace('\0', '')
    
    def _trim_to_max_length(self, text: str) -> str:
        """Ensure text doesn't exceed maximum length"""
        if len(text) > self.max_length:
            logger.warning(f"Text exceeded maximum length of {self.max_length}. Truncating.")
            return text[:self.max_length]
        return text


class HTMLSanitizer(BaseSanitizer):
    """Sanitizer for HTML content"""
    
    def __init__(self, max_length: int = 4096):
        """Initialize the HTML sanitizer."""
        super().__init__(max_length)
        # Common XSS attack patterns
        self.xss_patterns = [
            r'<script.*?>.*?</script>',
            r'javascript:',
            r'onerror=',
            r'onload=',
            r'eval\(',
            r'setTimeout\(',
            r'document\.cookie',
        ]
    
    def clean(self, text: str) -> str:
        """
        Clean HTML content.
        
        Args:
            text: HTML text to clean
            
        Returns:
            Sanitized text
        """
        # Apply base sanitization
        text = super().clean(text)
        
        # Remove potential XSS patterns
        for pattern in self.xss_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # HTML entity decode
        text = html.unescape(text)
        
        # Remove all HTML tags
        text = re.sub(r'<[^>]*>', '', text)
        
        return text


class TextNormalizer(BaseSanitizer):
    """
    Normalizes text content for consistent processing
    without removing semantic meaning.
    """
    
    def __init__(self, max_length: int = 4096):
        """Initialize the text normalizer."""
        super().__init__(max_length)
    
    def clean(self, text: str) -> str:
        """
        Normalize plain text content.
        
        Args:
            text: Plain text to normalize
            
        Returns:
            Normalized text
        """
        # Apply base sanitization
        text = super().clean(text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Normalize quotes
        text = text.replace('"', '"').replace('"', '"').replace(''', "'").replace(''', "'")
        
        # Remove control characters except newlines and tabs
        text = ''.join(ch for ch in text if ch == '\n' or ch == '\t' or ch >= ' ')
        
        return text


class JSONSanitizer(BaseSanitizer):
    """
    Sanitizes and validates JSON content
    """
    
    def __init__(self, max_length: int = 8192):
        """Initialize the JSON sanitizer."""
        super().__init__(max_length)
    
    def clean(self, text: str) -> str:
        """
        Sanitize and validate JSON content.
        
        Args:
            text: JSON string to sanitize
            
        Returns:
            Sanitized JSON string
            
        Raises:
            ValueError: If invalid JSON
        """
        # Apply base sanitization
        text = super().clean(text)
        
        # Validate JSON and reconstruct to ensure safety
        try:
            json_obj = json.loads(text)
            # Recreate JSON string with proper escaping
            return json.dumps(json_obj)
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON: {e}")
            raise ValueError(f"Invalid JSON: {e}")


class InputValidator:
    """
    Validates input based on predefined rules
    """
    
    def __init__(self, rules: Dict[str, Any] = None):
        """
        Initialize with validation rules.
        
        Args:
            rules: Dictionary of validation rules
        """
        self.rules = rules or {}
    
    def validate(self, text: str, rule_key: str = 'default') -> bool:
        """
        Validate text against a specific rule.
        
        Args:
            text: Text to validate
            rule_key: Key for the rule to apply
            
        Returns:
            True if valid, False otherwise
        """
        rule = self.rules.get(rule_key)
        if not rule:
            return True  # No rule defined means valid
        
        if 'min_length' in rule and len(text) < rule['min_length']:
            return False
        
        if 'max_length' in rule and len(text) > rule['max_length']:
            return False
        
        if 'pattern' in rule and not re.match(rule['pattern'], text):
            return False
        
        if 'allowed_chars' in rule:
            if not all(c in rule['allowed_chars'] for c in text):
                return False
        
        return True
