#!/usr/bin/env python3
"""
Test script to verify system API endpoints are working
"""

import requests
import json
import time

def test_system_endpoints():
    """Test all system API endpoints"""
    base_url = "http://localhost:8000"
    
    # First, let's try to login to get a token
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    print("Testing system API endpoints...")
    print("=" * 50)
    
    try:
        # Login to get token
        print("1. Attempting login...")
        login_response = requests.post(
            f"{base_url}/api/v1/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"   ✓ Login successful, got token")
            
            headers = {"Authorization": f"Bearer {token}"}
        else:
            print(f"   ✗ Login failed: {login_response.status_code}")
            headers = {}
        
        # Test system endpoints
        endpoints = [
            "/api/v1/system/info",
            "/api/v1/system/events",
            "/api/v1/system/api-health", 
            "/api/v1/system/db-performance"
        ]
        
        for endpoint in endpoints:
            print(f"\n2. Testing {endpoint}...")
            try:
                response = requests.get(f"{base_url}{endpoint}", headers=headers)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✓ Success! Response keys: {list(data.keys()) if isinstance(data, dict) else 'Array with ' + str(len(data)) + ' items'}")
                    
                    # Show sample data for system info
                    if endpoint == "/api/v1/system/info" and isinstance(data, dict):
                        print(f"   Sample data: hostname={data.get('hostname', 'N/A')}, cpu_usage={data.get('cpu_usage', 'N/A')}")
                        
                elif response.status_code == 404:
                    print(f"   ✗ Endpoint not found (404)")
                elif response.status_code == 401:
                    print(f"   ✗ Unauthorized (401)")
                else:
                    print(f"   ✗ Error: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"   ✗ Connection failed - is the server running?")
            except Exception as e:
                print(f"   ✗ Error: {e}")
                
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Make sure the web dashboard is running on port 8000")
        print("  Run: ./run_scraper.sh dashboard")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

if __name__ == "__main__":
    test_system_endpoints()
