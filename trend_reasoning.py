#!/usr/bin/env python3
"""
Trend Reasoning System

This module implements a memory-efficient, secure reasoning subsystem for real-time trend analysis.
It integrates with the existing trend-crawler system to provide enhanced reasoning capabilities
while maintaining strict memory footprint and latency constraints.
"""
import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F
# from torch.utils.data import DataLoader # Only if calibration_dataloader is used directly here

# --- Placeholders for External Dependencies ---
try:
    from pgvector_manager import VectorStoreManager
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("pgvector_manager.VectorStoreManager not found, using placeholder.")
    class VectorStoreManager:
        def get_context_gate(self, trend_text: str, device: Any, embed_size: int) -> torch.Tensor:
            # Placeholder: returns a zero tensor of appropriate size for context gating
            # Actual implementation should fetch relevant context vector
            logger.debug(f"VectorStoreManager.get_context_gate called for trend: {trend_text[:50]}...")
            return torch.zeros(1, embed_size, device=device) # Assuming batch size 1 for context vec

        def get_embedding(self, text: str, *args, **kwargs) -> torch.Tensor:
            logger.debug(f"VectorStoreManager.get_embedding called for text: {text[:50]}...")
            # Placeholder: returns a zero tensor.
            # Actual implementation should generate or retrieve an embedding.
            # The size should match config.embed_size
            return torch.zeros(64) # Assuming embed_size 64 based on TrendReasoningConfig default

        def add_embedding(self, text: str, data: Dict, *args, **kwargs):
            logger.debug(f"VectorStoreManager.add_embedding called for text: {text[:50]}...")
            pass # Placeholder

        def search_similar(self, text: str, limit: int = 3, *args, **kwargs) -> List[Dict[str, Any]]:
            logger.debug(f"VectorStoreManager.search_similar called for text: {text[:50]}...")
            return [{"text": "dummy similar text 1"}, {"text": "dummy similar text 2"}]


try:
    from security_pipeline import SecurityPipeline as BaseSecurityPipeline
    # This implies SecurityPipeline is a base class or a configurable module.
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("security_pipeline.BaseSecurityPipeline not found, using placeholder.")
    class BaseSecurityPipeline:
        def __init__(self, config: Optional[Any] = None):
            self.config = config if config else self._get_default_config()

        def _get_default_config(self):
            class DummyConfig:
                max_trend_length = 256
            return DummyConfig()

        def sanitize_input(self, x: Any) -> Any:
            # Basic sanitization placeholder
            if isinstance(x, str):
                # Limit length, remove potentially harmful characters (very basic)
                return x[:self.config.max_trend_length].strip()
            return x # Passthrough for non-string

        def sanitize_tensor(self, x: torch.Tensor) -> torch.Tensor:
            # Placeholder for tensor sanitization (e.g., clipping values)
            return x

        def validate_context(self, source: str, content: str) -> bool:
            # Placeholder for context validation
            logger.debug(f"Validating context from source: {source}")
            return True # Assume valid

        def validate_output(self, output: Any) -> Any:
            # Placeholder for output validation
            logger.debug("Validating output.")
            return output

# --- Enhanced Reasoning Configuration ---
class TrendReasoningConfig:
    def __init__(self,
                 embed_size: int = 64,
                 num_reasoning_layers: int = 2,
                 attention_heads: int = 4,
                 max_trend_length: int = 256,
                 knowledge_integration: bool = True,
                 dynamic_pruning: bool = True,
                 sparsity: float = 0.3, # Target sparsity (30% weights are zero)
                 attention_sparsity: float = 0.7, # Keep top (1-0.7)=30% of attention scores
                 max_memory_mb: float = 300.0,
                 target_latency_ms: int = 1500,
                 max_batch_size: int = 8):
        self.embed_size = embed_size
        self.num_reasoning_layers = num_reasoning_layers
        self.attention_heads = attention_heads
        self.max_trend_length = max_trend_length
        self.knowledge_integration = knowledge_integration
        self.dynamic_pruning = dynamic_pruning
        self.sparsity = sparsity # For weight pruning
        self.attention_sparsity = attention_sparsity # For attention mechanism
        self.max_memory_mb = max_memory_mb
        self.target_latency_ms = target_latency_ms
        self.max_batch_size = max_batch_size

    def adjust_for_memory_constraints(self, current_memory_mb: float):
        # Placeholder for dynamic config adjustment
        if current_memory_mb > self.max_memory_mb * 0.9:
            logger.warning(f"High memory usage ({current_memory_mb:.2f}MB), consider reducing batch size or increasing sparsity.")
            # Example: self.sparsity = min(0.5, self.sparsity + 0.1) # Increase pruning
            # This logic would be more sophisticated in a real scenario


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


# --- Trend-Specific Attention Mechanism ---
class TrendAttention(nn.Module):
    def __init__(self, config: TrendReasoningConfig, vector_store: VectorStoreManager, security: BaseSecurityPipeline):
        super().__init__()
        self.embed_size = config.embed_size
        self.heads = config.attention_heads
        self.head_dim = config.embed_size // config.attention_heads

        self.query = nn.Linear(config.embed_size, config.embed_size)
        self.key = nn.Linear(config.embed_size, config.embed_size)
        self.value = nn.Linear(config.embed_size, config.embed_size)
        self.fc_out = nn.Linear(config.embed_size, config.embed_size)

        # Temporal bias: Learned parameter per head, applied to QK^T
        # Directive: T_bias. Let's make it compatible with QK shape (N, H, S, S) or (H, S, S)
        # A simpler T_bias might be (embed_size) or (seq_len, seq_len) or (heads, seq_len, seq_len)
        # The attached code had: self.temporal_attention = nn.Parameter(torch.randn(config.embed_size))
        # and T_bias = self.temporal_attention[:seq_len].unsqueeze(0).unsqueeze(0)
        # This T_bias is (1,1,seq_len), added to QK (N,H,S,S). This broadcasts over N, H.
        # Let's refine this to be potentially per-head:
        self.temporal_bias_param = nn.Parameter(torch.randn(self.heads, 1, config.max_trend_length)) # (H, 1, S_max)

        # Context Gating: C_gate = σ(VectorStoreContext · W_gate)
        # Assuming VectorStoreContext is (N, embed_size)
        self.W_gate = nn.Linear(config.embed_size, self.heads) # Projects context to per-head gate values

        self.dropout = nn.Dropout(0.1)
        self.vector_store = vector_store
        self.security = security
        self.attention_sparsity = config.attention_sparsity # To keep top (1-attention_sparsity) scores

    def forward(self, x, mask=None, trend_text: Optional[str] = None):
        N, seq_len, _ = x.shape

        Q = self.query(x).view(N, seq_len, self.heads, self.head_dim).permute(0, 2, 1, 3) # (N, H, S, D_h)
        K = self.key(x).view(N, seq_len, self.heads, self.head_dim).permute(0, 2, 1, 3)   # (N, H, S, D_h)
        V = self.value(x).view(N, seq_len, self.heads, self.head_dim).permute(0, 2, 1, 3) # (N, H, S, D_h)

        # Energy = QK^T
        energy = torch.matmul(Q, K.transpose(-2, -1)) # (N, H, S, S)

        # Add temporal bias (T_bias)
        # self.temporal_bias_param is (H, 1, S_max), slice to current seq_len
        # T_bias should be added to QK^T.
        # Let's make T_bias (H, S, S) for simplicity or use a simpler form.
        # The previous T_bias = self.temporal_attention[:seq_len].unsqueeze(0).unsqueeze(0) was (1,1,S)
        # Let's use a learnable bias per head, for each query position, attending to keys.
        # temporal_bias_sliced = self.temporal_bias_param[:, :, :seq_len] # (H, 1, S)
        # energy = energy + temporal_bias_sliced.unsqueeze(2) # (N,H,S,S) + (H,1,1,S) -> (N,H,S,S)
        # For simplicity and matching the formula structure QK^T + T_bias / sqrt(d_k) + C_gate
        # Let T_bias be a learnable scalar or vector added to QK^T scores.
        # The previous code added it to QK before scaling.
        # Let's use the (H,1,S_max) version, applied to QK^T
        # This means T_bias affects how a query attends to keys based on query head and key position.
        current_temporal_bias = self.temporal_bias_param[:, :, :seq_len] # (H, 1, S)
        energy = energy + current_temporal_bias.unsqueeze(2) # Broadcasts (H,1,S) to (N,H,S,S) by adding to last dim of QK^T

        # Scale QK^T + T_bias
        energy = energy / (self.head_dim ** 0.5)

        # Context gating (C_gate)
        if trend_text is not None and self.vector_store:
            # Assuming get_context_gate returns (N, embed_size)
            context_vec = self.vector_store.get_context_gate(trend_text, x.device, self.embed_size)
            if context_vec.ndim == 2 and context_vec.shape[0] == N : # (N, embed_size)
                 # Project context_vec to (N, heads) and apply sigmoid
                context_gate_values = torch.sigmoid(self.W_gate(context_vec)) # (N, H)
                C_gate = context_gate_values.view(N, self.heads, 1, 1) # Reshape for broadcasting (N, H, 1, 1)
                energy = energy + C_gate # Add to scaled energy
            else:
                logger.warning(f"Context vector from get_context_gate has unexpected shape: {context_vec.shape}")


        if mask is not None: # e.g., padding mask
            energy = energy.masked_fill(mask.unsqueeze(1).unsqueeze(2) == 0, float("-1e20")) # Mask shape (N,S) -> (N,1,1,S)

        # Security pipeline hook on attention scores (energy)
        energy = self.security.sanitize_tensor(energy) # Sanitize pre-softmax scores

        # Sparse activation: keep only top (1 - self.attention_sparsity) % activations
        # If attention_sparsity = 0.7, keep top 30%
        if self.attention_sparsity > 0 and self.attention_sparsity < 1.0:
            # Calculate k for top-k
            # energy is (N, H, S, S). Sparsity per attention map (last dim S)
            num_elements_to_keep = max(1, int(seq_len * (1.0 - self.attention_sparsity)))
            if num_elements_to_keep < seq_len :
                # Find threshold for each attention map
                # This is computationally intensive to do perfectly with topk on each map.
                # A simpler global threshold or random sparsity might be used in practice.
                # For "top-k per row":
                threshold_values = torch.topk(energy, num_elements_to_keep, dim=-1, largest=True)[0][..., -1, None] # (N,H,S,1)
                energy = torch.where(energy >= threshold_values, energy, torch.full_like(energy, float('-inf')))

        attention = F.softmax(energy, dim=-1)
        attention = self.dropout(attention) # Apply dropout to attention scores

        out = torch.matmul(attention, V) # (N, H, S, D_h)
        out = out.permute(0, 2, 1, 3).contiguous().view(N, seq_len, self.embed_size) # (N, S, E)
        return self.fc_out(out)

# --- Trend Reasoning Block ---
class TrendReasoningBlock(nn.Module):
    def __init__(self, config: TrendReasoningConfig, vector_store: VectorStoreManager, security: BaseSecurityPipeline):
        super().__init__()
        self.attention = TrendAttention(config, vector_store, security)
        self.norm1 = nn.LayerNorm(config.embed_size)
        self.norm2 = nn.LayerNorm(config.embed_size)
        self.ff = nn.Sequential(
            nn.Linear(config.embed_size, config.embed_size * 2),
            nn.GELU(), # Or nn.ReLU based on NanoLLM's typical activation
            nn.Linear(config.embed_size * 2, config.embed_size)
        )
        self.config = config
        self.security = security

    def apply_pruning(self):
        # Dynamic pruning: sets 'sparsity'% of weights to zero
        if self.config.dynamic_pruning and self.config.sparsity > 0:
            with torch.no_grad():
                for name, param in self.named_parameters():
                    if param.dim() > 1 and 'weight' in name:  # Only prune weight matrices
                        # Create a mask where 'sparsity'% are True (to be zeroed)
                        mask_for_zeros = torch.rand_like(param) < self.config.sparsity
                        param.data[mask_for_zeros] = 0.0
                        logger.debug(f"Applied {self.config.sparsity*100}% pruning to {name}")


    def forward(self, x, mask=None, trend_text: Optional[str] = None):
        # Pre-LayerNorm
        normed_x = self.norm1(x)
        # Pass trend_text to attention
        attn_out = self.attention(normed_x, mask, trend_text)
        x = x + attn_out # Add & Norm (residual connection)

        # Feed Forward part
        normed_x_ff = self.norm2(x)
        ff_out = self.ff(normed_x_ff)
        x = x + ff_out # Add & Norm

        # Sanitize output of the block
        x = self.security.sanitize_tensor(x)
        return x

# --- Knowledge Integration System ---
class LRUCache:
    def __init__(self, capacity: int = 1000):
        self.cache = {}
        self.order = [] # Stores keys, oldest at the beginning
        self.capacity = capacity

    def get(self, key: str) -> Optional[Any]:
        if key in self.cache:
            self.order.remove(key)
            self.order.append(key) # Move to end (most recently used)
            return self.cache[key]
        return None

    def put(self, key: str, value: Any):
        if key in self.cache:
            self.order.remove(key)
        elif len(self.cache) >= self.capacity:
            oldest_key = self.order.pop(0) # Remove oldest
            del self.cache[oldest_key]
        self.cache[key] = value
        self.order.append(key)

class KnowledgeIntegrator:
    def __init__(self, vector_store: VectorStoreManager, security: BaseSecurityPipeline, cache_capacity: int = 1000):
        self.vector_store = vector_store
        self.cache = LRUCache(cache_capacity)
        self.security = security

    def get_context(self, trend_text: str) -> Optional[torch.Tensor]:
        # Input trend_text is a string
        sanitized_trend_text = self.security.sanitize_input(trend_text)
        if not sanitized_trend_text:
            return None

        cached_context = self.cache.get(sanitized_trend_text)
        if cached_context is not None:
            logger.debug(f"KI: Context for '{sanitized_trend_text[:30]}...' found in cache.")
            return cached_context

        # Retrieve context from vector store (e.g., embedding of related trends)
        # This should return a tensor.
        # The directive implies this provides a tensor for the model.
        # For now, let's assume get_embedding provides this.
        context_vec = self.vector_store.get_embedding(sanitized_trend_text)

        if context_vec is not None: # Assuming get_embedding returns Tensor or None
             # Validate context (optional, depends on security policy)
            if self.validate_context("vector_store_embedding", sanitized_trend_text):
                self.cache.put(sanitized_trend_text, context_vec)
                logger.debug(f"KI: Context for '{sanitized_trend_text[:30]}...' fetched and cached.")
                return context_vec
            else:
                logger.warning(f"KI: Context validation failed for '{sanitized_trend_text[:30]}...'")
        return None


    def update_knowledge(self, trend_data: Dict[str, Any]):
        # trend_data is a dictionary, e.g., {"text": "some new trend", "metadata": {...}}
        trend_text = trend_data.get('text')
        if not trend_text:
            logger.warning("KI: update_knowledge called with no text in trend_data.")
            return

        sanitized_trend_text = self.security.sanitize_input(trend_text)
        if not sanitized_trend_text:
            return

        # Add new trend information (embedding) to the vector store
        # The actual embedding generation might happen here or be pre-computed
        self.vector_store.add_embedding(sanitized_trend_text, trend_data)
        logger.info(f"KI: Knowledge updated for trend '{sanitized_trend_text[:30]}...'")


    def validate_context(self, source: str, content: str) -> bool:
        # Uses the security module's validation logic
        is_valid = self.security.validate_context(source, content)
        logger.debug(f"KI: Validation for source '{source}', content '{content[:30]}...': {is_valid}")
        return is_valid

# --- Enhanced NanoLLM with Reasoning ---
class EnhancedNanoLLM(nn.Module):
    def __init__(self, config: TrendReasoningConfig, vector_store: VectorStoreManager, security: BaseSecurityPipeline, vocab_size: int = 10000):
        super().__init__()
        self.config = config
        self.security = security

        self.embedding = nn.Embedding(vocab_size, config.embed_size)
        # Positional Encoding: learnable parameter
        self.position_encoding = nn.Parameter(torch.randn(1, config.max_trend_length, config.embed_size))

        self.reasoning_layers = nn.ModuleList([
            TrendReasoningBlock(config, vector_store, security) for _ in range(config.num_reasoning_layers)
        ])

        if config.knowledge_integration:
            self.knowledge_integrator = KnowledgeIntegrator(vector_store, security)
        else:
            self.knowledge_integrator = None

        self.classifier = nn.Linear(config.embed_size, 5) # Example: 5 trend categories

        # For quantization
        self.quant = torch.quantization.QuantStub()
        self.dequant = torch.quantization.DeQuantStub()
        self.is_quantized = False # Flag

        # Circuit breaker
        self.circuit_breaker_active = False
        self.quantization_error_threshold = 0.1 # Example threshold
        self.context_retrieval_timeout_ms = 500 # Example timeout

    def _get_token_ids(self, text_input: str, max_len: int) -> torch.Tensor:
        """Basic placeholder for tokenizing text. Replace with actual tokenizer."""
        # This should map to the vocab_size of self.embedding
        tokens = [abs(hash(word)) % self.embedding.num_embeddings for word in text_input.split()[:max_len]]
        if not tokens: tokens = [0] # Padding token or special start token
        # Pad to max_len or handle variable length sequences appropriately in batching
        if len(tokens) < max_len:
            tokens.extend([0] * (max_len - len(tokens))) # Pad with 0 (assuming 0 is padding_idx)

        return torch.tensor([tokens], dtype=torch.long, device=self.embedding.weight.device) # Batch size 1

    def forward(self, x_tokens: torch.Tensor, # Expect tokenized input (N, S)
                trend_text_for_attention: Optional[str] = None, # Raw text for TrendAttention's context gating
                context_trend_text_for_ki: Optional[str] = None # Raw text for KI's get_context
               ) -> torch.Tensor:

        if self.circuit_breaker_active:
            logger.warning("Circuit breaker active, returning zero tensor.")
            # Return a tensor of expected shape, e.g., (batch_size, num_classes)
            batch_size = x_tokens.size(0) if x_tokens is not None else 1
            return torch.zeros(batch_size, self.classifier.out_features, device=self.embedding.weight.device)

        # Input x_tokens is assumed to be already tokenized numerical input
        # The sanitize_input for tensors might be tricky if it changes shapes/values unexpectedly.
        # String inputs are sanitized before tokenization.
        # x_tokens = self.security.sanitize_tensor(x_tokens) # If needed for token IDs

        if self.is_quantized:
            embedded = self.quant(self.embedding(x_tokens))
        else:
            embedded = self.embedding(x_tokens) # (N, S, E)

        # Add position encoding
        seq_len = embedded.size(1)
        embedded = embedded + self.position_encoding[:, :seq_len, :] # (N, S, E)

        # Knowledge Integration
        if self.knowledge_integrator and context_trend_text_for_ki:
            # KI's get_context expects a string, returns a Tensor or None
            # This context should be (N, E) or broadcastable
            start_time = time.time()
            context_vec = self.knowledge_integrator.get_context(context_trend_text_for_ki) # Tensor (E,) or (1,E)
            retrieval_time_ms = (time.time() - start_time) * 1000
            if retrieval_time_ms > self.context_retrieval_timeout_ms:
                logger.warning(f"Context retrieval timeout ({retrieval_time_ms:.2f}ms), activating circuit breaker.")
                self.enable_circuit_breaker()
                return torch.zeros(x_tokens.size(0), self.classifier.out_features, device=x_tokens.device)

            if context_vec is not None:
                # Ensure context_vec is on the same device and unsqueezed for batch
                context_vec = context_vec.to(embedded.device)
                if context_vec.ndim == 1: # (E,)
                    context_vec = context_vec.unsqueeze(0).unsqueeze(1) # (1,1,E) to broadcast over N, S
                elif context_vec.ndim == 2: # (1,E) or (N,E)
                     context_vec = context_vec.unsqueeze(1) # (N,1,E) to broadcast over S
                embedded = embedded + context_vec # Add to embeddings (N,S,E)

        # Reasoning Layers
        # Create attention mask if needed (e.g., for padding tokens)
        # Assuming x_tokens uses 0 for padding
        attention_mask = (x_tokens != 0) if x_tokens is not None else None # (N, S)

        current_representation = embedded
        for layer in self.reasoning_layers:
            # Pass trend_text_for_attention for context gating inside TrendAttention
            current_representation = layer(current_representation, mask=attention_mask, trend_text=trend_text_for_attention)

        # Classifier
        # Use mean pooling over sequence length for classification
        pooled_output = current_representation.mean(dim=1) # (N, E)

        if self.is_quantized:
            logits = self.classifier(self.dequant(pooled_output))
        else:
            logits = self.classifier(pooled_output) # (N, NumClasses)

        return logits

    def quantize_model(self, calibration_dataloader: Optional[Any] = None, q_engine: str = 'fbgemm'):
        if self.is_quantized:
            logger.info("Model is already quantized.")
            return

        if q_engine not in torch.backends.quantized.supported_engines:
            logger.warning(f"Quantization engine '{q_engine}' not supported. Defaulting to fbgemm (if on x86).")
            q_engine = 'fbgemm' if 'fbgemm' in torch.backends.quantized.supported_engines else torch.backends.quantized.engine

        self.eval() # Model must be in eval mode for PTQ
        self.qconfig = torch.quantization.get_default_qconfig(q_engine)
        logger.info(f"Preparing model for quantization with engine: {q_engine}, qconfig: {self.qconfig}")

        # TODO: Add module fusion here if applicable, e.g.,
        # torch.quantization.fuse_modules(self.some_block, ['conv', 'relu'], inplace=True)
        # For transformers, fusion is often more complex (e.g. Linear+ReLU in FF, or parts of attention)
        # For now, we rely on prepare + convert.

        torch.quantization.prepare_qat(self, inplace=True) if hasattr(self, 'train') else torch.quantization.prepare(self, inplace=True)


        if calibration_dataloader:
            logger.info("Performing calibration for PTQ with provided dataloader...")
            with torch.no_grad():
                for i, batch_data in enumerate(calibration_dataloader):
                    # Adapt this to your dataloader structure
                    # Assuming batch_data is (tokens, trend_text_attention, trend_text_ki)
                    if isinstance(batch_data, tuple) and len(batch_data) == 3:
                         self(batch_data[0], trend_text_for_attention=batch_data[1], context_trend_text_for_ki=batch_data[2])
                    elif isinstance(batch_data, torch.Tensor): # Simple case: only tokens
                         self(batch_data)
                    else:
                        logger.warning(f"Calibration batch format not recognized, skipping batch {i}.")
                        continue
                    if i >= 10: # Limit calibration steps for speed in example
                        logger.info("Reached max calibration steps for this example.")
                        break
            logger.info("Calibration complete.")
        else:
            logger.warning("No calibration_dataloader provided for quantize_model. Quantization accuracy might be suboptimal.")

        torch.quantization.convert(self, inplace=True)
        self.is_quantized = True
        logger.info("Model successfully quantized to INT8.")
        # TODO: Add quantization error check and circuit breaker activation if too high

    def apply_dynamic_pruning(self):
        if self.config.dynamic_pruning:
            logger.info(f"Applying dynamic pruning with sparsity {self.config.sparsity}...")
            for layer in self.reasoning_layers:
                layer.apply_pruning()
            # Potentially prune embeddings or classifier too if beneficial
            # Example for embedding pruning (less common for dense embeddings):
            # with torch.no_grad():
            #     if hasattr(self.embedding, 'weight') and self.embedding.weight.dim() > 1:
            #         mask_for_zeros = torch.rand_like(self.embedding.weight) < self.config.sparsity
            #         self.embedding.weight.data[mask_for_zeros] = 0.0

    def enable_circuit_breaker(self):
        self.circuit_breaker_active = True
        logger.warning("Circuit breaker ENABLED for EnhancedNanoLLM.")

    def disable_circuit_breaker(self):
        self.circuit_breaker_active = False
        logger.info("Circuit breaker DISABLED for EnhancedNanoLLM.")

    def get_memory_usage_mb(self) -> float:
        mem_params = sum([param.nelement() * param.element_size() for param in self.parameters()])
        mem_bufs = sum([buf.nelement() * buf.element_size() for buf in self.buffers()])
        total_mem_bytes = mem_params + mem_bufs
        return total_mem_bytes / (1024 ** 2)

# --- Reasoning-Specific Security Rules ---
# This would typically extend or configure the main SecurityPipeline
class ReasoningSecurityRules(BaseSecurityPipeline): # Inherit from the actual base
    def __init__(self, config: Optional[TrendReasoningConfig] = None): # Use TrendReasoningConfig
        super().__init__(config) # Pass config to base
        # If TrendReasoningConfig is not directly usable by BaseSecurityPipeline, adapt this
        self.reasoning_config = config if config else TrendReasoningConfig()


    def sanitize_trend_text_input(self, text: str) -> str:
        # Rule: Sanitize trend text, e.g., length limits, character stripping
        if len(text) > self.reasoning_config.max_trend_length:
            logger.warning(f"Trend text exceeds max length ({len(text)} > {self.reasoning_config.max_trend_length}). Truncating.")
            text = text[:self.reasoning_config.max_trend_length]
        # Add other sanitization steps as needed
        return super().sanitize_input(text) # Use base sanitization too

    def validate_context_source(self, source: str, content_preview: str) -> bool:
        # Rule: Validate if the context source is trusted
        allowed_sources = ["vector_store_embedding", "user_provided_context"] # Example
        if source not in allowed_sources:
            logger.warning(f"Context source '{source}' is not in allowed list.")
            return False
        logger.debug(f"Context source '{source}' validated for content: {content_preview[:50]}...")
        return super().validate_context(source, content_preview)

    def monitor_api_call(self, service_name: str, cost: float):
        # Rule: Placeholder for monitoring external API calls related to reasoning
        logger.info(f"API Call Monitored: Service={service_name}, Cost=${cost:.4f}")
        # This would integrate with a central monitoring system / budget enforcer

# --- Semantic Velocity Analyzer ---
class SemanticVelocityAnalyzer:
    """
    Analyzes the velocity (rate of change) of trend semantics over time.
    Used to identify rapidly accelerating trends for the coolness classifier.
    """
    def __init__(self, window_size: int = 5, decay_factor: float = 0.8):
        """
        Initialize the velocity analyzer.

        Args:
            window_size: Number of historical embeddings to consider
            decay_factor: Weight decay for older embeddings (0-1)
        """
        self.window_size = window_size
        self.decay_factor = decay_factor
        self.trend_history = {}  # Maps trend_id -> List[Tuple[timestamp, embedding]]
        self.lock = threading.RLock()
        logger.info(f"Initialized SemanticVelocityAnalyzer with window_size={window_size}")

    def calculate_velocity(self,
                          current_embedding: torch.Tensor,
                          trend_id: Optional[str] = None,
                          store_embedding: bool = True) -> float:
        """
        Calculate the semantic velocity of a trend based on its embedding history.

        Args:
            current_embedding: The current semantic embedding tensor
            trend_id: Optional identifier to track this trend over time
            store_embedding: Whether to store this embedding in history

        Returns:
            velocity_score: A float between 0-1 indicating semantic velocity
        """
        # Normalize the embedding for consistent calculations
        if current_embedding.dim() > 1 and current_embedding.size(0) == 1:
            # If batch dimension present, remove it
            current_embedding = current_embedding.squeeze(0)

        # Ensure we're working with a normalized vector
        current_embedding = F.normalize(current_embedding, p=2, dim=0)

        # If no trend_id provided, we can only calculate instantaneous properties
        if trend_id is None:
            # For trends without history, use embedding properties as proxy for velocity
            # Higher entropy/variance in embedding often correlates with more dynamic content
            entropy = torch.var(current_embedding).item()
            # Scale to 0-1 range with sigmoid
            return torch.sigmoid(torch.tensor(entropy * 10)).item()

        # For trends with history, calculate actual velocity
        with self.lock:
            if trend_id not in self.trend_history:
                self.trend_history[trend_id] = []

            history = self.trend_history[trend_id]
            current_time = time.time()

            if store_embedding:
                # Add current embedding to history
                history.append((current_time, current_embedding.detach().cpu()))
                # Keep only window_size most recent embeddings
                if len(history) > self.window_size:
                    history.pop(0)

            # If we don't have enough history, return a default score
            if len(history) < 2:
                return 0.5  # Neutral velocity score

            # Calculate weighted semantic shift over time
            total_shift = 0.0
            total_weight = 0.0

            # Sort by timestamp to ensure chronological order
            sorted_history = sorted(history, key=lambda x: x[0])

            # Calculate weighted cosine distance between consecutive embeddings
            for i in range(1, len(sorted_history)):
                prev_time, prev_emb = sorted_history[i-1]
                curr_time, curr_emb = sorted_history[i]

                # Time difference in seconds
                time_diff = max(0.1, curr_time - prev_time)  # Avoid division by zero

                # Cosine similarity between embeddings
                cos_sim = F.cosine_similarity(prev_emb, curr_emb, dim=0).item()
                # Convert to distance (1 - similarity)
                semantic_distance = 1.0 - cos_sim

                # Calculate shift rate (distance/time)
                shift_rate = semantic_distance / time_diff

                # Apply decay factor based on recency
                weight = self.decay_factor ** (len(sorted_history) - i - 1)

                total_shift += shift_rate * weight
                total_weight += weight

            # Normalize the velocity score to 0-1 range
            if total_weight > 0:
                raw_velocity = total_shift / total_weight
                # Scale with sigmoid to ensure 0-1 range
                velocity_score = torch.sigmoid(torch.tensor(raw_velocity * 20)).item()
                return velocity_score

            return 0.5  # Default if no valid calculations

    def update_trend_history(self, trend_id: str, embedding: torch.Tensor):
        """Add a new embedding to the trend's history without calculating velocity."""
        with self.lock:
            if trend_id not in self.trend_history:
                self.trend_history[trend_id] = []

            history = self.trend_history[trend_id]
            current_time = time.time()

            # Add current embedding to history
            history.append((current_time, embedding.detach().cpu()))

            # Keep only window_size most recent embeddings
            if len(history) > self.window_size:
                history.pop(0)

    def clear_history(self, trend_id: Optional[str] = None):
        """Clear history for a specific trend or all trends."""
        with self.lock:
            if trend_id is None:
                self.trend_history.clear()
            elif trend_id in self.trend_history:
                del self.trend_history[trend_id]

# --- Enhanced Coolness Classifier ---
class CoolnessReasoner:
    def __init__(self, reasoning_model: EnhancedNanoLLM, security_pipeline: BaseSecurityPipeline, config: TrendReasoningConfig):
        self.model = reasoning_model
        self.security = security_pipeline # For input sanitization
        self.config = config
        # Initialize SemanticVelocityAnalyzer
        self.velocity_analyzer = SemanticVelocityAnalyzer()
        self.reasoning_cache = LRUCache(capacity=100) # Cache results

    def _tokenize_for_model(self, text: str) -> Optional[torch.Tensor]:
        """Tokenizes text for the EnhancedNanoLLM."""
        if not text: return None
        # Using the model's internal tokenizer or a compatible one
        # For this example, using the model's _get_token_ids
        return self.model._get_token_ids(text, self.config.max_trend_length)

    @torch.no_grad()
    def analyze_trend(self, trend_text_main: str,
                      context_text_for_ki: Optional[str] = None,
                      force_refresh: bool = False) -> Dict[str, Any]:

        sanitized_trend_text = self.security.sanitize_input(trend_text_main)
        if not sanitized_trend_text:
            return {"error": "Invalid trend text after sanitization", "category": -1, "velocity_score": 0.0}

        cache_key = f"{sanitized_trend_text}_{context_text_for_ki}"
        if not force_refresh:
            cached_result = self.reasoning_cache.get(cache_key)
            if cached_result:
                logger.debug(f"Coolness analysis for '{sanitized_trend_text[:30]}' found in cache.")
                return cached_result

        tokenized_trend = self._tokenize_for_model(sanitized_trend_text)
        if tokenized_trend is None:
            logger.error(f"Failed to tokenize trend_text: {sanitized_trend_text[:100]}")
            return {"error": "Tokenization failed", "category": -1, "velocity_score": 0.0}

        self.model.eval() # Ensure model is in eval mode for inference

        # The model's forward takes: x_tokens, trend_text_for_attention, context_trend_text_for_ki
        # Here, trend_text_main can serve as trend_text_for_attention
        classification_logits = self.model(tokenized_trend,
                                           trend_text_for_attention=sanitized_trend_text,
                                           context_trend_text_for_ki=context_text_for_ki)

        classification = classification_logits.argmax().item()

        # Calculate velocity score using the SemanticVelocityAnalyzer
        # Get the embedding from the model's pooled output
        with torch.no_grad():
            # Use the mean-pooled representation from the model as our semantic embedding
            embedding = self.model.embedding(tokenized_trend).mean(dim=1)  # (1, embed_size)

        # Generate a trend_id from the text for history tracking
        trend_id = f"trend_{hash(sanitized_trend_text) % 10000}"

        # Calculate velocity using the analyzer
        velocity_score = self.velocity_analyzer.calculate_velocity(
            current_embedding=embedding,
            trend_id=trend_id,
            store_embedding=True
        )

        result = {
            "category": classification,
            "velocity_score": velocity_score,
            "raw_logits": classification_logits.cpu().numpy().tolist()
        }
        self.reasoning_cache.put(cache_key, result)
        return result

# --- Base CostAwareDistiller (from attached file, slightly completed) ---
class CostAwareDistiller:
    def __init__(
        self,
        daily_budget: float = 1.0,
        # security_pipeline: BaseSecurityPipeline # Added for output validation
    ):
        self.daily_budget = daily_budget
        self.spent_today = 0.0
        self.last_reset_date = datetime.now().date()
        # self.security = security_pipeline

        # For token accounting (if external system is not available)
        self.token_accounting_available = False # Assume not available unless integrated
        try:
            # from groq_knowledge_distiller import TokenAccountingSystem, BudgetEnforcer # Example
            # self.token_accounting = TokenAccountingSystem()
            # self.budget_enforcer = BudgetEnforcer(daily_budget)
            # self.token_accounting_available = True
            pass # No actual import here for now
        except ImportError:
            logger.info("External token accounting system not found. Using basic internal tracking.")

        self.distillation_metrics = {
            "total_tokens_processed": 0,
            "total_distillation_cost": 0.0,
            "distillation_count": 0,
            "average_cost_per_distillation": 0.0
        }
        logger.info(f"Initialized CostAwareDistiller with ${daily_budget:.2f} daily budget.")

    def _check_reset_budget(self):
        today = datetime.now().date()
        if today > self.last_reset_date:
            logger.info(f"New day detected. Resetting daily budget. Spent yesterday: ${self.spent_today:.2f}")
            self.spent_today = 0.0
            self.last_reset_date = today
            # Reset metrics if they are daily too
            self.distillation_metrics["distillation_count"] = 0 # Example reset

    def can_spend(self, estimated_cost: float) -> bool:
        self._check_reset_budget()
        if self.token_accounting_available and hasattr(self, 'budget_enforcer'):
            # return self.budget_enforcer.can_spend(estimated_cost)
            pass # Placeholder for external system
        return (self.spent_today + estimated_cost) <= self.daily_budget

    def estimate_token_cost(self, text_tokens: int, response_tokens: int = 100, cost_per_1k_tokens: float = 0.001) -> float:
        total_tokens = text_tokens + response_tokens
        return (total_tokens / 1000) * cost_per_1k_tokens

    def log_spending(self, cost: float, tokens_processed: int):
        self._check_reset_budget()
        self.spent_today += cost

        # Update metrics
        self.distillation_metrics["total_tokens_processed"] += tokens_processed
        self.distillation_metrics["total_distillation_cost"] += cost
        self.distillation_metrics["distillation_count"] += 1
        if self.distillation_metrics["distillation_count"] > 0:
            self.distillation_metrics["average_cost_per_distillation"] = (
                self.distillation_metrics["total_distillation_cost"] /
                self.distillation_metrics["distillation_count"]
            )
        logger.info(f"Logged spending: ${cost:.4f}. Total spent today: ${self.spent_today:.2f}. Tokens: {tokens_processed}")

    def _call_teacher_model(self, processed_input: Any, context_for_teacher: Optional[Any]) -> Tuple[Any, int, float]:
        # Placeholder for actual teacher model call
        # This should return: (teacher_output, tokens_used, cost_incurred)
        logger.info(f"Calling mock teacher model with input: {str(processed_input)[:50]}... and context.")
        # Simulate API call
        time.sleep(0.1) # Simulate latency
        mock_output = f"Teacher distilled: {str(processed_input)[:50]}"
        mock_tokens = len(str(processed_input).split()) + 50 # Input + output tokens
        mock_cost = self.estimate_token_cost(len(str(processed_input).split()), 50)
        return mock_output, mock_tokens, mock_cost

# --- Enhanced CostAwareDistiller ---
class EnhancedCostAwareDistiller(CostAwareDistiller):
    def __init__(self,
                 reasoning_model: EnhancedNanoLLM,
                 knowledge_integrator: KnowledgeIntegrator,
                 security_pipeline: BaseSecurityPipeline, # For sanitization and validation
                 daily_budget: float = 1.0,
                 config: Optional[TrendReasoningConfig] = None): # For tokenization length
        super().__init__(daily_budget=daily_budget) # Pass budget to base
        self.reasoner = reasoning_model # This is EnhancedNanoLLM
        self.knowledge_integrator = knowledge_integrator
        self.security = security_pipeline
        self.config = config if config else TrendReasoningConfig() # For max_trend_length

        # Placeholder for HybridDistillationGate or similar for advanced routing/teacher selection
        # from hybrid_distillation_gate import HybridDistillationGate
        # self.knowledge_gate = HybridDistillationGate()

    def distill_trend_knowledge(self, raw_trend_text: str) -> Optional[str]:
        """
        Implements the new distillation flow:
        Raw Trend → SecuritySanitize → NanoLLMEnrich → VectorContextInject → TeacherDistill → OutputValidation
        """
        # 1. Raw Trend (Input: raw_trend_text)

        # 2. SecuritySanitize
        sanitized_text = self.security.sanitize_input(raw_trend_text)
        if not sanitized_text:
            logger.error("Distillation failed: Input sanitization resulted in empty text.")
            return None

        # Tokenize for NanoLLM
        # Using reasoner's internal tokenizer for consistency
        tokenized_input = self.reasoner._get_token_ids(sanitized_text, self.config.max_trend_length)
        if tokenized_input is None:
            logger.error("Distillation failed: Tokenization failed for NanoLLM.")
            return None

        # 3. NanoLLMEnrich
        # The NanoLLM can provide initial analysis, embeddings, or a summary.
        # For this example, let's assume it provides classified logits or a feature vector.
        # The `forward` of EnhancedNanoLLM returns logits.
        # We might need a different method on EnhancedNanoLLM for "enrichment" if logits are not enough.
        # Let's use the logits as a form of enrichment for now.
        # The NanoLLM itself might use KI internally if context_trend_text_for_ki is passed.
        # For this flow, let's make KI explicit for the teacher.
        self.reasoner.eval()
        with torch.no_grad():
            nano_llm_output_logits = self.reasoner(tokenized_input, trend_text_for_attention=sanitized_text)
        # nano_llm_enrichment = f"NanoLLM Analysis (category): {nano_llm_output_logits.argmax().item()}"
        # Or, use the pooled output before classifier as enrichment
        # For simplicity, we'll pass the sanitized_text and let the teacher use KI context.

        # 4. VectorContextInject (for the Teacher Model)
        # Get context from KnowledgeIntegrator based on the sanitized text
        context_for_teacher = self.knowledge_integrator.get_context(sanitized_text)
        # context_for_teacher is a tensor or None. Convert to string or structured data for teacher.
        if context_for_teacher is not None:
            # This is a tensor, teacher might expect text.
            # For now, let's assume teacher can handle a summary or we just indicate context was found.
            context_info_for_teacher = "Context from vector store was considered."
            # In a real system, this would be the actual context data formatted for the teacher.
        else:
            context_info_for_teacher = "No specific context found in vector store."

        # Prepare input for teacher model (e.g., combining original text, NanoLLM insights, KI context)
        # teacher_model_input = f"{sanitized_text}\nNanoLLM Insights: {nano_llm_enrichment}\nVector Context: {context_info_for_teacher}"
        # Simpler: teacher gets sanitized_text and context_info_for_teacher separately.

        # Estimate cost before calling teacher
        # Assuming teacher model input is primarily the sanitized_text
        estimated_cost = self.estimate_token_cost(len(sanitized_text.split()))
        if not self.can_spend(estimated_cost):
            logger.warning(f"Distillation skipped: Estimated cost ${estimated_cost:.4f} exceeds budget.")
            # Fallback: maybe return NanoLLM's direct output or nothing
            return f"Budget limited. NanoLLM category: {nano_llm_output_logits.argmax().item()}"


        # 5. TeacherDistillation
        try:
            # _call_teacher_model is a placeholder in CostAwareDistiller base
            # It should interact with the actual teacher model (e.g. Groq, OpenAI, local model)
            # It needs to handle API calls, errors, and return (output, tokens_used, cost)
            teacher_output, tokens_used, actual_cost = self._call_teacher_model(sanitized_text, context_info_for_teacher)
            self.log_spending(actual_cost, tokens_used) # Log after successful call
        except Exception as e:
            logger.error(f"Error during TeacherDistillation: {e}")
            # Fallback or error handling
            return f"Error in teacher distillation. NanoLLM category: {nano_llm_output_logits.argmax().item()}"


        # 6. OutputValidation
        validated_teacher_output = self.security.validate_output(teacher_output)

        logger.info(f"Distillation successful for trend: {sanitized_text[:30]}...")
        return validated_teacher_output


# --- Orchestrator/Usage Example (adapted from attached file) ---
class TrendReasoningOrchestrator:
    def __init__(
        self,
        config: TrendReasoningConfig,
        model: EnhancedNanoLLM,
        knowledge_integrator: KnowledgeIntegrator,
        security_pipeline: BaseSecurityPipeline # Use the base or the specific ruleset
    ):
        self.config = config
        self.model = model # EnhancedNanoLLM
        self.knowledge_integrator = knowledge_integrator
        self.security_pipeline = security_pipeline

        # CoolnessReasoner for direct trend analysis (not distillation)
        self.coolness_reasoner = CoolnessReasoner(model, security_pipeline, config)

        # Distiller for knowledge enrichment
        self.distiller = EnhancedCostAwareDistiller(
            reasoning_model=model,
            knowledge_integrator=knowledge_integrator,
            security_pipeline=security_pipeline, # Pass security to distiller
            daily_budget=1.0, # Example budget
            config=config
        )

        self.process_queue = [] # Consider collections.deque(maxlen=...)
        self.lock = threading.RLock()

        logger.info("Initialized TrendReasoningOrchestrator.")

    def _get_trend_text_from_id(self, trend_id: int) -> Optional[str]:
        # Placeholder: In a real system, this would fetch trend data from a DB or service
        logger.debug(f"Fetching trend text for ID: {trend_id}")
        if trend_id == 1: return "Example trend about AI advancements and new models."
        if trend_id == 2: return "Another trend discussing market volatility and cryptocurrency."
        return f"This is a sample trend text for ID {trend_id}. It might be longer and more complex."


    def analyze_single_trend(self, trend_id: int, force_refresh: bool = False) -> Dict[str, Any]:
        raw_trend_text = self._get_trend_text_from_id(trend_id)
        if not raw_trend_text:
            return {"error": "Trend ID not found or text is empty", "trend_id": trend_id}

        # Basic input validation (example)
        if not isinstance(trend_id, int) or trend_id <= 0:
            logger.warning(f"Invalid trend_id: {trend_id}")
            return {"error": "Invalid trend ID"}

        # Check memory usage (example hook)
        current_mem_mb = self.model.get_memory_usage_mb()
        if current_mem_mb > self.config.max_memory_mb:
            logger.warning(f"High memory usage ({current_mem_mb:.2f}MB) before processing trend {trend_id}. Activating circuit breaker.")
            self.model.enable_circuit_breaker() # Example of failure handling
            # Optionally, try to clear caches or prune more aggressively
            self.coolness_reasoner.reasoning_cache = LRUCache(capacity=10) # Drastically reduce cache
            self.model.apply_dynamic_pruning() # Re-apply pruning

        # If circuit breaker is on for the model, CoolnessReasoner might also be affected
        if self.model.circuit_breaker_active:
             return {"error": "System circuit breaker active, analysis aborted.", "trend_id": trend_id}


        start_time = time.time()
        # Use CoolnessReasoner for direct analysis
        # Context for KI can be the trend text itself or related texts
        analysis_result = self.coolness_reasoner.analyze_trend(
            raw_trend_text,
            context_text_for_ki=raw_trend_text, # KI uses this to find related context
            force_refresh=force_refresh
        )
        elapsed_ms = (time.time() - start_time) * 1000

        if "error" not in analysis_result and elapsed_ms > self.config.target_latency_ms:
            logger.warning(f"Reasoning latency ({elapsed_ms:.2f}ms) for trend {trend_id} exceeded target ({self.config.target_latency_ms}ms).")
            # Potentially enable circuit breaker or adjust config if this happens frequently
            # self.config.adjust_for_memory_constraints(current_mem_mb) # Example adjustment call

        analysis_result["meta"] = {
            "trend_id": trend_id,
            "latency_ms": elapsed_ms,
            "memory_footprint_mb": current_mem_mb, # Approx. before this call
            "timestamp": datetime.now().isoformat()
        }
        return analysis_result

    def enrich_trend_with_distillation(self, trend_id: int) -> Dict[str, Any]:
        raw_trend_text = self._get_trend_text_from_id(trend_id)
        if not raw_trend_text:
            return {"error": "Trend ID not found for distillation", "trend_id": trend_id}

        start_time = time.time()
        distilled_info = self.distiller.distill_trend_knowledge(raw_trend_text)
        elapsed_ms = (time.time() - start_time) * 1000

        if distilled_info is None or "error" in (distilled_info if isinstance(distilled_info, dict) else {}):
            logger.error(f"Distillation failed for trend {trend_id}.")
            return {"error": "Distillation process failed", "trend_id": trend_id, "meta": {"latency_ms": elapsed_ms}}

        return {
            "trend_id": trend_id,
            "distilled_content": distilled_info,
            "meta": {"latency_ms": elapsed_ms, "timestamp": datetime.now().isoformat()}
        }

    def process_batch_analysis(self, trend_ids: List[int], force_refresh: bool = False) -> List[Dict[str, Any]]:
        # Simplified batch processing: iterate and call single analysis
        # True batching would involve preparing a batch tensor for the model
        results = []
        for trend_id in trend_ids:
            # Basic queue management (example)
            with self.lock:
                if len(self.process_queue) >= self.config.max_batch_size * 2: # Limit queue size
                    self.process_queue.pop(0)
                self.process_queue.append(trend_id)

            results.append(self.analyze_single_trend(trend_id, force_refresh))
        return results

    def close(self):
        # Placeholder for cleanup if needed (e.g., closing DB connections in VectorStoreManager)
        logger.info("TrendReasoningOrchestrator closing down.")
        if hasattr(self.knowledge_integrator, 'close'):
            self.knowledge_integrator.close()


# --- Initialization and Main Execution Example ---
def initialize_enhanced_system(vocab_size_for_model: int = 10000) -> Dict[str, Any]:
    logger.info("Initializing Enhanced Trend Reasoning System...")

    # Configuration
    reasoning_config = TrendReasoningConfig(
        embed_size=64,
        num_reasoning_layers=2,
        attention_heads=4,
        max_trend_length=256,
        knowledge_integration=True,
        dynamic_pruning=True,
        sparsity=0.3,
        attention_sparsity=0.7 # Keep top 30% attention scores
    )

    # Core Components (using placeholders for external ones)
    vector_store = VectorStoreManager() # Placeholder

    # Use ReasoningSecurityRules or the BaseSecurityPipeline
    # security_rules = ReasoningSecurityRules(config=reasoning_config)
    # For simplicity, using the base placeholder if ReasoningSecurityRules is not fully integrated/tested
    security_pipeline = BaseSecurityPipeline(config=reasoning_config) # Pass config

    # Enhanced NanoLLM
    nano_llm = EnhancedNanoLLM(
        config=reasoning_config,
        vector_store=vector_store,
        security=security_pipeline,
        vocab_size=vocab_size_for_model
    )

    # Apply Pruning (if enabled in config)
    if reasoning_config.dynamic_pruning:
        nano_llm.apply_dynamic_pruning()

    # Quantization (PTQ)
    # Create a dummy calibration dataloader for the example
    # In a real scenario, this dataloader should provide representative data
    class DummyCalibrationDataset(torch.utils.data.Dataset):
        def __init__(self, num_samples, max_len, vocab_size):
            self.num_samples = num_samples
            self.max_len = max_len
            self.vocab_size = vocab_size
            self.sample_texts = [f"Sample calibration trend text number {i}" for i in range(num_samples)]

        def __len__(self):
            return self.num_samples

        def __getitem__(self, idx):
            # Return (tokens, trend_text_for_attention, trend_text_for_ki)
            text = self.sample_texts[idx]
            # Crude tokenization for dummy data
            tokens = [abs(hash(word)) % self.vocab_size for word in text.split()[:self.max_len]]
            if not tokens: tokens = [0]
            if len(tokens) < self.max_len: tokens.extend([0] * (self.max_len - len(tokens)))

            return (torch.tensor([tokens], dtype=torch.long), # (1, max_len)
                    text, # trend_text_for_attention
                    text  # context_trend_text_for_ki
                   )

    # Only create dataloader if quantization is to be performed
    # calibration_dataset = DummyCalibrationDataset(num_samples=20, max_len=reasoning_config.max_trend_length, vocab_size=vocab_size_for_model)
    # calibration_loader = torch.utils.data.DataLoader(calibration_dataset, batch_size=1) # Batch size 1 for this dummy loader
    # nano_llm.quantize_model(calibration_dataloader=calibration_loader)
    # logger.info(f"NanoLLM quantized. Memory usage: {nano_llm.get_memory_usage_mb():.2f} MB")


    # Knowledge Integrator (used by NanoLLM and Distiller)
    knowledge_integrator = KnowledgeIntegrator(vector_store, security_pipeline)
    # Update KI in NanoLLM if it was created with a different instance or None
    if reasoning_config.knowledge_integration:
        nano_llm.knowledge_integrator = knowledge_integrator


    # Orchestrator
    orchestrator = TrendReasoningOrchestrator(
        config=reasoning_config,
        model=nano_llm,
        knowledge_integrator=knowledge_integrator,
        security_pipeline=security_pipeline
    )

    logger.info("Enhanced Trend Reasoning System Initialized.")
    return {
        "orchestrator": orchestrator,
        "nano_llm": nano_llm,
        "config": reasoning_config,
        "security": security_pipeline,
        "ki": knowledge_integrator
    }

if __name__ == '__main__':
    logger.info("Starting Trend Reasoning System Demo...")

    initialized_system = initialize_enhanced_system()
    orchestrator = initialized_system["orchestrator"]
    nano_llm_model = initialized_system["nano_llm"]
    current_config = initialized_system["config"]

    # --- Demonstrate Quantization (Optional - uncomment to run) ---
    # logger.info("Demonstrating model quantization...")
    # vocab_size = nano_llm_model.embedding.num_embeddings # Get vocab_size from model
    # calibration_dataset = DummyCalibrationDataset(num_samples=10, max_len=current_config.max_trend_length, vocab_size=vocab_size)
    # # Ensure DataLoader is imported: from torch.utils.data import DataLoader
    # from torch.utils.data import DataLoader
    # calibration_loader = DataLoader(calibration_dataset, batch_size=1)
    #
    # logger.info(f"Model memory before quantization: {nano_llm_model.get_memory_usage_mb():.2f} MB")
    # nano_llm_model.quantize_model(calibration_dataloader=calibration_loader)
    # logger.info(f"Model memory after quantization: {nano_llm_model.get_memory_usage_mb():.2f} MB")
    # logger.info(f"Model is_quantized: {nano_llm_model.is_quantized}")


    # --- Demonstrate Trend Analysis ---
    logger.info("\n--- Analyzing Sample Trend (ID 1) ---")
    analysis1 = orchestrator.analyze_single_trend(trend_id=1)
    logger.info(f"Analysis for Trend 1: {analysis1}")

    logger.info("\n--- Analyzing Sample Trend (ID 2) with Forced Refresh ---")
    analysis2 = orchestrator.analyze_single_trend(trend_id=2, force_refresh=True)
    logger.info(f"Analysis for Trend 2: {analysis2}")

    # --- Demonstrate Knowledge Integration (Update) ---
    logger.info("\n--- Updating Knowledge Base ---")
    ki_component = initialized_system["ki"]
    ki_component.update_knowledge({"text": "New important trend about sustainable energy.", "source": "demo_feed"})
    # Try to get context for this new trend (it should be cached by KI if get_context is called)
    # context_for_new_trend = ki_component.get_context("New important trend about sustainable energy.")
    # logger.info(f"Context retrieved for new trend (should be a tensor if successful): {type(context_for_new_trend)}")


    # --- Demonstrate Distillation ---
    logger.info("\n--- Distilling Knowledge for Trend (ID 1) ---")
    distilled_result = orchestrator.enrich_trend_with_distillation(trend_id=1)
    logger.info(f"Distilled Result for Trend 1: {distilled_result}")

    # --- Demonstrate Circuit Breaker ---
    # logger.info("\n--- Demonstrating Circuit Breaker (Manual Activation) ---")
    # nano_llm_model.enable_circuit_breaker()
    # analysis_cb = orchestrator.analyze_single_trend(trend_id=3) # Assuming trend_id 3 exists or _get_trend_text_from_id handles it
    # logger.info(f"Analysis with Circuit Breaker ON: {analysis_cb}")
    # nano_llm_model.disable_circuit_breaker()
    # analysis_cb_off = orchestrator.analyze_single_trend(trend_id=3)
    # logger.info(f"Analysis with Circuit Breaker OFF: {analysis_cb_off}")

    # --- Batch Processing Example ---
    logger.info("\n--- Batch Processing Example ---")
    batch_results = orchestrator.process_batch_analysis([1,2,3,4,5]) # Assuming _get_trend_text_from_id can handle these
    logger.info(f"Batch analysis results (first 2): {batch_results[:2]}")


    logger.info("\nTrend Reasoning System Demo Finished.")
    orchestrator.close()
