#!/usr/bin/env python3
"""
Example test script for validating the trend summarization system.
This creates a simple test environment with mock data to ensure the system works.
"""

import os
import sys
import logging
import torch
import numpy as np
import psycopg2
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project directory to path to allow imports
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import our custom modules
from nano_neural_network import NanoLLM, NeuralOptimizer, ActiveMemory
from secure_services import SecureDistillationSystem
from trend_summarizer import TrendSummarizer

class MockVectorStoreManager:
    """Mock VectorStoreManager for testing"""
    def __init__(self):
        self.db_host = "localhost"
        self.db_port = "5432"
        self.db_name = "trend_crawler_test"
        self.db_user = "postgres"
        self.db_password = "postgres"

def setup_test_environment():
    """Set up a test environment with mock components"""
    # Create nano LLM
    nano_llm = NanoLLM(vocab_size=1000, d_model=32, nhead=2)
    
    # Create optimizer and memory
    optimizer = NeuralOptimizer(nano_llm.parameters(), lr=1e-3)
    active_memory = ActiveMemory(capacity=100)
    
    # Create secure distillation system
    distillation_system = SecureDistillationSystem(
        nano_llm_instance=nano_llm,
        neural_optimizer_instance=optimizer,
        active_memory_instance=active_memory
    )
    
    # Patch summarize_with_nano_llm to return test summaries
    def mock_summarize(_, texts, prompt_template=None):
        return ["This is a mock summary about " + text.split()[0] for text in texts]
    
    distillation_system.summarize_with_nano_llm = mock_summarize
    
    # Create mock vector store manager
    vector_store_manager = MockVectorStoreManager()
    
    # Mock database connection for trend summarizer
    conn = MagicMock()
    cursor = MagicMock()
    cursor.fetchall = MagicMock(return_value=[
        ("Content about AI and machine learning", ),
        ("Content about neural networks", ),
        ("Content about natural language processing", )
    ])
    cursor.fetchone = MagicMock(return_value=([0.1, 0.2, 0.3, 0.4],))
    conn.cursor = MagicMock(return_value=cursor)
    
    # Create trend summarizer with mocked connection
    trend_summarizer = TrendSummarizer(
        secure_distiller=distillation_system,
        vector_store_manager=vector_store_manager
    )
    trend_summarizer.conn = conn
    
    return {
        'nano_llm': nano_llm,
        'optimizer': optimizer,
        'active_memory': active_memory,
        'distillation_system': distillation_system,
        'vector_store_manager': vector_store_manager,
        'trend_summarizer': trend_summarizer
    }

def test_trend_summarization():
    """Test trend summarization functionality"""
    components = setup_test_environment()
    trend_summarizer = components['trend_summarizer']
    
    # Test summarize_trend
    summary = trend_summarizer.summarize_trend(1, top_k=3)
    logger.info(f"Generated summary: {summary}")
    
    # Test analyze_semantic_velocity
    velocity = trend_summarizer.analyze_semantic_velocity(1)
    logger.info(f"Semantic velocity: {velocity}")
    
    # Test detect_semantic_shifts
    shifts = trend_summarizer.detect_semantic_shifts(1)
    logger.info(f"Semantic shifts: {shifts}")
    
    # Test identify_sleeper_trends
    sleepers = trend_summarizer.identify_sleeper_trends()
    logger.info(f"Identified sleeper trends: {sleepers}")
    
    # Test predict_trend_significance
    significance = trend_summarizer.predict_trend_significance(1)
    logger.info(f"Trend significance prediction: {significance}")

def main():
    """Main entry point"""
    logger.info("Running trend summarization system test")
    test_trend_summarization()
    logger.info("Test completed successfully")

if __name__ == "__main__":
    main()
