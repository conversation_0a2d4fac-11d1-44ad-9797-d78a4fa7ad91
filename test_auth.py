#!/usr/bin/env python3
"""
Test authentication functionality
"""
import os
import sys
sys.path.append('.')

# Import necessary components
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel
from typing import Optional

# User models
class User(BaseModel):
    """Base user model."""
    username: str
    email: str
    full_name: Optional[str] = None
    disabled: Optional[bool] = False
    is_admin: Optional[bool] = False

class UserInDB(User):
    """User model with password hash."""
    password_hash: str

# Set up password context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Create in-memory database
USERS_DB = {}
POSTGRES_AVAILABLE = False

def verify_password(plain_password, hashed_password):
    """Verify a password against a hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_user(username: str):
    """Get a user by username."""
    if not POSTGRES_AVAILABLE:
        # Fall back to in-memory database
        user_data = USERS_DB.get(username)
        if user_data:
            return UserInDB(**user_data)
        return None
    
    # Would connect to database here if available
    return None

def authenticate_user(username: str, password: str):
    """Authenticate a user."""
    user = get_user(username)
    if not user:
        return False
    if not verify_password(password, user.password_hash):
        return False
    return user

# Initialize admin user
def init_admin_user():
    """Initialize admin user in memory."""
    admin_password = "admin"
    admin_user = {
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Admin User",
        "disabled": False,
        "is_admin": True,
        "password_hash": pwd_context.hash(admin_password),
        "needs_password_change": True
    }
    USERS_DB["admin"] = admin_user
    print(f"Created admin user: username=admin, password=admin")
    return admin_user

if __name__ == "__main__":
    # Test the authentication system
    print("Testing authentication system...")
    
    # Initialize admin user
    admin_user = init_admin_user()
    
    # Test authentication
    print("\nTesting authentication...")
    result = authenticate_user("admin", "admin")
    if result:
        print("✓ Authentication successful!")
        print(f"User: {result.username}, Admin: {result.is_admin}")
    else:
        print("✗ Authentication failed!")
    
    # Test wrong password
    print("\nTesting wrong password...")
    result = authenticate_user("admin", "wrong")
    if result:
        print("✗ Authentication should have failed!")
    else:
        print("✓ Authentication correctly failed with wrong password")
        
    # Test non-existent user
    print("\nTesting non-existent user...")
    result = authenticate_user("nonexistent", "password")
    if result:
        print("✗ Authentication should have failed!")
    else:
        print("✓ Authentication correctly failed for non-existent user")
