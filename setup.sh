#!/bin/bash

# This script helps set up and activate the trend-crawler environment

echo "Setting up Trend Crawler environment..."

# Check if Anaconda is installed
if ! command -v conda &> /dev/null
then
    echo "Anaconda/Miniconda is not installed or not in your PATH."
    echo "Please install <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON> first."
    exit 1
fi

# Check if environment exists
if conda env list | grep -q "trend-crawler"; then
    echo "Trend-crawler environment already exists."
else
    echo "Creating trend-crawler environment..."
    conda create -y -n trend-crawler python=3.9
    echo "Environment created."
fi

# Activate environment
echo "Activating trend-crawler environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate trend-crawler

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
pip install -r requirements-twitter.txt
pip install -r requirements-captcha.txt
pip install -r requirements-advanced.txt

# Install playwright dependencies
echo "Installing Playwright browsers..."
python -m playwright install

# Check .env file
if [ -f .env ]; then
    echo ".env file exists."
    
    # Validate critical environment variables
    source .env
    
    missing_vars=()
    
    if [ -z "$TWITTER_USERNAME" ] || [ "$TWITTER_USERNAME" == "your_twitter_username_or_email" ]; then
        missing_vars+=("TWITTER_USERNAME")
    fi
    
    if [ -z "$TWITTER_PASSWORD" ] || [ "$TWITTER_PASSWORD" == "your_twitter_password" ]; then
        missing_vars+=("TWITTER_PASSWORD")
    fi
    
    if [ -z "$PROXY_SERVER" ]; then
        missing_vars+=("PROXY_SERVER")
    fi
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        echo "WARNING: The following environment variables need to be updated in .env:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
    else
        echo "All critical environment variables are set."
    fi
else
    echo "Creating .env file..."
    cat > .env << EOL
# Twitter API credentials
TWITTER_BEARER_TOKEN=your_api_token_here

# Twitter login credentials for browser-based scraping
TWITTER_USERNAME=jmorgan_scraper
TWITTER_PASSWORD=Tr3ndCr@wl3r2025!

# Bright Data proxy settings
PROXY_SERVER=http://brd-customer-hl_caaa2374-zone-residential_proxy1:<EMAIL>:33335
BRIGHTDATA_HOST=brd.superproxy.io
BRIGHTDATA_PORT=33335
BRIGHTDATA_USERNAME=brd-customer-hl_caaa2374-zone-residential_proxy1
BRIGHTDATA_PASSWORD=grh8h9v2fmib
BRIGHTDATA_API_TOKEN=5b4f64bce0299cd3a647c175106e9c0e642a8805f39859d27116fdec0eb832a1

# Scraping configuration
HEADLESS_MODE=true
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=5
USER_AGENT_ROTATION=true
CAPTCHA_SOLVING=true

# Database settings (if used)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trend_crawler
DB_USER=postgres
DB_PASSWORD=postgres
EOL
    echo ".env file created with default values."
fi

# Check proxy configuration
if [ -f proxy_config.json ]; then
    echo "proxy_config.json exists."
else
    echo "Creating proxy_config.json..."
    cat > proxy_config.json << EOL
{
    "brightdata": {
        "enabled": true,
        "proxies": [
            {
                "server": "brd.superproxy.io:33335",
                "username": "brd-customer-hl_caaa2374-zone-residential_proxy1",
                "password": "grh8h9v2fmib",
                "country": "us"
            },
            {
                "server": "brd.superproxy.io:33335",
                "username": "brd-customer-hl_caaa2374-zone-residential_proxy1",
                "password": "grh8h9v2fmib",
                "country": "gb"
            }
        ]
    },
    "rotation_interval": 300,
    "health_check_interval": 600,
    "captcha_solver": {
        "enabled": true,
        "default_service": "2captcha",
        "api_keys": {
            "2captcha": "your_2captcha_api_key_here"
        }
    }
}
EOL
    echo "proxy_config.json created with default values."
fi

# Create necessary directories
mkdir -p logs
mkdir -p data
mkdir -p static
mkdir -p templates

echo "Setup complete! Environment 'trend-crawler' is now active."
echo ""
echo "To use this environment in the future, run:"
echo "conda activate trend-crawler"
echo ""
echo "To run the Twitter scraper:"
echo "python twitter_x_scraper.py"
echo ""
echo "To run the web dashboard:"
echo "python web_dashboard.py"
