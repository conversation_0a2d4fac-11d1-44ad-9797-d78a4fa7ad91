#!/usr/bin/env python3
"""
Minimal test for TensorFlow lazy loading without any external dependencies.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_basic_python():
    """Test basic Python functionality."""
    logger.info("Testing basic Python functionality...")
    import time
    import numpy as np
    logger.info("✅ Basic imports work")
    return True

def test_tensorflow_import_isolation():
    """Test TensorFlow import isolation using importlib."""
    logger.info("Testing TensorFlow import isolation...")
    
    try:
        import importlib
        logger.info("Attempting to import TensorFlow using importlib...")
        
        # This should not hang because we're using dynamic import
        tf_module = importlib.import_module('tensorflow')
        logger.info("✅ TensorFlow imported successfully via importlib")
        return True
        
    except ImportError:
        logger.warning("TensorFlow not available")
        return False
    except Exception as e:
        logger.error(f"TensorFlow import failed: {e}")
        return False

def main():
    """Run minimal tests."""
    logger.info("Starting minimal TensorFlow lazy loading test...")
    
    # Test 1: Basic Python
    if not test_basic_python():
        logger.error("Basic Python test failed")
        return 1
    
    # Test 2: TensorFlow import isolation
    if not test_tensorflow_import_isolation():
        logger.error("TensorFlow import isolation failed")
        return 1
    
    logger.info("🎉 All minimal tests passed!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
