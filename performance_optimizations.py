#!/usr/bin/env python3
"""
Performance Optimizations for trend-crawler

This module implements performance optimizations for the trend-crawler
application, including mixed precision operations, gradient checkpointing,
and memory optimization.
"""

import os
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
import threading
import time
import json
import gc
# Import psutil if available
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available, using fallback methods for memory tracking")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """
    Performance optimizer for neural network operations.

    Implements mixed precision training, gradient checkpointing,
    and memory optimization for better performance.
    """

    def __init__(self,
                 model: nn.Module,
                 use_mixed_precision: bool = True,
                 use_gradient_checkpointing: bool = True,
                 memory_limit_mb: int = 500,
                 device: Optional[str] = None):
        """
        Initialize the performance optimizer.

        Args:
            model: Model to optimize
            use_mixed_precision: Whether to use mixed precision
            use_gradient_checkpointing: Whether to use gradient checkpointing
            memory_limit_mb: Memory limit in MB
            device: Device for computation
        """
        self.model = model
        self.use_mixed_precision = use_mixed_precision
        self.use_gradient_checkpointing = use_gradient_checkpointing
        self.memory_limit_mb = memory_limit_mb
        self.device = device if device else "cuda" if torch.cuda.is_available() else "cpu"

        # Initialize mixed precision scaler
        self.scaler = torch.cuda.amp.GradScaler() if use_mixed_precision and torch.cuda.is_available() else None

        # Enable gradient checkpointing if requested
        if use_gradient_checkpointing and hasattr(model, 'gradient_checkpointing_enable'):
            model.gradient_checkpointing_enable()

        # Initialize memory tracker
        self.memory_tracker = MemoryTracker(limit_mb=memory_limit_mb)

        logger.info(f"PerformanceOptimizer initialized with mixed precision: {use_mixed_precision}, "
                   f"gradient checkpointing: {use_gradient_checkpointing}, "
                   f"memory limit: {memory_limit_mb}MB, "
                   f"device: {self.device}")

    def optimize_forward_backward(self,
                                 forward_fn: Callable,
                                 inputs: Any,
                                 optimizer: torch.optim.Optimizer) -> Dict[str, Any]:
        """
        Optimize forward and backward pass.

        Args:
            forward_fn: Forward function
            inputs: Inputs to forward function
            optimizer: Optimizer

        Returns:
            Dictionary with optimization results
        """
        # Check memory before computation
        self.memory_tracker.check_memory()

        # Mixed precision forward pass
        if self.use_mixed_precision and self.scaler is not None:
            with torch.cuda.amp.autocast():
                outputs = forward_fn(inputs)

            # Mixed precision backward pass
            self.scaler.scale(outputs["loss"]).backward()

            # Update weights
            self.scaler.step(optimizer)
            self.scaler.update()
        else:
            # Standard forward pass
            outputs = forward_fn(inputs)

            # Standard backward pass
            outputs["loss"].backward()

            # Update weights
            optimizer.step()

        # Zero gradients
        optimizer.zero_grad()

        # Check memory after computation
        memory_info = self.memory_tracker.check_memory()

        return {
            **outputs,
            "memory_info": memory_info
        }

    def quantize_model(self,
                      quantization_type: str = "dynamic",
                      dtype: torch.dtype = torch.qint8) -> nn.Module:
        """
        Quantize model for inference.

        Args:
            quantization_type: Type of quantization
            dtype: Data type for quantization

        Returns:
            Quantized model
        """
        if not hasattr(torch, 'quantization'):
            logger.warning("Quantization not available in this PyTorch version")
            return self.model

        try:
            # Create a copy of the model for quantization
            model_copy = type(self.model)(*self.model.__init_args__, **self.model.__init_kwargs__)
            model_copy.load_state_dict(self.model.state_dict())

            # Prepare model for quantization
            if quantization_type == "dynamic":
                # Dynamic quantization
                quantized_model = torch.quantization.quantize_dynamic(
                    model_copy,
                    {nn.Linear},
                    dtype=dtype
                )
            elif quantization_type == "static":
                # Static quantization (requires calibration)
                model_copy.eval()
                model_copy.qconfig = torch.quantization.get_default_qconfig('fbgemm')
                torch.quantization.prepare(model_copy, inplace=True)
                # Calibration would happen here
                quantized_model = torch.quantization.convert(model_copy, inplace=False)
            else:
                logger.warning(f"Unknown quantization type: {quantization_type}")
                return self.model

            # Check model size reduction
            original_size = self._get_model_size_mb(self.model)
            quantized_size = self._get_model_size_mb(quantized_model)

            logger.info(f"Model quantized from {original_size:.2f}MB to {quantized_size:.2f}MB "
                       f"({(1 - quantized_size / original_size) * 100:.2f}% reduction)")

            return quantized_model
        except Exception as e:
            logger.error(f"Error quantizing model: {e}")
            return self.model

    def _get_model_size_mb(self, model: nn.Module) -> float:
        """
        Get model size in MB.

        Args:
            model: Model to measure

        Returns:
            Model size in MB
        """
        param_size = 0
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()

        buffer_size = 0
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()

        total_size = param_size + buffer_size
        return total_size / (1024 * 1024)  # Convert bytes to MB

    def prune_model(self,
                   amount: float = 0.3,
                   method: str = "magnitude") -> nn.Module:
        """
        Prune model parameters.

        Args:
            amount: Amount to prune
            method: Pruning method

        Returns:
            Pruned model
        """
        if not hasattr(torch, 'nn.utils.prune'):
            logger.warning("Pruning not available in this PyTorch version")
            return self.model

        try:
            # Create a copy of the model for pruning
            model_copy = type(self.model)(*self.model.__init_args__, **self.model.__init_kwargs__)
            model_copy.load_state_dict(self.model.state_dict())

            # Prune model
            for name, module in model_copy.named_modules():
                if isinstance(module, nn.Linear):
                    if method == "magnitude":
                        torch.nn.utils.prune.l1_unstructured(module, 'weight', amount=amount)
                    elif method == "random":
                        torch.nn.utils.prune.random_unstructured(module, 'weight', amount=amount)
                    else:
                        logger.warning(f"Unknown pruning method: {method}")

            # Make pruning permanent
            for name, module in model_copy.named_modules():
                if isinstance(module, nn.Linear):
                    torch.nn.utils.prune.remove(module, 'weight')

            # Check model size reduction
            original_size = self._get_model_size_mb(self.model)
            pruned_size = self._get_model_size_mb(model_copy)

            logger.info(f"Model pruned from {original_size:.2f}MB to {pruned_size:.2f}MB "
                       f"({(1 - pruned_size / original_size) * 100:.2f}% reduction)")

            return model_copy
        except Exception as e:
            logger.error(f"Error pruning model: {e}")
            return self.model


class MemoryTracker:
    """
    Tracks memory usage and enforces limits.

    Implements memory monitoring and garbage collection
    to prevent out-of-memory errors.
    """

    def __init__(self, limit_mb: int = 500, check_interval: int = 10):
        """
        Initialize the memory tracker.

        Args:
            limit_mb: Memory limit in MB
            check_interval: Check interval in seconds
        """
        self.limit_mb = limit_mb
        self.check_interval = check_interval
        self.last_check = time.time()

        # Start monitoring thread
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_memory, daemon=True)
        self.monitor_thread.start()

        logger.info(f"MemoryTracker initialized with limit: {limit_mb}MB")

    def check_memory(self) -> Dict[str, float]:
        """
        Check current memory usage.

        Returns:
            Dictionary with memory usage information
        """
        # Get CPU memory usage
        cpu_memory_mb = 0.0
        if PSUTIL_AVAILABLE:
            process = psutil.Process(os.getpid())
            cpu_memory_mb = process.memory_info().rss / (1024 * 1024)

        # Get GPU memory usage if available
        gpu_memory_mb = 0.0
        if torch.cuda.is_available():
            gpu_memory_mb = torch.cuda.memory_allocated() / (1024 * 1024)

        # Check if memory limit exceeded
        if cpu_memory_mb > self.limit_mb or gpu_memory_mb > self.limit_mb:
            logger.warning(f"Memory limit exceeded: CPU {cpu_memory_mb:.2f}MB, GPU {gpu_memory_mb:.2f}MB")
            self._reduce_memory()

        return {
            "cpu_memory_mb": cpu_memory_mb,
            "gpu_memory_mb": gpu_memory_mb,
            "limit_mb": self.limit_mb
        }

    def _reduce_memory(self):
        """Reduce memory usage."""
        # Force garbage collection
        gc.collect()

        # Clear CUDA cache if available
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        logger.info("Performed memory reduction")

    def _monitor_memory(self):
        """Monitor memory usage periodically."""
        while self.running:
            if time.time() - self.last_check > self.check_interval:
                self.check_memory()
                self.last_check = time.time()

            time.sleep(1.0)

    def stop(self):
        """Stop memory monitoring."""
        self.running = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)

        logger.info("MemoryTracker stopped")
