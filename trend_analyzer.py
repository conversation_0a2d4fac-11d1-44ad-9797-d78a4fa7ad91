#!/usr/bin/env python3
"""
TrendAnal<PERSON>zer - Analyzes trends with Twitter profile data integration.
"""

import re
import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime
import numpy as np # Added for potential vector operations
from sklearn.cluster import DBSCAN # Added for clustering example

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import dependencies
try:
    from resource_manager import ResourceManager
    RESOURCE_MANAGER_AVAILABLE = True
except ImportError:
    RESOURCE_MANAGER_AVAILABLE = False
    logger.warning("ResourceManager not available. Some features will be limited.")

try:
    from twitter_profile_scraper import TwitterProfileScraper
    PROFILE_SCRAPER_AVAILABLE = True
except ImportError:
    PROFILE_SCRAPER_AVAILABLE = False
    logger.warning("Twitter<PERSON>rofileScraper not available. Profile analysis will be limited.")

try:
    from twitter_x_scraper import TwitterXScraper
    TWITTER_X_SCRAPER_AVAILABLE = True
except ImportError:
    TWITTER_X_SCRAPER_AVAILABLE = False
    logger.warning("TwitterXScraper not available. Tweet analysis will be limited.")

try:
    from twitter_scraper_service import TwitterScraperService, calculate_coolness_score_with_twitter
    TWITTER_SCRAPER_SERVICE_AVAILABLE = True
except (ImportError, AttributeError):
    TWITTER_SCRAPER_SERVICE_AVAILABLE = False
    logger.warning("TwitterScraperService not available. Using alternative scrapers.")

# Import TradingView integration
try:
    from anti_scraping.tradingview.tradingview_integration import TradingViewIntegration
    TRADINGVIEW_INTEGRATION_AVAILABLE = True
except ImportError:
    TRADINGVIEW_INTEGRATION_AVAILABLE = False
    logger.warning("TradingView integration not available. Financial trend analysis will be limited.")

from postgres_integration import PostgresIntegration # Added
from vector_store_manager import VectorStoreManager # Added
from secure_services import SecureDistillationSystem # Added for type hint


class TrendAnalyzer:
    """
    Analyzes trends, identifies new trends from content, and integrates with nano-network services.
    """

    def __init__(self,
                 db_host: str,
                 db_port: str,
                 db_name: str,
                 db_user: str,
                 db_password: str,
                 vector_store_manager: VectorStoreManager,
                 summarization_service: SecureDistillationSystem, # Added
                 max_workers=3,
                 embedding_model_name: str = 'all-MiniLM-L6-v2'): # Though VSM handles embedding
        """
        Initialize the trend analyzer.
        """
        # self.db = db # Replaced by pg_integration
        self.pg_integration = PostgresIntegration(
            host=db_host,
            port=db_port,
            dbname=db_name,
            user=db_user,
            password=db_password
        )
        self.vector_store_manager = vector_store_manager
        self.summarization_service = summarization_service # Added
        self.max_workers = max_workers

        # Embedding model - VectorStoreManager is expected to handle actual embedding generation.
        # This analyzer might need to know the dimension if doing direct vector math.
        # For now, not initializing a separate SentenceTransformer here.
        self.embedding_dim = 384 # Assuming 'all-MiniLM-L6-v2' dim, should be configurable or fetched

        # Initialize resource manager
        if RESOURCE_MANAGER_AVAILABLE:
            self.resource_manager = ResourceManager()
        else:
            self.resource_manager = None

        # Initialize profile scraper
        if PROFILE_SCRAPER_AVAILABLE:
            try:
                # Try with simplest initialization
                self.profile_scraper = TwitterProfileScraper()
            except Exception as e:
                logger.error(f"Error initializing TwitterProfileScraper: {e}")
                self.profile_scraper = None
        else:
            self.profile_scraper = None

        # Initialize trend scraper
        if TWITTER_SCRAPER_SERVICE_AVAILABLE:
            self.trend_scraper = TwitterScraperService(
                db=self.pg_integration,
                max_workers=self.max_workers
            )
        elif TWITTER_X_SCRAPER_AVAILABLE:
            self.trend_scraper = None  # Will be initialized as async when needed
        else:
            self.trend_scraper = None

        # Initialize TradingView integration
        if TRADINGVIEW_INTEGRATION_AVAILABLE:
            try:
                self.tradingview_integration = TradingViewIntegration()
                logger.info("TradingView integration initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing TradingView integration: {e}")
                self.tradingview_integration = None
        else:
            self.tradingview_integration = None

        # Track available components
        self.profile_analysis_available = PROFILE_SCRAPER_AVAILABLE
        self.trend_analysis_available = (
            TWITTER_SCRAPER_SERVICE_AVAILABLE or TWITTER_X_SCRAPER_AVAILABLE
        )
        self.financial_analysis_available = TRADINGVIEW_INTEGRATION_AVAILABLE

        logger.info(f"TrendAnalyzer initialized with DB: {db_name}, VSM, Summarization Service.")
        logger.info(f"Profile analysis: {self.profile_analysis_available}, "
                   f"trend analysis: {self.trend_analysis_available}, "
                   f"financial analysis: {self.financial_analysis_available}")

    def add_content_batch_and_analyze(self, content_items: List[Dict[str, Any]]) -> Tuple[List[str], List[int], List[int]]:
        """
        Processes a batch of new content items: embeds, stores, clusters,
        identifies/updates trends, and summarizes new/updated trends.

        Args:
            content_items: List of content items. Each item is a dict with
                           keys like 'id', 'content_text', 'source', 'published_at', 'url'.

        Returns:
            Tuple of (processed_content_ids, new_trend_ids, updated_trend_ids)
        """
        logger.info(f"Starting to process batch of {len(content_items)} content items.")
        processed_content_ids: List[str] = []
        new_trend_ids: List[int] = []
        updated_trend_ids: List[int] = []

        if not content_items:
            return processed_content_ids, new_trend_ids, updated_trend_ids

        # 1. Embed content texts
        texts_to_embed = [item['content_text'] for item in content_items]
        try:
            embeddings = self.vector_store_manager.embed_texts(texts_to_embed)
            if len(embeddings) != len(content_items):
                logger.error("Mismatch in number of embeddings and content items. Aborting batch.")
                return [], [], []
        except Exception as e:
            logger.error(f"Failed to embed texts: {e}", exc_info=True)
            return [], [], []

        # Add embeddings to content items
        for i, item in enumerate(content_items):
            item['embedding'] = embeddings[i]

        # 2. Store content items with their embeddings
        try:
            # Assuming add_content_item_batch takes list of dicts and handles DB insertion
            # If not, loop and call add_content_item individually
            # For now, let's assume a way to add them and get their DB IDs if not already present.
            # The 'id' in content_items is assumed to be the unique external ID.
            # VectorStoreManager.add_content_item should handle mapping this to internal IDs.

            # This part is simplified. In reality, we'd check if content exists,
            # then add/update. VectorStoreManager should manage this.
            stored_item_details = self.vector_store_manager.add_content_item_batch(content_items)
            # stored_item_details could be a list of internal DB IDs or confirm existing ones.
            processed_content_ids = [item['id'] for item in content_items] # Assuming all are processed
            logger.info(f"Stored/updated {len(processed_content_ids)} content items.")

        except Exception as e:
            logger.error(f"Failed to store content items: {e}", exc_info=True)
            return processed_content_ids, new_trend_ids, updated_trend_ids # Return what's processed so far

        # 3. Clustering and Trend Identification (Simplified Placeholder)
        # This is a complex step. A real implementation would involve:
        # - Fetching recent embeddings (including newly added ones) that are not yet part of a strong trend.
        # - Performing clustering (e.g., DBSCAN, K-Means, or more advanced methods).
        # - Matching clusters to existing trends or creating new ones.
        # - Updating trend_content_map and trend centroids.

        logger.info("Performing clustering and trend identification (simplified placeholder)...")
        try:
            # Example: Fetch all embeddings from the last N days or a certain count
            # For simplicity, let's assume we operate on the current batch + some recent context
            # This is highly dependent on how VectorStoreManager provides data for clustering

            # --- Placeholder for actual clustering logic ---
            # This would typically involve:
            # 1. Fetching candidate embeddings from vector_store_manager
            #    (e.g., recent items, items not strongly clustered)
            # 2. Running a clustering algorithm (e.g., DBSCAN)
            #    dbscan = DBSCAN(eps=0.5, min_samples=5, metric='cosine') # Example params
            #    labels = dbscan.fit_predict(np.array(all_embeddings_for_clustering))
            # 3. Interpreting labels to identify new trends or assign items to existing ones.
            #    - For each cluster (label != -1):
            #        - If it's a new coherent group -> create new trend in `trends` table.
            #          (self.vector_store_manager.create_trend(name, initial_embedding, description))
            #          Add to new_trend_ids.
            #        - If it maps to an existing trend -> update trend.
            #          (self.vector_store_manager.update_trend_centroid(trend_id, new_centroid))
            #          Add to updated_trend_ids.
            #        - Update `trend_content_map` for all items in the cluster.

            # For this placeholder, let's imagine one new trend and one updated trend from the batch
            # This part needs to be properly implemented based on the project's clustering strategy.
            if content_items:
                # Simulate: if batch is large enough, form a new trend
                if len(content_items) > 2: # Arbitrary condition
                    example_new_trend_name = f"New Trend from batch {datetime.now().strftime('%Y%m%d%H%M%S')}"
                    # Use average of first few embeddings as initial
                    initial_emb = np.mean(np.array([item['embedding'] for item in content_items[:3]]), axis=0).tolist()

                    # Create trend via VectorStoreManager or pg_integration
                    # trend_id = self.vector_store_manager.create_trend(name=example_new_trend_name, initial_embedding=initial_emb)
                    # For now, using pg_integration directly as VSM might not have create_trend
                    insert_query = """
                        INSERT INTO trends (name, initial_embedding, current_centroid, updated_at)
                        VALUES (%s, %s, %s, %s) RETURNING id;
                    """
                    trend_id = self.pg_integration.execute_insert_returning_id(
                        insert_query,
                        (example_new_trend_name, initial_emb, initial_emb, datetime.now())
                    )
                    if trend_id:
                        new_trend_ids.append(trend_id)
                        logger.info(f"Placeholder: Created new trend ID {trend_id}")
                        # Associate content with this trend (placeholder)
                        # for item_detail in stored_item_details:
                        #    self.vector_store_manager.map_content_to_trend(item_detail['internal_id'], trend_id)

                # Simulate: an existing trend gets updated (e.g., trend_id = 1)
                # This would involve finding an existing trend that current items belong to.
                # existing_trend_to_update = self.vector_store_manager.get_some_existing_trend_id()
                # if existing_trend_to_update:
                #    updated_trend_ids.append(existing_trend_to_update)
                #    self.vector_store_manager.update_trend_centroid(existing_trend_to_update, new_calculated_centroid)
                #    logger.info(f"Placeholder: Updated trend ID {existing_trend_to_update}")
            # --- End Placeholder ---
            logger.info(f"Clustering placeholder complete. New trends: {new_trend_ids}, Updated trends: {updated_trend_ids}")

        except Exception as e:
            logger.error(f"Error during clustering/trend identification: {e}", exc_info=True)
            # Continue to summarization if some trends were identified before error

        # 4. Trend Summarization (F.1) for new or significantly updated trends
        trends_to_summarize = list(set(new_trend_ids + updated_trend_ids)) # Summarize both new and updated
        if trends_to_summarize:
            logger.info(f"Starting summarization for trends: {trends_to_summarize}")
        for trend_id in trends_to_summarize:
            try:
                # Fetch top K content texts for this trend
                # VectorStoreManager needs a method like get_content_texts_for_trend
                # For now, using a placeholder or assuming it can get texts.
                # If using current batch, filter items associated with this trend_id.
                # This is simplified as clustering logic is a placeholder.

                # Placeholder: get some texts associated with this trend_id
                # In a real scenario, query DB for texts belonging to trend_id
                # texts_for_summary = self.vector_store_manager.get_top_k_texts_for_trend(trend_id, k=5)

                # Simplified: use texts from the current batch if they were part of this trend
                # This requires the clustering step to have mapped items to trends.
                # For this placeholder, let's just use a few from the input batch if it's a new trend.
                sample_texts_for_summary = []
                if trend_id in new_trend_ids and content_items: # If it's a new trend from this batch
                    sample_texts_for_summary = [item['content_text'] for item in content_items[:min(5, len(content_items))]]
                elif content_items: # For updated trends, also take some from current batch as example
                     sample_texts_for_summary = [item['content_text'] for item in content_items[:min(3, len(content_items))]]


                if sample_texts_for_summary:
                    self._summarize_trend_content(trend_id, sample_texts_for_summary)
                else:
                    logger.warning(f"No sample texts found to summarize for trend ID {trend_id} in this batch.")
            except Exception as e:
                logger.error(f"Error triggering summarization for trend ID {trend_id}: {e}", exc_info=True)

        logger.info(f"Batch processing complete. Processed IDs: {len(processed_content_ids)}, New Trends: {new_trend_ids}, Updated Trends: {updated_trend_ids}")
        return processed_content_ids, new_trend_ids, updated_trend_ids

    def _summarize_trend_content(self, trend_id: int, top_k_content_texts: List[str]) -> None:
        """
        Generates a summary for a trend using NanoLLM via SecureDistillationSystem
        and stores it in the trends.description field. (F.1)
        """
        if not self.summarization_service:
            logger.warning("Summarization service not available. Skipping trend summarization.")
            return
        if not top_k_content_texts:
            logger.warning(f"No content texts provided for summarizing trend ID: {trend_id}")
            return

        # Combine top K texts into a single document for summarization
        combined_text_for_summary = "\n\n--- Next Item ---\n\n".join(top_k_content_texts)

        # Ensure the combined text is not excessively long
        max_summary_input_length = 4000 # Should be configurable
        if len(combined_text_for_summary) > max_summary_input_length:
            logger.warning(f"Combined text for trend {trend_id} too long ({len(combined_text_for_summary)} chars). Truncating to {max_summary_input_length}.")
            combined_text_for_summary = combined_text_for_summary[:max_summary_input_length]

        logger.info(f"Requesting summary for trend ID: {trend_id} with combined text length: {len(combined_text_for_summary)}")

        try:
            # summarize_with_nano_llm expects a list of texts. We pass a single combined text in a list.
            summaries = self.summarization_service.summarize_with_nano_llm(
                [combined_text_for_summary],
                prompt_template="Summarize the key information and common themes from the following text items related to an emerging trend: {text}"
            )
            if summaries and summaries[0]: # Check if a summary was returned
                trend_description = summaries[0].strip()
                if not trend_description: # Check if summary is not just whitespace
                    logger.warning(f"Generated summary for trend ID {trend_id} is empty.")
                    return

                logger.info(f"Generated summary for trend ID {trend_id}: {trend_description[:150]}...")

                update_query = "UPDATE trends SET description = %s, updated_at = %s WHERE id = %s"
                self.pg_integration.execute_update(update_query, (trend_description, datetime.now(), trend_id))
                logger.info(f"Stored summary for trend ID {trend_id} in database.")
            else:
                logger.warning(f"Failed to generate a valid summary for trend ID: {trend_id}. Summaries list: {summaries}")
        except Exception as e:
            logger.error(f"Error during trend summarization for trend ID {trend_id}: {e}", exc_info=True)

    def identify_sleeper_trends(self, min_cohesion_score: float = 0.7, max_current_volume: int = 50, min_avg_growth_rate: float = 0.01) -> List[int]:
        """
        Identifies "sleeper" trends: small, cohesive semantic cores with slow initial traction. (F.3)
        This is a placeholder for more detailed logic.
        """
        logger.info("Identifying sleeper trends (placeholder implementation)...")
        sleeper_trend_ids: List[int] = []
        try:
            # Placeholder Logic:
            # 1. Fetch all active trends with their creation date, item count, and embeddings of content.
            #    trends_data = self.vector_store_manager.get_all_trends_with_details()
            #    (This method would need to be created in VectorStoreManager)
            #
            # 2. For each trend:
            #    a. Calculate semantic core cohesion:
            #       - Get all embeddings for content items in the trend.
            #       - If few items, cohesion is high by default or skip.
            #       - Calculate average pairwise cosine similarity among items. Or variance from centroid.
            #       - cohesion_score = calculate_cohesion(trend_embeddings)
            #    b. Calculate current volume (item_count).
            #    c. Calculate growth rate:
            #       - (item_count / age_in_days). This is a simple rate.
            #       - More advanced: look at item addition velocity over time.
            #
            # 3. Filter:
            #    if cohesion_score >= min_cohesion_score and \\
            #       item_count <= max_current_volume and \\
            #       growth_rate >= min_avg_growth_rate and growth_rate < some_upper_threshold_to_exclude_fast_trends:
            #        sleeper_trend_ids.append(trend['id'])

            # Example: Query trends that are not too old, have few items.
            query = """
                SELECT t.id, COUNT(tc.content_id) as item_count, t.created_at
                FROM trends t
                JOIN trend_content_map tc ON t.id = tc.trend_id
                WHERE t.created_at > NOW() - INTERVAL '90 days' -- Consider trends from last 90 days
                GROUP BY t.id, t.created_at
                HAVING COUNT(tc.content_id) <= %s
                ORDER BY item_count ASC;
            """
            candidate_trends = self.pg_integration.execute_query_fetchall(query, (max_current_volume,))

            if candidate_trends:
                for trend_row in candidate_trends:
                    trend_id, item_count, created_at = trend_row
                    # Further checks for cohesion and growth rate would go here.
                    # For this placeholder, let's assume some pass.
                    if item_count > 5: # Need some items to be a "trend"
                         # Simple growth: if it's not too new and has some items.
                        age_days = (datetime.now() - created_at).days + 1
                        growth_rate = item_count / age_days
                        if growth_rate >= min_avg_growth_rate and growth_rate < 0.5 : # slow but steady
                            # Cohesion check would be complex, involving fetching embeddings.
                            # Placeholder: Assume it passes cohesion for now.
                            logger.info(f"Potential sleeper trend: ID {trend_id}, Items: {item_count}, Age: {age_days}d, Growth: {growth_rate:.2f}/day")
                            sleeper_trend_ids.append(trend_id)

            if sleeper_trend_ids:
                logger.info(f"Identified potential sleeper trends: {sleeper_trend_ids}")
            else:
                logger.info("No sleeper trends identified with current placeholder logic.")

        except Exception as e:
            logger.error(f"Error identifying sleeper trends: {e}", exc_info=True)
        return sleeper_trend_ids[:5] # Limit for now

    def check_semantic_drift(self, trend_id: int, time_window_days: int = 7, comparison_window_days: int = 30, threshold: float = 0.2) -> Optional[Dict[str, Any]]:
        """
        Detects semantic shifts in an ongoing trend by monitoring its embedding centroid. (F.4)
        Compares centroid of recent items (last `time_window_days`) vs. older items (from `comparison_window_days` ago).
        This is a placeholder for more detailed logic.
        """
        logger.info(f"Detecting semantic shifts for trend ID: {trend_id} (placeholder implementation)...")
        try:
            # 1. Get current centroid of the trend (or centroid of items in last `time_window_days`)
            #    current_centroid_data = self.vector_store_manager.get_trend_centroid_for_period(trend_id, days_ago_start=time_window_days, days_ago_end=0)
            #    This method needs to exist in VSM: calculate centroid from items in a period.
            #    For now, let's use the stored current_centroid if available.
            current_trend_info = self.vector_store_manager.get_trend_by_id(trend_id)
            if not current_trend_info or 'current_centroid' not in current_trend_info or not current_trend_info['current_centroid']:
                logger.warning(f"No current centroid found for trend {trend_id} to check drift.")
                return None
            current_centroid = np.array(current_trend_info['current_centroid'])

            # 2. Get centroid of items from an older period for comparison
            #    (e.g., items between `comparison_window_days` and `comparison_window_days - time_window_days` ago)
            #    historical_centroid_data = self.vector_store_manager.get_trend_centroid_for_period(
            #        trend_id, days_ago_start=comparison_window_days, days_ago_end=comparison_window_days - time_window_days
            #    )
            #    This is a placeholder for fetching/calculating historical centroid.
            #    A simpler approach: compare current_centroid with initial_embedding if trend is mature.

            # Placeholder: Fetch embeddings of items older than `time_window_days` for this trend
            query_old_embeddings = """
                SELECT ci.embedding FROM crawled_content ci
                JOIN trend_content_map tcm ON ci.id = tcm.content_id
                WHERE tcm.trend_id = %s AND ci.published_at < NOW() - INTERVAL '%s days'
                LIMIT 100;
            """ # Limit for performance
            old_embeddings_rows = self.pg_integration.execute_query_fetchall(query_old_embeddings, (trend_id, time_window_days))

            if not old_embeddings_rows or len(old_embeddings_rows) < 5: # Need enough old items
                logger.info(f"Not enough historical data for trend {trend_id} to check drift.")
                return None

            old_embeddings = np.array([row[0] for row in old_embeddings_rows])
            historical_centroid = np.mean(old_embeddings, axis=0)

            # 3. Compare centroids
            # Cosine distance = 1 - cosine similarity
            similarity = np.dot(current_centroid, historical_centroid) / (np.linalg.norm(current_centroid) * np.linalg.norm(historical_centroid))
            distance = 1 - similarity

            logger.info(f"Trend {trend_id}: Semantic distance between current and historical centroid ({time_window_days}d old): {distance:.4f}")

            if distance > threshold:
                shift_details = {
                    "trend_id": trend_id,
                    "drift_score": distance,
                    "threshold": threshold,
                    "message": f"Significant semantic drift detected (score: {distance:.4f})."
                }
                logger.info(shift_details["message"])
                # Store this event?
                # self.vector_store_manager.log_semantic_shift_event(trend_id, distance, shift_details)
                return shift_details
            else:
                logger.info(f"No significant semantic drift detected for trend {trend_id}.")

        except Exception as e:
            logger.error(f"Error checking semantic drift for trend {trend_id}: {e}", exc_info=True)
        return None

    def analyze_trend(self, trend_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced analysis with profile data.

        Args:
            trend_data: Trend data to analyze

        Returns:
            Enhanced trend data with analysis
        """
        # Make a copy to avoid modifying the original
        result = trend_data.copy()

        # Get text content
        text = trend_data.get('text', '')

        # Get keywords
        keywords = trend_data.get('keywords', [])
        if not keywords and 'category' in trend_data:
            # Extract keywords from category
            category = trend_data['category']
            if category and category.startswith('category_'):
                parts = category.split('_')
                if len(parts) >= 3:
                    keywords = [parts[2]]  # Use the keyword part

        # Get base coolness score
        base_score = trend_data.get('coolness_score', 0.5)

        # Analyze tweets if trend scraper is available
        tweet_metrics = None
        if self.trend_analysis_available:
            tweet_metrics = self._get_tweet_metrics(keywords)

            # Update base score with tweet metrics
            if tweet_metrics:
                if TWITTER_SCRAPER_SERVICE_AVAILABLE:
                    base_score = calculate_coolness_score_with_twitter(base_score, tweet_metrics)
                else:
                    # Simple calculation if the full function is not available
                    volume_boost = 0.2 * min(1, tweet_metrics.get('volume', 0) / 1000)
                    engagement_boost = 0.15 * min(1, tweet_metrics.get('avg_likes', 0) / 500)
                    virality_boost = 0.1 * min(1, tweet_metrics.get('avg_retweets', 0) / 200)
                    sentiment_boost = 0.05 * tweet_metrics.get('avg_sentiment', 0)
                    base_score += volume_boost + engagement_boost + virality_boost + sentiment_boost
                    base_score = min(base_score, 1.0)

        # Get financial data if available
        financial_data = None
        financial_score = 0.0
        if self.financial_analysis_available and keywords:
            financial_data, financial_score = self._get_financial_metrics(keywords)

            if financial_data:
                # Update base score with financial data (max 25% boost)
                if financial_score > 0:
                    base_score = min(base_score * (1 + 0.25 * financial_score), 1.0)
                elif financial_score < 0:
                    base_score = max(base_score * (1 + 0.25 * financial_score), 0.1)

                # Store financial data in result
                result['financial_data'] = financial_data

        # Extract mentioned users
        mentioned_users = self._extract_mentioned_users(text)

        # Calculate influencer impact if profile scraper is available
        influencer_score = 0.0
        influencer_profiles = []
        if self.profile_analysis_available and mentioned_users:
            influencer_score, influencer_profiles = self._calculate_influencer_impact(mentioned_users)

            # Store profiles in result
            result['influencer_profiles'] = influencer_profiles

        # Combine scores
        final_score = min(base_score * (1 + influencer_score), 1.0)

        # Update result
        result['coolness_score'] = final_score
        result['influencers'] = mentioned_users
        result['influencer_score'] = influencer_score

        # Add financial score if available
        if financial_data:
            result['financial_score'] = financial_score

        # Add tweet metrics if available
        if tweet_metrics:
            result['twitter_metrics'] = tweet_metrics

        # Save to database if available
        if self.pg_integration and 'url' in result:
            self._save_analysis_to_db(result)

        return result

    def _extract_mentioned_users(self, text: str) -> List[str]:
        """
        Find @mentions in text.

        Args:
            text: Text to analyze

        Returns:
            List of mentioned usernames
        """
        # Find all @mentions
        mentions = re.findall(r'@(\w+)', text)

        # Remove duplicates while preserving order
        unique_mentions = []
        seen = set()
        for mention in mentions:
            if mention.lower() not in seen:
                unique_mentions.append(mention)
                seen.add(mention.lower())

        return unique_mentions

    def _calculate_influencer_impact(self, usernames: List[str]) -> Tuple[float, List[Dict[str, Any]]]:
        """
        Calculate influencer impact on trend score.

        Args:
            usernames: List of usernames to analyze

        Returns:
            Tuple of (impact_score, profile_data_list)
        """
        if not self.profile_scraper or not usernames:
            return 0.0, []

        total_influence = 0.0
        profiles = []

        # Limit to top 3 mentions to avoid excessive scraping
        for username in usernames[:3]:
            try:
                # Try different scraping methods
                if hasattr(self.profile_scraper, 'scrape_profile'):
                    profile = self.profile_scraper.scrape_profile(username)
                elif hasattr(self.profile_scraper, 'get_profile'):
                    profile = self.profile_scraper.get_profile(username)
                elif hasattr(self.profile_scraper, 'scrape'):
                    profile = self.profile_scraper.scrape(username)
                else:
                    logger.warning(f"No suitable method found to scrape profile for {username}")
                    continue

                if profile and profile.get('exists', False):
                    influence = profile.get('influence_score', 0)
                    total_influence += influence
                    profiles.append(profile)

                    # Save profile to database if available
                    if self.pg_integration and hasattr(self.profile_scraper, 'save_to_db'):
                        self.profile_scraper.save_to_db(profile)
            except Exception as e:
                logger.error(f"Error scraping profile for {username}: {e}")

        # Calculate impact score (max 30% boost)
        impact_score = min(total_influence * 0.1, 0.3)

        return impact_score, profiles

    def _get_financial_metrics(self, keywords: List[str]) -> Tuple[Optional[Dict[str, Any]], float]:
        """
        Get financial metrics from TradingView for keywords.

        Args:
            keywords: List of keywords to analyze

        Returns:
            Tuple of (financial_data, financial_score) or (None, 0.0)
        """
        if not self.financial_analysis_available or not self.tradingview_integration:
            return None, 0.0

        if not keywords:
            return None, 0.0

        try:
            # Extract potential financial symbols from keywords
            symbols = []
            for keyword in keywords:
                # Check if keyword contains common financial symbols
                if any(market in keyword.upper() for market in ['BTC', 'ETH', 'USD', 'EUR', 'JPY', 'GBP', 'NASDAQ', 'DOW', 'S&P']):
                    symbols.append(keyword.upper())
                # Check for stock ticker patterns (1-5 uppercase letters)
                elif re.match(r'^[A-Z]{1,5}$', keyword):
                    symbols.append(keyword.upper())

            if not symbols:
                # Try to use keywords as-is if no symbols found
                symbols = [keyword.upper() for keyword in keywords[:2]]  # Limit to first 2 keywords

            # Use thread-based approach to avoid event loop conflicts
            import threading
            import queue

            result_queue = queue.Queue()

            def run_async_in_thread():
                try:
                    # Create new event loop for this thread
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Get financial data
                    financial_data = {}

                    for symbol in symbols[:2]:  # Limit to 2 symbols
                        try:
                            # Try different exchanges for the symbol
                            exchanges = ["BINANCE", "NASDAQ", "NYSE"]
                            for exchange in exchanges:
                                symbol_data = loop.run_until_complete(
                                    self.tradingview_integration.get_symbol_data(symbol, exchange)
                                )

                                if "error" not in symbol_data:
                                    # Calculate trend score
                                    trend_score = self.tradingview_integration.calculate_trend_score(symbol_data)
                                    symbol_data["trend_score"] = trend_score

                                    financial_data[f"{exchange}:{symbol}"] = symbol_data
                                    break  # Stop trying other exchanges if we found data
                        except Exception as e:
                            logger.error(f"Error getting financial data for {symbol}: {e}")

                    # Calculate overall financial score
                    financial_score = 0.0
                    if financial_data:
                        scores = [data.get("trend_score", 0) for data in financial_data.values()]
                        if scores:
                            financial_score = sum(scores) / len(scores)

                    # Put result in queue
                    result_queue.put((financial_data, financial_score))

                except Exception as e:
                    logger.error(f"Thread error getting financial metrics: {e}")
                    result_queue.put((None, 0.0))
                finally:
                    loop.close()

            # Start thread and wait for result
            thread = threading.Thread(target=run_async_in_thread)
            thread.start()
            thread.join(timeout=60)  # Wait up to 60 seconds

            # Get result from queue if available
            if not result_queue.empty():
                return result_queue.get()
            else:
                logger.error("Timed out waiting for financial metrics")

        except Exception as e:
            logger.error(f"Error getting financial metrics: {e}")

        return None, 0.0

    def _get_tweet_metrics(self, keywords: List[str]) -> Optional[Dict[str, Any]]:
        """
        Get tweet metrics for keywords.

        Args:
            keywords: List of keywords to analyze

        Returns:
            Dictionary of tweet metrics or None
        """
        if not keywords:
            return None

        # Use first keyword
        keyword = keywords[0]

        # Use TwitterScraperService if available
        if TWITTER_SCRAPER_SERVICE_AVAILABLE and self.trend_scraper and self.trend_scraper.available:
            metrics = self.trend_scraper.get_trend_metrics([keyword])
            if metrics and keyword in metrics:
                return metrics[keyword]

        # Use TwitterXScraper as fallback
        elif TWITTER_X_SCRAPER_AVAILABLE:
            try:
                # Use thread-based approach to avoid event loop conflicts
                import threading
                import queue

                result_queue = queue.Queue()

                def run_async_in_thread():
                    try:
                        # Create new event loop for this thread
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        # Run the async function
                        metrics = loop.run_until_complete(self._get_tweet_metrics_async(keyword))

                        # Put result in queue
                        result_queue.put(metrics)
                    except Exception as e:
                        logger.error(f"Thread error getting tweet metrics: {e}")
                        result_queue.put(None)
                    finally:
                        loop.close()

                # Start thread and wait for result
                thread = threading.Thread(target=run_async_in_thread)
                thread.start()
                thread.join(timeout=30)  # Wait up to 30 seconds

                # Get result from queue if available
                if not result_queue.empty():
                    return result_queue.get()
                else:
                    logger.error("Timed out waiting for tweet metrics")
            except Exception as e:
                logger.error(f"Error getting tweet metrics: {e}")

        return None

    async def _get_tweet_metrics_async(self, keyword: str) -> Optional[Dict[str, Any]]:
        """
        Get tweet metrics asynchronously using TwitterXScraper.

        Args:
            keyword: Keyword to analyze

        Returns:
            Dictionary of tweet metrics or None
        """
        try:
            async with TwitterXScraper() as scraper:
                try:
                    # Scrape tweets
                    tweets = await scraper.scrape_tweets(keyword, limit=50)

                    if not tweets:
                        return None

                    # Calculate metrics
                    volume = len(tweets)
                    likes = [t.get('likes', 0) for t in tweets]
                    retweets = [t.get('retweets', 0) for t in tweets]
                    replies = [t.get('replies', 0) for t in tweets]
                    sentiment = [t.get('sentiment_score', 0) for t in tweets]

                    # Calculate averages
                    avg_likes = sum(likes) / volume if volume > 0 else 0
                    avg_retweets = sum(retweets) / volume if volume > 0 else 0
                    avg_replies = sum(replies) / volume if volume > 0 else 0
                    avg_sentiment = sum(sentiment) / volume if volume > 0 else 0

                    # Get top tweet
                    if volume > 0:
                        top_tweet_idx = likes.index(max(likes))
                        top_tweet = tweets[top_tweet_idx]
                    else:
                        top_tweet = {}

                    return {
                        'volume': volume,
                        'avg_likes': avg_likes,
                        'avg_retweets': avg_retweets,
                        'avg_replies': avg_replies,
                        'avg_sentiment': avg_sentiment,
                        'top_tweet': top_tweet
                    }
                except Exception as e:
                    logger.error(f"Error scraping tweets for {keyword}: {e}")
                    return None
        except Exception as e:
            logger.error(f"Error initializing TwitterXScraper: {e}")
            return None