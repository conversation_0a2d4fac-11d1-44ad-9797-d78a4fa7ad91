#!/usr/bin/env python3
"""
Test script to verify proxy integration with trend_crawler.py
"""

import sys
import os
import logging
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_proxy_integration():
    """Test that the proxy manager is properly integrated with crawl_website function."""
    
    print("Testing proxy integration with trend_crawler...")
    
    try:
        # Import the modules
        from trend_crawler import crawl_website, PROXY_MANAGER_AVAILABLE
        from proxy_manager import Proxy<PERSON>anager
        
        print(f"Proxy manager available: {PROXY_MANAGER_AVAILABLE}")
        
        if not PROXY_MANAGER_AVAILABLE:
            print("❌ Proxy manager not available - skipping proxy integration test")
            return False
        
        # Create a mock proxy manager
        mock_proxy_manager = Mock(spec=ProxyManager)
        mock_proxy_config = {
            'id': 'test_proxy_1',
            'host': '127.0.0.1',
            'port': 8080,
            'username': 'testuser',
            'password': 'testpass'
        }
        mock_proxy_manager.get_proxy.return_value = mock_proxy_config
        mock_proxy_manager.update_proxy_stats = Mock()
        
        # Mock the requests.get call
        with patch('trend_crawler.requests.get') as mock_get:
            # Set up the mock response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '<html><body><h1>Test Content</h1><p>This is test content for proxy testing.</p></body></html>'
            mock_get.return_value = mock_response
            
            # Test the crawl_website function with proxy manager
            result = crawl_website('https://example.com', mock_proxy_manager)
            
            # Verify the result
            if result and 'Test Content' in result:
                print("✅ crawl_website successfully returned content")
            else:
                print("❌ crawl_website did not return expected content")
                return False
            
            # Verify that get_proxy was called
            if mock_proxy_manager.get_proxy.called:
                print("✅ Proxy manager get_proxy() was called")
            else:
                print("❌ Proxy manager get_proxy() was not called")
                return False
            
            # Verify that requests.get was called with proxy configuration
            if mock_get.called:
                call_args = mock_get.call_args
                proxies = call_args[1].get('proxies')
                if proxies:
                    print("✅ requests.get was called with proxy configuration")
                    print(f"   Proxy config: {proxies}")
                else:
                    print("❌ requests.get was not called with proxy configuration")
                    return False
            else:
                print("❌ requests.get was not called")
                return False
            
            # Verify that update_proxy_stats was called
            if mock_proxy_manager.update_proxy_stats.called:
                print("✅ Proxy manager update_proxy_stats() was called")
                call_args = mock_proxy_manager.update_proxy_stats.call_args
                print(f"   Stats update args: {call_args}")
            else:
                print("❌ Proxy manager update_proxy_stats() was not called")
                return False
        
        print("✅ All proxy integration tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_manager_initialization():
    """Test that the proxy manager can be initialized."""
    
    print("\nTesting proxy manager initialization...")
    
    try:
        from proxy_manager import ProxyManager
        
        # Try to create a proxy manager instance
        # This might fail if configuration is missing, which is expected
        try:
            proxy_manager = ProxyManager()
            print("✅ Proxy manager initialized successfully")
            
            # Test getting a proxy (might return None if no proxies configured)
            proxy = proxy_manager.get_proxy()
            if proxy:
                print(f"✅ Got proxy: {proxy.get('id', 'unknown')}")
            else:
                print("ℹ️  No proxies available (this is expected if no proxies are configured)")
            
            return True
            
        except Exception as e:
            print(f"ℹ️  Proxy manager initialization failed (expected if no config): {e}")
            return True  # This is expected behavior
            
    except ImportError as e:
        print(f"❌ Could not import ProxyManager: {e}")
        return False

def test_crawl_website_without_proxy():
    """Test that crawl_website works without a proxy manager."""
    
    print("\nTesting crawl_website without proxy manager...")
    
    try:
        from trend_crawler import crawl_website
        
        # Mock the requests.get call
        with patch('trend_crawler.requests.get') as mock_get:
            # Set up the mock response
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '<html><body><h1>Test Content</h1><p>This is test content without proxy.</p></body></html>'
            mock_get.return_value = mock_response
            
            # Test the crawl_website function without proxy manager
            result = crawl_website('https://example.com', None)
            
            # Verify the result
            if result and 'Test Content' in result:
                print("✅ crawl_website works without proxy manager")
            else:
                print("❌ crawl_website failed without proxy manager")
                return False
            
            # Verify that requests.get was called without proxy configuration
            if mock_get.called:
                call_args = mock_get.call_args
                proxies = call_args[1].get('proxies')
                if proxies is None:
                    print("✅ requests.get was called without proxy configuration")
                else:
                    print("❌ requests.get was called with unexpected proxy configuration")
                    return False
            else:
                print("❌ requests.get was not called")
                return False
        
        print("✅ crawl_website without proxy test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    
    print("=" * 60)
    print("PROXY INTEGRATION TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_proxy_manager_initialization,
        test_crawl_website_without_proxy,
        test_proxy_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Proxy integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
