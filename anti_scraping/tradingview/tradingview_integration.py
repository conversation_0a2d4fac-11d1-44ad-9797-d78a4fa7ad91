"""
TradingView integration module with enhanced anti-scraping capabilities.
Connects TradingView scrapers to the main trend analysis pipeline.
"""
import asyncio
import logging
import json
from typing import Dict, Any, List, Optional, Union
import os
import time
from datetime import datetime, timedelta

# Import our enhanced scrapers with anti-scraping capabilities
from .chart_scraper import Enhanced<PERSON>hart<PERSON>craper
from .ideas_scraper import EnhancedIdeasScraper
from .technicals_scraper import EnhancedTechnicalsScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TradingViewIntegration:
    """
    Integrates TradingView scrapers with the trend analysis pipeline.
    Provides coordinated access to TradingView data sources with enhanced anti-detection.
    """
    
    def __init__(self, cache_dir: str = "./data_cache"):
        """
        Initialize the TradingView integration.
        
        Args:
            cache_dir: Directory to cache scraped data
        """
        self.chart_scraper = EnhancedChartScraper()
        self.ideas_scraper = EnhancedIdeasScraper()
        self.technicals_scraper = EnhancedTechnicalsScraper()
        
        # Create cache directory if it doesn't exist
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(os.path.join(cache_dir, "charts"), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, "ideas"), exist_ok=True)
        os.makedirs(os.path.join(cache_dir, "technicals"), exist_ok=True)
        
    async def get_symbol_data(self, symbol: str, exchange: str = "BINANCE") -> Dict[str, Any]:
        """
        Get comprehensive data for a symbol from TradingView.
        Includes chart data, trading ideas, and technical indicators.
        
        Args:
            symbol: Trading symbol (e.g., "BTCUSD")
            exchange: Exchange name
            
        Returns:
            Dictionary with combined data from all sources
        """
        tasks = []
        
        # Check cache first
        cached_data = self._get_from_cache(symbol, exchange)
        if cached_data:
            logger.info(f"Using cached data for {symbol} on {exchange}")
            return cached_data
            
        try:
            # Set up tasks for parallel execution
            chart_url = f"https://www.tradingview.com/chart/?symbol={exchange}:{symbol}"
            
            # Create tasks
            tasks.append(self.chart_scraper.get_chart_data(chart_url))
            tasks.append(self.technicals_scraper.get_technicals(symbol, "crypto", exchange))
            tasks.append(self.ideas_scraper.scrape(symbol, 1, 2))  # Fetch 2 pages of ideas
            
            # Execute all tasks in parallel
            chart_data, technical_data, ideas_data = await asyncio.gather(*tasks)
            
            # Process ideas sentiment
            sentiment_data = self.ideas_scraper.get_sentiment_analysis(ideas_data)
            
            # Combine all data sources
            combined_data = {
                "symbol": symbol,
                "exchange": exchange,
                "timestamp": datetime.now().isoformat(),
                "chart_data": chart_data,
                "technical_indicators": technical_data,
                "trading_ideas": {
                    "ideas": ideas_data[:10],  # Limit to top 10 ideas
                    "sentiment": sentiment_data,
                    "total_count": len(ideas_data)
                }
            }
            
            # Cache the data
            self._save_to_cache(combined_data, symbol, exchange)
            
            return combined_data
            
        except Exception as e:
            logger.error(f"Error getting symbol data: {e}")
            return {
                "symbol": symbol,
                "exchange": exchange,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        finally:
            # Clean up resources
            await self.cleanup()
            
    async def get_bulk_symbols_data(self, symbols: List[str], exchange: str = "BINANCE") -> Dict[str, Dict[str, Any]]:
        """
        Get data for multiple symbols in an efficient manner with anti-detection measures.
        
        Args:
            symbols: List of symbols
            exchange: Exchange name
            
        Returns:
            Dictionary mapping symbols to their data
        """
        result = {}
        
        # Process in small batches with delays to avoid rate limiting
        batch_size = 3
        symbol_batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
        
        for batch in symbol_batches:
            batch_tasks = []
            
            for symbol in batch:
                # Add jitter to avoid detection
                await asyncio.sleep(random.uniform(0.5, 2.0))
                batch_tasks.append(self.get_symbol_data(symbol, exchange))
                
            # Wait for all tasks in this batch
            batch_results = await asyncio.gather(*batch_tasks)
            
            # Map results to symbols
            for symbol, data in zip(batch, batch_results):
                result[symbol] = data
                
            # Add delay between batches
            if len(symbol_batches) > 1:
                await asyncio.sleep(random.uniform(3.0, 8.0))
                
        return result
        
    def _get_from_cache(self, symbol: str, exchange: str) -> Optional[Dict[str, Any]]:
        """Get data from cache if it exists and is recent."""
        cache_file = os.path.join(self.cache_dir, f"{exchange}_{symbol}.json")
        
        if not os.path.exists(cache_file):
            return None
            
        # Check if cache is recent (less than 1 hour old)
        file_age = time.time() - os.path.getmtime(cache_file)
        if file_age > 3600:  # 1 hour in seconds
            return None
            
        try:
            with open(cache_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error reading cache: {e}")
            return None
            
    def _save_to_cache(self, data: Dict[str, Any], symbol: str, exchange: str) -> None:
        """Save data to cache."""
        cache_file = os.path.join(self.cache_dir, f"{exchange}_{symbol}.json")
        
        try:
            with open(cache_file, 'w') as f:
                json.dump(data, f)
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")
            
    async def cleanup(self) -> None:
        """Clean up resources."""
        cleanup_tasks = [
            self.chart_scraper.cleanup(),
            self.ideas_scraper.cleanup(),
            self.technicals_scraper.cleanup()
        ]
        
        await asyncio.gather(*cleanup_tasks)
        
    def calculate_trend_score(self, data: Dict[str, Any]) -> float:
        """
        Calculate a trend score from TradingView data for trend analysis.
        
        Args:
            data: Combined TradingView data for a symbol
            
        Returns:
            Trend score (-1.0 to 1.0 where positive is bullish)
        """
        if "error" in data:
            return 0.0
            
        score_components = []
        
        # Add technical indicators score (-2 to +2)
        technical_rating = 0
        if "technical_indicators" in data and "rating" in data["technical_indicators"]:
            technical_rating = data["technical_indicators"]["rating"]
            score_components.append(technical_rating / 2.0)  # Convert to -1.0 to 1.0 scale
            
        # Add sentiment score from trading ideas
        sentiment_score = 0.0
        if "trading_ideas" in data and "sentiment" in data["trading_ideas"]:
            sentiment = data["trading_ideas"]["sentiment"]
            positive = sentiment.get("positive_percent", 0)
            negative = sentiment.get("negative_percent", 0)
            if positive > 0 or negative > 0:
                sentiment_score = (positive - negative) / 100.0  # Convert to -1.0 to 1.0 scale
            score_components.append(sentiment_score)
            
        # Add price momentum from chart data
        momentum_score = 0.0
        if "chart_data" in data and "data" in data["chart_data"] and len(data["chart_data"]["data"]) > 1:
            chart_data = data["chart_data"]["data"]
            
            # Calculate short-term momentum (last 10 periods)
            short_window = min(10, len(chart_data))
            recent_data = chart_data[-short_window:]
            
            price_changes = []
            for i in range(1, len(recent_data)):
                prev_close = recent_data[i-1]["close"]
                curr_close = recent_data[i]["close"]
                if prev_close > 0:
                    change = (curr_close - prev_close) / prev_close
                    price_changes.append(change)
            
            if price_changes:
                avg_change = sum(price_changes) / len(price_changes)
                # Normalize to roughly -1.0 to 1.0 scale (5% daily change is significant)
                momentum_score = min(1.0, max(-1.0, avg_change * 20.0))
                score_components.append(momentum_score)
                
        # Average all components for final score
        if score_components:
            return sum(score_components) / len(score_components)
        else:
            return 0.0