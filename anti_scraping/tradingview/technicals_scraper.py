"""
Enhanced TradingView technicals scraper with anti-scraping capabilities.
"""
import logging
import time
import random
import asyncio
from typing import Dict, Any, List, Optional, Union
import json

import requests
import aiohttp
from bs4 import BeautifulSoup

# Import our anti-scraping components
from ..scraping_shield import ScrapingShield

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedTechnicalsScraper:
    """
    Enhanced TradingView technicals scraper with advanced anti-detection capabilities.
    Uses the ScrapingShield to implement browser fingerprinting, proxy rotation,
    and other anti-scraping techniques for accessing technical indicator data.
    """
    
    def __init__(self):
        """Initialize the enhanced technicals scraper with anti-scraping capabilities."""
        self.shield = ScrapingShield()
        self.session = requests.Session()
        
    def _random_delay(self, min_delay: float = 1.0, max_delay: float = 5.0) -> None:
        """Add a random delay between requests to simulate human behavior."""
        delay = random.uniform(min_delay, max_delay)
        logger.debug(f"Adding random delay of {delay:.2f} seconds")
        time.sleep(delay)
        
    async def get_technicals(self, symbol: str, screener: str = "crypto", exchange: str = "BINANCE") -> Dict[str, Any]:
        """
        Get technical indicators for a symbol with anti-detection measures.
        
        Args:
            symbol: Trading symbol (e.g., "BTCUSD")
            screener: Screener type (e.g., "crypto", "forex", "america")
            exchange: Exchange name (e.g., "BINANCE", "COINBASE")
            
        Returns:
            Dictionary with technical indicators data
        """
        try:
            url = f"https://www.tradingview.com/symbols/{exchange}-{symbol}/technicals/"
            
            # Apply WAF bypass headers
            headers = self.shield.waf_bypass_headers(url)
            
            # Add TLS fingerprint spoofing
            ssl_context = self.shield.spoof_tls_fingerprint("chrome")
            
            # Set up async HTTP client with our security measures
            async with aiohttp.ClientSession(headers=headers) as session:
                # Fetch the main page with technical indicators
                response = await session.get(url, ssl=ssl_context)
                
                if response.status != 200:
                    logger.error(f"Failed to fetch technicals page: Status {response.status}")
                    return {"error": f"HTTP error: {response.status}"}
                
                content = await response.text()
                
                # Check for honeypots or traps
                if self.shield.check_for_legal_honeypots(content, url):
                    logger.warning("Legal honeypot detected on technicals page")
                    return {"error": "Legal honeypot detected"}
                
                # Apply device-level fingerprinting countermeasures
                browser = await self.shield.init_browser()
                page = self.shield.page
                
                try:
                    await self.shield.spoof_hardware_metrics(page)
                    await page.goto(url)
                    
                    # Wait for technical indicators to load
                    await page.waitForSelector(".technicals-widget")
                    
                    # Extract data using JavaScript in the browser context
                    data = await self._extract_technicals_data(page)
                    
                    # Apply human-like behavior
                    await self.shield.simulate_human_behavior()
                    
                    return data
                finally:
                    await self.shield.cleanup()
                    
        except Exception as e:
            logger.error(f"Error fetching technical indicators: {e}")
            return {"error": str(e)}
            
    async def _extract_technicals_data(self, page) -> Dict[str, Any]:
        """Extract technical indicators data from the page using JavaScript evaluation."""
        try:
            # Execute JavaScript to extract technical data
            data = await page.evaluate("""
            () => {
                // Helper function to extract indicator values
                const getIndicatorValues = (container) => {
                    if (!container) return {};
                    
                    const indicators = {};
                    const rows = container.querySelectorAll('tr');
                    
                    rows.forEach(row => {
                        const nameEl = row.querySelector('.technicals-widget-title');
                        const valueEl = row.querySelector('.technicals-widget-value');
                        
                        if (nameEl && valueEl) {
                            const name = nameEl.textContent.trim();
                            const value = valueEl.textContent.trim();
                            indicators[name] = value;
                        }
                    });
                    
                    return indicators;
                };
                
                // Extract summary
                const summaryElement = document.querySelector('.technicals-widget-summary');
                const summary = summaryElement ? summaryElement.textContent.trim() : 'Unknown';
                
                // Extract technical indicators by timeframe
                const result = {
                    summary,
                    timeframes: {}
                };
                
                const timeframes = ['1m', '5m', '15m', '1h', '4h', '1D', '1W', '1M'];
                timeframes.forEach(timeframe => {
                    const tabElement = document.querySelector(`[data-timeframe="${timeframe}"]`);
                    if (tabElement) {
                        const isActive = tabElement.classList.contains('active');
                        
                        if (!isActive) {
                            tabElement.click();
                            // Allow time for the content to update
                            setTimeout(() => {}, 100);
                        }
                        
                        const oscillatorsContainer = document.querySelector('.technicals-widget-oscillators');
                        const maContainer = document.querySelector('.technicals-widget-moving-averages');
                        
                        result.timeframes[timeframe] = {
                            oscillators: getIndicatorValues(oscillatorsContainer),
                            movingAverages: getIndicatorValues(maContainer)
                        };
                    }
                });
                
                return result;
            }
            """)
            
            # Post-process data
            if not data or not data.get('timeframes'):
                return {"error": "Failed to extract technical indicators"}
                
            # Add technical rating based on summary
            summary = data.get('summary', '').lower()
            if 'strong buy' in summary:
                data['rating'] = 2  # Strong Buy
            elif 'buy' in summary:
                data['rating'] = 1  # Buy
            elif 'strong sell' in summary:
                data['rating'] = -2  # Strong Sell
            elif 'sell' in summary:
                data['rating'] = -1  # Sell
            else:
                data['rating'] = 0  # Neutral
                
            return data
            
        except Exception as e:
            logger.error(f"Error extracting technical data: {e}")
            return {"error": f"Data extraction error: {str(e)}"}
            
    async def get_multi_symbol_technicals(self, symbols: List[str], screener: str = "crypto", exchange: str = "BINANCE") -> Dict[str, Dict[str, Any]]:
        """
        Get technical indicators for multiple symbols with anti-detection measures.
        Uses intelligent scheduling and proxy rotation to avoid detection.
        
        Args:
            symbols: List of trading symbols
            screener: Screener type
            exchange: Exchange name
            
        Returns:
            Dictionary mapping symbols to their technical indicators
        """
        result = {}
        
        # Group symbols into batches to avoid overwhelming the server
        batch_size = 3
        symbol_batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
        
        for batch in symbol_batches:
            # Process symbols in batch with some parallelism, but not too much
            tasks = []
            for symbol in batch:
                # Add jitter to avoid perfectly synchronized requests
                await asyncio.sleep(random.uniform(0.5, 2.0))
                
                # Create task for this symbol
                tasks.append(self.get_technicals(symbol, screener, exchange))
                
            # Wait for all tasks in this batch
            batch_results = await asyncio.gather(*tasks)
            
            # Map results to symbols
            for symbol, technical_data in zip(batch, batch_results):
                result[symbol] = technical_data
                
            # Rotate proxy after each batch
            if len(symbol_batches) > 1:
                try:
                    logger.info("Rotating proxy between batches")
                    await self.shield.rotate_proxy()
                except Exception as e:
                    logger.error(f"Error rotating proxy: {e}")
                    
                # Add delay between batches
                await asyncio.sleep(random.uniform(3.0, 8.0))
                
        return result
        
    def get_technical_rating(self, technical_data: Dict[str, Any]) -> int:
        """
        Get numerical rating from technical indicators data.
        
        Args:
            technical_data: Technical indicators data 
            
        Returns:
            Rating from -2 (Strong Sell) to 2 (Strong Buy)
        """
        if "error" in technical_data:
            return 0  # Neutral if there's an error
            
        return technical_data.get('rating', 0)
        
    async def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'shield'):
            await self.shield.cleanup()
        if hasattr(self, 'session') and self.session:
            self.session.close()