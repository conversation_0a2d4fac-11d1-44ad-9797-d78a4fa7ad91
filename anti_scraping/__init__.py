"""
Anti-Scraping Framework for Trend Crawler

This package provides comprehensive anti-scraping countermeasures for web scraping
operations, implementing advanced techniques to avoid detection and blocking.
"""

from .core import AntiScrapingMiddleware
from .tradingview import EnhancedChartScraper, EnhancedIdeasScraper, EnhancedTechnicalsScraper

__all__ = [
    'AntiScrapingMiddleware',
    'EnhancedChartScraper',
    'EnhancedIdeasScraper',
    'EnhancedTechnicalsScraper'
]
