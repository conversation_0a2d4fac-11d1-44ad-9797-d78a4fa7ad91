"""
Anti-Scraping Framework for Trend Crawler

This package provides comprehensive anti-scraping countermeasures for web scraping
operations, implementing advanced techniques to avoid detection and blocking.
"""

from .core import AntiScrapingMiddleware

# Import TradingView scrapers from the root tradingview directory
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from tradingview import (
        EnhancedChartScraper,
        EnhancedIdeasScraper,
        EnhancedTechnicalsScraper
    )
except ImportError:
    # Fallback imports if the main tradingview module is not available
    EnhancedChartScraper = None
    EnhancedIdeasScraper = None
    EnhancedTechnicalsScraper = None

__all__ = [
    'AntiScrapingMiddleware',
]

# Only include TradingView scrapers if they're available
if EnhancedChartScraper is not None:
    __all__.extend([
        'EnhancedChartScraper',
        'EnhancedIdeasScraper',
        'EnhancedTechnicalsScraper'
    ])
