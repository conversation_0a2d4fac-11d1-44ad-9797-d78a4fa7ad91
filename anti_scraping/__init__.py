"""
Anti-Scraping Framework for Trend Crawler

This package provides comprehensive anti-scraping countermeasures for web scraping
operations, implementing advanced techniques to avoid detection and blocking.
"""

from .core import AntiScrapingMiddleware

# TradingView scrapers have been moved to the root tradingview/ directory
# Import them from the root level
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from tradingview.chart_scraper import EnhancedChartScraper
    from tradingview.ideas_scraper import EnhancedIdeasScraper
    from tradingview.technicals_scraper import EnhancedTechnicalsScraper
    _TRADINGVIEW_AVAILABLE = True
except ImportError as e:
    # Graceful degradation if TradingView modules are not available
    _TRADINGVIEW_AVAILABLE = False
    import logging
    logging.getLogger(__name__).warning(f"TradingView scrapers not available: {e}")

__all__ = ['AntiScrapingMiddleware']

if _TRADINGVIEW_AVAILABLE:
    __all__.extend([
        'EnhancedChartScraper',
        'EnhancedIdeasScraper',
        'EnhancedTechnicalsScraper'
    ])
