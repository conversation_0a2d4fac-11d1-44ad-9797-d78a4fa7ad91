"""
Anti-scraping middleware for protecting web scrapers from detection.
"""
import random
import logging
import time
from typing import Dict, Any, List, Optional, Union
import os
import json

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AntiScrapingMiddleware:
    """
    Middleware that provides anti-scraping capabilities to data source adapters.
    Implements browser fingerprinting, proxy rotation, and other anti-detection measures.
    """
    
    def __init__(self, proxy_config_path: str = "proxy_config.json"):
        """
        Initialize the anti-scraping middleware.
        
        Args:
            proxy_config_path: Path to proxy configuration file
        """
        self.proxy_config_path = proxy_config_path
        self.proxies = []
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67"
        ]
        self._load_proxies()
        
    def _load_proxies(self):
        """Load proxies from configuration file."""
        try:
            if os.path.exists(self.proxy_config_path):
                with open(self.proxy_config_path, 'r') as f:
                    config = json.load(f)
                    self.proxies = config.get("proxies", [])
                    logger.info(f"Loaded {len(self.proxies)} proxies from configuration")
            else:
                logger.warning(f"Proxy configuration file not found: {self.proxy_config_path}")
        except Exception as e:
            logger.error(f"Error loading proxy configuration: {e}")
    
    def get_next_proxy(self) -> str:
        """
        Get the next proxy from the rotation.
        
        Returns:
            Proxy URL in format "*********************:port" or empty string if none available
        """
        if not self.proxies:
            return ""
            
        return random.choice(self.proxies)
    
    def get_random_user_agent(self) -> str:
        """
        Get a random user agent.
        
        Returns:
            Random user agent string
        """
        return random.choice(self.user_agents)
    
    def get_enhanced_browser_options(self, headless: bool = True) -> Options:
        """
        Get enhanced browser options with anti-detection measures.
        
        Args:
            headless: Whether to run in headless mode
            
        Returns:
            Configured Chrome options
        """
        options = Options()
        
        if headless:
            options.add_argument("--headless")
            options.add_argument("--disable-gpu")
        
        # Set random user agent
        options.add_argument(f"user-agent={self.get_random_user_agent()}")
        
        # Disable automation flags
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option("useAutomationExtension", False)
        
        # Additional anti-detection measures
        options.add_argument("--disable-extensions")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--window-size=1920,1080")
        
        return options
    
    def random_delay(self, min_delay: float = 1.0, max_delay: float = 5.0) -> float:
        """
        Add a random delay to simulate human behavior.
        
        Args:
            min_delay: Minimum delay in seconds
            max_delay: Maximum delay in seconds
            
        Returns:
            The actual delay applied
        """
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        return delay
    
    def get_waf_bypass_headers(self, url: str = "") -> Dict[str, str]:
        """
        Get headers to bypass WAF protection.
        
        Args:
            url: Target URL
            
        Returns:
            Headers dictionary
        """
        return {
            "User-Agent": self.get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": f"max-age=0,no-cache,no-store,must-revalidate,{random.randint(1, 1000)}"
        }
    
    def simulate_human_behavior(self, driver) -> None:
        """
        Simulate human behavior with the given browser driver.
        
        Args:
            driver: Selenium WebDriver instance
        """
        try:
            # Scroll down
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 2);")
            self.random_delay(1.0, 2.0)
            
            # Scroll down a bit more
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 1.5);")
            self.random_delay(0.5, 1.5)
            
            # Scroll back up
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight / 3);")
        except Exception as e:
            logger.error(f"Error simulating human behavior: {e}")
    
    def check_for_captcha(self, html_content: str) -> bool:
        """
        Check if a page contains a CAPTCHA.
        
        Args:
            html_content: HTML content to check
            
        Returns:
            True if CAPTCHA detected, False otherwise
        """
        captcha_indicators = [
            "captcha", "CAPTCHA", "recaptcha", "reCAPTCHA",
            "I'm not a robot", "verify you are human", 
            "bot check", "security check"
        ]
        
        return any(indicator in html_content for indicator in captcha_indicators)
    
    def create_webdriver(self, headless: bool = True) -> webdriver.Chrome:
        """
        Create a WebDriver instance with automatic ChromeDriver management.
        
        Args:
            headless: Whether to run in headless mode
            
        Returns:
            Configured Chrome WebDriver instance
        """
        try:
            # Get Chrome options
            options = self.get_enhanced_browser_options(headless=headless)
            
            # Create service with automatically managed ChromeDriver
            service = Service(ChromeDriverManager().install())
            
            # Create WebDriver
            driver = webdriver.Chrome(service=service, options=options)
            
            # Set additional properties to avoid detection
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            logger.info("Created WebDriver with automatic ChromeDriver management")
            return driver
            
        except Exception as e:
            logger.error(f"Error creating WebDriver: {e}")
            # Fallback to system ChromeDriver
            try:
                options = self.get_enhanced_browser_options(headless=headless)
                driver = webdriver.Chrome(options=options)
                logger.warning("Using system ChromeDriver as fallback")
                return driver
            except Exception as fallback_error:
                logger.error(f"Fallback WebDriver creation failed: {fallback_error}")
                raise