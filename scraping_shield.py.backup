#!/usr/bin/env python3
"""
Advanced anti-detection system for web scraping operations.
"""
import os
import json
import random
import asyncio
import logging
import time
import ssl
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
import aiohttp
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from bs4 import BeautifulSoup
import undetected_chromedriver as uc
from fp.fp import FreeProxy
from curl_cffi import requests as curl_requests
import nest_asyncio
from twocaptcha import TwoCaptcha
from anticaptchaofficial.recaptchav2proxyless import recaptchaV2Proxyless
from pathlib import Path
from selenium.webdriver.chrome.options import Options
from selenium import webdriver
from fake_useragent import UserAgent
from selenium_stealth import stealth
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError, HTTPError, ProxyError, SSLError
import psycopg2
from psycopg2.extras import Json
import numpy as np
import pyautogui
from PIL import Image
import cv2
import tensorflow as tf
import capsolver
import warnings
import platform
import traceback
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes

# Configure logging with standard format
log_handler = logging.StreamHandler()

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    handlers=[log_handler],
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create module logger
logger = logging.getLogger(__name__)

# Add file handler for persistent logging
try:
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/scraping_shield.log')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
except Exception as e:
    logger.error(f"Failed to set up file logging: {e}")

class ScrapingShield:
    """Advanced anti-detection system for web scraping."""

    def __init__(self, config_path: str = "proxy_config.json"):
        """Initialize the scraping shield."""
        self.logger = logging.getLogger(__name__)

        try:
            self.config = self._load_config(config_path)
            self.proxy_pool = self._initialize_proxy_pool()  # Make sure proxy_pool is accessible
            self.ua = UserAgent(fallback='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36')
            self.current_fingerprint = None
            self.db_conn = self._init_db_connection()
            self.browser: Optional[Browser] = None
            self.page: Optional[Page] = None
            self.current_proxy: Optional[str] = None
            self.fingerprint_pool: List[Dict[str, Any]] = []
            self.captcha_solver = None
            self._init_captcha_solver()

            # Set retry parameters
            self.max_retries = self.config.get('max_retries', 3)
            self.retry_delay = self.config.get('retry_delay', 2)
            self.exponential_backoff = self.config.get('exponential_backoff', True)

            logger.info(f"ScrapingShield initialized with {len(self.proxy_pool)} proxies")
        except Exception as e:
            logger.error(f"Failed to initialize ScrapingShield: {e}")
            logger.debug(f"Initialization error details: {traceback.format_exc()}")
            raise

    def _init_captcha_solver(self):
        """Initialize CAPTCHA solver based on available API keys."""
        try:
            if os.getenv('TWOCAPTCHA_API_KEY'):
                self.captcha_solver = TwoCaptcha(os.getenv('TWOCAPTCHA_API_KEY'))
                logger.info("2Captcha solver initialized")
            elif os.getenv('ANTI_CAPTCHA_API_KEY'):
                # We'll initialize this when needed
                logger.info("Anti-Captcha solver will be used when needed")
            elif os.getenv('CAPSOLVER_API_KEY'):
                capsolver.api_key = os.getenv('CAPSOLVER_API_KEY')
                logger.info("CapSolver initialized")
            else:
                logger.warning("No CAPTCHA solving service API keys found")
        except Exception as e:
            logger.error(f"Failed to initialize CAPTCHA solver: {e}")

    def _load_config(self, config_path: str) -> Dict:
        """Load proxy and configuration settings"""
        try:
            with open(config_path) as f:
                config = json.load(f)
                logger.info(f"Configuration loaded from {config_path}")
                return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using empty config")
            return {}
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in config file {config_path}")
            return {}
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            logger.debug(f"Config loading error details: {traceback.format_exc()}")
            return {}

    def _initialize_proxy_pool(self) -> List[Dict]:
        """Initialize and validate proxy pool"""
        proxies = self.config.get('proxies', [])
        valid_proxies = []

        logger.info(f"Validating {len(proxies)} proxies")

        for i, proxy in enumerate(proxies):
            try:
                if self._validate_proxy(proxy):
                    valid_proxies.append(proxy)
                    logger.debug(f"Proxy {i+1}/{len(proxies)} validated")
                else:
                    logger.warning(f"Proxy {i+1}/{len(proxies)} failed validation: {proxy.get('host')}")
            except Exception as e:
                logger.warning(f"Error validating proxy {i+1}/{len(proxies)}: {e}")

        if not valid_proxies and proxies:
            logger.warning("No valid proxies found, proceeding without proxies")

        logger.info(f"Proxy pool initialized with {len(valid_proxies)} valid proxies")
        return valid_proxies

    def _validate_proxy(self, proxy: Dict) -> bool:
        """Validate proxy connectivity and performance"""
        try:
            test_url = 'https://api.ipify.org?format=json'
            proxy_url = f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"

            # Add authentication if provided
            if proxy.get('username') and proxy.get('password'):
                proxy_auth = f"{proxy['username']}:{proxy['password']}@"
                proxy_url = f"{proxy['protocol']}://{proxy_auth}{proxy['host']}:{proxy['port']}"

            response = requests.get(
                test_url,
                proxies={'http': proxy_url, 'https': proxy_url},
                timeout=10
            )

            if response.status_code == 200:
                logger.debug(f"Proxy validated: {proxy.get('host')}:{proxy.get('port')}")
                return True
            else:
                logger.debug(f"Proxy returned status {response.status_code}: {proxy.get('host')}:{proxy.get('port')}")
                return False

        except Timeout:
            logger.debug(f"Proxy timeout: {proxy.get('host')}:{proxy.get('port')}")
            return False
        except ConnectionError:
            logger.debug(f"Proxy connection error: {proxy.get('host')}:{proxy.get('port')}")
            return False
        except ProxyError:
            logger.debug(f"Proxy error: {proxy.get('host')}:{proxy.get('port')}")
            return False
        except Exception as e:
            logger.debug(f"Proxy validation error ({proxy.get('host')}:{proxy.get('port')}): {e}")
            return False

    def _init_db_connection(self):
        """Initialize database connection for metrics"""
        if not self.config.get('db', {}).get('enabled', False):
            logger.info("Database metrics disabled in configuration")
            return None

        try:
            conn = psycopg2.connect(
                dbname=self.config['db']['name'],
                user=self.config['db']['user'],
                password=self.config['db']['password'],
                host=self.config['db']['host']
            )
            logger.info(f"Connected to database at {self.config['db']['host']}")
            return conn
        except KeyError:
            logger.warning("Database configuration incomplete")
            return None
        except psycopg2.OperationalError as e:
            logger.error(f"Failed to connect to database: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error connecting to database: {e}")
            logger.debug(f"Database connection error details: {traceback.format_exc()}")
            return None

    async def init_browser(self):
        """Initialize browser with anti-detection measures."""
        try:
            playwright = await async_playwright().start()

            # Configure browser options
            browser_options = {
                'args': [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                ],
                'ignoreDefaultArgs': ['--enable-automation'],
                'headless': True
            }

            if self.current_proxy:
                browser_options['proxy'] = {
                    'server': self.current_proxy
                }

            # Launch browser with custom options
            self.browser = await playwright.chromium.launch(**browser_options)

            # Create new page with randomized fingerprint
            self.page = await self.browser.new_page()
            await self._apply_fingerprint()

            # Hook into page events
            await self._setup_request_interception()

            logger.info("Browser initialized with anti-detection measures")

        except Exception as e:
            logger.error(f"Failed to initialize browser: {e}")
            raise

    async def _apply_fingerprint(self):
        """Apply randomized browser fingerprint."""
        if not self.page:
            return

        # Load or generate fingerprint pool
        if not self.fingerprint_pool:
            self.fingerprint_pool = self._generate_fingerprint_pool()

        # Select random fingerprint
        fingerprint = random.choice(self.fingerprint_pool)

        # Apply fingerprint
        await self.page.add_init_script("""
            const fingerprint = arguments[0];

            // Override navigator properties
            Object.defineProperty(navigator, 'platform', {
                get: () => fingerprint.platform
            });
            Object.defineProperty(navigator, 'userAgent', {
                get: () => fingerprint.userAgent
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => fingerprint.languages
            });

            // Override WebGL fingerprint
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) {
                    return fingerprint.webglVendor;
                }
                if (parameter === 37446) {
                    return fingerprint.webglRenderer;
                }
                return getParameter.apply(this, arguments);
            };

            // Add noise to canvas fingerprint
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function() {
                const context = originalGetContext.apply(this, arguments);
                if (context && arguments[0] === '2d') {
                    const originalFillText = context.fillText;
                    context.fillText = function() {
                        const noise = Math.random() * 0.0001;
                        arguments[1] += noise;
                        arguments[2] += noise;
                        return originalFillText.apply(this, arguments);
                    };
                }
                return context;
            };
        """, [fingerprint])

        # Set user agent
        await self.page.set_extra_http_headers({
            'User-Agent': fingerprint['userAgent'],
            'Accept-Language': fingerprint['languages'][0]
        })

        logger.info(f"Applied fingerprint: {fingerprint['id']}")

    def _generate_fingerprint_pool(self, size: int = 100) -> List[Dict[str, Any]]:
        """Generate a pool of realistic browser fingerprints."""
        fingerprints = []

        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            # Add more user agents...
        ]

        platforms = ['Win32', 'MacIntel', 'Linux x86_64']
        languages = [
            ['en-US', 'en'],
            ['en-GB', 'en'],
            ['es-ES', 'es', 'en'],
            # Add more language combinations...
        ]

        webgl_vendors = [
            'Google Inc. (Intel)',
            'Apple GPU',
            'NVIDIA Corporation',
            # Add more vendors...
        ]

        webgl_renderers = [
            'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)',
            'Apple M1',
            'NVIDIA GeForce RTX 3080',
            # Add more renderers...
        ]

        for i in range(size):
            fingerprint = {
                'id': f'fp_{i}',
                'userAgent': random.choice(user_agents),
                'platform': random.choice(platforms),
                'languages': random.choice(languages),
                'webglVendor': random.choice(webgl_vendors),
                'webglRenderer': random.choice(webgl_renderers),
                'screenResolution': random.choice(['1920x1080', '2560x1440', '3840x2160']),
                'colorDepth': random.choice([24, 32]),
                'hardwareConcurrency': random.choice([4, 8, 12, 16]),
                'deviceMemory': random.choice([4, 8, 16, 32])
            }
            fingerprints.append(fingerprint)

        return fingerprints

    async def _setup_request_interception(self):
        """Setup request interception for additional anti-detection."""
        if not self.page:
            return

        await self.page.route("**/*", lambda route: self._handle_request(route))

    async def _handle_request(self, route):
        """Handle intercepted requests with anti-detection measures."""
        request = route.request

        # Skip non-document requests
        if request.resource_type not in ['document', 'xhr', 'fetch']:
            await route.continue_()
            return

        # Add random headers
        headers = request.headers
        headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '" Not A;Brand";v="99", "Chromium";v="96"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        })

        await route.continue_(headers=headers)

    async def rotate_proxy(self):
        """Rotate to a new proxy server."""
        try:
            if not self.proxy_pool:
                self._load_proxies()

            # Select new proxy
            new_proxy = random.choice(self.proxy_pool)
            self.current_proxy = new_proxy['server']

            if self.browser:
                await self.browser.close()
                await self.init_browser()

            logger.info(f"Rotated to new proxy: {new_proxy['id']}")

        except Exception as e:
            logger.error(f"Failed to rotate proxy: {e}")
            raise

    def _load_proxies(self):
        """Load and validate proxy servers."""
        try:
            proxy_sources = []

            # Load BrightData proxies
            if self.config.get('brightdata', {}).get('enabled'):
                proxy_sources.extend(self.config['brightdata']['proxies'])

            # Load StormProxies
            if self.config.get('stormproxies', {}).get('enabled'):
                proxy_sources.extend(self.config['stormproxies']['proxies'])

            # Add fallback proxies using FreeProxy
            fallback_proxies = FreeProxy(
                country_id=['US', 'GB', 'CA'],
                timeout=1
            ).get_proxy_list()

            for proxy in fallback_proxies:
                proxy_sources.append({
                    'server': proxy,
                    'type': 'free'
                })

            # Validate and store proxies
            self.proxy_pool = []
            for i, proxy in enumerate(proxy_sources):
                proxy['id'] = f"proxy_{i}"
                self.proxy_pool.append(proxy)

            logger.info(f"Loaded {len(self.proxy_pool)} proxies")

        except Exception as e:
            logger.error(f"Failed to load proxies: {e}")
            raise

    async def avoid_detection(self, url: str):
        """Apply various techniques to avoid bot detection."""
        if not self.page:
            return

        try:
            # Randomize viewport size
            await self.page.set_viewport_size({
                'width': random.randint(1024, 1920),
                'height': random.randint(768, 1080)
            })

            # Add random delays
            await asyncio.sleep(random.uniform(1, 3))

            # Check for common honeypots
            await self._check_honeypots(url)

            # Simulate human-like behavior
            await self.simulate_human_behavior()

        except Exception as e:
            logger.error(f"Failed to apply anti-detection measures: {e}")
            raise

    async def _check_honeypots(self, url: str):
        """Check for and handle honeypot traps."""
        try:
            # Get page content
            response = await curl_requests.get(url)
            soup = BeautifulSoup(response.content, 'html.parser')

            # Check for hidden elements
            hidden_elements = soup.find_all(style=lambda x: x and ('display:none' in x or 'visibility:hidden' in x))
            for element in hidden_elements:
                logger.warning(f"Potential honeypot detected: {element.get('class', ['unknown'])}")

            # Check for trap links
            trap_links = soup.find_all('a', href=lambda x: x and ('javascript:void(0)' in x or '#' == x))
            for link in trap_links:
                logger.warning(f"Potential trap link detected: {link.get('class', ['unknown'])}")

        except Exception as e:
            logger.error(f"Failed to check for honeypots: {e}")

    async def simulate_human_behavior(self):
        """Simulate human-like browser behavior."""
        if not self.page:
            return

        try:
            # Random mouse movements
            for _ in range(random.randint(3, 7)):
                await self.page.mouse.move(
                    random.randint(0, 1000),
                    random.randint(0, 1000)
                )
                await asyncio.sleep(random.uniform(0.1, 0.3))

            # Random scrolling
            await self.page.evaluate("""
                window.scrollTo({
                    top: Math.random() * document.body.scrollHeight,
                    behavior: 'smooth'
                });
            """)
            await asyncio.sleep(random.uniform(1, 2))

            # Simulate focus/blur events
            await self.page.evaluate("""
                document.activeElement.blur();
                setTimeout(() => document.activeElement.focus(), 500);
            """)

        except Exception as e:
            logger.error(f"Failed to simulate human behavior: {e}")

    async def solve_captcha(self, site_key: str, url: str) -> Optional[str]:
        """Solve CAPTCHA using 2captcha service."""
        try:
            result = self.captcha_solver.recaptcha(
                sitekey=site_key,
                url=url
            )

            if result and 'code' in result:
                logger.info("Successfully solved CAPTCHA")
                return result['code']

            logger.warning("Failed to solve CAPTCHA")
            return None

        except Exception as e:
            logger.error(f"CAPTCHA solving error: {e}")
            return None

    def get_driver(self, url: str) -> uc.Chrome:
        """Get an undetectable Chrome instance with randomized fingerprint"""
        options = Options()
        proxy = self._get_next_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy["protocol"]}://{proxy["host"]}:{proxy["port"]}')

        driver = uc.Chrome(options=options)

        # Apply stealth settings
        stealth(
            driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True,
        )

        # Set random viewport size
        width = random.randint(1024, 1920)
        height = random.randint(768, 1080)
        driver.set_window_size(width, height)

        return driver

    def _get_next_proxy(self) -> Optional[Dict]:
        """Get next proxy using intelligent rotation"""
        if not self.proxy_pool:
            return None

        # Get proxy with best performance metrics
        cursor = self.db_conn.cursor()
        cursor.execute("""
            SELECT proxy_id, success_rate
            FROM proxy_performance_summary
            ORDER BY success_rate DESC
            LIMIT 1
        """)
        result = cursor.cursor.fetchone()

        if result:
            proxy_id = result[0]
            return next((p for p in self.proxy_pool if p['id'] == proxy_id), random.choice(self.proxy_pool))
        return random.choice(self.proxy_pool)

    def randomize_behavior(self, driver) -> None:
        """Add random human-like behavior patterns"""
        # Random scroll
        scroll_height = random.randint(100, 500)
        driver.execute_script(f"window.scrollBy(0, {scroll_height})")
        time.sleep(random.uniform(0.5, 2.0))

        # Random mouse movements using Bezier curves
        points = self._generate_bezier_curve()
        for point in points:
            driver.execute_script(
                f"document.elementFromPoint({point[0]}, {point[1]}).scrollIntoView()"
            )
            time.sleep(random.uniform(0.1, 0.3))

    def _generate_bezier_curve(self, num_points: int = 10) -> List[List[float]]:
        """Generate smooth mouse movement curve"""
        control_points = np.array([
            [0, 0],
            [random.randint(100, 400), random.randint(100, 400)],
            [random.randint(400, 800), random.randint(400, 800)],
            [random.randint(800, 1000), random.randint(800, 1000)]
        ])

        t = np.linspace(0, 1, num_points)
        curve = np.zeros((num_points, 2))

        for i in range(num_points):
            curve[i] = (1-t[i])**3 * control_points[0] + \
                      3*(1-t[i])**2 * t[i] * control_points[1] + \
                      3*(1-t[i]) * t[i]**2 * control_points[2] + \
                      t[i]**3 * control_points[3]

        return curve.tolist()

    def log_detection_event(self, event_type: str, url: str, detection_method: str, additional_info: Dict) -> None:
        """Log detection events for analysis"""
        if not self.db_conn:
            return

        cursor = self.db_conn.cursor()
        cursor.execute("""
            INSERT INTO detection_events
            (event_type, url, detection_method, additional_info)
            VALUES (%s, %s, %s, %s)
        """, (event_type, url, detection_method, Json(additional_info)))
        self.db_conn.commit()

    def handle_captcha(self, driver) -> bool:
        """Handle various types of CAPTCHAs"""
        try:
            # Check for common CAPTCHA elements
            captcha_present = any([
                driver.find_elements_by_id("captcha"),
                driver.find_elements_by_class_name("g-recaptcha"),
                driver.find_elements_by_class_name("h-captcha")
            ])

            if captcha_present:
                # Log CAPTCHA encounter
                self.log_detection_event(
                    "captcha_detected",
                    driver.current_url,
                    "element_detection",
                    {"captcha_type": "visual"}
                )

                # Implement CAPTCHA solving service here
                # For now, we'll just wait for manual intervention
                time.sleep(30)  # Wait for manual solving
                return True

        except Exception as e:
            self.logger.error(f"Error handling CAPTCHA: {e}")
            return False

        return True

    def detect_honeypots(self, driver) -> bool:
        """Detect and avoid honeypot traps"""
        try:
            # Check for common honeypot elements
            suspicious_elements = driver.find_elements_by_css_selector(
                '[style*="display:none"], [style*="visibility:hidden"], [style*="opacity:0"]'
            )

            if suspicious_elements:
                self.log_detection_event(
                    "honeypot_detected",
                    driver.current_url,
                    "element_detection",
                    {"num_elements": len(suspicious_elements)}
                )
                return True

        except Exception as e:
            self.logger.error(f"Error detecting honeypots: {e}")

        return False

    async def cleanup(self):
        """Clean up resources."""
        if self.browser:
            await self.browser.close()
        if self.db_conn:
            self.db_conn.close()

    def spoof_tls_fingerprint(self, browser_type: str = "chrome") -> ssl.SSLContext:
        """Spoof TLS fingerprints to mimic real browsers.

        Uses curl-impersonate techniques to match Chrome/Firefox TLS signatures.
        """
        context = ssl.create_default_context()

        # TLS fingerprinting countermeasures
        if (browser_type == "chrome"):
            # Chrome 100 TLS fingerprint
            context.set_ciphers(
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:'
                'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305'
            )
            context.options |= ssl.OP_NO_SSLv2
            context.options |= ssl.OP_NO_SSLv3
            context.options |= ssl.OP_NO_TLSv1
            context.options |= ssl.OP_NO_TLSv1_1
            # Set application_layer_protocol_negotiation
            context.set_alpn_protocols(['h2', 'http/1.1'])
        elif (browser_type == "firefox"):
            # Firefox 91+ TLS fingerprint
            context.set_ciphers(
                'TLS_AES_128_GCM_SHA256:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_256_GCM_SHA384:'
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256'
            )
            context.options |= ssl.OP_NO_SSLv2
            context.options |= ssl.OP_NO_SSLv3
            context.options |= ssl.OP_NO_TLSv1
            context.options |= ssl.OP_NO_TLSv1_1
            context.set_alpn_protocols(['h2', 'http/1.1'])

        # Set grease cipher suites to mimic ClientHello messages
        self._add_cipher_suite_grease(context)

        return context

    def _add_cipher_suite_grease(self, context: ssl.SSLContext):
        """Add GREASE (Generate Random Extensions And Sustain Extensibility) values.

        These are random values in specific ranges that browsers add to TLS handshakes.
        """
        # This is a simplified version as Python's ssl module doesn't fully support this
        # In real implementation, we'd use a custom TLS library
        logger.info("Adding GREASE values to TLS handshake")

    def waf_bypass_headers(self, url: str) -> Dict[str, str]:
        """Generate headers specifically designed to bypass common WAFs like Cloudflare, Akamai, etc."""
        host = url.split('://')[1].split('/')[0]

        # Base realistic browser headers
        headers = {
            'Host': host,
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': f"https://www.google.com/search?q={host}",
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

        # Add random browser-specific headers to increase legitimacy
        browsers = {
            'chrome': {
                'Sec-Ch-Ua': '"Google Chrome";v="101", " Not;A Brand";v="99", "Chromium";v="101"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': f'"{random.choice(["Windows", "Linux", "macOS"])}"',
            },
            'firefox': {
                'TE': 'trailers',
                'Accept-Language': 'en-US,en;q=0.9,fr;q=0.8,de;q=0.7',
            },
            'safari': {
                'X-Apple-Http-Actions': 'Action=BROWSE',
                'Accept-Language': 'en-US,en;q=0.9,fr;q=0.8',
            }
        }

        # Randomly choose browser headers
        browser_type = random.choice(list(browsers.keys()))
        headers.update(browsers[browser_type])

        # Add junk headers that some WAFs use for fingerprinting
        junk_header_names = [f'X-{random.randrange(1000, 9999)}-Extra',
                             f'X-Forwarded-For-{random.randrange(100, 999)}']

        for name in junk_header_names:
            headers[name] = str(random.randrange(1000000, 9999999))

        # Handle WAF-specific bypasses
        if 'cloudflare' in self.detect_waf(url):
            headers.update(self._cloudflare_bypass_headers())

        return headers

    def detect_waf(self, url: str) -> List[str]:
        """Detect which WAF is being used by a site."""
        try:
            detected_wafs = []
            response = requests.get(url, headers={'User-Agent': self.ua.random}, timeout=10)

            # Check for WAF signatures in headers and cookies
            headers = response.headers
            cookies = response.cookies

            # Cloudflare detection
            if (any(h for h in headers if 'cf-' in h.lower()) or
                    any(c for c in cookies if 'cf_' in c.lower()) or
                    '__cfduid' in [c.name for c in cookies]):
                detected_wafs.append('cloudflare')

            # Akamai detection
            if (any(h for h in headers if 'akamai' in h.lower()) or
                    'x-akamai-transformed' in [h.lower() for h in headers]):
                detected_wafs.append('akamai')

            # Incapsula detection
            if ('x-iinfo' in [h.lower() for h in headers] or
                    'visid_incap' in [c.name for c in cookies] or
                    'incap_ses' in [c.name for c in cookies]):
                detected_wafs.append('incapsula')

            # DataDome detection
            if ('x-datadome' in [h.lower() for h in headers] or
                    'datadome' in [c.name for c in cookies]):
                detected_wafs.append('datadome')

            return detected_wafs

        except Exception as e:
            logger.error(f"Error detecting WAF: {e}")
            return []

    def _cloudflare_bypass_headers(self) -> Dict[str, str]:
        """Generate headers specifically to bypass Cloudflare protection."""
        return {
            'CF-IPCountry': random.choice(['US', 'CA', 'GB', 'AU']),
            'CF-Visitor': '{"scheme":"https"}',
            'Cookie': f'cf_clearance={self._generate_fake_cf_clearance()}'
        }

    def _generate_fake_cf_clearance(self) -> str:
        """Generate a realistic-looking CF clearance cookie."""
        # This is for demonstration only and won't actually bypass Cloudflare
        timestamp = int(time.time())
        random_hex = ''.join(random.choice('0123456789abcdef') for _ in range(32))
        return f"{random_hex}-{timestamp}-1-0-{random.randint(100, 999)}"

    def handle_advanced_captcha(self, site_key: str, url: str, captcha_type: str = "recaptcha") -> Optional[str]:
        """Enhanced CAPTCHA solver with multiple providers and fallbacks.

        Args:
            site_key: CAPTCHA site key
            url: Page URL
            captcha_type: Type of CAPTCHA ("recaptcha", "hcaptcha", "turnstile", etc.)

        Returns:
            CAPTCHA solution token or None if failed
        """
        start_time = time.time()
        solution = None

        try:
            # Try primary solver - CapSolver
            if os.getenv('CAPSOLVER_API_KEY'):
                logger.info(f"Attempting to solve {captcha_type} with CapSolver")
                capsolver.api_key = os.getenv('CAPSOLVER_API_KEY')

                if captcha_type.lower() == "recaptcha":
                    solution = capsolver.solve({
                        "type": "ReCaptchaV2TaskProxyLess",
                        "websiteURL": url,
                        "websiteKey": site_key
                    })
                elif captcha_type.lower() == "hcaptcha":
                    solution = capsolver.solve({
                        "type": "HCaptchaTaskProxyLess",
                        "websiteURL": url,
                        "websiteKey": site_key
                    })

                if solution and solution.get('gRecaptchaResponse'):
                    solve_time = time.time() - start_time
                    self._log_captcha_metrics(
                        site_key=site_key,
                        url=url,
                        captcha_type=captcha_type,
                        solver="capsolver",
                        success=True,
                        solve_time=solve_time
                    )
                    return solution.get('gRecaptchaResponse')

            # Fallback to 2Captcha
            if os.getenv('TWOCAPTCHA_API_KEY'):
                logger.info(f"Attempting to solve {captcha_type} with 2Captcha")
                result = self.captcha_solver.recaptcha(
                    sitekey=site_key,
                    url=url
                )

                if result and 'code' in result:
                    solve_time = time.time() - start_time
                    self._log_captcha_metrics(
                        site_key=site_key,
                        url=url,
                        captcha_type=captcha_type,
                        solver="2captcha",
                        success=True,
                        solve_time=solve_time
                    )
                    return result['code']

            # Last resort - Anti-Captcha
            if os.getenv('ANTI_CAPTCHA_API_KEY'):
                logger.info(f"Attempting to solve {captcha_type} with Anti-Captcha")
                solver = recaptchaV2Proxyless()
                solver.set_key(os.getenv('ANTI_CAPTCHA_API_KEY'))
                solver.set_website_url(url)
                solver.set_website_key(site_key)

                solution = solver.solve_and_return_solution()
                if solution:
                    solve_time = time.time() - start_time
                    self._log_captcha_metrics(
                        site_key=site_key,
                        url=url,
                        captcha_type=captcha_type,
                        solver="anticaptcha",
                        success=True,
                        solve_time=solve_time
                    )
                    return solution

            # All automated solvers failed
            logger.warning(f"All CAPTCHA solvers failed for {captcha_type}")
            self._log_captcha_metrics(
                site_key=site_key,
                url=url,
                captcha_type=captcha_type,
                solver="all",
                success=False,
                solve_time=time.time() - start_time
            )
            return None

        except Exception as e:
            logger.error(f"CAPTCHA solving error: {e}")
            self._log_captcha_metrics(
                site_key=site_key,
                url=url,
                captcha_type=captcha_type,
                solver="error",
                success=False,
                solve_time=time.time() - start_time,
                error=str(e)
            )
            return None

    def _log_captcha_metrics(self, **kwargs):
        """Log CAPTCHA solving metrics to the database."""
        if not self.db_conn:
            return

        try:
            cursor = self.db_conn.cursor()
            cursor.execute("""
                INSERT INTO captcha_events
                (site_key, url, captcha_type, solver, success, solve_time, error_message, timestamp)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                kwargs.get('site_key'),
                kwargs.get('url'),
                kwargs.get('captcha_type'),
                kwargs.get('solver'),
                kwargs.get('success'),
                kwargs.get('solve_time'),
                kwargs.get('error', ''),
                datetime.now()
            ))
            self.db_conn.commit()
        except Exception as e:
            logger.error(f"Failed to log CAPTCHA metrics: {e}")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    async def spoof_hardware_metrics(self, page: Page) -> None:
        """Spoof hardware-level metrics that are used for advanced fingerprinting.

        Handles advanced hardware metrics like battery status, device memory,
        CPU cores, and sensor data that modern anti-bot systems check.
        """
        if not page:
            return

        # Create a realistic hardware profile
        hardware_profile = {
            'deviceMemory': random.choice([2, 4, 8, 16, 32]),
            'hardwareConcurrency': random.choice([2, 4, 6, 8, 12, 16]),
            'battery': {
                'charging': random.choice([True, False]),
                'chargingTime': random.randint(0, 1000) if random.choice([True, False]) else float('Infinity'),
                'dischargingTime': random.randint(1000, 10000),
                'level': round(random.uniform(0.1, 1.0), 2)
            },
            'connection': {
                'downlink': round(random.uniform(1.5, 15.0), 1),
                'effectiveType': random.choice(['slow-2g', '2g', '3g', '4g']),
                'rtt': random.randint(50, 300),
                'saveData': random.choice([True, False])
            },
            'sensors': {
                'gyroscope': [round(random.uniform(-1, 1), 5) for _ in range(3)],
                'accelerometer': [round(random.uniform(-10, 10), 5) for _ in range(3)]
            },
            'timezone': {
                'offset': random.choice([-480, -420, -360, -300, -240, -180, -120, -60, 0, 60, 120, 180, 240, 300, 360, 420, 480]),
                'timezoneId': random.choice(['America/Los_Angeles', 'America/Denver', 'America/Chicago', 'America/New_York',
                                            'Europe/London', 'Europe/Berlin', 'Asia/Dubai', 'Asia/Tokyo'])
            }
        }

        # Apply the hardware profile to the page
        await page.evaluate("""
        (profile) => {
            // Override device memory
            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => profile.deviceMemory
            });

            // Override CPU cores
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => profile.hardwareConcurrency
            });

            // Override battery API if it exists
            if ('getBattery' in navigator) {
                navigator.getBattery = function() {
                    return Promise.resolve({
                        charging: profile.battery.charging,
                        chargingTime: profile.battery.chargingTime,
                        dischargingTime: profile.battery.dischargingTime,
                        level: profile.battery.level,
                        addEventListener: function() {},
                        removeEventListener: function() {}
                    });
                };
            }

            // Override connection API
            if ('connection' in navigator) {
                Object.defineProperties(navigator.connection, {
                    'downlink': { get: () => profile.connection.downlink },
                    'effectiveType': { get: () => profile.connection.effectiveType },
                    'rtt': { get: () => profile.connection.rtt },
                    'saveData': { get: () => profile.connection.saveData }
                });
            }

            // Override timezone info
            Object.defineProperty(Intl, 'DateTimeFormat', {
                get: () => function() {
                    return {
                        resolvedOptions: () => ({
                            timeZone: profile.timezone.timezoneId,
                            locale: 'en-US'
                        })
                    };
                }
            });

            // Override Date to maintain timezone consistency
            const originalDate = Date;
            const dateHandler = {
                construct: function(target, args) {
                    const date = new target(...args);
                    date.getTimezoneOffset = () => profile.timezone.offset;
                    return date;
                }
            };
            Date = new Proxy(originalDate, dateHandler);

            // Disable access to sensors when possible
            if ('permissions' in navigator) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(args) {
                    if (args.name === 'accelerometer' ||
                        args.name === 'gyroscope' ||
                        args.name === 'magnetometer') {
                        return Promise.resolve({state: "denied"});
                    }
                    return originalQuery.call(this, args);
                };
            }
        }
        """, hardware_profile)

        logger.info("Applied hardware-level fingerprint spoofing")

    async def emulate_mobile_device(self, page: Page, device_type: str = None) -> None:
        """Emulate a specific mobile device to evade mobile-specific detections.

        Args:
            page: The Playwright page to manipulate
            device_type: Device to emulate (e.g. 'iPhone X', 'Pixel 3', etc.)
                         If None, a random device will be selected
        """
        if not page:
            return

        # Device emulation presets
        devices = {
            'iPhone X': {
                'userAgent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1',
                'viewport': {'width': 375, 'height': 812, 'deviceScaleFactor': 3, 'isMobile': True, 'hasTouch': True, 'isLandscape': False},
                'geolocation': {'latitude': 37.332331, 'longitude': -122.031219},
                'platform': 'iPhone'
            },
            'Pixel 3': {
                'userAgent': 'Mozilla/5.0 (Linux; Android 10; Pixel 3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36',
                'viewport': {'width': 393, 'height': 786, 'deviceScaleFactor': 2.75, 'isMobile': True, 'hasTouch': True, 'isLandscape': False},
                'geolocation': {'latitude': 40.712776, 'longitude': -74.005974},
                'platform': 'Android'
            },
            'iPad Pro': {
                'userAgent': 'Mozilla/5.0 (iPad; CPU OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                'viewport': {'width': 1024, 'height': 1366, 'deviceScaleFactor': 2, 'isMobile': True, 'hasTouch': True, 'isLandscape': False},
                'geolocation': {'latitude': 51.507351, 'longitude': -0.127758},
                'platform': 'iPad'
            },
            'Galaxy S20': {
                'userAgent': 'Mozilla/5.0 (Linux; Android 11; SM-G981U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Mobile Safari/537.36',
                'viewport': {'width': 360, 'height': 800, 'deviceScaleFactor': 3, 'isMobile': True, 'hasTouch': True, 'isLandscape': False},
                'geolocation': {'latitude': 35.689487, 'longitude': 139.691706},
                'platform': 'Android'
            }
        }

        # Select device
        if not device_type or device_type not in devices:
            device_type = random.choice(list(devices.keys()))

        device = devices[device_type]

        # Apply device settings
        await page.setViewport(device['viewport'])
        await page.setUserAgent(device['userAgent'])

        # Set geolocation
        context = page.context
        await context.setGeolocation(device['geolocation'])

        # Apply device-specific overrides
        await page.evaluate("""
        (device) => {
            // Override navigator platform
            Object.defineProperty(navigator, 'platform', {
                get: () => device.platform
            });

            // Override touch points
            if (device.viewport.hasTouch) {
                Object.defineProperty(navigator, 'maxTouchPoints', {
                    get: () => 5
                });
            }

            // Override mobile checks
            window.ontouchstart = device.viewport.hasTouch ? {} : undefined;

            // Mobile-specific CSS media query matching
            if (device.viewport.isMobile) {
                const originalMatchMedia = window.matchMedia;
                window.matchMedia = function(query) {
                    if (query.includes('(hover: none)') ||
                        query.includes('(pointer: coarse)') ||
                        query.includes('(max-device-width)')) {
                        return {matches: true, media: query};
                    }
                    return originalMatchMedia(query);
                };
            }
        }
        """, device)

        logger.info(f"Emulated mobile device: {device_type}")

    async def mitigate_dynamic_layout(self, url: str) -> Dict[str, Any]:
        """Handle dynamic layout changes and selector randomization.

        Uses AI-based content extraction to identify elements regardless of CSS class changes.

        Args:
            url: URL to parse

        Returns:
            Dictionary with extracted content
        """
        # Get page content
        async with aiohttp.ClientSession() as session:
            headers = self.waf_bypass_headers(url)
            async with session.get(url, headers=headers) as response:
                content = await response.text()

        # Parse content with BeautifulSoup
        soup = BeautifulSoup(content, 'html.parser')

        # Initialize result dictionary
        result = {
            'title': None,
            'main_content': None,
            'navigation': [],
            'comments': [],
            'timestamps': []
        }

        # Title extraction - relatively stable across redesigns
        title_element = soup.find('title')
        if title_element:
            result['title'] = title_element.text.strip()

        # Extract main content using multiple fallback strategies
        # Strategy 1: Look for common content containers
        content_selectors = [
            'div[role="main"]',
            'main',
            'article',
            '.content',
            '#content',
            '.post',
            '.entry'
        ]

        for selector in content_selectors:
            main_content = soup.select_one(selector)
            if main_content and len(main_content.text.strip()) > 100:  # Minimum content length
                result['main_content'] = main_content.text.strip()
                break

        # Strategy 2: Semantic layout analysis (if strategy 1 fails)
        if not result['main_content']:
            # Find the div with the most text content - likely the main content
            divs = soup.find_all('div')
            main_div = None
            max_length = 0

            for div in divs:
                text = div.text.strip()
                if len(text) > max_length:
                    max_length = len(text)
                    main_div = div

            if main_div and max_length > 100:
                result['main_content'] = main_div.text.strip()

        # Extract navigation links - usually in header/nav elements
        nav_elements = soup.find_all(['nav', 'header'])
        for nav in nav_elements:
            links = nav.find_all('a')
            for link in links:
                if link.text.strip() and link.get('href'):
                    result['navigation'].append({
                        'text': link.text.strip(),
                        'href': link.get('href')
                    })

        # Extract comments using multiple fallback strategies
        # Strategy 1: Look for common comment containers
        comment_selectors = [
            '.comment',
            '.comments',
            '[data-testid="comment"]',
            '[data-name="comment-item"]',
            '.user-comment',
            '.review'
        ]

        for selector in comment_selectors:
            comments = soup.select(selector)
            if comments:
                for comment in comments:
                    # Extract comment text, author, and timestamp if available
                    comment_data = {'text': comment.text.strip()}

                    # Try to find author
                    author_selectors = ['.author', '.username', '[data-username]', '.user']
                    for author_selector in author_selectors:
                        author = comment.select_one(author_selector)
                        if author:
                            comment_data['author'] = author.text.strip()
                            break

                    # Try to find timestamp
                    time_selectors = ['time', '.timestamp', '.date', '[datetime]']
                    for time_selector in time_selectors:
                        timestamp = comment.select_one(time_selector)
                        if timestamp:
                            comment_data['timestamp'] = timestamp.text.strip()
                            break

                    result['comments'].append(comment_data)
                break  # Stop if we found comments with the current selector

        # Extract all timestamps on the page for temporal analysis
        time_elements = soup.find_all('time') + soup.select('[datetime]')
        for time_elem in time_elements:
            datetime_value = time_elem.get('datetime') or time_elem.text.strip()
            if datetime_value:
                result['timestamps'].append(datetime_value)

        return result

    async def setup_proxy_chaining(self) -> None:
        """Set up multi-layer proxy chaining for enhanced anonymity.

        Creates a chain of proxies (e.g., residential → datacenter → Tor) to make
        tracking more difficult and improve residential IP usage efficiency.
        """
        try:
            if not self.config.get('enable_proxy_chaining', False):
                logger.info("Proxy chaining disabled in configuration")
                return

            # Load proxy tiers
            residential_proxies = self.config.get('residential_proxies', [])
            datacenter_proxies = self.config.get('datacenter_proxies', [])

            if not residential_proxies or not datacenter_proxies:
                logger.warning("Insufficient proxies for chaining")
                return

            # Select one proxy from each tier
            residential = random.choice(residential_proxies)
            datacenter = random.choice(datacenter_proxies)

            # Build proxy chain configuration
            proxy_chain = [{
                'host': residential['host'],
                'port': residential['port'],
                'username': residential.get('username'),
                'password': residential.get('password'),
                'protocol': residential.get('protocol', 'http')
            }, {
                'host': datacenter['host'],
                'port': datacenter['port'],
                'username': datacenter.get('username'),
                'password': datacenter.get('password'),
                'protocol': datacenter.get('protocol', 'http')
            }]

            # Add Tor as final hop if enabled
            if self.config.get('use_tor', False):
                proxy_chain.append({
                    'host': '127.0.0.1',
                    'port': 9050,
                    'protocol': 'socks5'
                })

            # Set up the proxy chain
            self.current_proxy_chain = proxy_chain

            # Format for curl_cffi
            formatted_chain = self._format_proxy_chain_for_curl()

            logger.info(f"Established {len(proxy_chain)}-hop proxy chain")
            return formatted_chain

        except Exception as e:
            logger.error(f"Failed to set up proxy chain: {e}")
            return None

    def _format_proxy_chain_for_curl(self) -> str:
        """Format proxy chain for curl_cffi."""
        if not hasattr(self, 'current_proxy_chain') or not self.current_proxy_chain:
            return None

        proxies = []
        for proxy in self.current_proxy_chain:
            auth = f"{proxy.get('username')}:{proxy.get('password')}@" if proxy.get('username') else ""
            proxies.append(f"{proxy.get('protocol', 'http')}://{auth}{proxy['host']}:{proxy['port']}")

        return ";".join(proxies)

    def check_for_legal_honeypots(self, content: str, url: str) -> bool:
        """Check for legal honeypots and data poisoning.

        Args:
            content: HTML content to analyze
            url: URL being scraped

        Returns:
            True if legal honeypot detected, False otherwise
        """
        # Signs of deliberate data poisoning
        suspicious = False
        soup = BeautifulSoup(content, 'html.parser')

        # Check for hidden copyright traps
        copyright_traps = soup.find_all(string=lambda text: 'copyright trap' in (text or '').lower())
        if copyright_traps:
            logger.warning(f"Possible copyright trap detected at {url}")
            suspicious = True

        # Check for abnormal data patterns that indicate honeypots
        if 'price' in url.lower() or 'product' in url.lower():
            # Look for prices
            price_elements = soup.select('[itemprop="price"], .price, [data-price]')
            for price_elem in price_elements:
                price_text = price_elem.text.strip().replace('$', '').replace(',', '')
                try:
                    price = float(price_text)
                    # Suspiciously high or low prices
                    if price > 1000000 or price < 0.01:
                        logger.warning(f"Suspicious price value detected: {price} at {url}")
                        suspicious = True
                except ValueError:
                    pass

        # Check for hidden tracking pixels that might be used in lawsuits
        tracking_pixels = soup.select('img[width="1"][height="1"], img[style*="display:none"]')
        if tracking_pixels:
            logger.info(f"Tracking pixels detected: {len(tracking_pixels)} at {url}")

        # Check for hidden form fields designed to catch bots
        honeypot_fields = soup.select('input[name*="hp"], input[style*="display:none"]')
        if honeypot_fields:
            logger.warning(f"Potential form honeypot fields detected at {url}")
            suspicious = True

        # Check for unusual or gibberish content that might be used to identify copies
        paragraphs = soup.find_all('p')
        if paragraphs:
            text_sample = ' '.join([p.text for p in paragraphs[:3]])
            if len(text_sample) > 50:
                # Check for random character sequences that might be fingerprinting
                import re
                random_strings = re.findall(r'[A-Za-z0-9]{20,}', text_sample)
                if random_strings:
                    logger.warning(f"Unusual text patterns detected at {url}: {random_strings}")
                    suspicious = True

        return suspicious

    def rotate_user_agent(self) -> str:
        """Get a random, realistic user agent string (instance method)."""
        try:
            return self.ua.random
        except Exception as e:
            logger.warning(f"Error rotating user agent: {e}")
            # Fallback user agents if fake-useragent fails
            fallback_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15',
                'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/20100101 Firefox/93.0'
            ]
            return random.choice(fallback_agents)

    def get_enhanced_headers(self) -> Dict[str, str]:
        """Get enhanced browser headers that help bypass detection."""
        try:
            user_agent = self.rotate_user_agent()

            # Base headers that most browsers use
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
            }

            # Add Chrome-specific headers if using a Chrome UA
            if 'Chrome' in user_agent and 'Firefox' not in user_agent:
                headers.update({
                    'sec-ch-ua': '"Google Chrome";v="101", " Not;A Brand";v="99", "Chromium";v="101"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"' if 'Windows' in user_agent else '"macOS"' if 'Mac' in user_agent else '"Linux"'
                })

            logger.debug(f"Enhanced headers generated with UA: {user_agent[:20]}...")
            return headers

        except Exception as e:
            logger.error(f"Error generating enhanced headers: {e}")
            # Return a minimal set of headers as fallback
            return {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36'
            }

    def get_session_with_proxy(self) -> requests.Session:
        """Get a requests session with a proxy configured."""
        session = requests.Session()

        try:
            if not self.proxy_pool:
                logger.warning("No proxies available, using direct connection")
                return session

            # Select a proxy from the pool
            proxy = random.choice(self.proxy_pool)
            proxy_url = f"{proxy['protocol']}://"

            # Add authentication if provided
            if proxy.get('username') and proxy.get('password'):
                proxy_url += f"{proxy['username']}:{proxy['password']}@"

            proxy_url += f"{proxy['host']}:{proxy['port']}"

            # Configure session with proxy
            session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }

            # Add enhanced headers
            headers = self.get_enhanced_headers()
            for header, value in headers.items():
                session.headers[header] = value

            # Set reasonable timeout
            session.request = lambda method, url, **kwargs: super(requests.Session, session).request(
                method, url, **kwargs, timeout=kwargs.get('timeout', 30)
            )

            logger.debug(f"Created session with proxy: {proxy['host']}:{proxy['port']}")

            return session

        except Exception as e:
            logger.error(f"Error setting up session with proxy: {e}")
            logger.debug(f"Session setup error details: {traceback.format_exc()}")
            return session

    def detect_honeypot(self, response) -> bool:
        """
        Detect if a page is likely a honeypot based on common indicators.

        Args:
            response: The requests/aiohttp response object

        Returns:
            True if the page is likely a honeypot, False otherwise
        """
        try:
            # Get page content
            if hasattr(response, 'text'):
                # requests Response object
                content = response.text
            elif hasattr(response, 'content'):
                # Some custom response object with content attribute
                content = response.content.decode('utf-8', errors='ignore')
            else:
                logger.warning("Unknown response object type, can't detect honeypot")
                return False

            # Parse HTML
            soup = BeautifulSoup(content, 'html.parser')

            # Check for hidden elements that could be honeypots
            honeypot_indicators = [
                # Hidden links
                soup.select('a[style*="display:none"]'),
                soup.select('a[style*="visibility:hidden"]'),
                soup.select('a[style*="opacity:0"]'),

                # Hidden inputs
                soup.select('input[style*="display:none"]'),

                # Hidden divs with links
                soup.select('div[style*="display:none"] a'),

                # Hidden forms
                soup.select('form[style*="display:none"]'),
            ]

            # Check if any indicators were found
            for indicator in honeypot_indicators:
                if indicator:
                    logger.warning(f"Honeypot indicator detected: {len(indicator)} hidden elements")
                    return True

            # Check for unusual patterns in URLs (parameters that look like tracking)
            links = soup.find_all('a', href=True)
            tracking_param_pattern = ['clickid', 'trackid', 'affiliate', 'bot', 'tracking']

            for link in links:
                href = link.get('href', '')
                if any(param in href.lower() for param in tracking_param_pattern):
                    logger.warning(f"Potential tracking link found: {href[:50]}")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error detecting honeypot: {e}")
            logger.debug(f"Honeypot detection error: {traceback.format_exc()}")
            return False

    async def make_request(self, url: str, method: str = 'get', retries: int = None, **kwargs) -> Optional[requests.Response]:
        """
        Make HTTP request with anti-detection measures and retry logic.

        Args:
            url: URL to request
            method: HTTP method (get, post, etc.)
            retries: Number of retries (defaults to self.max_retries)
            **kwargs: Additional arguments to pass to requests

        Returns:
            Response object or None if all attempts failed
        """
        if retries is None:
            retries = self.max_retries

        session = self.get_session_with_proxy()
        method_func = getattr(session, method.lower())
        headers = kwargs.pop('headers', self.get_enhanced_headers())

        for attempt in range(1, retries + 1):
            try:
                logger.debug(f"Request attempt {attempt}/{retries} to {url}")

                # Add jitter to avoid detection of regular patterns
                if attempt > 1 and self.retry_delay > 0:
                    delay = self.retry_delay
                    if self.exponential_backoff:
                        delay = delay * (2 ** (attempt - 1))
                    # Add jitter (±30%)
                    delay = delay * random.uniform(0.7, 1.3)
                    await asyncio.sleep(delay)

                # Make the request
                response = method_func(url, headers=headers, **kwargs)
                response.raise_for_status()

                # Check for honeypot
                if self.detect_honeypot(response):
                    logger.warning(f"Honeypot detected at {url}")
                    # You may want to handle this differently than a retry
                    continue

                return response

            except Timeout:
                logger.warning(f"Request timeout on attempt {attempt}/{retries}")
            except ConnectionError:
                logger.warning(f"Connection error on attempt {attempt}/{retries}")
            except ProxyError:
                logger.warning(f"Proxy error on attempt {attempt}/{retries}, rotating proxy")
                session = self.get_session_with_proxy()  # Get a new session with different proxy
                method_func = getattr(session, method.lower())
            except HTTPError as e:
                status_code = getattr(e.response, 'status_code', None)
                logger.warning(f"HTTP error {status_code} on attempt {attempt}/{retries}")

                # Handle specific status codes
                if status_code == 403:
                    logger.warning("Access forbidden (403), rotating proxy and UA")
                    session = self.get_session_with_proxy()
                    method_func = getattr(session, method.lower())
                    headers = self.get_enhanced_headers()
                elif status_code == 429:
                    logger.warning("Rate limited (429), adding longer delay")
                    await asyncio.sleep(self.retry_delay * 3)
            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt}/{retries}: {e}")
                logger.debug(f"Request error details: {traceback.format_exc()}")

        logger.error(f"All {retries} request attempts to {url} failed")
        return None

# Helper functions to use outside the class
def create_enhanced_headers() -> Dict[str, str]:
    """Create enhanced HTTP headers that help bypass detection systems."""
    shield = ScrapingShield()
    return shield.get_enhanced_headers()

def rotate_proxies() -> Optional[str]:
    """Rotate and get a new proxy from the configured pool."""
    shield = ScrapingShield()
    if not shield.proxy_pool:
        return None
    proxy = random.choice(shield.proxy_pool)
    return f"{proxy['protocol']}://{proxy['host']}:{proxy['port']}"

def detect_honeypot(response) -> bool:
    """Detect if a response is from a honeypot."""
    shield = ScrapingShield()
    return shield.detect_honeypot(response)











"""
ScrapingShield - Advanced anti-scraping protection for web scrapers.
Provides comprehensive protection against detection and blocking.
"""

import logging
import random
import time
import ssl
import socket
import re
from typing import Dict, List, Optional, Union, Any, Tuple
import asyncio
import json
import os

import requests
from bs4 import BeautifulSoup
import curl_cffi
from curl_cffi.requests import Session as CurlSession

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ScrapingShield:
    """
    Comprehensive anti-scraping protection for web scrapers.
    Implements advanced techniques to avoid detection and blocking.
    """

    def __init__(self, config_path: str = "scraping_shield_config.json"):
        """
        Initialize the scraping shield.

        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.user_agents = self._load_user_agents()
        self.proxies = self._load_proxies()
        self.current_proxy_index = 0
        self.curl_session = None
        self.session = requests.Session()
        self.headers = self._get_default_headers()

        # Initialize curl session if enabled
        if self.config.get("use_curl_cffi", True):
            self._init_curl_session()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        default_config = {
            "use_curl_cffi": True,
            "default_browser_fingerprint": "chrome110",
            "proxy_rotation_frequency": 10,  # Rotate every 10 requests
            "request_delay_min": 1.0,
            "request_delay_max": 5.0,
            "captcha_service": "2captcha",
            "captcha_api_key": "",
            "use_residential_proxies": True,
            "max_retries": 3,
            "honeypot_detection": True
        }

        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
                    logger.info(f"Loaded configuration from {self.config_path}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")

        return default_config

    def _load_user_agents(self) -> List[str]:
        """Load user agents from configuration or use defaults."""
        default_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 OPR/78.0.4093.147",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
        ]

        user_agents_file = self.config.get('user_agents_file')
        if user_agents_file and os.path.exists(user_agents_file):
            try:
                with open(user_agents_file, 'r') as f:
                    return [line.strip() for line in f if line.strip()]
            except Exception as e:
                logger.error(f"Error loading user agents file: {e}")

        return default_agents

    def _load_proxies(self) -> List[str]:
        """Load proxies from configuration or use defaults."""
        default_proxies = []

        proxies_file = self.config.get('proxies_file')
        if proxies_file and os.path.exists(proxies_file):
            try:
                with open(proxies_file, 'r') as f:
                    return [line.strip() for line in f if line.strip()]
            except Exception as e:
                logger.error(f"Error loading proxies file: {e}")

        return default_proxies

    def _get_default_headers(self) -> Dict[str, str]:
        """Get default headers for requests."""
        return {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }

    def _init_curl_session(self) -> None:
        """Initialize curl_cffi session with browser fingerprinting."""
        try:
            browser = self.config.get("default_browser_fingerprint", "chrome110")
            self.curl_session = CurlSession(impersonate=browser)
            logger.info(f"Initialized curl_cffi session with {browser} fingerprint")
        except Exception as e:
            logger.error(f"Error initializing curl_cffi session: {e}")
            self.curl_session = None

    def get_random_user_agent(self) -> str:
        """Get a random user agent."""
        return random.choice(self.user_agents)

    def get_next_proxy(self) -> Optional[str]:
        """Get the next proxy from the rotation."""
        if not self.proxies:
            return None

        proxy = self.proxies[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
        return proxy

    async def rotate_proxy(self) -> Optional[str]:
        """Rotate to the next proxy and return it."""
        proxy = self.get_next_proxy()

        if proxy:
            # Update the session with the new proxy
            self.session.proxies = {
                "http": proxy,
                "https": proxy
            }

            # Update curl session if available
            if self.curl_session:
                try:
                    self.curl_session.close()
                    browser = self.config.get("default_browser_fingerprint", "chrome110")
                    self.curl_session = CurlSession(
                        impersonate=browser,
                        proxies={"http": proxy, "https": proxy}
                    )
                except Exception as e:
                    logger.error(f"Error updating curl session proxy: {e}")

            logger.info(f"Rotated to new proxy: {proxy}")

        return proxy

    def waf_bypass_headers(self, url: str) -> Dict[str, str]:
        """
        Get headers designed to bypass WAF protection.

        Args:
            url: Target URL

        Returns:
            Headers dictionary with WAF bypass techniques
        """
        # Get a random user agent
        user_agent = self.get_random_user_agent()

        # Generate a random client identifier
        client_id = f"{random.randint(1000, 9999)}.{random.randint(1000, 9999)}"

        # Add jitter to cache control
        jitter = str(random.randint(1, 1000))

        headers = {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": f"max-age=0,no-cache,no-store,must-revalidate,{jitter}",
            "Pragma": "no-cache",
            "TE": "Trailers",
            "X-Client-ID": client_id
        }

        # Add random headers to further confuse WAFs
        if random.random() < 0.3:  # 30% chance
            headers["X-Forwarded-For"] = f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"

        if random.random() < 0.3:  # 30% chance
            headers["X-Requested-With"] = "XMLHttpRequest"

        return headers

    def spoof_tls_fingerprint(self, browser: str = "chrome") -> ssl.SSLContext:
        """
        Create an SSL context that spoofs a specific browser's TLS fingerprint.

        Args:
            browser: Browser to mimic ("chrome", "firefox", "safari", etc.)

        Returns:
            Configured SSL context
        """
        # Create SSL context
        context = ssl.create_default_context()

        # Configure based on browser
        if browser == "chrome":
            # Chrome cipher suites
            ciphers = (
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:'
                'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:'
                'DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384'
            )
            context.set_ciphers(ciphers)
            context.options |= ssl.OP_NO_SSLv2
            context.options |= ssl.OP_NO_SSLv3
            context.options |= ssl.OP_NO_TLSv1
            context.options |= ssl.OP_NO_TLSv1_1

        elif browser == "firefox":
            # Firefox cipher suites
            ciphers = (
                'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:'
                'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:'
                'DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:TLS_AES_128_GCM_SHA256:'
                'TLS_CHACHA20_POLY1305_SHA256:TLS_AES_256_GCM_SHA384'
            )
            context.set_ciphers(ciphers)
            context.options |= ssl.OP_NO_SSLv2
            context.options |= ssl.OP_NO_SSLv3
            context.options |= ssl.OP_NO_TLSv1
            context.options |= ssl.OP_NO_TLSv1_1

        return context

    def check_for_legal_honeypots(self, content: str, url: str) -> bool:
        """
        Check if content contains legal honeypots.

        Args:
            content: Content to check
            url: URL of the content

        Returns:
            True if honeypot detected, False otherwise
        """
        if not self.config.get("honeypot_detection", True):
            return False

        # Patterns that might indicate legal honeypots
        honeypot_patterns = [
            r"copyright.*reserved",
            r"terms.*service",
            r"not.*authorized",
            r"forbidden.*access",
            r"legal.*action",
            r"lawsuit",
            r"cease.*desist",
            r"unauthorized.*use",
            r"prohibited.*scraping",
            r"automated.*access",
            r"bot.*detection"
        ]

        for pattern in honeypot_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                logger.warning(f"Potential legal honeypot detected in content from {url}")
                return True

        return False

    def detect_honeypot_elements(self, html: str) -> List[str]:
        """
        Detect potential honeypot elements in HTML.

        Args:
            html: HTML content to analyze

        Returns:
            List of detected honeypot selectors
        """
        honeypot_selectors = []

        # Parse HTML
        soup = BeautifulSoup(html, "html.parser")

        # Check for hidden elements that might be honeypots
        hidden_elements = soup.select('[style*="display: none"], [style*="visibility: hidden"], [style*="opacity: 0"]')
        for element in hidden_elements:
            honeypot_selectors.append(str(element.get("id", "")) or str(element.get("class", "")))

        # Check for elements with suspicious class names
        suspicious_classes = soup.select('.hidden, .trap, .honeypot, .bot-check, .captcha-field')
        for element in suspicious_classes:
            honeypot_selectors.append(str(element.get("id", "")) or str(element.get("class", "")))

        return honeypot_selectors

    def random_delay(self, min_delay: Optional[float] = None, max_delay: Optional[float] = None) -> float:
        """
        Add a random delay to simulate human behavior.

        Args:
            min_delay: Minimum delay in seconds (defaults to config value)
            max_delay: Maximum delay in seconds (defaults to config value)

        Returns:
            The actual delay applied
        """
        min_delay = min_delay or self.config.get("request_delay_min", 1.0)
        max_delay = max_delay or self.config.get("request_delay_max", 5.0)

        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        return delay

    def cleanup(self) -> None:
        """Clean up resources."""
        if self.curl_session:
            try:
                self.curl_session.close()
            except Exception as e:
                logger.error(f"Error closing curl session: {e}")

        if self.session:
            try:
                self.session.close()
            except Exception as e:
                logger.error(f"Error closing requests session: {e}")
