#!/usr/bin/env python3
"""
Test script for the anti-scraping implementation.
"""

import logging
import argparse
import json
import time

# Updated import paths
from tradingview.ideas_scraper import EnhancedIdeasScraper
from scraping_shield import ScrapingShield

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_tradingview_ideas_scraper(symbol: str = "BTCUSD", pages: int = 1) -> None:
    """
    Test the TradingView ideas scraper with anti-scraping measures.
    
    Args:
        symbol: Trading symbol to scrape ideas for
        pages: Number of pages to scrape
    """
    logger.info(f"Testing TradingView ideas scraper for symbol {symbol} ({pages} pages)")
    
    try:
        # Initialize the scraper
        scraper = EnhancedIdeasScraper()
        
        # Scrape ideas
        start_time = time.time()
        ideas = scraper.scrape(symbol=symbol, startPage=1, endPage=pages)
        end_time = time.time()
        
        # Calculate statistics
        elapsed_time = end_time - start_time
        ideas_count = len(ideas)
        
        logger.info(f"Scraped {ideas_count} ideas in {elapsed_time:.2f} seconds")
        
        # Get sentiment analysis
        sentiment = scraper.get_sentiment_analysis(ideas)
        
        # Print results
        print("\n=== TradingView Ideas Scraper Test Results ===")
        print(f"Symbol: {symbol}")
        print(f"Pages scraped: {pages}")
        print(f"Ideas found: {ideas_count}")
        print(f"Time taken: {elapsed_time:.2f} seconds")
        print(f"Ideas per second: {ideas_count / elapsed_time:.2f}")
        print("\nSentiment Analysis:")
        print(f"  Positive: {sentiment['positive']} ({sentiment['positive_percent']:.1f}%)")
        print(f"  Negative: {sentiment['negative']} ({sentiment['negative_percent']:.1f}%)")
        print(f"  Neutral: {sentiment['neutral']} ({sentiment['neutral_percent']:.1f}%)")
        print(f"  Overall: {sentiment['overall']}")
        
        # Save results to file
        with open(f"tradingview_ideas_{symbol}.json", "w") as f:
            json.dump(ideas, f, indent=2)
        
        logger.info(f"Saved results to tradingview_ideas_{symbol}.json")
        
    except Exception as e:
        logger.error(f"Error testing TradingView ideas scraper: {e}")
    finally:
        # Clean up
        if 'scraper' in locals():
            scraper.cleanup()


def test_scraping_shield() -> None:
    """Test the ScrapingShield functionality."""
    logger.info("Testing ScrapingShield")
    
    try:
        # Initialize the shield
        shield = ScrapingShield()
        
        # Test user agent rotation
        user_agents = [shield.rotate_user_agent() for _ in range(5)]
        
        # Test WAF bypass headers
        headers = shield.waf_bypass_headers("https://www.example.com")
        
        # Test TLS fingerprint spoofing
        ssl_context = shield.spoof_tls_fingerprint("chrome")
        
        # Print results
        print("\n=== ScrapingShield Test Results ===")
        print("User Agent Rotation:")
        for i, ua in enumerate(user_agents, 1):
            print(f"  {i}. {ua}")
        
        print("\nWAF Bypass Headers:")
        for key, value in headers.items():
            print(f"  {key}: {value}")
        
        print("\nTLS Fingerprint Spoofing:")
        print(f"  SSL Context: {ssl_context}")
        
        # Test honeypot detection
        from unittest.mock import Mock
        
        # Create a mock response object with HTML content
        mock_response = Mock()
        mock_response.text = """
        <div class="content">
            <div style="display: none" class="honeypot">This is a trap</div>
            <div class="real-content">This is real content</div>
        </div>
        """
        
        is_honeypot = shield.detect_honeypot(mock_response)
        
        print("\nHoneypot Detection:")
        if is_honeypot:
            print("  Honeypot detected!")
        else:
            print("  No honeypots detected")
        
    except Exception as e:
        logger.error(f"Error testing ScrapingShield: {e}")
    finally:
        # Clean up
        if 'shield' in locals():
            shield.cleanup()


def main() -> None:
    """Main function."""
    parser = argparse.ArgumentParser(description="Test anti-scraping implementation")
    parser.add_argument("--test", choices=["tradingview", "shield", "all"], default="all",
                        help="Test to run (default: all)")
    parser.add_argument("--symbol", type=str, default="BTCUSD",
                        help="Symbol to use for TradingView test (default: BTCUSD)")
    parser.add_argument("--pages", type=int, default=1,
                        help="Number of pages to scrape for TradingView test (default: 1)")
    
    args = parser.parse_args()
    
    if args.test in ["tradingview", "all"]:
        test_tradingview_ideas_scraper(args.symbol, args.pages)
    
    if args.test in ["shield", "all"]:
        test_scraping_shield()


if __name__ == "__main__":
    main()
