#!/usr/bin/env python3
"""
Comprehensive test suite for the Production TensorFlow CAPTCHA Solver.

Tests the revolutionary human behavior simulation approach and CPU-first
optimization with GPU acceleration capabilities.
"""

import os
import sys
import time
import tempfile
import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from PIL import Image
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ml_captcha_solvers import (
    TENSORFLOW_AVAILABLE, 
    TENSORFLOW_MODE,
    get_best_solver,
    get_solver_info,
    create_solver
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestProductionSolverIntegration:
    """Test production solver integration and availability."""
    
    def test_production_solver_available(self):
        """Test that production solver is properly integrated."""
        solver_info = get_solver_info()
        
        assert 'tensorflow' in solver_info
        tf_info = solver_info['tensorflow']
        
        if TENSORFLOW_AVAILABLE:
            assert tf_info['available']
            if TENSORFLOW_MODE == 'production':
                assert tf_info['features']['production_optimized']
                assert tf_info['features']['human_behavior_sim']
                assert tf_info['features']['resource_management']
                
                # Production solver should be recommended
                assert solver_info['recommended_solver'] == 'ProductionTensorFlowSolver'
    
    def test_best_solver_selection(self):
        """Test that the best solver is selected correctly."""
        best_solver = get_best_solver()
        
        if TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production':
            assert best_solver.__name__ == 'ProductionTensorFlowSolver'
        elif TENSORFLOW_AVAILABLE:
            assert 'TensorFlow' in best_solver.__name__
    
    def test_solver_creation(self):
        """Test solver creation through factory function."""
        if TENSORFLOW_AVAILABLE:
            solver = create_solver()
            assert solver is not None
            
            # Should have production features if available
            if TENSORFLOW_MODE == 'production':
                assert hasattr(solver, 'behavior_sim')
                assert hasattr(solver, 'resource_mgr')
                assert hasattr(solver, 'get_statistics')
                assert hasattr(solver, 'health_check')


@pytest.mark.skipif(not TENSORFLOW_AVAILABLE or TENSORFLOW_MODE != 'production', 
                   reason="Production TensorFlow solver not available")
class TestProductionTensorFlowSolver:
    """Test the ProductionTensorFlowSolver specifically."""
    
    @pytest.fixture
    def solver(self):
        """Create a production solver instance for testing."""
        from ml_captcha_solvers import ProductionTensorFlowSolver
        return ProductionTensorFlowSolver()
    
    @pytest.fixture
    def mock_image(self):
        """Create a mock CAPTCHA image for testing."""
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
            # Create a simple test image
            img = Image.new('RGB', (150, 50), color='white')
            img.save(tmp.name)
            yield tmp.name
        os.unlink(tmp.name)
    
    def test_solver_initialization(self, solver):
        """Test solver initialization and components."""
        assert solver is not None
        assert hasattr(solver, 'behavior_sim')
        assert hasattr(solver, 'resource_mgr')
        assert hasattr(solver, 'stats')
        
        # Check initial state
        assert not solver._model_loaded
        assert solver.stats['total_solves'] == 0
        assert solver.stats['successful_solves'] == 0
    
    def test_hardware_detection(self, solver):
        """Test hardware detection and resource management."""
        hw_profile = solver.resource_mgr.hardware_profile
        
        assert 'cpu_count' in hw_profile
        assert 'memory_gb' in hw_profile
        assert 'gpu_available' in hw_profile
        assert 'is_cloud' in hw_profile
        assert 'is_container' in hw_profile
        
        # Resource limits should be calculated
        limits = solver.resource_mgr.resource_limits
        assert 'max_memory_mb' in limits
        assert 'cpu_threads' in limits
        assert 'enable_gpu' in limits
        
        # Should have an optimization strategy
        assert solver.resource_mgr.optimization_strategy in [
            'gpu_accelerated', 'cpu_high_performance', 
            'cloud_optimized', 'cpu_conservative'
        ]
    
    def test_human_behavior_simulation(self, solver):
        """Test human behavior simulation features."""
        behavior_sim = solver.behavior_sim
        
        # Test response time calculation
        for complexity in [0.0, 0.5, 1.0]:
            response_time = behavior_sim.calculate_response_time(complexity)
            
            # Should be within human range
            assert 0.8 <= response_time <= 3.5  # Allowing for variance and fatigue
            
            # More complex CAPTCHAs should generally take longer
            if complexity > 0.5:
                complex_time = behavior_sim.calculate_response_time(complexity)
                simple_time = behavior_sim.calculate_response_time(0.1)
                # Allow for variance - complex should be at least occasionally longer
                # We can't guarantee every time due to random variance
    
    def test_fatigue_simulation(self, solver):
        """Test fatigue simulation over multiple solves."""
        behavior_sim = solver.behavior_sim
        
        # Simulate multiple solves
        initial_times = []
        for i in range(5):
            initial_times.append(behavior_sim.calculate_response_time(0.5))
        
        # Simulate more solves to trigger fatigue
        for i in range(15):  # Beyond fatigue threshold
            behavior_sim.calculate_response_time(0.5)
        
        # Get times after fatigue should kick in
        fatigue_times = []
        for i in range(5):
            fatigue_times.append(behavior_sim.calculate_response_time(0.5))
        
        # Average time should increase due to fatigue
        avg_initial = sum(initial_times) / len(initial_times)
        avg_fatigue = sum(fatigue_times) / len(fatigue_times)
        
        # Fatigue should generally increase times, but allow for variance
        assert behavior_sim.solve_count > 10  # Should have triggered fatigue threshold
    
    def test_behavioral_analytics(self, solver):
        """Test behavioral analytics and human score calculation."""
        behavior_sim = solver.behavior_sim
        
        # Generate some solve history
        for i in range(10):
            behavior_sim.calculate_response_time(0.5)
        
        analytics = behavior_sim.get_analytics()
        
        assert 'session_duration' in analytics
        assert 'total_solves' in analytics
        assert 'avg_response_time' in analytics
        assert 'response_variance' in analytics
        assert 'human_score' in analytics
        
        # Human score should be between 0 and 1
        assert 0.0 <= analytics['human_score'] <= 1.0
        
        # Should have reasonable values
        assert analytics['total_solves'] == 10
        assert analytics['avg_response_time'] > 0
        assert analytics['session_duration'] > 0
    
    def test_health_check(self, solver):
        """Test comprehensive health check functionality."""
        health = solver.health_check()
        
        assert 'timestamp' in health
        assert 'model_exists' in health
        assert 'model_loaded' in health
        assert 'dependencies_ok' in health
        assert 'stats' in health
        
        # Should include memory usage if psutil available
        if 'memory_usage_mb' in health:
            assert isinstance(health['memory_usage_mb'], (int, float, str))
    
    def test_statistics_tracking(self, solver):
        """Test comprehensive statistics tracking."""
        stats = solver.get_statistics()
        
        # Should include solver stats
        assert 'total_solves' in stats
        assert 'successful_solves' in stats
        assert 'avg_solve_time' in stats
        
        # Should include hardware info
        assert 'hardware_profile' in stats
        assert 'optimization_strategy' in stats
        
        # Should include behavioral analytics
        assert 'behavioral_analytics' in stats
        assert 'model_loaded' in stats
    
    @patch('os.path.exists')
    def test_model_path_handling(self, mock_exists, solver):
        """Test model path detection and handling."""
        mock_exists.return_value = True
        
        # Should have a valid model path
        assert solver.model_path is not None
        assert isinstance(solver.model_path, str)
    
    def test_complexity_estimation(self, solver, mock_image):
        """Test CAPTCHA complexity estimation."""
        complexity = solver._estimate_complexity(mock_image)
        
        assert isinstance(complexity, float)
        assert 0.0 <= complexity <= 1.0
    
    def test_lazy_import(self, solver):
        """Test lazy import functionality."""
        # Should not have imports initially
        assert solver._tf is None
        assert solver._np is None
        assert solver._pil is None
        
        # Calling lazy import should load dependencies
        solver._lazy_import()
        
        # Should have TensorFlow and other deps
        assert solver._tf is not None
        assert solver._np is not None
        assert solver._pil is not None
    
    @patch('tensorflow.saved_model.load')
    @patch('os.path.exists')
    def test_model_loading(self, mock_exists, mock_load, solver):
        """Test model loading with mocks."""
        mock_exists.return_value = True
        mock_model = Mock()
        mock_load.return_value = mock_model
        
        # Load model
        solver._load_model()
        
        assert solver._model_loaded
        assert solver.model is not None
    
    def test_cpu_optimization_preference(self, solver):
        """Test that CPU optimization is preferred in resource-constrained environments."""
        strategy = solver.resource_mgr.optimization_strategy
        
        # In most test environments without high-end GPUs, should prefer CPU strategies
        if not solver.resource_mgr.hardware_profile.get('gpu_memory_gb', 0) >= 2.0:
            assert strategy in ['cpu_high_performance', 'cpu_conservative', 'cloud_optimized']
    
    def test_anti_bot_timing_feature(self, solver):
        """Test that timing delays are treated as anti-bot protection feature."""
        behavior_sim = solver.behavior_sim
        
        # Generate multiple response times
        times = []
        for _ in range(20):
            times.append(behavior_sim.calculate_response_time(0.5))
        
        # Should have natural variance (anti-bot feature)
        variance = behavior_sim._calculate_variance(times)
        assert variance > 0  # Should not be perfectly consistent
        
        # Average should be in human range
        avg_time = sum(times) / len(times)
        assert 0.8 <= avg_time <= 2.5
        
        # Should occasionally have "distraction" spikes
        max_time = max(times)
        assert max_time >= 0.8  # At least minimum human time


@pytest.mark.skipif(not TENSORFLOW_AVAILABLE or TENSORFLOW_MODE != 'production', 
                   reason="Production TensorFlow solver not available")
class TestHumanBehaviorSimulator:
    """Test the HumanBehaviorSimulator component specifically."""
    
    @pytest.fixture
    def behavior_sim(self):
        """Create a behavior simulator for testing."""
        from ml_captcha_solvers import HumanBehaviorSimulator
        return HumanBehaviorSimulator()
    
    def test_initialization(self, behavior_sim):
        """Test behavior simulator initialization."""
        assert behavior_sim.solve_count == 0
        assert len(behavior_sim.solve_history) == 0
        assert behavior_sim.session_start > 0
    
    def test_response_time_calculation(self, behavior_sim):
        """Test response time calculation with different complexities."""
        # Test different complexity levels
        for complexity in [0.0, 0.25, 0.5, 0.75, 1.0]:
            response_time = behavior_sim.calculate_response_time(complexity)
            
            # Should be reasonable human time
            assert isinstance(response_time, float)
            assert response_time > 0
            
            # Should generally correlate with complexity
            assert 0.5 <= response_time <= 4.0  # Allowing for variance and fatigue
    
    def test_solve_history_tracking(self, behavior_sim):
        """Test that solve history is properly tracked."""
        # Generate some solves
        for i in range(5):
            behavior_sim.calculate_response_time(0.5)
        
        assert len(behavior_sim.solve_history) == 5
        assert behavior_sim.solve_count == 5
        
        # Check history structure
        for entry in behavior_sim.solve_history:
            assert 'time' in entry
            assert 'response_time' in entry
            assert 'complexity' in entry
            assert 'solve_count' in entry
    
    def test_human_score_calculation(self, behavior_sim):
        """Test human-like behavior scoring."""
        # Need some history for scoring
        for i in range(10):
            behavior_sim.calculate_response_time(0.5)
        
        score = behavior_sim._calculate_human_score()
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
    
    def test_distraction_spikes(self, behavior_sim):
        """Test occasional distraction spikes (very human!)."""
        times = []
        
        # Generate many response times to catch distraction spikes
        for _ in range(100):
            times.append(behavior_sim.calculate_response_time(0.5))
        
        # Should have some variance and occasional spikes
        max_time = max(times)
        min_time = min(times)
        
        assert max_time > min_time  # Should have variance
        
        # At least some times should be noticeably different
        variance = behavior_sim._calculate_variance(times)
        assert variance > 0


@pytest.mark.skipif(not TENSORFLOW_AVAILABLE or TENSORFLOW_MODE != 'production', 
                   reason="Production TensorFlow solver not available")
class TestProductionResourceManager:
    """Test the ProductionResourceManager component."""
    
    @pytest.fixture
    def resource_mgr(self):
        """Create a resource manager for testing."""
        from ml_captcha_solvers import ProductionResourceManager
        return ProductionResourceManager()
    
    def test_initialization(self, resource_mgr):
        """Test resource manager initialization."""
        assert hasattr(resource_mgr, 'hardware_profile')
        assert hasattr(resource_mgr, 'resource_limits')
        assert hasattr(resource_mgr, 'optimization_strategy')
    
    def test_hardware_detection(self, resource_mgr):
        """Test hardware detection capabilities."""
        hw = resource_mgr.hardware_profile
        
        # Should detect basic hardware info
        assert 'cpu_count' in hw
        assert 'memory_gb' in hw
        assert 'gpu_available' in hw
        
        # CPU count should be reasonable
        assert hw['cpu_count'] >= 1
        assert hw['memory_gb'] >= 0
        
        # GPU availability should be boolean
        assert isinstance(hw['gpu_available'], bool)
    
    def test_resource_limits(self, resource_mgr):
        """Test resource limit calculation."""
        limits = resource_mgr.resource_limits
        
        assert 'max_memory_mb' in limits
        assert 'cpu_threads' in limits
        assert 'enable_gpu' in limits
        
        # Limits should be reasonable
        assert limits['max_memory_mb'] > 0
        assert limits['cpu_threads'] >= 1
        assert isinstance(limits['enable_gpu'], bool)
    
    def test_optimization_strategy(self, resource_mgr):
        """Test optimization strategy determination."""
        strategy = resource_mgr.optimization_strategy
        
        valid_strategies = [
            'gpu_accelerated', 'cpu_high_performance', 
            'cloud_optimized', 'cpu_conservative'
        ]
        assert strategy in valid_strategies
    
    @patch('tensorflow.config.threading.set_inter_op_parallelism_threads')
    @patch('tensorflow.config.threading.set_intra_op_parallelism_threads')
    def test_tensorflow_configuration(self, mock_intra, mock_inter, resource_mgr):
        """Test TensorFlow configuration."""
        mock_tf = Mock()
        mock_tf.config.threading.set_inter_op_parallelism_threads = mock_inter
        mock_tf.config.threading.set_intra_op_parallelism_threads = mock_intra
        mock_tf.config.list_physical_devices.return_value = []
        
        resource_mgr.configure_tensorflow(mock_tf)
        
        # Should configure threading
        mock_inter.assert_called_once()
        mock_intra.assert_called_once()


class TestProductionFeatures:
    """Test production-specific features and integrations."""
    
    def test_package_integration(self):
        """Test that production solver integrates properly with package."""
        solver_info = get_solver_info()
        
        # Should provide comprehensive info
        assert 'tensorflow' in solver_info
        assert 'pytorch' in solver_info
        
        if TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production':
            tf_info = solver_info['tensorflow']
            assert tf_info['features']['production_optimized']
            assert tf_info['features']['human_behavior_sim']
            assert tf_info['features']['resource_management']
    
    def test_cpu_first_optimization(self):
        """Test that CPU-first optimization is working."""
        if TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production':
            from ml_captcha_solvers import ProductionTensorFlowSolver
            solver = ProductionTensorFlowSolver()
            
            # Should prefer CPU in most environments
            strategy = solver.resource_mgr.optimization_strategy
            
            # Unless we have a high-end GPU, should use CPU strategies
            hw = solver.resource_mgr.hardware_profile
            if not (hw.get('gpu_available') and hw.get('gpu_memory_gb', 0) >= 2.0):
                assert 'cpu' in strategy.lower() or strategy == 'cloud_optimized'
    
    def test_anti_bot_timing_philosophy(self):
        """Test that slower timing is embraced as an anti-bot feature."""
        if TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production':
            from ml_captcha_solvers import HumanBehaviorSimulator
            behavior_sim = HumanBehaviorSimulator()
            
            # Multiple response times should show human-like patterns
            times = [behavior_sim.calculate_response_time(0.5) for _ in range(10)]
            
            # Should be in human range (0.8-2.5s is the sweet spot)
            avg_time = sum(times) / len(times)
            assert 0.8 <= avg_time <= 2.5
            
            # Should have natural variance (bots are too consistent)
            variance = behavior_sim._calculate_variance(times)
            assert variance > 0.01  # Should not be robot-like


def run_performance_benchmark():
    """Optional performance benchmark for production solver."""
    if not (TENSORFLOW_AVAILABLE and TENSORFLOW_MODE == 'production'):
        print("Production solver not available for benchmarking")
        return
    
    print("\n🚀 Production Solver Performance Benchmark")
    print("=" * 50)
    
    from ml_captcha_solvers import ProductionTensorFlowSolver
    solver = ProductionTensorFlowSolver()
    
    # Display configuration
    print(f"Hardware Profile: {solver.resource_mgr.hardware_profile}")
    print(f"Optimization Strategy: {solver.resource_mgr.optimization_strategy}")
    print(f"Resource Limits: {solver.resource_mgr.resource_limits}")
    
    # Test health check performance
    start_time = time.time()
    health = solver.health_check()
    health_time = time.time() - start_time
    
    print(f"\nHealth Check: {health_time:.3f}s")
    print(f"Health Status: {health}")
    
    # Test behavior simulation
    behavior_times = []
    for complexity in [0.2, 0.5, 0.8]:
        response_time = solver.behavior_sim.calculate_response_time(complexity)
        behavior_times.append(response_time)
        print(f"Complexity {complexity}: {response_time:.3f}s (human-like)")
    
    analytics = solver.behavior_sim.get_analytics()
    print(f"\nBehavioral Analytics: {analytics}")
    
    stats = solver.get_statistics()
    print(f"Full Statistics: {stats}")


if __name__ == "__main__":
    # Run the benchmark if called directly
    run_performance_benchmark()
    
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
