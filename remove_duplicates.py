#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to identify and remove duplicate files in the dataSets directory.
Duplicates are identified by:
1. Files with patterns like "(1)" or "(2)" in their names
2. Files that have the same content but different names
"""

import os
import re
import sys
import hashlib
from pathlib import Path
from collections import defaultdict

def compute_file_hash(file_path):
    """Compute SHA-256 hash of a file to identify duplicates by content."""
    hash_sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        for chunk in iter(lambda: f.read(4096), b''):
            hash_sha256.update(chunk)
    return hash_sha256.hexdigest()

def is_numbered_duplicate(filename):
    """Check if the filename has a pattern like '(1)' or '(2)' indicating it's a duplicate."""
    return bool(re.search(r'\(\d+\)', filename))

def get_base_filename(filename):
    """
    Get the base filename without the numbered duplicate pattern.
    Example: 'file(1).pdf' -> 'file.pdf'
    """
    return re.sub(r'\(\d+\)', '', filename)

def find_duplicates_by_name(directory):
    """
    Find duplicate files based on naming patterns like (1), (2), etc.
    Returns a dictionary mapping original files to their duplicates.
    """
    duplicates = defaultdict(list)
    
    # First pass: Group files by their base name (without the (n) pattern)
    base_to_files = defaultdict(list)
    
    for root, _, files in os.walk(directory):
        for filename in files:
            file_path = os.path.join(root, filename)
            
            # Skip directories and non-regular files
            if not os.path.isfile(file_path):
                continue
                
            # Check if this is a numbered duplicate
            if is_numbered_duplicate(filename):
                base_name = get_base_filename(filename)
                base_to_files[base_name].append(file_path)
            else:
                # Also add the original file to the mapping
                base_to_files[filename].append(file_path)
    
    # Second pass: Identify duplicates
    for base_name, file_paths in base_to_files.items():
        if len(file_paths) > 1:
            # Sort files so that the one without a number comes first (original)
            file_paths.sort(key=lambda x: 0 if not is_numbered_duplicate(os.path.basename(x)) else 1)
            
            # The first file is considered the original, the rest are duplicates
            original = file_paths[0]
            for duplicate in file_paths[1:]:
                duplicates[original].append(duplicate)
    
    return duplicates

def find_duplicates_by_content(directory):
    """
    Find duplicate files based on content (hash).
    Returns a dictionary mapping original files to their duplicates.
    """
    duplicates = defaultdict(list)
    hash_to_files = defaultdict(list)
    
    print("Computing file hashes to find content duplicates...")
    file_count = 0
    
    for root, _, files in os.walk(directory):
        for filename in files:
            file_path = os.path.join(root, filename)
            
            # Skip directories and non-regular files
            if not os.path.isfile(file_path):
                continue
            
            try:
                file_hash = compute_file_hash(file_path)
                hash_to_files[file_hash].append(file_path)
                
                file_count += 1
                if file_count % 100 == 0:
                    print(f"Processed {file_count} files...")
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    # Identify duplicates
    for file_hash, file_paths in hash_to_files.items():
        if len(file_paths) > 1:
            # Sort files by path length (shorter paths first)
            file_paths.sort(key=lambda x: len(x))
            
            # The first file is considered the original, the rest are duplicates
            original = file_paths[0]
            for duplicate in file_paths[1:]:
                # Only add if not already identified as a duplicate by name
                duplicates[original].append(duplicate)
    
    return duplicates

def remove_duplicates(duplicates, dry_run=True):
    """
    Remove duplicate files.
    If dry_run is True, only print what would be done without actually removing files.
    """
    total_duplicates = sum(len(dups) for dups in duplicates.values())
    total_size_saved = 0
    
    print(f"\nFound {total_duplicates} duplicate files")
    
    for original, duplicate_list in duplicates.items():
        print(f"\nOriginal: {original}")
        
        for duplicate in duplicate_list:
            try:
                size = os.path.getsize(duplicate)
                total_size_saved += size
                
                if dry_run:
                    print(f"  Would remove: {duplicate} ({size / (1024*1024):.2f} MB)")
                else:
                    print(f"  Removing: {duplicate} ({size / (1024*1024):.2f} MB)")
                    os.remove(duplicate)
            except Exception as e:
                print(f"  Error processing {duplicate}: {e}")
    
    print(f"\nTotal space that would be saved: {total_size_saved / (1024*1024):.2f} MB")
    
    return total_duplicates, total_size_saved

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = "/home/<USER>/appsUndCode/dataSets/"
    
    dry_run = True  # Default to dry run for safety
    
    if len(sys.argv) > 2 and sys.argv[2].lower() == "--delete":
        dry_run = False
    
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)
    
    print(f"Scanning for duplicates in {directory}...")
    print("First checking for duplicates by filename pattern...")
    
    # Find duplicates by name pattern
    name_duplicates = find_duplicates_by_name(directory)
    
    # Find duplicates by content
    print("\nNow checking for duplicates by content...")
    content_duplicates = find_duplicates_by_content(directory)
    
    # Combine the results (name duplicates take precedence)
    all_duplicates = {}
    all_duplicates.update(name_duplicates)
    
    # Add content duplicates that aren't already in name duplicates
    for original, duplicates in content_duplicates.items():
        if original not in all_duplicates:
            all_duplicates[original] = []
        
        for duplicate in duplicates:
            # Check if this duplicate is already marked for deletion
            already_marked = False
            for _, dups in name_duplicates.items():
                if duplicate in dups:
                    already_marked = True
                    break
            
            if not already_marked and duplicate not in all_duplicates[original]:
                all_duplicates[original].append(duplicate)
    
    # Remove empty entries
    all_duplicates = {k: v for k, v in all_duplicates.items() if v}
    
    # Remove duplicates
    total_dups, total_size = remove_duplicates(all_duplicates, dry_run)
    
    if dry_run:
        print("\nThis was a dry run. No files were actually deleted.")
        print("To actually delete the files, run with the --delete flag:")
        print(f"python {sys.argv[0]} {directory} --delete")
    else:
        print(f"\nSuccessfully removed {total_dups} duplicate files, saving {total_size / (1024*1024):.2f} MB")
