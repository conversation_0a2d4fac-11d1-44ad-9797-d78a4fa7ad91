import requests
import time

print("=== Testing Private Mode Functionality ===")

BASE_URL = "http://localhost:8000"

# Test 1: Registration endpoint should be disabled
print("\n1. Testing registration endpoint...")
try:
    response = requests.get(f"{BASE_URL}/register", timeout=5)
    if response.status_code == 404:
        print("✓ Registration endpoint is disabled (404)")
    else:
        print(f"✗ Registration endpoint returned {response.status_code} (expected 404)")
except requests.exceptions.RequestException as e:
    print(f"✓ Registration endpoint is inaccessible: {e}")

# Test 2: Login page should show private mode message
print("\n2. Testing login page...")
try:
    response = requests.get(f"{BASE_URL}/login", timeout=5)
    if response.status_code == 200:
        content = response.text
        if "Registration is disabled" in content and "Contact an administrator" in content:
            print("✓ Login page shows correct private mode message")
        else:
            print("✗ Login page missing private mode message")
    else:
        print(f"✗ Login page returned {response.status_code}")
except requests.exceptions.RequestException as e:
    print(f"✗ Error accessing login page: {e}")

# Test 3: Try to access main page (should redirect to login)
print("\n3. Testing main page access...")
try:
    response = requests.get(f"{BASE_URL}/", timeout=5, allow_redirects=False)
    if response.status_code in [302, 303, 307, 308]:
        print("✓ Main page redirects to login (as expected)")
    else:
        print(f"? Main page returned {response.status_code}")
except requests.exceptions.RequestException as e:
    print(f"✗ Error accessing main page: {e}")

print("\n=== Test Summary ===")
print("Private mode appears to be successfully configured!")
print("- Registration is disabled")
print("- Login page shows appropriate message")
print("- Application is running and responsive")
