#!/usr/bin/env python3
"""
Simple test for anti-scraping components
"""

import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if we can import the required modules."""
    logger.info("Testing imports...")
    
    try:
        from scraping_shield import ScrapingShield
        logger.info("✓ ScrapingShield imported successfully")
        return True
    except ImportError as e:
        logger.error("✗ Failed to import ScrapingShield: %s", e)
        return False

def test_tradingview_imports():
    """Test if we can import TradingView modules."""
    logger.info("Testing TradingView imports...")
    
    try:
        from tradingview.ideas_scraper import EnhancedIdeasScraper
        logger.info("✓ EnhancedIdeasScraper imported successfully")
        return True
    except ImportError as e:
        logger.error("✗ Failed to import EnhancedIdeasScraper: %s", e)
        return False

def test_shield_basic():
    """Test basic ScrapingShield functionality."""
    logger.info("Testing basic ScrapingShield functionality...")
    
    try:
        from scraping_shield import ScrapingShield
        
        # Create shield instance
        shield = ScrapingShield()
        logger.info("✓ ScrapingShield instance created")
        
        # Test user agent rotation
        user_agent = shield.rotate_user_agent()
        logger.info("✓ User agent rotated: %s", user_agent[:50] + "...")
        
        # Test WAF bypass headers
        headers = shield.waf_bypass_headers("https://example.com")
        logger.info("✓ WAF bypass headers generated: %d headers", len(headers))
        
        return True
        
    except Exception as e:
        logger.error("✗ Error testing ScrapingShield: %s", e)
        return False

def main():
    """Main test function."""
    logger.info("=== Anti-Scraping Component Test ===")
    
    results = {
        "imports": test_imports(),
        "tradingview_imports": test_tradingview_imports(),
        "shield_basic": test_shield_basic()
    }
    
    logger.info("=== Test Results ===")
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        logger.info("%s: %s", test_name, status)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    logger.info("=== Summary ===")
    logger.info("Total tests: %d", total_tests)
    logger.info("Passed: %d", passed_tests)
    logger.info("Failed: %d", total_tests - passed_tests)
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.warning("⚠️  Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
