#!/usr/bin/env python3
"""
Login Diagnostic Script for Web Dashboard
Diagnoses admin login issues and determines if it's a database or password problem.
Updated to use scraper_metrics database.
"""

import os
import sys
import sqlite3
import hashlib
import bcrypt
from contextlib import contextmanager

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_section(title):
    """Print a formatted section header"""
    print(f"\n{'-'*40}")
    print(f"  {title}")
    print(f"{'-'*40}")

def test_imports():
    """Test if required modules can be imported"""
    print_section("Testing Required Imports")
    
    required_modules = [
        'sqlite3', 'hashlib', 'bcrypt', 'psycopg2', 'flask', 'werkzeug'
    ]
    
    available_modules = []
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            available_modules.append(module)
            print(f"✓ {module} - Available")
        except ImportError as e:
            missing_modules.append(module)
            print(f"✗ {module} - Missing: {e}")
    
    return len(missing_modules) == 0

def test_database_connectivity():
    """Test database connectivity"""
    print_section("Testing Database Connectivity")
    
    # Check environment variables
    db_config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '5432'),
        'name': os.getenv('DB_NAME', 'scraper_metrics'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', '')
    }
    
    print("Database Configuration:")
    for key, value in db_config.items():
        if key == 'password':
            print(f"  {key}: {'*' * len(value) if value else '(empty)'}")
        else:
            print(f"  {key}: {value}")
    
    # Test PostgreSQL connection
    print("\nTesting PostgreSQL connection...")
    try:
        import psycopg2
        conn_string = f"host='{db_config['host']}' port='{db_config['port']}' dbname='{db_config['name']}' user='{db_config['user']}' password='{db_config['password']}'"
        
        with psycopg2.connect(conn_string) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT version();")
                version = cur.fetchone()[0]
                print(f"✓ PostgreSQL connection successful")
                print(f"  Version: {version}")
                return True, 'postgresql'
                
    except Exception as e:
        print(f"✗ PostgreSQL connection failed: {e}")
    
    # Test SQLite fallback
    print("\nTesting SQLite fallback...")
    try:
        db_file = os.path.join(os.path.dirname(__file__), 'scraper_metrics.db')
        print(f"SQLite file: {db_file}")
        print(f"SQLite file exists: {os.path.exists(db_file)}")
        
        with sqlite3.connect(db_file) as conn:
            cur = conn.cursor()
            cur.execute("SELECT sqlite_version();")
            version = cur.fetchone()[0]
            print(f"✓ SQLite connection successful")
            print(f"  Version: {version}")
            return True, 'sqlite'
            
    except Exception as e:
        print(f"✗ SQLite connection failed: {e}")
    
    return False, None

def check_users_table(db_type):
    """Check if users table exists and examine its structure"""
    print_section("Checking Users Table")
    
    try:
        if db_type == 'postgresql':
            import psycopg2
            db_config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '5432'),
                'name': os.getenv('DB_NAME', 'trend_crawler'),
                'user': os.getenv('DB_USER', 'postgres'),
                'password': os.getenv('DB_PASSWORD', '')
            }
            conn_string = f"host='{db_config['host']}' port='{db_config['port']}' dbname='{db_config['name']}' user='{db_config['user']}' password='{db_config['password']}'"
            
            with psycopg2.connect(conn_string) as conn:
                with conn.cursor() as cur:
                    # Check if users table exists
                    cur.execute("""
                        SELECT table_name FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = 'users';
                    """)
                    table_exists = cur.fetchone() is not None
                    print(f"Users table exists: {table_exists}")
                    
                    if table_exists:
                        # Get table structure
                        cur.execute("""
                            SELECT column_name, data_type, is_nullable 
                            FROM information_schema.columns 
                            WHERE table_name = 'users' 
                            ORDER BY ordinal_position;
                        """)
                        columns = cur.fetchall()
                        print("\nTable structure:")
                        for col_name, data_type, nullable in columns:
                            print(f"  {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
                        
                        # Get user count
                        cur.execute("SELECT COUNT(*) FROM users;")
                        user_count = cur.fetchone()[0]
                        print(f"\nTotal users: {user_count}")
                        
                        # Check for admin user
                        cur.execute("SELECT username, is_active, needs_password_change FROM users WHERE username = 'admin';")
                        admin_user = cur.fetchone()
                        if admin_user:
                            username, is_active, needs_pwd_change = admin_user
                            print(f"\nAdmin user found:")
                            print(f"  Username: {username}")
                            print(f"  Active: {is_active}")
                            print(f"  Needs password change: {needs_pwd_change}")
                            return True
                        else:
                            print("\n✗ Admin user not found in database")
                            return False
                    else:
                        print("✗ Users table does not exist")
                        return False
                        
        elif db_type == 'sqlite':
            db_file = os.path.join(os.path.dirname(__file__), 'trend_crawler.db')
            with sqlite3.connect(db_file) as conn:
                cur = conn.cursor()
                
                # Check if users table exists
                cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
                table_exists = cur.fetchone() is not None
                print(f"Users table exists: {table_exists}")
                
                if table_exists:
                    # Get table structure
                    cur.execute("PRAGMA table_info(users);")
                    columns = cur.fetchall()
                    print("\nTable structure:")
                    for col_info in columns:
                        col_id, col_name, col_type, not_null, default_val, pk = col_info
                        nullable = "NOT NULL" if not_null else "NULL"
                        print(f"  {col_name}: {col_type} ({nullable})")
                    
                    # Get user count
                    cur.execute("SELECT COUNT(*) FROM users;")
                    user_count = cur.fetchone()[0]
                    print(f"\nTotal users: {user_count}")
                    
                    # Check for admin user
                    cur.execute("SELECT username, is_active, needs_password_change FROM users WHERE username = 'admin';")
                    admin_user = cur.fetchone()
                    if admin_user:
                        username, is_active, needs_pwd_change = admin_user
                        print(f"\nAdmin user found:")
                        print(f"  Username: {username}")
                        print(f"  Active: {is_active}")
                        print(f"  Needs password change: {needs_pwd_change}")
                        return True
                    else:
                        print("\n✗ Admin user not found in database")
                        return False
                else:
                    print("✗ Users table does not exist")
                    return False
                    
    except Exception as e:
        print(f"✗ Error checking users table: {e}")
        return False

def test_password_verification():
    """Test password hashing and verification"""
    print_section("Testing Password Functions")
    
    test_passwords = ['admin', 'password', '123456']
    
    print("Testing bcrypt password hashing...")
    for password in test_passwords:
        try:
            # Hash the password
            hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
            print(f"✓ Hash created for '{password}': {hashed.decode('utf-8')[:20]}...")
            
            # Verify the password
            is_valid = bcrypt.checkpw(password.encode('utf-8'), hashed)
            print(f"  Verification: {'✓ PASS' if is_valid else '✗ FAIL'}")
            
        except Exception as e:
            print(f"✗ Error with password '{password}': {e}")

def test_authentication_with_database(db_type):
    """Test authentication against database"""
    print_section("Testing Authentication Against Database")
    
    test_passwords = ['admin', 'password', '123456']
    
    try:
        if db_type == 'postgresql':
            import psycopg2
            db_config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': os.getenv('DB_PORT', '5432'),
                'name': os.getenv('DB_NAME', 'trend_crawler'),
                'user': os.getenv('DB_USER', 'postgres'),
                'password': os.getenv('DB_PASSWORD', '')
            }
            conn_string = f"host='{db_config['host']}' port='{db_config['port']}' dbname='{db_config['name']}' user='{db_config['user']}' password='{db_config['password']}'"
            
            with psycopg2.connect(conn_string) as conn:
                with conn.cursor() as cur:
                    # Get admin user's password hash
                    cur.execute("SELECT password_hash FROM users WHERE username = 'admin';")
                    result = cur.fetchone()
                    
                    if result:
                        stored_hash = result[0]
                        print(f"Stored password hash: {stored_hash[:20]}...")
                        
                        for password in test_passwords:
                            try:
                                is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                                status = "✓ VALID" if is_valid else "✗ INVALID"
                                print(f"  Testing '{password}': {status}")
                                if is_valid:
                                    return True, password
                            except Exception as e:
                                print(f"  Testing '{password}': ✗ ERROR - {e}")
                    else:
                        print("✗ No admin user found to test against")
                        
        elif db_type == 'sqlite':
            db_file = os.path.join(os.path.dirname(__file__), 'trend_crawler.db')
            with sqlite3.connect(db_file) as conn:
                cur = conn.cursor()
                
                # Get admin user's password hash
                cur.execute("SELECT password_hash FROM users WHERE username = 'admin';")
                result = cur.fetchone()
                
                if result:
                    stored_hash = result[0]
                    print(f"Stored password hash: {stored_hash[:20]}...")
                    
                    for password in test_passwords:
                        try:
                            is_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                            status = "✓ VALID" if is_valid else "✗ INVALID"
                            print(f"  Testing '{password}': {status}")
                            if is_valid:
                                return True, password
                        except Exception as e:
                            print(f"  Testing '{password}': ✗ ERROR - {e}")
                else:
                    print("✗ No admin user found to test against")
                    
    except Exception as e:
        print(f"✗ Error during authentication test: {e}")
    
    return False, None

def main():
    """Main diagnostic function"""
    print_header("LOGIN DIAGNOSTIC TOOL")
    print("Diagnosing admin login issues for the web dashboard...")
    
    # Test 1: Check imports
    imports_ok = test_imports()
    if not imports_ok:
        print("\n⚠️  Some required modules are missing. This may cause issues.")
    
    # Test 2: Test database connectivity
    db_connected, db_type = test_database_connectivity()
    
    if not db_connected:
        print_header("DIAGNOSIS: DATABASE CONNECTIVITY ISSUE")
        print("❌ Cannot connect to any database (PostgreSQL or SQLite)")
        print("\nRECOMMENDATIONS:")
        print("1. Check PostgreSQL service is running")
        print("2. Verify database credentials in environment variables")
        print("3. Ensure database 'trend_crawler' exists")
        print("4. Check if SQLite file permissions are correct")
        print("\n⚠️  This appears to be a DATABASE CONNECTIVITY ISSUE")
        print("   Do NOT reset the admin password - fix database connection first")
        return
    
    print(f"\n✓ Connected to {db_type.upper()} database")
    
    # Test 3: Check users table
    table_ok = check_users_table(db_type)
    
    if not table_ok:
        print_header("DIAGNOSIS: DATABASE SCHEMA ISSUE")
        print("❌ Users table missing or admin user not found")
        print("\nRECOMMENDATIONS:")
        print("1. Run db_setup.py to create database tables")
        print("2. Run reset_admin_password.py to create admin user")
        print("3. Check if database initialization was completed")
        print("\n⚠️  This appears to be a DATABASE SCHEMA ISSUE")
        print("   Fix database schema before testing login")
        return
    
    # Test 4: Test password functions
    test_password_verification()
    
    # Test 5: Test authentication
    auth_success, valid_password = test_authentication_with_database(db_type)
    
    if auth_success:
        print_header("DIAGNOSIS: LOGIN SHOULD WORK")
        print(f"✓ Admin user authentication successful with password: '{valid_password}'")
        print("\nRECOMMENDATIONS:")
        print("1. Try logging in with the working password found above")
        print("2. Check web application logs for other errors")
        print("3. Verify web server is running correctly")
        print("4. Check browser console for JavaScript errors")
        print("\n✅ This does NOT appear to be a database or password issue")
        print("   The login should work - check web application itself")
    else:
        print_header("DIAGNOSIS: PASSWORD ISSUE")
        print("❌ Admin user exists but none of the test passwords work")
        print("\nRECOMMENDATIONS:")
        print("1. Reset admin password using reset_admin_password.py")
        print("2. Or manually update password in database")
        print("\n⚠️  This appears to be a PASSWORD ISSUE, not a database issue")
        print("   Safe to reset admin password to 'admin'")

if __name__ == "__main__":
    main()
