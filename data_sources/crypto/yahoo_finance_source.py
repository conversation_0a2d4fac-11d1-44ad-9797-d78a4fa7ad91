"""
Yahoo Finance Crypto Data Source Module

This module provides a data source for cryptocurrency data from Yahoo Finance,
integrated with the anti-scraping framework.
"""

import logging
import sys
import os
import time
import pandas as pd
from typing import Dict, Any, List, Optional, Union
import json

# Try to import selenium-related dependencies
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.support.wait import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logging.warning("Selenium not available. Yahoo Finance scraping will be disabled.")

# Try to import anti-scraping module
try:
    from anti_scraping.core import AntiScrapingMiddleware
    ANTI_SCRAPING_AVAILABLE = True
except ImportError:
    ANTI_SCRAPING_AVAILABLE = False
    logging.warning("Anti-scraping middleware not available. Scraper will be less robust.")


class EnhancedYahooFinanceCryptoScraper:
    """
    Enhanced Yahoo Finance cryptocurrency scraper with anti-scraping measures.
    """

    def __init__(self):
        """Initialize the enhanced Yahoo Finance crypto scraper."""
        self.anti_scraping = AntiScrapingMiddleware() if ANTI_SCRAPING_AVAILABLE else None
        self.available = SELENIUM_AVAILABLE
        self.yahoo_finance_url = 'https://finance.yahoo.com/cryptocurrencies'
        self.data_cache = None
        self.last_cache_time = 0
        self.cache_expiry = 3600  # Cache expiry in seconds (1 hour)

    def _create_driver(self):
        """
        Create a Selenium WebDriver with enhanced anti-detection measures.
        
        Returns:
            WebDriver object or None if Selenium is not available
        """
        if not self.available:
            logging.warning("Selenium not available, cannot create WebDriver")
            return None
            
        try:
            chrome_options = Options()
            
            # Apply anti-scraping measures if available
            if self.anti_scraping:
                chrome_options = self.anti_scraping.get_enhanced_browser_options()
                
                # Get a proxy from the middleware
                proxy = self.anti_scraping.get_next_proxy()
                if proxy:
                    chrome_options.add_argument(f'--proxy-server={proxy}')
            
            # Default options if anti-scraping is not available
            else:
                chrome_options.add_argument('--no-sandbox')
                chrome_options.add_argument('--disable-dev-shm-usage')
                chrome_options.add_argument('--headless')
                chrome_options.add_argument('--start-maximized')
                chrome_options.add_argument('--start-fullscreen')
                chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
                chrome_options.add_experimental_option('useAutomationExtension', False)
                
            # Create the WebDriver
            driver = webdriver.Chrome(options=chrome_options)
            
            # Additional anti-detection measures
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            return driver
        except Exception as e:
            logging.error(f"Error creating WebDriver: {e}")
            return None

    def _get_table_header(self, driver):
        """
        Extract table headers from Yahoo Finance crypto table.
        
        Args:
            driver: WebDriver instance
            
        Returns:
            List of header column names
        """
        try:
            header = driver.find_elements(By.TAG_NAME, 'th')
            header_list = [item.text for index, item in enumerate(header) if index < 10]
            return header_list
        except Exception as e:
            logging.error(f"Error getting table header: {e}")
            return []

    def _get_table_rows(self, driver):
        """
        Get number of rows in the table.
        
        Args:
            driver: WebDriver instance
            
        Returns:
            Number of rows in the table
        """
        try:
            return len(driver.find_elements(By.XPATH, '//*[@id="scr-res-table"]/div[1]/table/tbody/tr'))
        except Exception as e:
            logging.error(f"Error getting table rows: {e}")
            return 0

    def _parse_table_row(self, row_num, driver, header_list):
        """
        Parse a single row from the crypto table.
        
        Args:
            row_num: Row number to parse
            driver: WebDriver instance
            header_list: List of column headers
            
        Returns:
            Dictionary containing row data
        """
        row_data = {}
        try:
            for index, item in enumerate(header_list):
                time.sleep(0.05)  # Small delay to avoid detection
                column_xpath = f'//*[@id="scr-res-table"]/div[1]/table/tbody/tr[{row_num}]/td[{index+1}]'
                row_data[item] = driver.find_element(By.XPATH, column_xpath).text
            return row_data
        except Exception as e:
            logging.error(f"Error parsing table row {row_num}: {e}")
            return {}

    def _parse_multiple_pages(self, driver, total_crypto=100):
        """
        Parse multiple pages of cryptocurrency data.
        
        Args:
            driver: WebDriver instance
            total_crypto: Maximum number of cryptocurrencies to collect
            
        Returns:
            List of cryptocurrency data dictionaries
        """
        table_data = []
        page_num = 1
        is_scraping = True
        header_list = self._get_table_header(driver)
        
        if not header_list:
            logging.error("Failed to get table headers")
            return []
        
        try:
            while is_scraping:
                # Get number of rows on current page
                table_rows = self._get_table_rows(driver)
                logging.info(f'Found {table_rows} rows on Page: {page_num}')
                
                if table_rows == 0:
                    break
                
                # Parse each row
                for i in range(1, table_rows + 1):
                    row_data = self._parse_table_row(i, driver, header_list)
                    if row_data:
                        table_data.append(row_data)
                
                # Check if we have enough data
                if len(table_data) >= total_crypto:
                    logging.info(f'Collected {len(table_data)} cryptocurrencies, stopping')
                    break
                
                # Try to click "Next" button if we need more data
                try:
                    element = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable(
                            (By.XPATH, '//*[@id="scr-res-table"]/div[2]/button[3]')
                        )
                    )
                    element.click()
                    page_num += 1
                    time.sleep(2)  # Wait for page to load
                except Exception as e:
                    logging.info(f'No more pages to scrape or error clicking Next: {e}')
                    break
            
            return table_data
        except Exception as e:
            logging.error(f"Error parsing multiple pages: {e}")
            return table_data

    def scrape_crypto_data(self, total_crypto=100, force_refresh=False):
        """
        Scrape cryptocurrency data from Yahoo Finance.
        
        Args:
            total_crypto: Maximum number of cryptocurrencies to collect
            force_refresh: Whether to force a cache refresh
            
        Returns:
            List of cryptocurrency data dictionaries
        """
        # Use cached data if available and not expired
        current_time = time.time()
        if (not force_refresh and 
            self.data_cache is not None and 
            current_time - self.last_cache_time < self.cache_expiry):
            logging.info("Using cached cryptocurrency data")
            return self.data_cache
        
        if not self.available:
            logging.warning("Selenium not available, cannot scrape Yahoo Finance")
            return []
        
        driver = None
        try:
            logging.info(f"Scraping Yahoo Finance for top {total_crypto} cryptocurrencies")
            driver = self._create_driver()
            
            if not driver:
                return []
            
            # Navigate to Yahoo Finance cryptocurrencies page
            driver.get(self.yahoo_finance_url)
            time.sleep(3)  # Wait for page to load
            
            # Parse cryptocurrency data
            crypto_data = self._parse_multiple_pages(driver, total_crypto)
            
            # Update cache
            self.data_cache = crypto_data
            self.last_cache_time = current_time
            
            logging.info(f"Successfully scraped {len(crypto_data)} cryptocurrencies from Yahoo Finance")
            return crypto_data
        except Exception as e:
            logging.error(f"Error scraping Yahoo Finance: {e}")
            return []
        finally:
            if driver:
                driver.quit()

    def get_trending_cryptos(self, limit=10):
        """
        Get trending cryptocurrencies based on percent change.
        
        Args:
            limit: Maximum number of trending cryptos to return
            
        Returns:
            List of trending cryptocurrency dictionaries
        """
        try:
            # Get crypto data
            crypto_data = self.scrape_crypto_data()
            
            if not crypto_data:
                return []
            
            # Process data to get trending cryptos
            for crypto in crypto_data:
                # Clean up % Change value for sorting
                change_key = '% Change'
                if change_key in crypto:
                    change_str = crypto[change_key].replace('%', '').replace(',', '').strip()
                    try:
                        crypto['change_value'] = abs(float(change_str))
                    except (ValueError, TypeError):
                        crypto['change_value'] = 0
                else:
                    crypto['change_value'] = 0
            
            # Sort by absolute percent change
            trending = sorted(crypto_data, key=lambda x: x.get('change_value', 0), reverse=True)
            return trending[:limit]
        except Exception as e:
            logging.error(f"Error getting trending cryptos: {e}")
            return []

    def get_market_overview(self):
        """
        Get cryptocurrency market overview statistics.
        
        Returns:
            Dictionary with market overview data
        """
        try:
            # Get crypto data
            crypto_data = self.scrape_crypto_data()
            
            if not crypto_data:
                return {}
            
            # Calculate market overview statistics
            total_market_cap = 0
            total_volume = 0
            positive_changes = 0
            
            for crypto in crypto_data:
                # Process market cap
                market_cap_key = 'Market Cap'
                if market_cap_key in crypto and crypto[market_cap_key]:
                    market_cap_str = crypto[market_cap_key].replace('$', '').replace(',', '').strip()
                    if market_cap_str.lower().endswith('t'):
                        market_cap = float(market_cap_str[:-1]) * 1e12
                    elif market_cap_str.lower().endswith('b'):
                        market_cap = float(market_cap_str[:-1]) * 1e9
                    elif market_cap_str.lower().endswith('m'):
                        market_cap = float(market_cap_str[:-1]) * 1e6
                    else:
                        try:
                            market_cap = float(market_cap_str)
                        except (ValueError, TypeError):
                            market_cap = 0
                    total_market_cap += market_cap
                
                # Process volume
                volume_key = 'Volume'
                if volume_key in crypto and crypto[volume_key]:
                    volume_str = crypto[volume_key].replace('$', '').replace(',', '').strip()
                    if volume_str.lower().endswith('t'):
                        volume = float(volume_str[:-1]) * 1e12
                    elif volume_str.lower().endswith('b'):
                        volume = float(volume_str[:-1]) * 1e9
                    elif volume_str.lower().endswith('m'):
                        volume = float(volume_str[:-1]) * 1e6
                    else:
                        try:
                            volume = float(volume_str)
                        except (ValueError, TypeError):
                            volume = 0
                    total_volume += volume
                
                # Count positive changes
                change_key = '% Change'
                if change_key in crypto:
                    change_str = crypto[change_key].replace('%', '').replace(',', '').strip()
                    try:
                        change_value = float(change_str)
                        if change_value > 0:
                            positive_changes += 1
                    except (ValueError, TypeError):
                        pass
            
            # Calculate market sentiment
            if len(crypto_data) > 0:
                positive_ratio = positive_changes / len(crypto_data)
                if positive_ratio > 0.7:
                    market_sentiment = "very_bullish"
                elif positive_ratio > 0.5:
                    market_sentiment = "bullish"
                elif positive_ratio < 0.3:
                    market_sentiment = "very_bearish"
                elif positive_ratio < 0.5:
                    market_sentiment = "bearish"
                else:
                    market_sentiment = "neutral"
            else:
                market_sentiment = "unknown"
                positive_ratio = 0
            
            return {
                "total_market_cap": total_market_cap,
                "total_volume": total_volume,
                "positive_coins_percent": positive_ratio * 100,
                "total_coins_analyzed": len(crypto_data),
                "market_sentiment": market_sentiment,
            }
        except Exception as e:
            logging.error(f"Error getting market overview: {e}")
            return {}


class YahooFinanceSource:
    """
    Data source for Yahoo Finance cryptocurrency data.
    """

    def __init__(self):
        """Initialize the Yahoo Finance data source."""
        self.scraper = EnhancedYahooFinanceCryptoScraper()
        self.available = self.scraper.available

    def get_trends(self, topic: Optional[str] = None) -> Dict[str, Any]:
        """
        Get cryptocurrency trend data from Yahoo Finance, optionally filtered by topic.
        
        Args:
            topic: Optional topic to filter trends by
            
        Returns:
            Dictionary with cryptocurrency trend data
        """
        if not self.available:
            return {"error": "Yahoo Finance data source not available"}
            
        try:
            # Get market overview stats
            market_overview = self.scraper.get_market_overview()
            
            # Get trending cryptocurrencies
            trending_cryptos = self.scraper.get_trending_cryptos(limit=20)
            
            # Filter by topic if provided
            filtered_cryptos = []
            if topic and trending_cryptos:
                topic_lower = topic.lower()
                filtered_cryptos = [
                    crypto for crypto in trending_cryptos 
                    if any(topic_lower in str(val).lower() for val in crypto.values())
                ]
            
            result = {
                "market_overview": market_overview,
                "trending_cryptos": filtered_cryptos if topic else trending_cryptos,
                "source": "yahoo_finance"
            }
            
            return result
        except Exception as e:
            logging.error(f"Error retrieving Yahoo Finance cryptocurrency trends: {e}")
            return {"error": str(e)}