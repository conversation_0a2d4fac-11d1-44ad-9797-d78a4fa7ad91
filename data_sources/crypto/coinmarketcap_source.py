"""
CoinMarketCap Data Source Module

This module provides a data source for cryptocurrency data from CoinMarketCap,
integrated with the anti-scraping framework.
"""

import logging
import sys
import os
import time
from typing import Dict, Any, List, Optional

# Try to import dependencies
try:
    # Add the parent directory to system path to import the pycoincap module
    sys.path.append(os.path.join(os.path.dirname(__file__), "../.."))
    from coinmarketcap_data_collector.pycoincap.core import CryptoMarket, Coin, Stats
    COINMARKETCAP_AVAILABLE = True
except ImportError:
    COINMARKETCAP_AVAILABLE = False
    logging.warning("CoinMarketCap package (pycoincap) not available. Some features will be limited.")

# Try to import anti-scraping module
try:
    from anti_scraping.core import AntiScrapingMiddleware
    ANTI_SCRAPING_AVAILABLE = True
except ImportError:
    ANTI_SCRAPING_AVAILABLE = False
    logging.warning("Anti-scraping middleware not available. Using standard requests.")

import requests


class EnhancedCryptoMarket:
    """
    Enhanced CoinMarketCap API client with anti-scraping measures.
    """

    def __init__(self):
        """Initialize the enhanced crypto market client."""
        self.__COIN_MARKET_CAP_URL = 'https://api.coinmarketcap.com/v1/'
        self.original_client = CryptoMarket() if COINMARKETCAP_AVAILABLE else None
        self.anti_scraping = AntiScrapingMiddleware() if ANTI_SCRAPING_AVAILABLE else None
        self.available = COINMARKETCAP_AVAILABLE

    def __call_market(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call the CoinMarketCap API with enhanced anti-scraping measures.
        
        Args:
            endpoint: API endpoint to call
            params: Parameters to pass to the API
            
        Returns:
            Dictionary with API response data
        """
        if not self.available:
            logging.warning("CoinMarketCap API client not available")
            return {}
            
        try:
            if self.anti_scraping:
                # Get enhanced headers
                headers = self.anti_scraping.get_enhanced_headers()
                
                # Use session with proxy rotation
                session = self.anti_scraping.get_session_with_proxy()
                
                response = session.get(
                    self.__COIN_MARKET_CAP_URL + endpoint, 
                    params=params,
                    headers=headers
                )
            else:
                # Use standard requests if anti-scraping is not available
                response = requests.get(
                    self.__COIN_MARKET_CAP_URL + endpoint, 
                    params=params
                )
                
            if response.status_code != 200:
                response.raise_for_status()
                
            data = response.json()
            return data
        except requests.exceptions.HTTPError as e:
            logging.error(f"HTTP error when calling CoinMarketCap API: {e}")
            return {}
        except requests.exceptions.RequestException as e:
            logging.error(f"Request error when calling CoinMarketCap API: {e}")
            return {}
        except Exception as e:
            logging.error(f"Unexpected error when calling CoinMarketCap API: {e}")
            return {}

    def get_coins(self, start: int = 1, limit: int = 100, convert: str = "USD") -> List[Dict[str, Any]]:
        """
        Get multiple cryptocurrencies data.
        
        Args:
            start: Starting rank to retrieve
            limit: Maximum number of cryptocurrencies to retrieve
            convert: Currency to convert prices to
            
        Returns:
            List of cryptocurrency data dictionaries
        """
        if not self.available:
            return []
            
        result = []
        for i in range(start, start + limit, 10):  # Process in batches of 10
            try:
                params = {"start": i, "limit": min(10, start + limit - i), "convert": convert}
                data = self.__call_market('ticker/', params)
                
                if isinstance(data, list):
                    result.extend(data)
                
                # Add delay to avoid rate limiting
                time.sleep(1)
            except Exception as e:
                logging.error(f"Error retrieving batch starting at {i}: {e}")
        
        return result

    def get_coin(self, coin_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get data for a specific cryptocurrency.
        
        Args:
            coin_id: ID or symbol of the cryptocurrency
            **kwargs: Additional parameters
            
        Returns:
            Dictionary with cryptocurrency data
        """
        if not self.available:
            return {}
            
        try:
            params = {}
            params.update(kwargs)
            
            data = self.__call_market(f'ticker/{coin_id}', params)
            
            if isinstance(data, list) and data:
                return data[0]
            return {}
        except Exception as e:
            logging.error(f"Error retrieving coin {coin_id}: {e}")
            return {}

    def get_stats(self, convert: str = "USD") -> Dict[str, Any]:
        """
        Get global cryptocurrency market statistics.
        
        Args:
            convert: Currency to convert values to
            
        Returns:
            Dictionary with market statistics
        """
        if not self.available:
            return {}
            
        try:
            params = {"convert": convert}
            return self.__call_market('global', params)
        except Exception as e:
            logging.error(f"Error retrieving global stats: {e}")
            return {}

    def get_trending_coins(self) -> List[Dict[str, Any]]:
        """
        Get trending cryptocurrencies based on 24h price changes.
        
        Returns:
            List of trending cryptocurrencies
        """
        try:
            # Get top 100 coins
            coins = self.get_coins(limit=100)
            
            # Sort by 24h percent change (absolute value)
            if coins:
                trending = sorted(
                    coins,
                    key=lambda x: abs(float(x.get('percent_change_24h', 0) or 0)),
                    reverse=True
                )
                return trending[:10]  # Return top 10 trending coins
            return []
        except Exception as e:
            logging.error(f"Error getting trending coins: {e}")
            return []

    def get_market_sentiments(self) -> Dict[str, Any]:
        """
        Get market sentiment indicators based on price movements.
        
        Returns:
            Dictionary with market sentiment data
        """
        try:
            # Get market stats
            stats = self.get_stats()
            
            # Get top 30 coins
            coins = self.get_coins(limit=30)
            
            if not coins:
                return {}
                
            # Calculate sentiment metrics
            pos_change_count = sum(1 for c in coins if float(c.get('percent_change_24h', 0) or 0) > 0)
            neg_change_count = len(coins) - pos_change_count
            
            # Calculate average changes
            avg_change_1h = sum(float(c.get('percent_change_1h', 0) or 0) for c in coins) / len(coins)
            avg_change_24h = sum(float(c.get('percent_change_24h', 0) or 0) for c in coins) / len(coins)
            avg_change_7d = sum(float(c.get('percent_change_7d', 0) or 0) for c in coins) / len(coins)
            
            # Calculate market sentiment
            if pos_change_count > neg_change_count * 2:
                market_sentiment = "very_bullish"
            elif pos_change_count > neg_change_count:
                market_sentiment = "bullish"
            elif neg_change_count > pos_change_count * 2:
                market_sentiment = "very_bearish"
            elif neg_change_count > pos_change_count:
                market_sentiment = "bearish"
            else:
                market_sentiment = "neutral"
                
            return {
                "market_sentiment": market_sentiment,
                "positive_coins_percent": (pos_change_count / len(coins)) * 100,
                "average_change_1h": avg_change_1h,
                "average_change_24h": avg_change_24h,
                "average_change_7d": avg_change_7d,
                "btc_dominance": stats.get("bitcoin_percentage_of_market_cap", 0),
                "total_market_cap_usd": stats.get("total_market_cap_usd", 0)
            }
        except Exception as e:
            logging.error(f"Error calculating market sentiments: {e}")
            return {}


class CoinMarketCapSource:
    """
    Data source for CoinMarketCap cryptocurrency data.
    """

    def __init__(self):
        """Initialize the CoinMarketCap data source."""
        self.client = EnhancedCryptoMarket()
        self.available = self.client.available

    def get_trends(self, topic: Optional[str] = None) -> Dict[str, Any]:
        """
        Get cryptocurrency trend data, optionally filtered by topic.
        
        Args:
            topic: Optional topic to filter trends by
            
        Returns:
            Dictionary with cryptocurrency trend data
        """
        if not self.available:
            return {"error": "CoinMarketCap data source not available"}
            
        try:
            # Get market stats
            market_stats = self.client.get_stats()
            
            # Get trending coins
            trending_coins = self.client.get_trending_coins()
            
            # Get market sentiments
            market_sentiments = self.client.get_market_sentiments()
            
            # Filter by topic if provided
            filtered_coins = []
            if topic and trending_coins:
                topic_lower = topic.lower()
                filtered_coins = [
                    coin for coin in trending_coins 
                    if topic_lower in coin.get('name', '').lower() or 
                       topic_lower in coin.get('symbol', '').lower()
                ]
            
            result = {
                "market_stats": market_stats,
                "trending_coins": filtered_coins if topic else trending_coins,
                "market_sentiments": market_sentiments,
                "source": "coinmarketcap"
            }
            
            return result
        except Exception as e:
            logging.error(f"Error retrieving cryptocurrency trends: {e}")
            return {"error": str(e)}