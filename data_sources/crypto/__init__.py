"""
Cryptocurrency Data Sources Package

This package provides data sources for cryptocurrency data from CoinMarketCap
and Yahoo Finance, integrated with the anti-scraping framework.
"""

import logging
from typing import Dict, Any, Optional

# Initialize logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import data sources if available
try:
    from .coinmarketcap_source import CoinMarketCapSource
    COINMARKETCAP_AVAILABLE = True
except ImportError:
    COINMARKETCAP_AVAILABLE = False
    logger.warning("CoinMarketCap data source not available")

try:
    from .yahoo_finance_source import YahooFinanceSource
    YAHOO_FINANCE_AVAILABLE = True
except ImportError:
    YAHOO_FINANCE_AVAILABLE = False
    logger.warning("Yahoo Finance data source not available")


class CryptoTrendAnalyzer:
    """
    Analyzer for cryptocurrency trends from multiple sources.
    """
    
    def __init__(self):
        """Initialize the crypto trend analyzer with available data sources."""
        self.coinmarketcap = CoinMarketCapSource() if COINMARKETCAP_AVAILABLE else None
        self.yahoo_finance = YahooFinanceSource() if YAHOO_FINANCE_AVAILABLE else None
        self.available = COINMARKETCAP_AVAILABLE or YAHOO_FINANCE_AVAILABLE
    
    def get_trending_cryptocurrencies(self, topic: Optional[str] = None) -> Dict[str, Any]:
        """
        Get trending cryptocurrencies from multiple sources.
        
        Args:
            topic: Optional topic to filter trends by
            
        Returns:
            Dictionary with combined cryptocurrency trend data
        """
        results = {"sources_available": []}
        
        # Get CoinMarketCap data if available
        if self.coinmarketcap and self.coinmarketcap.available:
            coinmarketcap_data = self.coinmarketcap.get_trends(topic)
            if not coinmarketcap_data.get("error"):
                results["coinmarketcap"] = coinmarketcap_data
                results["sources_available"].append("coinmarketcap")
                logger.info("CoinMarketCap data collected successfully")
                
        # Get Yahoo Finance data if available
        if self.yahoo_finance and self.yahoo_finance.available:
            yahoo_data = self.yahoo_finance.get_trends(topic)
            if not yahoo_data.get("error"):
                results["yahoo_finance"] = yahoo_data
                results["sources_available"].append("yahoo_finance")
                logger.info("Yahoo Finance data collected successfully")
        
        # Combine insights from both sources
        if results["sources_available"]:
            results["combined_insights"] = self._combine_crypto_insights(results)
            
        return results
    
    def _combine_crypto_insights(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine insights from different cryptocurrency data sources.
        
        Args:
            data: Dictionary with data from different sources
            
        Returns:
            Dictionary with combined insights
        """
        combined = {
            "trending_coins": [],
            "market_sentiment": "unknown",
            "total_market_cap": 0,
            "positive_sentiment_ratio": 0,
        }
        
        sentiment_scores = {
            "very_bullish": 2,
            "bullish": 1,
            "neutral": 0,
            "bearish": -1,
            "very_bearish": -2,
            "unknown": 0
        }
        
        sentiment_count = 0
        sentiment_sum = 0
        
        # Process CoinMarketCap data
        if "coinmarketcap" in data:
            # Extract trending coins
            cmc_trending = data["coinmarketcap"].get("trending_coins", [])
            for coin in cmc_trending:
                combined["trending_coins"].append({
                    "name": coin.get("name", "Unknown"),
                    "symbol": coin.get("symbol", "Unknown"),
                    "price_usd": coin.get("price_usd", "0"),
                    "percent_change_24h": coin.get("percent_change_24h", "0"),
                    "source": "coinmarketcap"
                })
                
            # Extract market sentiment
            cmc_sentiment = data["coinmarketcap"].get("market_sentiments", {}).get("market_sentiment")
            if cmc_sentiment:
                sentiment_sum += sentiment_scores.get(cmc_sentiment, 0)
                sentiment_count += 1
                
            # Extract market cap
            cmc_market_cap = data["coinmarketcap"].get("market_stats", {}).get("total_market_cap_usd", 0)
            if cmc_market_cap:
                combined["total_market_cap"] = float(cmc_market_cap)
                
            # Extract positive sentiment ratio
            cmc_positive_ratio = data["coinmarketcap"].get("market_sentiments", {}).get("positive_coins_percent", 0)
            if cmc_positive_ratio:
                combined["positive_sentiment_ratio"] = cmc_positive_ratio
            
        # Process Yahoo Finance data
        if "yahoo_finance" in data:
            # Extract trending coins
            yahoo_trending = data["yahoo_finance"].get("trending_cryptos", [])
            for coin in yahoo_trending:
                # Check if this coin is already in the list from CoinMarketCap
                symbol = coin.get("Symbol", "Unknown")
                if not any(c.get("symbol") == symbol for c in combined["trending_coins"]):
                    combined["trending_coins"].append({
                        "name": coin.get("Name", "Unknown"),
                        "symbol": symbol,
                        "price_usd": coin.get("Price (Intraday)", "0"),
                        "percent_change_24h": coin.get("% Change", "0"),
                        "source": "yahoo_finance"
                    })
                    
            # Extract market sentiment
            yahoo_sentiment = data["yahoo_finance"].get("market_overview", {}).get("market_sentiment")
            if yahoo_sentiment:
                sentiment_sum += sentiment_scores.get(yahoo_sentiment, 0)
                sentiment_count += 1
                
            # Extract market cap if not already set
            if combined["total_market_cap"] == 0:
                yahoo_market_cap = data["yahoo_finance"].get("market_overview", {}).get("total_market_cap", 0)
                if yahoo_market_cap:
                    combined["total_market_cap"] = float(yahoo_market_cap)
                    
            # Extract positive sentiment ratio if not already set
            if combined["positive_sentiment_ratio"] == 0:
                yahoo_positive_ratio = data["yahoo_finance"].get("market_overview", {}).get("positive_coins_percent", 0)
                if yahoo_positive_ratio:
                    combined["positive_sentiment_ratio"] = yahoo_positive_ratio
        
        # Calculate overall market sentiment
        if sentiment_count > 0:
            avg_sentiment = sentiment_sum / sentiment_count
            if avg_sentiment >= 1.5:
                combined["market_sentiment"] = "very_bullish"
            elif avg_sentiment >= 0.5:
                combined["market_sentiment"] = "bullish"
            elif avg_sentiment <= -1.5:
                combined["market_sentiment"] = "very_bearish"
            elif avg_sentiment <= -0.5:
                combined["market_sentiment"] = "bearish"
            else:
                combined["market_sentiment"] = "neutral"
                
        return combined


# Create an instance of the crypto trend analyzer
crypto_trend_analyzer = CryptoTrendAnalyzer()


def get_crypto_trends(topic: Optional[str] = None) -> Dict[str, Any]:
    """
    Get cryptocurrency trends data, optionally filtered by topic.
    
    Args:
        topic: Optional topic to filter trends by
        
    Returns:
        Dictionary with cryptocurrency trend data
    """
    if crypto_trend_analyzer.available:
        return crypto_trend_analyzer.get_trending_cryptocurrencies(topic)
    else:
        return {"error": "No cryptocurrency data sources available"}