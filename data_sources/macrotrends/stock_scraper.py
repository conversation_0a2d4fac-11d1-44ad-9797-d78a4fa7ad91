"""
MacroTrends data scraper with anti-scraping capabilities.
Adapted from the MacroTrends-data-scrapper repository.
"""

import os
import csv
import json
import logging
import time
import random
import datetime
from typing import Dict, List, Any, Optional, Union, Tuple

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedMacroTrendsScraper:
    """
    Enhanced MacroTrends scraper with anti-scraping capabilities.
    """
    
    def __init__(self, output_dir: str = "data", headless: bool = True):
        """
        Initialize the MacroTrends scraper.
        
        Args:
            output_dir: Directory to save output data
            headless: Whether to run browser in headless mode
        """
        self.output_dir = output_dir
        self.headless = headless
        self.anti_scraping = AntiScrapingMiddleware()
        self.base_url = "https://www.macrotrends.net/stocks/stock-screener"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
    def scrape_stock_screener(self, criteria: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Scrape stock screener data from MacroTrends.
        
        Args:
            criteria: Dictionary of screening criteria 
                     (e.g. {"market_cap_min": 1000000000, "pe_ratio_max": 30})
            
        Returns:
            List of stock data dictionaries
        """
        logger.info("Starting MacroTrends stock screener scraping")
        
        # Get browser options with anti-scraping measures
        options = self.anti_scraping.get_enhanced_browser_options(self.headless)
        
        # Get rotating proxy
        proxy = self.anti_scraping.get_next_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        driver = None
        try:
            # Initialize driver with anti-detection measures
            driver = webdriver.Chrome(options=options)
            
            # Set window size for consistent rendering
            driver.set_window_size(1366, 768)
            
            # Navigate to stock screener
            driver.get(self.base_url)
            
            # Wait for content to load
            wait = WebDriverWait(driver, 15)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "table.stock-screener-table")))
            
            # Simulate human behavior
            self.anti_scraping.simulate_human_behavior(driver)
            
            # Apply screening criteria if provided
            if criteria:
                self._apply_screening_criteria(driver, wait, criteria)
            
            # Extract stock data
            stocks = self._extract_stock_data(driver, wait)
            
            # Save data to file
            csv_path = self._save_stocks_csv(stocks)
            
            logger.info(f"Successfully scraped {len(stocks)} stocks from MacroTrends")
            return stocks
            
        except Exception as e:
            logger.error(f"Error scraping MacroTrends stock screener: {e}")
            return []
            
        finally:
            if driver:
                driver.quit()
    
    def _apply_screening_criteria(self, driver, wait, criteria: Dict) -> None:
        """
        Apply screening criteria to the stock screener.
        
        Args:
            driver: Selenium WebDriver instance
            wait: WebDriverWait instance
            criteria: Dictionary of screening criteria
        """
        try:
            logger.info("Applying screening criteria")
            
            # Click on the filters button if it exists
            try:
                filter_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.filters-button")))
                filter_button.click()
                time.sleep(random.uniform(1, 2))
            except:
                logger.warning("Could not find filters button, proceeding with default view")
            
            # Apply market cap filter
            if "market_cap_min" in criteria:
                try:
                    market_cap_min = wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "input.market-cap-min")))
                    market_cap_min.clear()
                    market_cap_min.send_keys(str(criteria["market_cap_min"]))
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set minimum market cap")
            
            if "market_cap_max" in criteria:
                try:
                    market_cap_max = wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "input.market-cap-max")))
                    market_cap_max.clear()
                    market_cap_max.send_keys(str(criteria["market_cap_max"]))
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set maximum market cap")
            
            # Apply PE ratio filter
            if "pe_ratio_min" in criteria:
                try:
                    pe_min = wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "input.pe-ratio-min")))
                    pe_min.clear()
                    pe_min.send_keys(str(criteria["pe_ratio_min"]))
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set minimum PE ratio")
            
            if "pe_ratio_max" in criteria:
                try:
                    pe_max = wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "input.pe-ratio-max")))
                    pe_max.clear()
                    pe_max.send_keys(str(criteria["pe_ratio_max"]))
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set maximum PE ratio")
            
            # Apply dividend yield filter
            if "dividend_yield_min" in criteria:
                try:
                    div_min = wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "input.dividend-yield-min")))
                    div_min.clear()
                    div_min.send_keys(str(criteria["dividend_yield_min"]))
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set minimum dividend yield")
            
            # Apply sector filter
            if "sector" in criteria:
                try:
                    sector_select = Select(wait.until(EC.presence_of_element_located(
                        (By.CSS_SELECTOR, "select.sector-select"))))
                    sector_select.select_by_visible_text(criteria["sector"])
                    time.sleep(random.uniform(0.5, 1))
                except:
                    logger.warning("Could not set sector filter")
            
            # Apply filters by clicking the apply button
            try:
                apply_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.apply-filters")))
                apply_button.click()
                time.sleep(random.uniform(2, 3))
            except:
                logger.warning("Could not click apply filters button")
            
            logger.info("Finished applying screening criteria")
            
        except Exception as e:
            logger.error(f"Error applying screening criteria: {e}")
    
    def _extract_stock_data(self, driver, wait) -> List[Dict[str, Any]]:
        """
        Extract stock data from the stock screener table.
        
        Args:
            driver: Selenium WebDriver instance
            wait: WebDriverWait instance
            
        Returns:
            List of stock data dictionaries
        """
        stocks = []
        
        try:
            # Get table headers
            headers = []
            header_elements = driver.find_elements(By.CSS_SELECTOR, "table.stock-screener-table th")
            
            for header in header_elements:
                header_text = header.text.strip()
                if header_text:  # Skip empty headers
                    headers.append(header_text)
            
            # Extract data rows
            rows = driver.find_elements(By.CSS_SELECTOR, "table.stock-screener-table tbody tr")
            
            for row in rows:
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    
                    if len(cells) < len(headers):
                        logger.warning(f"Row has fewer cells ({len(cells)}) than headers ({len(headers)})")
                        continue
                    
                    stock_data = {}
                    
                    # Extract data from each cell
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            header = headers[i]
                            cell_text = cell.text.strip()
                            stock_data[header] = cell_text
                    
                    # Extract ticker and company name
                    try:
                        ticker_element = row.find_element(By.CSS_SELECTOR, "td.ticker")
                        ticker = ticker_element.text.strip()
                        stock_data["Ticker"] = ticker
                        
                        company_element = row.find_element(By.CSS_SELECTOR, "td.company-name")
                        company = company_element.text.strip()
                        stock_data["Company Name"] = company
                    except:
                        # Fall back to using positional data if specific classes aren't found
                        pass
                    
                    # Extract additional metrics (if not already captured in headers)
                    metrics = ["Market Cap", "PE Ratio", "Price", "Dividend Yield", "EPS", "52 Week Range", "Revenue Growth"]
                    for metric in metrics:
                        if metric not in stock_data:
                            try:
                                metric_element = row.find_element(By.CSS_SELECTOR, f"td.{metric.lower().replace(' ', '-')}")
                                stock_data[metric] = metric_element.text.strip()
                            except:
                                pass
                    
                    # Add timestamp
                    stock_data["Timestamp"] = datetime.datetime.now().isoformat()
                    
                    stocks.append(stock_data)
                    
                    # Small random delay between processing rows
                    time.sleep(random.uniform(0.05, 0.15))
                
                except Exception as e:
                    logger.warning(f"Error processing stock data row: {e}")
            
        except Exception as e:
            logger.error(f"Error extracting stock data: {e}")
        
        return stocks
    
    def _save_stocks_csv(self, stocks: List[Dict[str, Any]]) -> str:
        """
        Save stock data to a CSV file.
        
        Args:
            stocks: List of stock data dictionaries
            
        Returns:
            Path to the saved CSV file
        """
        if not stocks:
            return ""
            
        # Create filename with current date
        date_str = datetime.datetime.now().strftime("%Y-%m-%d")
        filename = f"macrotrends_stocks_{date_str}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            # Get all headers from all stocks
            all_headers = set()
            for stock in stocks:
                all_headers.update(stock.keys())
            
            # Remove timestamp from headers list (will be added at the end)
            if "Timestamp" in all_headers:
                all_headers.remove("Timestamp")
            
            # Sort headers to ensure consistent order, with Ticker and Company Name first
            headers = []
            if "Ticker" in all_headers:
                headers.append("Ticker")
                all_headers.remove("Ticker")
            
            if "Company Name" in all_headers:
                headers.append("Company Name")
                all_headers.remove("Company Name")
            
            # Add remaining headers alphabetically
            headers.extend(sorted(all_headers))
            
            # Add timestamp at the end
            headers.append("Timestamp")
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=headers)
                writer.writeheader()
                writer.writerows(stocks)
                
            logger.info(f"Saved MacroTrends stock data to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error saving stock data: {e}")
            return ""
    
    def scrape_financial_metrics(self, ticker: str) -> Dict[str, Any]:
        """
        Scrape detailed financial metrics for a specific stock.
        
        Args:
            ticker: Stock ticker symbol
            
        Returns:
            Dictionary of financial metrics
        """
        logger.info(f"Scraping financial metrics for ticker: {ticker}")
        
        # Get browser options with anti-scraping measures
        options = self.anti_scraping.get_enhanced_browser_options(self.headless)
        
        # Get rotating proxy
        proxy = self.anti_scraping.get_next_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        driver = None
        try:
            # Initialize driver with anti-detection measures
            driver = webdriver.Chrome(options=options)
            
            # Set window size for consistent rendering
            driver.set_window_size(1366, 768)
            
            # Navigate to stock page
            url = f"https://www.macrotrends.net/stocks/charts/{ticker}"
            driver.get(url)
            
            # Wait for content to load
            wait = WebDriverWait(driver, 15)
            
            # Handle possible company selection if ticker has multiple matches
            try:
                company_select = wait.until(EC.visibility_of_element_located(
                    (By.CSS_SELECTOR, "select.company-search-select")))
                select = Select(company_select)
                select.select_by_index(0)  # Select first match
                time.sleep(random.uniform(1, 2))
            except (TimeoutException, NoSuchElementException):
                # No selection needed, proceed
                pass
                
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.jumbotron")))
            
            # Simulate human behavior
            self.anti_scraping.simulate_human_behavior(driver)
            
            # Extract company profile and metrics
            metrics = self._extract_financial_metrics(driver, wait, ticker)
            
            # Save data to file
            json_path = self._save_metrics_json(metrics, ticker)
            
            logger.info(f"Successfully scraped financial metrics for {ticker}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error scraping financial metrics for {ticker}: {e}")
            return {"ticker": ticker, "error": str(e)}
            
        finally:
            if driver:
                driver.quit()
    
    def _extract_financial_metrics(self, driver, wait, ticker: str) -> Dict[str, Any]:
        """
        Extract financial metrics from the stock page.
        
        Args:
            driver: Selenium WebDriver instance
            wait: WebDriverWait instance
            ticker: Stock ticker symbol
            
        Returns:
            Dictionary of financial metrics
        """
        metrics = {"ticker": ticker, "timestamp": datetime.datetime.now().isoformat()}
        
        try:
            # Extract company name from jumbotron
            try:
                jumbotron = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.jumbotron")))
                company_name = jumbotron.find_element(By.CSS_SELECTOR, "h2").text.strip()
                metrics["company_name"] = company_name
            except:
                logger.warning(f"Could not extract company name for {ticker}")
            
            # Extract current stock price
            try:
                price_element = driver.find_element(By.CSS_SELECTOR, "td.price-today")
                price = price_element.text.strip()
                metrics["current_price"] = price
            except:
                logger.warning(f"Could not extract current price for {ticker}")
            
            # Extract financial metrics from the overview page
            key_metrics = {
                "market_cap": "Market Cap",
                "pe_ratio": "PE Ratio",
                "eps": "EPS (TTM)",
                "dividend_yield": "Dividend Yield",
                "dividend_per_share": "Dividend Per Share",
                "revenue": "Revenue (TTM)",
                "revenue_growth": "Revenue Growth (YoY)"
            }
            
            for key, label in key_metrics.items():
                try:
                    # Try to find metric by label
                    metric_row = driver.find_element(By.XPATH, f"//td[contains(text(), '{label}')]/parent::tr")
                    value_cell = metric_row.find_element(By.XPATH, "./td[2]")
                    metrics[key] = value_cell.text.strip()
                except:
                    try:
                        # Try alternative approach with data attributes
                        metric_element = driver.find_element(By.CSS_SELECTOR, f"[data-metric='{key}']")
                        metrics[key] = metric_element.text.strip()
                    except:
                        logger.warning(f"Could not extract {label} for {ticker}")
            
            # Navigate to income statement page
            try:
                income_link = driver.find_element(By.XPATH, "//a[contains(text(), 'Income Statement')]")
                income_link.click()
                time.sleep(random.uniform(2, 3))
                
                # Extract annual revenue
                try:
                    revenue_rows = driver.find_elements(By.XPATH, "//td[contains(text(), 'Revenue')]/parent::tr")
                    if revenue_rows:
                        revenue_cells = revenue_rows[0].find_elements(By.TAG_NAME, "td")
                        annual_revenue = {}
                        
                        for i in range(1, min(4, len(revenue_cells))):  # Get last 3 years
                            try:
                                year = driver.find_element(By.XPATH, f"//table//th[{i+1}]").text.strip()
                                annual_revenue[year] = revenue_cells[i].text.strip()
                            except:
                                pass
                                
                        metrics["annual_revenue"] = annual_revenue
                except:
                    logger.warning(f"Could not extract annual revenue for {ticker}")
            except:
                logger.warning(f"Could not navigate to income statement for {ticker}")
            
            # Navigate to balance sheet
            try:
                balance_link = driver.find_element(By.XPATH, "//a[contains(text(), 'Balance Sheet')]")
                balance_link.click()
                time.sleep(random.uniform(2, 3))
                
                # Extract total assets and liabilities
                for item in ["Total Assets", "Total Liabilities"]:
                    try:
                        item_rows = driver.find_elements(By.XPATH, f"//td[contains(text(), '{item}')]/parent::tr")
                        if item_rows:
                            item_cells = item_rows[0].find_elements(By.TAG_NAME, "td")
                            key = item.lower().replace(" ", "_")
                            
                            if len(item_cells) > 1:
                                metrics[key] = item_cells[1].text.strip()
                    except:
                        logger.warning(f"Could not extract {item} for {ticker}")
            except:
                logger.warning(f"Could not navigate to balance sheet for {ticker}")
            
        except Exception as e:
            logger.error(f"Error extracting financial metrics for {ticker}: {e}")
        
        return metrics
    
    def _save_metrics_json(self, metrics: Dict[str, Any], ticker: str) -> str:
        """
        Save financial metrics to a JSON file.
        
        Args:
            metrics: Dictionary of financial metrics
            ticker: Stock ticker symbol
            
        Returns:
            Path to the saved JSON file
        """
        if not metrics:
            return ""
            
        # Create filename with current date
        date_str = datetime.datetime.now().strftime("%Y-%m-%d")
        filename = f"{ticker}_metrics_{date_str}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved financial metrics for {ticker} to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error saving financial metrics for {ticker}: {e}")
            return ""