"""
Google Trends scraper with anti-scraping capabilities.
Adapted from the scrapping-google-trends repository.
"""

import os
import json
import logging
import time
import datetime
from typing import Dict, List, Any, Optional
import random

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedGoogleTrendsScraper:
    """
    Enhanced Google Trends scraper with anti-scraping capabilities.
    """
    
    def __init__(self, output_dir: str = "data", headless: bool = True):
        """
        Initialize the Google Trends scraper.
        
        Args:
            output_dir: Directory to save output data
            headless: Whether to run browser in headless mode
        """
        self.output_dir = output_dir
        self.headless = headless
        self.anti_scraping = AntiScrapingMiddleware()
        self.base_url = "https://trends.google.com/trends/trendingsearches/daily?geo=US"
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
    def scrape_trends(self, region: str = "US") -> List[Dict[str, Any]]:
        """
        Scrape trending searches from Google Trends.
        
        Args:
            region: Region code (country) to get trends for
            
        Returns:
            List of trending search dictionaries
        """
        logger.info(f"Starting Google Trends scraping for region: {region}")
        
        # Get browser options with anti-scraping measures
        options = self.anti_scraping.get_enhanced_browser_options(self.headless)
        
        # Get rotating proxy
        proxy = self.anti_scraping.get_next_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        driver = None
        try:
            # Initialize driver with anti-detection measures
            driver = webdriver.Chrome(options=options)
            
            # Set window size for consistent rendering
            driver.set_window_size(1366, 768)
            
            # Navigate to Google Trends
            url = f"https://trends.google.com/trends/trendingsearches/daily?geo={region}"
            driver.get(url)
            
            # Wait for content to load
            wait = WebDriverWait(driver, 15)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.feed-list-wrapper")))
            
            # Simulate human behavior
            self.anti_scraping.simulate_human_behavior(driver)
            
            # Extract trending searches
            trends = self._extract_trends(driver, wait)
            
            # Save trends to file
            json_path = self._save_trends_json(trends, region)
            
            logger.info(f"Successfully scraped {len(trends)} trending searches from Google Trends")
            return trends
            
        except Exception as e:
            logger.error(f"Error scraping Google Trends: {e}")
            return []
            
        finally:
            if driver:
                driver.quit()
    
    def _extract_trends(self, driver, wait) -> List[Dict[str, Any]]:
        """
        Extract trending searches from the Google Trends page.
        
        Args:
            driver: Selenium WebDriver instance
            wait: WebDriverWait instance
            
        Returns:
            List of trending search dictionaries
        """
        trends = []
        
        try:
            # Get all trend cards
            trend_cards = driver.find_elements(By.CSS_SELECTOR, "div.feed-item")
            
            for index, card in enumerate(trend_cards):
                try:
                    # Extract title
                    title_element = card.find_element(By.CSS_SELECTOR, "div.title")
                    title = title_element.text.strip()
                    
                    # Extract search count
                    search_count_element = card.find_element(By.CSS_SELECTOR, "div.search-count-title")
                    search_count = search_count_element.text.strip()
                    
                    # Get related articles if available
                    related_articles = []
                    try:
                        article_elements = card.find_elements(By.CSS_SELECTOR, "a.article")
                        
                        for article in article_elements:
                            article_data = {}
                            
                            # Get article source
                            source_element = article.find_element(By.CSS_SELECTOR, "div.source-and-time span.source")
                            article_data["source"] = source_element.text.strip()
                            
                            # Get article time
                            time_element = article.find_element(By.CSS_SELECTOR, "div.source-and-time span.time")
                            article_data["time"] = time_element.text.strip()
                            
                            # Get article title
                            article_title_element = article.find_element(By.CSS_SELECTOR, "div.article-title")
                            article_data["title"] = article_title_element.text.strip()
                            
                            # Get article URL
                            article_data["url"] = article.get_attribute("href")
                            
                            # Get article image if available
                            try:
                                image_element = article.find_element(By.CSS_SELECTOR, "div.article-image img")
                                article_data["image_url"] = image_element.get_attribute("src")
                            except NoSuchElementException:
                                article_data["image_url"] = None
                            
                            related_articles.append(article_data)
                    
                    except Exception as e:
                        logger.warning(f"Error extracting related articles for {title}: {e}")
                    
                    # Create trend data dictionary
                    trend_data = {
                        "position": index + 1,
                        "title": title,
                        "search_count": search_count,
                        "related_articles": related_articles,
                        "timestamp": datetime.datetime.now().isoformat()
                    }
                    
                    trends.append(trend_data)
                    
                    # Small random delay between processing cards to seem more human-like
                    time.sleep(random.uniform(0.1, 0.3))
                
                except Exception as e:
                    logger.warning(f"Error processing trend card {index}: {e}")
            
        except Exception as e:
            logger.error(f"Error extracting trends: {e}")
        
        return trends
    
    def _save_trends_json(self, trends: List[Dict[str, Any]], region: str) -> str:
        """
        Save trending searches to a JSON file.
        
        Args:
            trends: List of trending search dictionaries
            region: Region code
            
        Returns:
            Path to the saved JSON file
        """
        if not trends:
            return ""
            
        # Create filename with current date
        date_str = datetime.datetime.now().strftime("%Y-%m-%d")
        filename = f"google_trends_{region}_{date_str}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(trends, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved Google Trends data to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Error saving trends data: {e}")
            return ""
    
    def search_trends_archive(self, query: str, days_back: int = 7) -> List[Dict[str, Any]]:
        """
        Search Google Trends archive for a specific query.
        
        Args:
            query: Search query
            days_back: Number of days to look back
            
        Returns:
            List of matching trend dictionaries
        """
        logger.info(f"Searching Google Trends archive for: {query}")
        
        # Get browser options with anti-scraping measures
        options = self.anti_scraping.get_enhanced_browser_options(self.headless)
        
        # Get rotating proxy
        proxy = self.anti_scraping.get_next_proxy()
        if proxy:
            options.add_argument(f'--proxy-server={proxy}')
        
        driver = None
        matching_trends = []
        
        try:
            # Initialize driver with anti-detection measures
            driver = webdriver.Chrome(options=options)
            
            # Set window size for consistent rendering
            driver.set_window_size(1366, 768)
            
            # Calculate date range
            end_date = datetime.datetime.now()
            start_date = end_date - datetime.timedelta(days=days_back)
            
            # Format dates for URL
            start_str = start_date.strftime("%Y%m%d")
            end_str = end_date.strftime("%Y%m%d")
            
            # Navigate to Google Trends with date range
            url = f"https://trends.google.com/trends/trendingsearches/daily?geo=US&date={start_str}_{end_str}"
            driver.get(url)
            
            # Wait for content to load
            wait = WebDriverWait(driver, 15)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.feed-list-wrapper")))
            
            # Simulate human behavior
            self.anti_scraping.simulate_human_behavior(driver)
            
            # Extract all trends
            all_trends = self._extract_trends(driver, wait)
            
            # Filter for matching trends
            query_lower = query.lower()
            for trend in all_trends:
                if query_lower in trend["title"].lower():
                    trend["query_match"] = "title"
                    matching_trends.append(trend)
                    continue
                    
                # Check related articles for match
                for article in trend.get("related_articles", []):
                    if query_lower in article.get("title", "").lower():
                        trend["query_match"] = "article"
                        matching_trends.append(trend)
                        break
            
            logger.info(f"Found {len(matching_trends)} matching trends for query: {query}")
            return matching_trends
            
        except Exception as e:
            logger.error(f"Error searching Google Trends archive: {e}")
            return []
            
        finally:
            if driver:
                driver.quit()