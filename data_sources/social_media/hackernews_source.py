"""
HackerNews data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
from typing import List, Dict, Any, Optional
import requests

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedHackerNewsSource:
    """
    Enhanced HackerNews source with anti-scraping capabilities.
    """
    
    def __init__(self):
        """Initialize the HackerNews data source."""
        self.base_url = "https://hn.algolia.com/api/v1"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search HackerNews for topics.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching HackerNews for: {topic}")
        search_url = f"{self.base_url}/search"
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        params = {
            "query": topic,
            "tags": "story",
            "numericFilters": "points>1",
            "hitsPerPage": num_results,
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(0.5, 2.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(search_url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for hit in data.get("hits", []):
                result = {
                    "source": "hackernews",
                    "title": hit.get("title"),
                    "author": hit.get("author"),
                    "url": hit.get("url") or f"https://news.ycombinator.com/item?id={hit.get('objectID')}",
                    "created_at": hit.get("created_at"),
                    "engagement": {
                        "score": hit.get("points", 0),
                        "comments": hit.get("num_comments", 0),
                    },
                    "engagement_score": self._calculate_engagement_score(hit),
                    "metadata": {"object_id": hit.get("objectID")},
                    "content": hit.get("story_text", ""),
                    "timestamp": time.time()
                }
                results.append(result)
                
                # Small random delay between processing items
                time.sleep(random.uniform(0.05, 0.2))
                
            logger.info(f"Found {len(results)} results for '{topic}' on HackerNews")
            return results
            
        except Exception as e:
            logger.error(f"HackerNews error: {str(e)}")
            return []
    
    def _calculate_engagement_score(self, hit: Dict[str, Any]) -> int:
        """
        Calculate engagement score based on points and comments.
        
        Args:
            hit: HackerNews hit data
            
        Returns:
            Engagement score
        """
        points = hit.get("points", 0)
        comments = hit.get("num_comments", 0)
        
        # Simple weighted formula
        return points * 2 + comments * 3