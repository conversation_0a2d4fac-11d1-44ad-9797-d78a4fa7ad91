"""
The Verge data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
import re
from typing import List, Dict, Any, Optional
import requests
from bs4 import BeautifulSoup

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedVergeSource:
    """
    Enhanced The Verge source with anti-scraping capabilities.
    """
    
    def __init__(self):
        """Initialize The Verge data source."""
        self.base_url = "https://www.theverge.com"
        self.search_url = "https://www.theverge.com/search"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search The Verge for articles related to the topic.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching The Verge for: {topic}")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        params = {
            "q": topic
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(1.0, 3.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.search_url, headers=headers, params=params)
            response.raise_for_status()
            
            # Check for CAPTCHA
            if self.anti_scraping.check_for_captcha(response.text):
                logger.warning("The Verge CAPTCHA detected. Trying with different headers/proxy.")
                
                # Try again with different headers/proxy
                headers = self.anti_scraping.get_waf_bypass_headers()
                proxy = self.anti_scraping.get_next_proxy()
                
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
                    response = self.session.get(self.search_url, headers=headers, params=params, proxies=proxies)
                    response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract articles
            articles = []
            article_elements = soup.select("div.c-compact-river__entry")[:num_results]
            
            for article in article_elements:
                try:
                    # Extract title
                    title_element = article.select_one("h2.c-entry-box--compact__title a")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    url = title_element.get("href") if title_element else None
                    if url and not url.startswith("http"):
                        url = self.base_url + url
                    
                    # Extract author
                    author_element = article.select_one("span.c-byline__author")
                    author = author_element.get_text().strip() if author_element else "The Verge"
                    
                    # Extract date
                    date_element = article.select_one("time.c-byline__item")
                    date = date_element.get_text().strip() if date_element else ""
                    
                    # Extract categories/tags
                    tags = []
                    tag_elements = article.select("li.c-entry-box--compact__label a")
                    for tag_element in tag_elements:
                        tags.append(tag_element.get_text().strip())
                    
                    # Calculate engagement score - The Verge doesn't expose engagement metrics
                    # We'll use a placeholder based on recency
                    engagement_score = self._calculate_estimated_engagement(date)
                    
                    result = {
                        "source": "theverge",
                        "title": title,
                        "url": url,
                        "author": author,
                        "created_at": date,
                        "engagement": {
                            "score": engagement_score,
                            "estimated": True
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "tags": tags,
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing The Verge article: {e}")
            
            logger.info(f"Found {len(articles)} results for '{topic}' on The Verge")
            return articles
            
        except Exception as e:
            logger.error(f"The Verge error: {str(e)}")
            return []
    
    def get_latest(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get latest articles from The Verge.
        
        Args:
            limit: Maximum number of articles to return
            
        Returns:
            List of latest articles
        """
        logger.info("Fetching latest articles from The Verge")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(1.0, 3.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.base_url, headers=headers)
            response.raise_for_status()
            
            # Check for CAPTCHA
            if self.anti_scraping.check_for_captcha(response.text):
                logger.warning("The Verge CAPTCHA detected. Trying with different headers/proxy.")
                
                # Try again with different headers/proxy
                headers = self.anti_scraping.get_waf_bypass_headers()
                proxy = self.anti_scraping.get_next_proxy()
                
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
                    response = self.session.get(self.base_url, headers=headers, proxies=proxies)
                    response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract featured and recent articles
            articles = []
            
            # First get the hero/featured article
            hero_article = soup.select_one("div.c-entry-hero")
            if hero_article:
                try:
                    # Extract title
                    title_element = hero_article.select_one("h2.c-entry-hero__title a")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    url = title_element.get("href") if title_element else None
                    if url and not url.startswith("http"):
                        url = self.base_url + url
                    
                    # Extract author
                    author_element = hero_article.select_one("span.c-byline__author")
                    author = author_element.get_text().strip() if author_element else "The Verge"
                    
                    # Extract date
                    date_element = hero_article.select_one("time.c-byline__item")
                    date = date_element.get_text().strip() if date_element else ""
                    
                    # Extract excerpt
                    excerpt_element = hero_article.select_one("p.c-entry-hero__desc")
                    excerpt = excerpt_element.get_text().strip() if excerpt_element else ""
                    
                    # Featured articles get higher engagement score
                    engagement_score = 90.0
                    
                    result = {
                        "source": "theverge",
                        "title": title,
                        "url": url,
                        "author": author,
                        "created_at": date,
                        "engagement": {
                            "score": engagement_score,
                            "estimated": True,
                            "featured": True
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": excerpt,
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                except Exception as e:
                    logger.warning(f"Error processing The Verge featured article: {e}")
            
            # Then get regular articles
            article_elements = soup.select("div.c-compact-river__entry")[:limit]
            
            for article in article_elements:
                try:
                    # Extract title
                    title_element = article.select_one("h2.c-entry-box--compact__title a")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    url = title_element.get("href") if title_element else None
                    if url and not url.startswith("http"):
                        url = self.base_url + url
                    
                    # Extract author
                    author_element = article.select_one("span.c-byline__author")
                    author = author_element.get_text().strip() if author_element else "The Verge"
                    
                    # Extract date
                    date_element = article.select_one("time.c-byline__item")
                    date = date_element.get_text().strip() if date_element else ""
                    
                    # Calculate engagement score based on position and recency
                    position_score = max(90 - (article_elements.index(article) * 5), 50)
                    date_score = self._calculate_estimated_engagement(date)
                    engagement_score = (position_score + date_score) / 2
                    
                    result = {
                        "source": "theverge",
                        "title": title,
                        "url": url,
                        "author": author,
                        "created_at": date,
                        "engagement": {
                            "score": engagement_score,
                            "estimated": True
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing The Verge article: {e}")
            
            logger.info(f"Found {len(articles)} latest articles on The Verge")
            return articles[:limit]
            
        except Exception as e:
            logger.error(f"The Verge latest error: {str(e)}")
            return []
    
    def _calculate_estimated_engagement(self, date_str: str) -> float:
        """
        Calculate an estimated engagement score based on date.
        
        Args:
            date_str: Date string from The Verge
            
        Returns:
            Estimated engagement score (0-100)
        """
        try:
            # Check for "hour" or "minute" in the date string to identify very recent articles
            if re.search(r'hour|minute|min|hr', date_str.lower()):
                # Very recent articles get a high score
                return random.uniform(85, 95)
            
            # Check for "day" in the date string
            day_match = re.search(r'(\d+)\s+day', date_str.lower())
            if day_match:
                days = int(day_match.group(1))
                if days <= 1:
                    return random.uniform(75, 85)
                elif days <= 3:
                    return random.uniform(65, 80)
                elif days <= 7:
                    return random.uniform(55, 70)
                else:
                    return random.uniform(45, 60)
            
            # Older articles or can't parse date
            return random.uniform(40, 55)
        except:
            # If we can't parse the date, return a mid-range score
            return 50.0