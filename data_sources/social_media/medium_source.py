"""
Medium data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
import re
from typing import List, Dict, Any, Optional
import requests
from bs4 import BeautifulSoup

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedMediumSource:
    """
    Enhanced Medium source with anti-scraping capabilities.
    """
    
    def __init__(self):
        """Initialize the Medium data source."""
        self.base_url = "https://medium.com"
        self.search_url = "https://medium.com/search"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search Medium for articles related to the topic.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching Medium for: {topic}")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        params = {
            "q": topic,
            "sort": "relevance"
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(1.0, 3.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.search_url, headers=headers, params=params)
            response.raise_for_status()
            
            # Check for CAPTCHA
            if self.anti_scraping.check_for_captcha(response.text):
                logger.warning("Medium CAPTCHA detected. Trying with different headers/proxy.")
                
                # Try again with different headers/proxy
                headers = self.anti_scraping.get_waf_bypass_headers()
                proxy = self.anti_scraping.get_next_proxy()
                
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
                    response = self.session.get(self.search_url, headers=headers, params=params, proxies=proxies)
                    response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract articles
            articles = []
            article_elements = soup.select("article")[:num_results]
            
            for article in article_elements:
                try:
                    # Extract title
                    title_element = article.select_one("h2, h3")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    link_element = article.select_one("a[href*='/p/']")
                    url = self.base_url + link_element.get("href") if link_element else None
                    
                    # Extract author
                    author_element = article.select_one("a[href*='/@']")
                    author = author_element.get_text().strip() if author_element else "Unknown"
                    
                    # Extract claps/likes (engagement)
                    claps_element = article.select_one("button, span[data-testid*='claps']")
                    claps_text = claps_element.get_text().strip() if claps_element else "0"
                    claps = self._extract_number(claps_text) if claps_text else 0
                    
                    # Extract snippet
                    snippet_element = article.select_one("p, div[data-testid*='preview-text']")
                    snippet = snippet_element.get_text().strip() if snippet_element else ""
                    
                    # Calculate engagement score
                    engagement_score = self._calculate_engagement_score(claps)
                    
                    result = {
                        "source": "medium",
                        "title": title,
                        "url": url,
                        "author": author,
                        "engagement": {
                            "score": engagement_score,
                            "claps": claps,
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": snippet,
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing Medium article: {e}")
            
            logger.info(f"Found {len(articles)} results for '{topic}' on Medium")
            return articles
            
        except Exception as e:
            logger.error(f"Medium error: {str(e)}")
            return []
    
    def _extract_number(self, text: str) -> int:
        """
        Extract numeric value from text.
        
        Args:
            text: Text containing a number
            
        Returns:
            The extracted number as an integer
        """
        try:
            match = re.search(r'(\d+\.?\d*)[KkMm]?', text)
            if match:
                number = match.group(1)
                if 'k' in text.lower():
                    return int(float(number) * 1000)
                elif 'm' in text.lower():
                    return int(float(number) * 1000000)
                else:
                    return int(float(number))
            return 0
        except:
            return 0
    
    def _calculate_engagement_score(self, claps: int) -> float:
        """
        Calculate engagement score based on claps.
        
        Args:
            claps: Number of claps
            
        Returns:
            Engagement score (0-100)
        """
        # Logarithmic scale to handle wide range of clap counts
        if claps <= 0:
            return 0
            
        # Log base 10 scale with multiplier
        log_value = min(2 * (1 + 10 * (claps / 1000)), 100)
        return log_value