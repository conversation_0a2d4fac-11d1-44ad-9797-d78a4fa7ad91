"""
Reddit data source for trend analysis.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
import datetime
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from anti_scraping.core import AntiScrapingMiddleware
from data_sources.social_media import BaseMediaSource

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RedditSource(BaseMediaSource):
    """
    Reddit source for trending topics.
    """
    
    def __init__(self, output_dir: str = "data"):
        """
        Initialize the Reddit source.
        
        Args:
            output_dir: Directory to save output data
        """
        super().__init__("Reddit", output_dir)
        self.anti_scraping = AntiScrapingMiddleware()
        self.base_url = "https://www.reddit.com"
        self.api_base_url = "https://www.reddit.com"
        self.subreddits = [
            "all", "popular", "technology", "programming", 
            "datascience", "news", "worldnews", "science"
        ]
        
    def _create_session(self) -> requests.Session:
        """
        Create a requests session with retry logic and anti-scraping measures.
        
        Returns:
            Configured requests.Session
        """
        session = requests.Session()
        
        # Configure retry logic
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET"],
            backoff_factor=1
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        
        # Add anti-scraping headers
        session.headers.update(self.anti_scraping.get_waf_bypass_headers(self.base_url))
        
        return session
        
    def get_trending(self, limit: int = 25) -> List[Dict[str, Any]]:
        """
        Get trending topics from Reddit.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of trending topic dictionaries
        """
        logger.info("Getting trending topics from Reddit")
        
        all_posts = []
        session = self._create_session()
        
        # Collect trending posts from selected subreddits
        for subreddit in self.subreddits[:3]:  # Limit to first 3 subreddits to avoid rate limiting
            try:
                url = f"{self.api_base_url}/r/{subreddit}/hot.json?limit={limit}"
                
                # Add random delay to avoid rate limiting
                self.anti_scraping.random_delay(1.0, 3.0)
                
                response = session.get(url)
                response.raise_for_status()
                
                data = response.json()
                
                # Extract posts
                if 'data' in data and 'children' in data['data']:
                    for post_data in data['data']['children']:
                        post = post_data['data']
                        
                        processed_post = {
                            'id': post['id'],
                            'title': post['title'],
                            'subreddit': post['subreddit'],
                            'author': post['author'],
                            'created_utc': post['created_utc'],
                            'url': self.base_url + post['permalink'],
                            'external_url': post.get('url', ''),
                            'score': post['score'],
                            'num_comments': post['num_comments'],
                            'upvote_ratio': post.get('upvote_ratio', 0),
                            'timestamp': datetime.datetime.now().isoformat()
                        }
                        
                        # Calculate engagement score
                        processed_post['engagement_score'] = self.calculate_engagement_score(processed_post)
                        
                        all_posts.append(processed_post)
            
            except Exception as e:
                logger.error(f"Error getting trending from r/{subreddit}: {e}")
        
        # Save posts to file
        self._save_posts(all_posts, "trending")
        
        # Sort by engagement score
        all_posts.sort(key=lambda x: x['engagement_score'], reverse=True)
        
        return all_posts[:limit]
    
    def search(self, query: str, limit: int = 25) -> List[Dict[str, Any]]:
        """
        Search Reddit for topics matching the query.
        
        Args:
            query: Search query
            limit: Maximum number of results to return
            
        Returns:
            List of matching topic dictionaries
        """
        logger.info(f"Searching Reddit for: {query}")
        
        all_posts = []
        session = self._create_session()
        
        try:
            # Format query for URL
            formatted_query = query.replace(' ', '+')
            url = f"{self.api_base_url}/search.json?q={formatted_query}&sort=relevance&limit={limit}"
            
            response = session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract posts
            if 'data' in data and 'children' in data['data']:
                for post_data in data['data']['children']:
                    post = post_data['data']
                    
                    processed_post = {
                        'id': post['id'],
                        'title': post['title'],
                        'subreddit': post['subreddit'],
                        'author': post['author'],
                        'created_utc': post['created_utc'],
                        'url': self.base_url + post['permalink'],
                        'external_url': post.get('url', ''),
                        'score': post['score'],
                        'num_comments': post['num_comments'],
                        'upvote_ratio': post.get('upvote_ratio', 0),
                        'timestamp': datetime.datetime.now().isoformat(),
                        'query': query
                    }
                    
                    # Calculate engagement score
                    processed_post['engagement_score'] = self.calculate_engagement_score(processed_post)
                    
                    all_posts.append(processed_post)
        
        except Exception as e:
            logger.error(f"Error searching Reddit for {query}: {e}")
        
        # Save posts to file
        self._save_posts(all_posts, f"search_{query}")
        
        # Sort by engagement score
        all_posts.sort(key=lambda x: x['engagement_score'], reverse=True)
        
        return all_posts[:limit]
        
    def calculate_engagement_score(self, item: Dict[str, Any]) -> int:
        """
        Calculate engagement score for a Reddit post.
        
        Formula: score + (num_comments * 3) + (upvote_ratio * 10)
        
        Args:
            item: Post data dictionary
            
        Returns:
            Engagement score
        """
        score = item.get('score', 0)
        comments = item.get('num_comments', 0)
        upvote_ratio = item.get('upvote_ratio', 0)
        
        # Basic score calculation
        engagement = score + (comments * 3)
        
        # Add upvote ratio component if available
        if upvote_ratio > 0:
            engagement += int(upvote_ratio * 10)
        
        return engagement
        
    def _save_posts(self, posts: List[Dict[str, Any]], prefix: str) -> None:
        """
        Save posts to a JSON file.
        
        Args:
            posts: List of post dictionaries
            prefix: Filename prefix
        """
        if not posts:
            return
            
        # Create directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Create filename with current date
        date_str = datetime.datetime.now().strftime("%Y-%m-%d")
        filename = f"reddit_{prefix}_{date_str}.json"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(posts, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(posts)} Reddit posts to {filepath}")
        except Exception as e:
            logger.error(f"Error saving posts: {e}")