"""
GitHub data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedGitHubSource:
    """
    Enhanced GitHub source with anti-scraping capabilities.
    """
    
    def __init__(self, token: Optional[str] = None):
        """
        Initialize the GitHub data source.
        
        Args:
            token: GitHub API token (optional, but recommended to avoid rate limits)
        """
        self.base_url = "https://api.github.com/search/repositories"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
        self.token = token
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search GitHub for repositories related to the topic.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching GitHub for: {topic}")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        headers.update({"Accept": "application/vnd.github.v3+json"})
        
        # Add GitHub token if available
        if self.token:
            headers["Authorization"] = f"token {self.token}"
        
        params = {
            "q": topic,
            "sort": "stars",
            "order": "desc",
            "per_page": num_results,
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(0.5, 2.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.base_url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for repo in data.get("items", [])[:num_results]:
                # Calculate engagement score
                stars = repo.get("stargazers_count", 0)
                forks = repo.get("forks_count", 0)
                watchers = repo.get("watchers_count", 0)
                
                # More sophisticated engagement scoring
                engagement_score = self._calculate_engagement_score(stars, forks, watchers)
                
                # Extract topics/tags
                topics = repo.get("topics", [])
                
                # Clean and prepare the description
                description = repo.get("description", "")
                
                result = {
                    "title": repo.get("name", ""),
                    "url": repo.get("html_url", ""),
                    "author": repo.get("owner", {}).get("login", ""),
                    "created_at": repo.get("created_at", ""),
                    "content": description,
                    "source": "github",
                    "engagement": {
                        "score": engagement_score,
                        "stars": stars,
                        "forks": forks,
                        "watchers": watchers,
                    },
                    "engagement_score": engagement_score,  # Duplicate for consistency
                    "tags": topics,
                    "language": repo.get("language", "Unknown"),
                    "timestamp": time.time()
                }
                results.append(result)
                
                # Small random delay between processing items
                time.sleep(random.uniform(0.05, 0.2))
            
            logger.info(f"Found {len(results)} results for '{topic}' on GitHub")
            return results
            
        except Exception as e:
            logger.error(f"GitHub error: {str(e)}")
            return []
    
    def _calculate_engagement_score(self, stars: int, forks: int, watchers: int) -> float:
        """
        Calculate engagement score based on stars, forks, and watchers.
        
        Args:
            stars: Number of stars
            forks: Number of forks
            watchers: Number of watchers
            
        Returns:
            Engagement score (0-100)
        """
        # Weighted formula with diminishing returns
        star_score = min(stars / 100, 50)  # Max 50 points from stars
        fork_score = min(forks / 20, 30)   # Max 30 points from forks
        watcher_score = min(watchers / 50, 20)  # Max 20 points from watchers
        
        total_score = star_score + fork_score + watcher_score
        return min(total_score, 100)  # Cap at 100