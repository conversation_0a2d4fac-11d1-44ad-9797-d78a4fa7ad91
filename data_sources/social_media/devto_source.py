"""
Dev.to data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
from typing import List, Dict, Any, Optional
import requests

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedDevToSource:
    """
    Enhanced Dev.to source with anti-scraping capabilities.
    """
    
    def __init__(self):
        """Initialize the Dev.to data source."""
        self.base_url = "https://dev.to/api"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search Dev.to for articles related to the topic.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching Dev.to for: {topic}")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        # Dev.to API parameters
        params = {
            "q": topic,
            "per_page": min(num_results, 30),  # API limit is 30
            "page": 1
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(0.5, 2.0)
            
            # Use session with anti-scraping headers
            search_url = f"{self.base_url}/articles/search"
            response = self.session.get(search_url, headers=headers, params=params)
            response.raise_for_status()
            articles = response.json()
            
            results = []
            for article in articles[:num_results]:
                try:
                    # Extract engagement metrics
                    reactions = article.get("positive_reactions_count", 0)
                    comments = article.get("comments_count", 0)
                    
                    # Calculate engagement score
                    engagement_score = self._calculate_engagement_score(reactions, comments)
                    
                    # Extract tags
                    tags = article.get("tag_list", [])
                    
                    result = {
                        "source": "devto",
                        "title": article.get("title", ""),
                        "url": article.get("url", ""),
                        "author": article.get("user", {}).get("name", ""),
                        "created_at": article.get("published_at", ""),
                        "engagement": {
                            "score": engagement_score,
                            "reactions": reactions,
                            "comments": comments
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": article.get("description", ""),
                        "tags": tags,
                        "reading_time_minutes": article.get("reading_time_minutes", 0),
                        "timestamp": time.time()
                    }
                    
                    results.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing Dev.to article: {e}")
            
            logger.info(f"Found {len(results)} results for '{topic}' on Dev.to")
            return results
            
        except Exception as e:
            logger.error(f"Dev.to error: {str(e)}")
            return []
    
    def get_trending(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get trending articles from Dev.to.
        
        Args:
            limit: Maximum number of articles to return
            
        Returns:
            List of trending articles
        """
        logger.info("Fetching trending articles from Dev.to")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(0.5, 2.0)
            
            # Use session with anti-scraping headers
            url = f"{self.base_url}/articles?top=7"  # Top articles from the last 7 days
            response = self.session.get(url, headers=headers)
            response.raise_for_status()
            articles = response.json()
            
            results = []
            for article in articles[:limit]:
                try:
                    # Extract engagement metrics
                    reactions = article.get("positive_reactions_count", 0)
                    comments = article.get("comments_count", 0)
                    
                    # Calculate engagement score
                    engagement_score = self._calculate_engagement_score(reactions, comments)
                    
                    # Extract tags
                    tags = article.get("tag_list", [])
                    
                    result = {
                        "source": "devto",
                        "title": article.get("title", ""),
                        "url": article.get("url", ""),
                        "author": article.get("user", {}).get("name", ""),
                        "created_at": article.get("published_at", ""),
                        "engagement": {
                            "score": engagement_score,
                            "reactions": reactions,
                            "comments": comments
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": article.get("description", ""),
                        "tags": tags,
                        "reading_time_minutes": article.get("reading_time_minutes", 0),
                        "timestamp": time.time()
                    }
                    
                    results.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing Dev.to trending article: {e}")
            
            logger.info(f"Found {len(results)} trending articles on Dev.to")
            return results
            
        except Exception as e:
            logger.error(f"Dev.to trending error: {str(e)}")
            return []
    
    def _calculate_engagement_score(self, reactions: int, comments: int) -> float:
        """
        Calculate engagement score based on reactions and comments.
        
        Args:
            reactions: Number of positive reactions
            comments: Number of comments
            
        Returns:
            Engagement score (0-100)
        """
        # Dev.to articles typically have fewer reactions than other platforms
        # so we need to adjust the scale accordingly
        reaction_score = min(reactions / 20, 70)  # Max 70 points from reactions
        comment_score = min(comments / 5, 30)     # Max 30 points from comments
        
        total_score = reaction_score + comment_score
        return min(total_score, 100)  # Cap at 100