"""
TechCrunch data source with anti-scraping capabilities.
Adapted from the trending-topics-scrapper repository.
"""

import logging
import time
import random
from typing import List, Dict, Any, Optional
import requests
from bs4 import BeautifulSoup
import re

from anti_scraping.core import AntiScrapingMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EnhancedTechCrunchSource:
    """
    Enhanced TechCrunch source with anti-scraping capabilities.
    """
    
    def __init__(self):
        """Initialize the TechCrunch data source."""
        self.base_url = "https://techcrunch.com"
        self.search_url = "https://search.techcrunch.com/search"
        self.anti_scraping = AntiScrapingMiddleware()
        self.session = requests.Session()
    
    def search(self, topic: str, num_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """
        Search TechCrunch for articles related to the topic.
        
        Args:
            topic: Search topic
            num_results: Maximum number of results to return
            **kwargs: Additional parameters
            
        Returns:
            List of search result dictionaries
        """
        logger.info(f"Searching TechCrunch for: {topic}")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        params = {
            "p": topic,
            "fr": "techcrunch",
            "b": 1,
            "ns": "techcrunch"
        }
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(1.0, 3.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.search_url, headers=headers, params=params)
            response.raise_for_status()
            
            # Check for CAPTCHA
            if self.anti_scraping.check_for_captcha(response.text):
                logger.warning("TechCrunch CAPTCHA detected. Trying with different headers/proxy.")
                
                # Try again with different headers/proxy
                headers = self.anti_scraping.get_waf_bypass_headers()
                proxy = self.anti_scraping.get_next_proxy()
                
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
                    response = self.session.get(self.search_url, headers=headers, params=params, proxies=proxies)
                    response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract articles
            articles = []
            article_elements = soup.select("div.SearchResults li.SearchResults-item")[:num_results]
            
            for article in article_elements:
                try:
                    # Extract title
                    title_element = article.select_one("h4.SearchResults-title a")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    url = title_element.get("href") if title_element else None
                    
                    # Extract author (if available)
                    author = "TechCrunch"  # Default
                    author_element = article.select_one("span.SearchResults-author")
                    if author_element:
                        author = author_element.get_text().strip()
                    
                    # Extract date
                    date_element = article.select_one("span.SearchResults-date")
                    date = date_element.get_text().strip() if date_element else ""
                    
                    # Extract snippet
                    snippet_element = article.select_one("p.SearchResults-snippet")
                    snippet = snippet_element.get_text().strip() if snippet_element else ""
                    
                    # Calculate engagement score - TechCrunch doesn't expose engagement metrics
                    # We'll use a placeholder based on recency
                    engagement_score = self._calculate_estimated_engagement(date)
                    
                    result = {
                        "source": "techcrunch",
                        "title": title,
                        "url": url,
                        "author": author,
                        "created_at": date,
                        "engagement": {
                            "score": engagement_score,
                            "estimated": True
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": snippet,
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing TechCrunch article: {e}")
            
            logger.info(f"Found {len(articles)} results for '{topic}' on TechCrunch")
            return articles
            
        except Exception as e:
            logger.error(f"TechCrunch error: {str(e)}")
            return []
    
    def get_latest(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get latest articles from TechCrunch.
        
        Args:
            limit: Maximum number of articles to return
            
        Returns:
            List of latest articles
        """
        logger.info("Fetching latest articles from TechCrunch")
        
        # Apply anti-scraping measures
        headers = self.anti_scraping.get_waf_bypass_headers()
        
        try:
            # Add random delay to avoid detection
            self.anti_scraping.random_delay(1.0, 3.0)
            
            # Use session with anti-scraping headers
            response = self.session.get(self.base_url, headers=headers)
            response.raise_for_status()
            
            # Check for CAPTCHA
            if self.anti_scraping.check_for_captcha(response.text):
                logger.warning("TechCrunch CAPTCHA detected. Trying with different headers/proxy.")
                
                # Try again with different headers/proxy
                headers = self.anti_scraping.get_waf_bypass_headers()
                proxy = self.anti_scraping.get_next_proxy()
                
                if proxy:
                    proxies = {"http": proxy, "https": proxy}
                    response = self.session.get(self.base_url, headers=headers, proxies=proxies)
                    response.raise_for_status()
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Extract articles
            articles = []
            article_elements = soup.select("div.content article.post")[:limit]
            
            for article in article_elements:
                try:
                    # Extract title
                    title_element = article.select_one("h2.post-block__title a")
                    title = title_element.get_text().strip() if title_element else "No title"
                    
                    # Extract link
                    url = title_element.get("href") if title_element else None
                    
                    # Extract author
                    author_element = article.select_one("span.river-byline__authors a")
                    author = author_element.get_text().strip() if author_element else "TechCrunch"
                    
                    # Extract date
                    date_element = article.select_one("time.river-byline__time")
                    date = date_element.get_text().strip() if date_element else ""
                    
                    # Extract excerpt
                    excerpt_element = article.select_one("div.post-block__content")
                    excerpt = excerpt_element.get_text().strip() if excerpt_element else ""
                    
                    # Calculate engagement score - TechCrunch doesn't expose engagement metrics
                    # We'll use a placeholder based on recency and position
                    engagement_score = max(100 - (article_elements.index(article) * 5), 50)
                    
                    result = {
                        "source": "techcrunch",
                        "title": title,
                        "url": url,
                        "author": author,
                        "created_at": date,
                        "engagement": {
                            "score": engagement_score,
                            "estimated": True
                        },
                        "engagement_score": engagement_score,  # Duplicate for consistency
                        "content": excerpt,
                        "timestamp": time.time()
                    }
                    
                    articles.append(result)
                    
                    # Small random delay between processing items
                    time.sleep(random.uniform(0.05, 0.2))
                    
                except Exception as e:
                    logger.warning(f"Error processing TechCrunch article: {e}")
            
            logger.info(f"Found {len(articles)} latest articles on TechCrunch")
            return articles
            
        except Exception as e:
            logger.error(f"TechCrunch latest error: {str(e)}")
            return []
    
    def _calculate_estimated_engagement(self, date_str: str) -> float:
        """
        Calculate an estimated engagement score based on date.
        
        Args:
            date_str: Date string from TechCrunch
            
        Returns:
            Estimated engagement score (0-100)
        """
        try:
            # Check for "hour" or "minute" in the date string to identify very recent articles
            if re.search(r'hour|minute|min|hr', date_str.lower()):
                # Very recent articles get a high score
                return random.uniform(85, 95)
            
            # Check for "day" in the date string
            day_match = re.search(r'(\d+)\s+day', date_str.lower())
            if day_match:
                days = int(day_match.group(1))
                if days <= 1:
                    return random.uniform(80, 90)
                elif days <= 3:
                    return random.uniform(70, 85)
                elif days <= 7:
                    return random.uniform(60, 75)
                else:
                    return random.uniform(50, 65)
            
            # Older articles or can't parse date
            return random.uniform(40, 60)
        except:
            # If we can't parse the date, return a mid-range score
            return 50.0