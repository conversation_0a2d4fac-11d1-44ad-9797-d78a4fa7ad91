"""
Social media data sources package.
This package provides adapters for various social media platforms.
"""

from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BaseMediaSource:
    """
    Base class for social media data sources.
    """
    
    def __init__(self, name: str, output_dir: str = "data"):
        """
        Initialize the social media source.
        
        Args:
            name: Name of the social media source
            output_dir: Directory to save output data
        """
        self.name = name
        self.output_dir = output_dir
        
    def get_trending(self) -> List[Dict[str, Any]]:
        """
        Get trending topics from the social media source.
        
        Returns:
            List of trending topic dictionaries
        """
        raise NotImplementedError("This method must be implemented by subclasses")
        
    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for topics matching the query.
        
        Args:
            query: Search query
            
        Returns:
            List of matching topic dictionaries
        """
        raise NotImplementedError("This method must be implemented by subclasses")
        
    def calculate_engagement_score(self, item: Dict[str, Any]) -> int:
        """
        Calculate engagement score for an item.
        
        Args:
            item: Item data dictionary
            
        Returns:
            Engagement score
        """
        raise NotImplementedError("This method must be implemented by subclasses")


# Import specific source implementations
try:
    from .reddit_source import RedditSource
except ImportError:
    logger.warning("Reddit source not available")

try:
    from .hackernews_source import HackerNewsSource
except ImportError:
    logger.warning("HackerNews source not available")

# Create a unified social media scraper
class SocialMediaScraper:
    """
    Unified social media scraper that aggregates data from multiple sources.
    """
    
    def __init__(self, output_dir: str = "data"):
        """
        Initialize the unified social media scraper.
        
        Args:
            output_dir: Directory to save output data
        """
        self.output_dir = output_dir
        self.sources = []
        
        try:
            self.sources.append(RedditSource(output_dir=output_dir))
            logger.info("Added Reddit source")
        except (ImportError, NameError):
            logger.warning("RedditSource not available")
            
        try:
            self.sources.append(HackerNewsSource(output_dir=output_dir))
            logger.info("Added HackerNews source")
        except (ImportError, NameError):
            logger.warning("HackerNewsSource not available")
        
    def get_all_trending(self) -> List[Dict[str, Any]]:
        """
        Get trending topics from all sources.
        
        Returns:
            List of trending topic dictionaries with source info
        """
        all_trending = []
        
        for source in self.sources:
            try:
                trending_items = source.get_trending()
                for item in trending_items:
                    item['source_name'] = source.name
                all_trending.extend(trending_items)
            except Exception as e:
                logger.error(f"Error getting trending from {source.name}: {e}")
                
        return all_trending
        
    def search_all(self, query: str) -> List[Dict[str, Any]]:
        """
        Search all sources for a query.
        
        Args:
            query: Search query
            
        Returns:
            List of matching topic dictionaries with source info
        """
        all_results = []
        
        for source in self.sources:
            try:
                search_results = source.search(query)
                for item in search_results:
                    item['source_name'] = source.name
                all_results.extend(search_results)
            except Exception as e:
                logger.error(f"Error searching {source.name}: {e}")
                
        return all_results