"""
Data sources package for the trend-crawler project.
Provides access to various data sources for trend analysis.
"""

from typing import List, Dict, Any, Optional, Union, Callable
import logging
import importlib
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Data source registry
_data_sources = {}

def import_data_source(module_path: str, class_name: str) -> Optional[type]:
    """
    Dynamically import a data source class.
    
    Args:
        module_path: The module path to import
        class_name: The class name to import from the module
        
    Returns:
        The imported class or None if import fails
    """
    try:
        module = importlib.import_module(module_path)
        return getattr(module, class_name)
    except (ImportError, AttributeError) as e:
        logger.warning(f"Could not import {class_name} from {module_path}: {e}")
        return None

# Define data sources to be imported
DATA_SOURCES = {
    "google_trends": {
        "module": "data_sources.google_trends.trends_scraper",
        "class": "GoogleTrendsScraper",
    },
    "macrotrends": {
        "module": "data_sources.macrotrends.stock_scraper",
        "class": "MacroTrendsStockScraper",
    },
    "social_media": {
        "reddit": {
            "module": "data_sources.social_media.reddit_source",
            "class": "RedditSource",
        },
        "hackernews": {
            "module": "data_sources.social_media.hackernews_source",
            "class": "HackerNewsSource",
        },
        "devto": {
            "module": "data_sources.social_media.devto_source",
            "class": "DevToSource",
        },
        "github": {
            "module": "data_sources.social_media.github_source",
            "class": "GithubSource",
        },
        "medium": {
            "module": "data_sources.social_media.medium_source",
            "class": "MediumSource",
        },
        "techcrunch": {
            "module": "data_sources.social_media.techcrunch_source",
            "class": "TechCrunchSource",
        },
        "verge": {
            "module": "data_sources.social_media.verge_source",
            "class": "VergeSource",
        },
    },
    "tradingview": {
        "module": "data_sources.tradingview",
        "classes": ["ChartScraper", "IdeasScraper", "StreamHandler"],
    },
    # Add new cryptocurrency data sources
    "crypto": {
        "coinmarketcap": {
            "module": "data_sources.crypto",
            "function": "get_crypto_trends",
            "source": "coinmarketcap"
        },
        "yahoo_finance": {
            "module": "data_sources.crypto",
            "function": "get_crypto_trends",
            "source": "yahoo_finance"
        },
        "combined": {
            "module": "data_sources.crypto",
            "function": "get_crypto_trends"
        }
    }
}

def get_data_source(source_name: str) -> Optional[type]:
    """
    Get a data source by name.
    
    Args:
        source_name: Name of the data source to retrieve
        
    Returns:
        The data source class or None if not available
    """
    if source_name in _data_sources:
        return _data_sources[source_name]
    
    # Handle nested social media sources
    if "." in source_name:
        category, name = source_name.split(".", 1)
        if category in DATA_SOURCES and name in DATA_SOURCES[category]:
            source_info = DATA_SOURCES[category][name]
            
            # Handle function-based sources (like crypto)
            if "function" in source_info:
                try:
                    module = importlib.import_module(source_info["module"])
                    function = getattr(module, source_info["function"])
                    # Create a wrapper function that calls the function with the specified source
                    if "source" in source_info:
                        def wrapper():
                            return lambda topic=None: {
                                source_info["source"]: function(topic).get(source_info["source"], {})
                            }
                        _data_sources[source_name] = wrapper()
                    else:
                        _data_sources[source_name] = lambda topic=None: function(topic)
                    return _data_sources[source_name]
                except (ImportError, AttributeError) as e:
                    logger.warning(f"Could not import {source_info['function']} from {source_info['module']}: {e}")
                    return None
            elif "class" in source_info:
                source_class = import_data_source(source_info["module"], source_info["class"])
                if source_class:
                    _data_sources[source_name] = source_class
                    return source_class
    elif source_name in DATA_SOURCES:
        source_info = DATA_SOURCES[source_name]
        if "class" in source_info:
            source_class = import_data_source(source_info["module"], source_info["class"])
            if source_class:
                _data_sources[source_name] = source_class
                return source_class
    
    return None

def get_all_available_sources() -> List[str]:
    """
    Get a list of all available data sources.
    
    Returns:
        List of available data source names
    """
    available_sources = []
    
    # Check top-level sources
    for source_name, source_info in DATA_SOURCES.items():
        if isinstance(source_info, dict) and "class" in source_info:
            try:
                source_class = import_data_source(source_info["module"], source_info["class"])
                if source_class:
                    available_sources.append(source_name)
            except Exception:
                pass
        # Check nested sources (like social_media and crypto)
        elif isinstance(source_info, dict):
            for nested_name, nested_info in source_info.items():
                if isinstance(nested_info, dict):
                    if "class" in nested_info:
                        try:
                            source_class = import_data_source(nested_info["module"], nested_info["class"])
                            if source_class:
                                available_sources.append(f"{source_name}.{nested_name}")
                        except Exception:
                            pass
                    elif "function" in nested_info:
                        try:
                            module = importlib.import_module(nested_info["module"])
                            function = getattr(module, nested_info["function"])
                            if function:
                                available_sources.append(f"{source_name}.{nested_name}")
                        except Exception:
                            pass
    
    return available_sources

class DataSourceManager:
    """
    Manager for accessing and using data sources.
    """
    def __init__(self):
        self.available_sources = get_all_available_sources()
        
    def get_source(self, source_name: str) -> Any:
        """
        Get an instance of a data source.
        
        Args:
            source_name: Name of the source to get
            
        Returns:
            Instance of the requested data source or None if not available
        """
        source_class_or_func = get_data_source(source_name)
        if source_class_or_func:
            try:
                # Check if it's a class or a function
                if callable(source_class_or_func) and not isinstance(source_class_or_func, type):
                    return source_class_or_func  # Return function as is
                else:
                    return source_class_or_func()  # Instantiate class
            except Exception as e:
                logger.error(f"Error instantiating {source_name}: {e}")
                return None
        return None
    
    def collect_trend_data(self, topic: str = None) -> Dict[str, Any]:
        """
        Collect trend data from all available sources.
        
        Args:
            topic: Optional topic to search for
            
        Returns:
            Dictionary with results from all available sources
        """
        results = {}
        
        for source_name in self.available_sources:
            source = self.get_source(source_name)
            if source:
                try:
                    # Different sources might have different methods for collecting data
                    # Try common method names based on the category
                    if callable(source) and not isinstance(source, type):
                        # Handle function sources (like those from crypto)
                        results[source_name] = source(topic)
                    elif hasattr(source, "scrape_trends"):
                        results[source_name] = source.scrape_trends(topic) if topic else source.scrape_trends()
                    elif hasattr(source, "scrape_stocks"):
                        results[source_name] = source.scrape_stocks(topic) if topic else source.scrape_stocks()
                    elif hasattr(source, "get_trends"):
                        results[source_name] = source.get_trends(topic) if topic else source.get_trends()
                    elif hasattr(source, "search"):
                        results[source_name] = source.search(topic) if topic else source.search()
                except Exception as e:
                    logger.error(f"Error collecting data from {source_name}: {e}")
                    results[source_name] = {"error": str(e)}
        
        return results

# Create a singleton instance of the manager
data_source_manager = DataSourceManager()