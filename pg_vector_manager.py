#!/usr/bin/env python3
"""
PgVector Manager for efficient vector operations using PostgreSQL

This module implements high-performance vector operations using PostgreSQL's
pgvector extension, optimized for real-time trend analysis.
"""

import os
import logging
import json
import time
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Set, Generator
import psycopg2
from psycopg2 import pool
from psycopg2.extras import execute_values, execute_batch, DictCursor, Json
from psycopg2.errors import DuplicateTable, UndefinedTable
from datetime import datetime, timedelta
import threading
import queue
import functools
import uuid
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


def with_connection(method):
    """Decorator to handle database connections from pool."""
    @functools.wraps(method)
    def wrapper(self, *args, **kwargs):
        conn = None
        try:
            conn = self.get_connection()
            return method(self, conn, *args, **kwargs)
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error in {method.__name__}: {e}")
            raise
        finally:
            if conn:
                self.return_connection(conn)
    return wrapper


class PgVectorManager:
    """
    High-performance vector operations manager for PostgreSQL with pgvector.
    
    Features:
    - Connection pooling with intelligent recycling
    - Batched vector operations for efficiency
    - Async write operations via queue
    - Advanced vector queries with hybrid retrieval
    """
    
    def __init__(self, 
                 db_config: Dict[str, str] = None,
                 min_pool_size: int = 2,
                 max_pool_size: int = 16,
                 pool_timeout: int = 30,
                 async_workers: int = 2):
        """
        Initialize the PgVector Manager.
        
        Args:
            db_config: Database configuration parameters
            min_pool_size: Minimum connection pool size
            max_pool_size: Maximum connection pool size
            pool_timeout: Connection timeout in seconds
            async_workers: Number of async worker threads
        """
        # Parse config
        self.db_config = db_config or {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', '5432'),
            'dbname': os.getenv('DB_NAME', 'trend_crawler'),
            'user': os.getenv('DB_USER', 'postgres'),
            'password': os.getenv('DB_PASSWORD', 'postgres')
        }
        
        # Connection management
        self.min_pool_size = min_pool_size
        self.max_pool_size = max_pool_size
        self.pool_timeout = pool_timeout
        
        # Initialize connection pool
        self._initialize_pool()
        
        # Async processing
        self.async_queue = queue.Queue()
        self.async_workers = []
        self.running = True
        
        # Start async workers
        for i in range(async_workers):
            worker = threading.Thread(target=self._async_worker, daemon=True)
            worker.start()
            self.async_workers.append(worker)
            
        # Check if pgvector extension is installed
        self._check_pgvector()
        
        logger.info(f"PgVectorManager initialized with {max_pool_size} connections")
        
    def _initialize_pool(self):
        """Initialize the connection pool."""
        try:
            self.conn_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=self.min_pool_size,
                maxconn=self.max_pool_size,
                **self.db_config,
                keepalives=1,
                keepalives_idle=30,
                keepalives_interval=10,
                keepalives_count=5
            )
            logger.info(f"Connection pool initialized with {self.min_pool_size}-{self.max_pool_size} connections")
        except Exception as e:
            logger.error(f"Failed to initialize connection pool: {e}")
            raise
    
    def _check_pgvector(self):
        """Check if pgvector extension is installed and create if needed."""
        with self.get_connection() as conn:
            with conn.cursor() as cur:
                # Check if extension exists
                cur.execute("SELECT extname FROM pg_extension WHERE extname = 'vector';")
                if cur.fetchone() is None:
                    logger.info("pgvector extension not found, creating...")
                    cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
                    conn.commit()
                    logger.info("pgvector extension created")
                else:
                    logger.info("pgvector extension found")
    
    def get_connection(self):
        """Get a connection from the pool."""
        try:
            conn = self.conn_pool.getconn()
            # Reset connection state in case it was left in a bad state
            conn.rollback()
            return conn
        except Exception as e:
            logger.error(f"Failed to get connection from pool: {e}")
            raise
    
    def return_connection(self, conn):
        """Return a connection to the pool."""
        try:
            self.conn_pool.putconn(conn)
        except Exception as e:
            logger.error(f"Failed to return connection to pool: {e}")
            # If returning fails, close the connection
            conn.close()
    
    def close(self):
        """Close all connections and stop async workers."""
        self.running = False
        
        # Wait for workers to finish
        for worker in self.async_workers:
            if worker.is_alive():
                worker.join(timeout=5.0)
        
        # Close connection pool
        if hasattr(self, 'conn_pool'):
            self.conn_pool.closeall()
            logger.info("Connection pool closed")
    
    def _async_worker(self):
        """Worker thread for async operations."""
        while self.running:
            try:
                # Get next task with timeout
                task = self.async_queue.get(timeout=1.0)
                if task is None:
                    break
                    
                # Extract task info
                func, args, kwargs = task
                
                # Execute task
                try:
                    func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Error in async worker: {e}")
                
                # Mark task as done
                self.async_queue.task_done()
                
            except queue.Empty:
                # Timeout waiting for task, just continue
                continue
    
    @with_connection
    def create_vector_table(self, conn, 
                           table_name: str, 
                           vector_dim: int,
                           with_metadata: bool = True,
                           if_not_exists: bool = True,
                           primary_key: str = None):
        """
        Create a table with vector column.
        
        Args:
            conn: Database connection
            table_name: Name of table to create
            vector_dim: Vector dimension
            with_metadata: Whether to include JSON metadata column
            if_not_exists: Add IF NOT EXISTS clause
            primary_key: Name of primary key column
        """
        exists_clause = "IF NOT EXISTS" if if_not_exists else ""
        pk_col = primary_key or "id"
        
        # Build SQL based on options
        sql = f"""
        CREATE TABLE {exists_clause} {table_name} (
            {pk_col} {'SERIAL PRIMARY KEY' if pk_col == 'id' else 'VARCHAR(64) PRIMARY KEY'},
            vector vector({vector_dim}) NOT NULL,
            created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        """
        
        if with_metadata:
            sql += ", metadata JSONB"
            
        sql += ");"
        
        # Create table
        with conn.cursor() as cur:
            try:
                cur.execute(sql)
                
                # Create vector index
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS {table_name}_vector_idx 
                ON {table_name} USING ivfflat (vector vector_l2_ops)
                WITH (lists = 100);
                """)
                
                conn.commit()
                logger.info(f"Created vector table {table_name} with dimension {vector_dim}")
                
                # Return table info
                return {
                    "table": table_name,
                    "dimension": vector_dim,
                    "created": True
                }
                
            except psycopg2.errors.DuplicateTable:
                conn.rollback()
                logger.info(f"Table {table_name} already exists")
                return {
                    "table": table_name,
                    "created": False,
                    "reason": "already_exists"
                }
    
    @with_connection
    def drop_vector_table(self, conn, table_name: str, if_exists: bool = True):
        """
        Drop a vector table.
        
        Args:
            conn: Database connection
            table_name: Name of table to drop
            if_exists: Add IF EXISTS clause
        """
        exists_clause = "IF EXISTS" if if_exists else ""
        
        with conn.cursor() as cur:
            try:
                cur.execute(f"DROP TABLE {exists_clause} {table_name};")
                conn.commit()
                logger.info(f"Dropped table {table_name}")
                return {"dropped": True, "table": table_name}
            except psycopg2.errors.UndefinedTable:
                conn.rollback()
                logger.warning(f"Table {table_name} does not exist")
                return {"dropped": False, "table": table_name, "reason": "not_exists"}
    
    @with_connection
    def upsert_vectors(self, conn, 
                      table_name: str,
                      vectors: List[np.ndarray],
                      ids: List[str] = None,
                      metadata_list: List[Dict[str, Any]] = None,
                      batch_size: int = 1000) -> Dict[str, Any]:
        """
        Insert or update vectors in bulk.
        
        Args:
            conn: Database connection
            table_name: Target table name
            vectors: List of vector arrays
            ids: List of IDs (generated if None)
            metadata_list: List of metadata dictionaries
            batch_size: Number of vectors per batch
            
        Returns:
            Dictionary with operation results
        """
        if not vectors:
            return {"inserted": 0, "updated": 0}
        
        # Generate IDs if not provided
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(vectors))]
        
        # Ensure metadata list exists
        if metadata_list is None:
            metadata_list = [{} for _ in range(len(vectors))]
            
        # Ensure same length
        if len(ids) != len(vectors) or len(vectors) != len(metadata_list):
            raise ValueError(f"Length mismatch: ids={len(ids)}, vectors={len(vectors)}, metadata={len(metadata_list)}")
        
        # Format data for insertion
        data = []
        for i, (id_val, vector, metadata) in enumerate(zip(ids, vectors, metadata_list)):
            data.append((
                id_val,
                vector.tolist(),  # Convert numpy array to list
                Json(metadata) if metadata else None
            ))
        
        # Use batch operation for efficiency
        inserted = 0
        updated = 0
        
        with conn.cursor() as cur:
            try:
                # Detect if table has metadata column
                cur.execute(f"""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = %s AND column_name = 'metadata';
                """, (table_name,))
                
                has_metadata = cur.fetchone() is not None
                
                # Build SQL based on table structure
                if has_metadata:
                    sql = f"""
                    INSERT INTO {table_name} (id, vector, metadata)
                    VALUES (%s, %s::vector, %s)
                    ON CONFLICT (id) DO UPDATE SET
                        vector = EXCLUDED.vector,
                        metadata = COALESCE({table_name}.metadata, '{{}}'::jsonb) || EXCLUDED.metadata
                    RETURNING (xmax = 0) AS inserted;
                    """
                else:
                    sql = f"""
                    INSERT INTO {table_name} (id, vector)
                    VALUES (%s, %s::vector)
                    ON CONFLICT (id) DO UPDATE SET
                        vector = EXCLUDED.vector
                    RETURNING (xmax = 0) AS inserted;
                    """
                
                # Process in batches
                for i in range(0, len(data), batch_size):
                    batch = data[i:i + batch_size]
                    
                    # Execute batch
                    if has_metadata:
                        for row in batch:
                            cur.execute(sql, row)
                            if cur.fetchone()[0]:
                                inserted += 1
                            else:
                                updated += 1
                    else:
                        for id_val, vector, _ in batch:
                            cur.execute(sql, (id_val, vector))
                            if cur.fetchone()[0]:
                                inserted += 1
                            else:
                                updated += 1
                
                conn.commit()
                
                result = {
                    "inserted": inserted,
                    "updated": updated,
                    "total": inserted + updated,
                    "table": table_name
                }
                
                logger.info(f"Upserted {inserted + updated} vectors to {table_name} ({inserted} new, {updated} updates)")
                return result
                
            except Exception as e:
                conn.rollback()
                logger.error(f"Error upserting vectors: {e}")
                raise
    
    def upsert_vectors_async(self, 
                           table_name: str,
                           vectors: List[np.ndarray],
                           ids: List[str] = None,
                           metadata_list: List[Dict[str, Any]] = None,
                           batch_size: int = 1000):
        """
        Asynchronously insert or update vectors.
        
        Args:
            table_name: Target table name
            vectors: List of vector arrays
            ids: List of IDs (generated if None) 
            metadata_list: List of metadata dictionaries
            batch_size: Number of vectors per batch
        """
        # Queue task for async worker
        self.async_queue.put((
            self.upsert_vectors,
            (table_name, vectors, ids, metadata_list, batch_size),
            {}
        ))
    
    @with_connection
    def search_vectors(self, conn,
                      table_name: str,
                      query_vector: np.ndarray,
                      k: int = 10,
                      metadata_filter: Dict[str, Any] = None,
                      include_similarity: bool = True,
                      min_similarity: float = None) -> List[Dict[str, Any]]:
        """
        Search for similar vectors.
        
        Args:
            conn: Database connection
            table_name: Table to search in
            query_vector: Vector to search for
            k: Number of results to return
            metadata_filter: JSONB filter conditions
            include_similarity: Whether to include similarity scores
            min_similarity: Minimum similarity threshold
            
        Returns:
            List of matching records
        """
        # Build SQL query
        sql_parts = [f"SELECT id"]
        
        # Add metadata selection if available
        sql_parts.append(", metadata" if self._has_metadata_column(conn, table_name) else "")
        
        # Add similarity calculation
        if include_similarity:
            sql_parts.append(", 1 - (vector <=> %s::vector) AS similarity")
        
        # Add from clause
        sql_parts.append(f"FROM {table_name}")
        
        # Add where conditions
        where_parts = []
        
        # Add metadata filter if provided
        if metadata_filter:
            for key, value in metadata_filter.items():
                if isinstance(value, (list, tuple)):
                    # Array containment
                    placeholders = ', '.join(['%s' for _ in range(len(value))])
                    where_parts.append(f"metadata->>{key} IN ({placeholders})")
                elif isinstance(value, dict):
                    # Handle operators
                    for op, val in value.items():
                        if op == 'eq':
                            where_parts.append(f"metadata->>{key} = %s")
                        elif op == 'gt':
                            where_parts.append(f"(metadata->>%s)::float > %s")
                        elif op == 'lt':
                            where_parts.append(f"(metadata->>%s)::float < %s")
                        elif op == 'contains':
                            where_parts.append(f"metadata->>%s ILIKE %s")
                else:
                    # Simple equality
                    where_parts.append(f"metadata->>{key} = %s")
        
        # Add similarity threshold if provided
        if min_similarity is not None:
            where_parts.append(f"1 - (vector <=> %s::vector) >= %s")
        
        # Combine where clauses
        if where_parts:
            sql_parts.append("WHERE " + " AND ".join(where_parts))
        
        # Add ordering by similarity
        sql_parts.append("ORDER BY vector <=> %s::vector")
        
        # Add limit
        sql_parts.append("LIMIT %s")
        
        # Combine SQL
        sql = " ".join(sql_parts)
        
        # Prepare parameters
        params = []
        
        # Add similarity calculation parameter if needed
        if include_similarity:
            params.append(query_vector.tolist())
        
        # Add metadata filter parameters
        if metadata_filter:
            for key, value in metadata_filter.items():
                if isinstance(value, (list, tuple)):
                    params.extend(value)
                elif isinstance(value, dict):
                    for op, val in value.items():
                        if op in ('eq', 'contains'):
                            params.append(val)
                        elif op in ('gt', 'lt'):
                            params.append(key)
                            params.append(val)
                else:
                    params.append(key)
                    params.append(value)
        
        # Add similarity threshold parameter if provided
        if min_similarity is not None:
            params.append(query_vector.tolist())
            params.append(min_similarity)
        
        # Add ordering parameter
        params.append(query_vector.tolist())
        
        # Add limit parameter
        params.append(k)
        
        # Execute query
        with conn.cursor(cursor_factory=DictCursor) as cur:
            cur.execute(sql, params)
            results = [dict(row) for row in cur.fetchall()]
            
            logger.debug(f"Found {len(results)} similar vectors in {table_name}")
            return results
    
    @with_connection
    def _has_metadata_column(self, conn, table_name: str) -> bool:
        """Check if table has metadata column."""
        with conn.cursor() as cur:
            cur.execute(f"""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = %s AND column_name = 'metadata';
            """, (table_name,))
            return cur.fetchone() is not None
    
    @with_connection
    def batch_search(self, conn,
                    table_name: str,
                    query_vectors: List[np.ndarray],
                    k: int = 10) -> List[List[Dict[str, Any]]]:
        """
        Perform multiple vector searches in batch.
        
        Args:
            conn: Database connection
            table_name: Table to search in
            query_vectors: List of query vectors
            k: Number of results per query
            
        Returns:
            List of result lists
        """
        results = []
        
        for qv in query_vectors:
            neighbors = self.search_vectors(table_name, qv, k)
            results.append(neighbors)
            
        return results
    
    @with_connection
    def delete_vectors(self, conn,
                      table_name: str,
                      ids: List[str] = None,
                      metadata_filter: Dict[str, Any] = None,
                      older_than: datetime = None) -> int:
        """
        Delete vectors from table.
        
        Args:
            conn: Database connection
            table_name: Table to delete from
            ids: List of IDs to delete
            metadata_filter: Filter by metadata
            older_than: Delete vectors older than this datetime
            
        Returns:
            Number of deleted vectors
        """
        if not (ids or metadata_filter or older_than):
            raise ValueError("At least one filter condition must be provided")
        
        # Build SQL
        sql = f"DELETE FROM {table_name}"
        
        # Build where conditions
        where_parts = []
        params = []
        
        if ids:
            placeholders = ','.join(['%s' for _ in ids])
            where_parts.append(f"id IN ({placeholders})")
            params.extend(ids)
            
        if metadata_filter:
            for key, value in metadata_filter.items():
                where_parts.append(f"metadata->>{key} = %s")
                params.append(value)
                
        if older_than:
            where_parts.append(f"created_at < %s")
            params.append(older_than)
            
        # Combine where clauses
        if where_parts:
            sql += " WHERE " + " AND ".join(where_parts)
            
        # Add returning count
        sql += " RETURNING id"
        
        # Execute
        with conn.cursor() as cur:
            cur.execute(sql, params)
            deleted_rows = cur.fetchall()
            conn.commit()
            
            deleted_count = len(deleted_rows)
            logger.info(f"Deleted {deleted_count} vectors from {table_name}")
            return deleted_count
    
    @with_connection
    def create_vector_index(self, conn,
                           table_name: str,
                           index_type: str = "ivfflat",
                           lists: int = 100,
                           replace: bool = False) -> Dict[str, Any]:
        """
        Create a vector index.
        
        Args:
            conn: Database connection
            table_name: Table name
            index_type: Type of index (ivfflat, hnsw)
            lists: Number of lists for ivfflat
            replace: Whether to replace existing index
            
        Returns:
            Dictionary with operation results
        """
        index_name = f"{table_name}_vector_idx"
        
        # Drop existing index if replace is True
        if replace:
            with conn.cursor() as cur:
                cur.execute(f"DROP INDEX IF EXISTS {index_name};")
        
        # Create index based on type
        with conn.cursor() as cur:
            if index_type == "ivfflat":
                cur.execute(f"""
                CREATE INDEX IF NOT EXISTS {index_name}
                ON {table_name} USING ivfflat (vector vector_l2_ops)
                WITH (lists = {lists});
                """)
            elif index_type == "hnsw":
                # Check if PostgreSQL version supports hnsw
                cur.execute("SHOW server_version_num;")
                version = int(cur.fetchone()[0])
                
                if version < 130000:  # PG 13 or higher
                    logger.warning("HNSW index requires PostgreSQL 13 or higher, falling back to ivfflat")
                    cur.execute(f"""
                    CREATE INDEX IF NOT EXISTS {index_name}
                    ON {table_name} USING ivfflat (vector vector_l2_ops)
                    WITH (lists = {lists});
                    """)
                else:
                    # For hnsw (available in newer pgvector versions)
                    try:
                        cur.execute(f"""
                        CREATE INDEX IF NOT EXISTS {index_name}
                        ON {table_name} USING hnsw (vector vector_l2_ops)
                        WITH (m = 16, ef_construction = 64);
                        """)
                    except Exception:
                        logger.warning("HNSW index creation failed, falling back to ivfflat")
                        cur.execute(f"""
                        CREATE INDEX IF NOT EXISTS {index_name}
                        ON {table_name} USING ivfflat (vector vector_l2_ops)
                        WITH (lists = {lists});
                        """)
            else:
                raise ValueError(f"Unknown index type: {index_type}")
            
            conn.commit()
            
            # Get index information
            cur.execute(f"""
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE tablename = %s AND indexname = %s;
            """, (table_name, index_name))
            
            index_info = cur.fetchone()
            
            return {
                "table": table_name,
                "index": index_name,
                "type": index_type,
                "created": index_info is not None,
                "definition": index_info[1] if index_info else None
            }
    
    @with_connection
    def stream_vectors(self, conn,
                      table_name: str,
                      batch_size: int = 1000,
                      include_metadata: bool = True) -> Generator[Dict[str, Any], None, None]:
        """
        Stream vectors from table in batches.
        
        Args:
            conn: Database connection
            table_name: Table to stream from
            batch_size: Batch size
            include_metadata: Whether to include metadata
            
        Yields:
            Batches of vectors
        """
        # Check if table exists
        with conn.cursor() as cur:
            cur.execute(f"SELECT to_regclass('{table_name}')")
            if cur.fetchone()[0] is None:
                logger.error(f"Table {table_name} does not exist")
                return
                
        # Build SQL
        if include_metadata and self._has_metadata_column(conn, table_name):
            sql = f"SELECT id, vector, metadata FROM {table_name}"
        else:
            sql = f"SELECT id, vector FROM {table_name}"
            
        # Stream in batches
        offset = 0
        while True:
            with conn.cursor(name=f"vector_stream_{table_name}", cursor_factory=DictCursor) as cur:
                cur.arraysize = batch_size
                cur.execute(f"{sql} LIMIT {batch_size} OFFSET {offset}")
                
                batch = cur.fetchall()
                if not batch:
                    break
                    
                # Convert to dictionary format
                yield [dict(row) for row in batch]
                
                # Update offset
                offset += batch_size
    
    @with_connection
    def table_stats(self, conn, table_name: str) -> Dict[str, Any]:
        """
        Get statistics about a vector table.
        
        Args:
            conn: Database connection
            table_name: Table name
            
        Returns:
            Dictionary with table statistics
        """
        stats = {"table": table_name}
        
        with conn.cursor() as cur:
            # Check if table exists
            cur.execute(f"SELECT to_regclass('{table_name}')")
            if cur.fetchone()[0] is None:
                stats["exists"] = False
                return stats
                
            stats["exists"] = True
            
            # Get row count
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            stats["row_count"] = cur.fetchone()[0]
            
            # Get table size
            cur.execute(f"SELECT pg_size_pretty(pg_total_relation_size('{table_name}'))")
            stats["table_size"] = cur.fetchone()[0]
            
            # Get index information
            cur.execute(f"""
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE tablename = %s;
            """, (table_name,))
            
            indexes = []
            for row in cur.fetchall():
                indexes.append({
                    "name": row[0],
                    "definition": row[1]
                })
            
            stats["indexes"] = indexes
            
            # Get column information
            cur.execute(f"""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = %s;
            """, (table_name,))
            
            stats["columns"] = {row[0]: row[1] for row in cur.fetchall()}
            
            return stats
    
    @with_connection
    def vector_cluster(self, conn,
                      table_name: str,
                      n_clusters: int = 10,
                      algorithm: str = "kmeans",
                      batch_size: int = 1000) -> Dict[str, Any]:
        """
        Perform clustering on vectors in a table.
        
        Args:
            conn: Database connection
            table_name: Table name
            n_clusters: Number of clusters
            algorithm: Clustering algorithm
            batch_size: Batch size for processing
            
        Returns:
            Dictionary with clustering results
        """
        from sklearn.cluster import KMeans, DBSCAN
        
        # Collect vectors
        vectors = []
        ids = []
        
        for batch in self.stream_vectors(table_name, batch_size, include_metadata=False):
            for item in batch:
                vectors.append(np.array(item["vector"]))
                ids.append(item["id"])
                
        if not vectors:
            return {"error": "No vectors found in table"}
            
        # Convert to numpy array
        vectors_array = np.vstack(vectors)
        
        # Perform clustering
        cluster_labels = None
        
        if algorithm == "kmeans":
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(vectors_array)
        elif algorithm == "dbscan":
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            cluster_labels = dbscan.fit_predict(vectors_array)
        else:
            return {"error": f"Unknown clustering algorithm: {algorithm}"}
            
        # Prepare results
        results = {
            "algorithm": algorithm,
            "n_clusters": n_clusters if algorithm == "kmeans" else len(set(cluster_labels)) - (1 if -1 in cluster_labels else 0),
            "n_vectors": len(vectors),
            "table": table_name
        }
        
        # Update metadata with cluster assignments
        cluster_counts = {}
        
        for vector_id, cluster in zip(ids, cluster_labels):
            cluster_id = int(cluster)
            
            # Update metadata in database
            with conn.cursor() as cur:
                if self._has_metadata_column(conn, table_name):
                    cur.execute(f"""
                    UPDATE {table_name}
                    SET metadata = jsonb_set(
                        COALESCE(metadata, '{{}}'::jsonb),
                        '{{cluster}}',
                        %s::jsonb,
                        true
                    )
                    WHERE id = %s;
                    """, (Json(cluster_id), vector_id))
                    
                    # Update cluster counts
                    cluster_counts[cluster_id] = cluster_counts.get(cluster_id, 0) + 1
        
        conn.commit()
        
        # Add cluster counts to results
        results["cluster_counts"] = cluster_counts
        
        return results
    
    def close(self):
        """Shut down the manager."""
        # Signal workers to stop
        self.running = False
        
        # Wait for workers to finish
        for worker in self.async_workers:
            self.async_queue.put(None)  # Signal to exit
            
        for worker in self.async_workers:
            worker.join(timeout=5.0)
            
        # Close connection pool
        if hasattr(self, 'conn_pool'):
            self.conn_pool.closeall()
    
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class RealTimeVectorProcessor:
    """
    Real-time vector processing with batching and streaming.
    
    Features:
    - Efficient batch processing
    - Stream processing of vectors
    - Automatic retries and error handling
    """
    
    def __init__(self, 
                 pg_vector_manager: PgVectorManager,
                 batch_size: int = 100,
                 max_batch_latency: float = 1.0,
                 processing_interval: float = 0.5):
        """
        Initialize the real-time vector processor.
        
        Args:
            pg_vector_manager: PgVectorManager instance
            batch_size: Maximum batch size
            max_batch_latency: Maximum seconds to wait for batch completion
            processing_interval: Interval between processing in seconds
        """
        self.manager = pg_vector_manager
        self.batch_size = batch_size
        self.max_batch_latency = max_batch_latency
        self.processing_interval = processing_interval
        
        # Batch processing queues
        self.batch_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # Processing state
        self.running = True
        self.current_batch = []
        self.batch_start_time = None
        
        # Start processing thread
        self.processor_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processor_thread.start()
        
        logger.info("RealTimeVectorProcessor initialized")
    
    def enqueue_vector(self, 
                      vector: np.ndarray,
                      metadata: Dict[str, Any] = None,
                      vector_id: str = None) -> str:
        """
        Enqueue a vector for processing.
        
        Args:
            vector: Vector to process
            metadata: Optional metadata
            vector_id: Optional vector ID
            
        Returns:
            Vector ID (generated if not provided)
        """
        # Generate ID if not provided
        if vector_id is None:
            vector_id = str(uuid.uuid4())
            
        # Ensure metadata is a dictionary
        metadata = metadata or {}
        
        # Add to queue
        self.batch_queue.put((vector_id, vector, metadata))
        
        return vector_id
    
    def enqueue_batch(self, 
                     vectors: List[np.ndarray],
                     metadata_list: List[Dict[str, Any]] = None,
                     ids: List[str] = None) -> List[str]:
        """
        Enqueue a batch of vectors.
        
        Args:
            vectors: List of vectors
            metadata_list: List of metadata dictionaries
            ids: List of vector IDs
            
        Returns:
            List of vector IDs
        """
        if not vectors:
            return []
            
        # Generate missing IDs
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(vectors))]
        elif len(ids) < len(vectors):
            # Append generated IDs for missing entries
            ids.extend([str(uuid.uuid4()) for _ in range(len(vectors) - len(ids))])
            
        # Ensure metadata list exists
        if metadata_list is None:
            metadata_list = [{} for _ in range(len(vectors))]
        elif len(metadata_list) < len(vectors):
            # Append empty metadata for missing entries
            metadata_list.extend([{} for _ in range(len(vectors) - len(metadata_list))])
            
        # Enqueue each item
        for vector_id, vector, metadata in zip(ids, vectors, metadata_list):
            self.batch_queue.put((vector_id, vector, metadata))
            
        return ids
    
    def _processing_loop(self):
        """Main processing loop."""
        while self.running:
            try:
                self._process_batch()
                time.sleep(self.processing_interval)
            except Exception as e:
                logger.error(f"Error in vector processing loop: {e}")
    
    def _process_batch(self):
        """Process a batch of vectors."""
        # Start new batch if needed
        if not self.current_batch:
            try:
                # Get first item with timeout
                item = self.batch_queue.get(timeout=0.1)
                self.current_batch.append(item)
                self.batch_start_time = time.time()
            except queue.Empty:
                # No items to process
                return
                
        # Try to fill batch until max size or timeout
        while len(self.current_batch) < self.batch_size:
            # Check if max latency is reached
            if time.time() - self.batch_start_time > self.max_batch_latency:
                break
                
            # Try to get next item without blocking
            try:
                item = self.batch_queue.get_nowait()
                self.current_batch.append(item)
                self.batch_queue.task_done()
            except queue.Empty:
                # No more items available right now
                break
                
        # Process batch if not empty
        if self.current_batch:
            try:
                # Split batch into components
                ids, vectors, metadata = zip(*self.current_batch)
                
                # Convert vectors to numpy arrays if needed
                vectors = [v if isinstance(v, np.ndarray) else np.array(v) for v in vectors]
                
                # Process batch
                # This would typically involve database operations
                logger.debug(f"Processing batch of {len(self.current_batch)} vectors")
                
                # Reset batch
                self.current_batch = []
                
            except Exception as e:
                logger.error(f"Error processing vector batch: {e}")
                
                # Retry individual items that failed
                for item in self.current_batch:
                    self.batch_queue.put(item)
                    
                # Reset batch
                self.current_batch = []
    
    def close(self):
        """Shut down the processor."""
        self.running = False
        
        # Wait for processor thread to finish
        if self.processor_thread.is_alive():
            self.processor_thread.join(timeout=5.0)
            
        # Process any remaining items
        while not self.batch_queue.empty():
            try:
                item = self.batch_queue.get_nowait()
                self.batch_queue.task_done()
            except queue.Empty:
                break


if __name__ == "__main__":
    # Simple test of the PgVector Manager
    import random
    
    # Create vector manager
    manager = PgVectorManager()
    
    try:
        # Create test table
        test_dim = 4
        table_name = "test_pgvector"
        
        manager.create_vector_table(table_name, test_dim)
        
        # Create some test vectors
        test_vectors = [np.random.rand(test_dim) for _ in range(10)]
        test_ids = [f"test_{i}" for i in range(10)]
        test_metadata = [{"group": random.choice(["A", "B", "C"]), "value": i} for i in range(10)]
        
        # Insert vectors
        manager.upsert_vectors(table_name, test_vectors, test_ids, test_metadata)
        
        # Search for vectors
        query_vector = np.random.rand(test_dim)
        results = manager.search_vectors(table_name, query_vector, k=3)
        
        print(f"Search results: {results}")
        
        # Get table stats
        stats = manager.table_stats(table_name)
        print(f"Table stats: {stats}")
        
        # Clean up
        manager.drop_vector_table(table_name)
        
    finally:
        # Close manager
        manager.close()
