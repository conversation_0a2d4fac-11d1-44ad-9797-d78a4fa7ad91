<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test - Clear Tokens</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <nav>
                <div class="logo">Trend Crawler - Token Cleaner</div>
            </nav>
        </header>
        
        <main>
            <h1>🧹 Clear Authentication Tokens</h1>
            
            <div class="dashboard-card">
                <h2>Token Management</h2>
                <div class="card-content">
                    <p>Use this page to clear your authentication tokens and test the redirect functionality.</p>
                    
                    <div style="margin: 1rem 0;">
                        <button onclick="clearTokens()" class="btn btn-danger">Clear All Tokens</button>
                        <button onclick="showTokens()" class="btn btn-secondary">Show Current Tokens</button>
                    </div>
                    
                    <div id="token-status" style="margin-top: 1rem;"></div>
                    
                    <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.1);">
                        <h3>Test Authentication Redirect</h3>
                        <p>After clearing tokens, try visiting these protected pages:</p>
                        
                        <div style="margin: 1rem 0;">
                            <button onclick="testAccountRedirect()" class="btn btn-primary">Test Account Page Redirect</button>
                            <button onclick="testDashboardRedirect()" class="btn btn-primary">Test Dashboard Redirect</button>
                        </div>
                        
                        <div style="margin: 1rem 0;">
                            <a href="/account" target="_blank" class="btn btn-secondary" style="margin: 0.25rem;">Account Page (New Tab)</a>
                            <a href="/" target="_blank" class="btn btn-secondary" style="margin: 0.25rem;">Dashboard (New Tab)</a>
                            <a href="/user-management" target="_blank" class="btn btn-secondary" style="margin: 0.25rem;">User Management (New Tab)</a>
                        </div>
                        
                        <p><small>These should redirect to the login page with a return URL if you're not authenticated.</small></p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function clearTokens() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');
            
            const statusDiv = document.getElementById('token-status');
            statusDiv.innerHTML = '<div class="alert alert-success">✅ All tokens cleared successfully!</div>';
            
            console.log('All authentication tokens have been cleared');
        }
        
        function showTokens() {
            const accessToken = localStorage.getItem('access_token');
            const refreshToken = localStorage.getItem('refresh_token');
            const userData = localStorage.getItem('user_data');
            
            const statusDiv = document.getElementById('token-status');
            
            let html = '<div class="alert alert-info"><h4>Current Token Status:</h4>';
            html += `<p><strong>Access Token:</strong> ${accessToken ? '✅ Present (' + accessToken.substring(0, 20) + '...)' : '❌ Not found'}</p>`;
            html += `<p><strong>Refresh Token:</strong> ${refreshToken ? '✅ Present (' + refreshToken.substring(0, 20) + '...)' : '❌ Not found'}</p>`;
            html += `<p><strong>User Data:</strong> ${userData ? '✅ Present' : '❌ Not found'}</p>`;
            html += '</div>';
            
            statusDiv.innerHTML = html;
        }
        
        function testAccountRedirect() {
            console.log('Testing account page redirect...');
            window.location.href = '/account';
        }
        
        function testDashboardRedirect() {
            console.log('Testing dashboard redirect...');
            window.location.href = '/';
        }
        
        // Show current status on page load
        document.addEventListener('DOMContentLoaded', function() {
            showTokens();
        });
    </script>
</body>
</html>
