#!/usr/bin/env python3
"""
Vector embeddings module for trend-crawler

This module handles vector embeddings generation, clustering,
and similarity search functionality.
"""

import os
import logging
import json
import time
import collections
import re
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union, Set
import psycopg2
from psycopg2.extras import execute_values, DictCursor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# Import sentence transformers if available
try:
    from sentence_transformers import SentenceTransformer
    import torch
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False
    logger.warning("SentenceTransformer not available. Install with: pip install sentence-transformers")

# Import clustering algorithms if available
try:
    from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering, AffinityPropagation, OPTICS
    from sklearn.metrics.pairwise import cosine_similarity
    CLUSTERING_AVAILABLE = True
except ImportError:
    CLUSTERING_AVAILABLE = False
    logger.warning("Scikit-learn not available. Install with: pip install scikit-learn")

# Import HDBSCAN if available (optional enhanced clustering)
try:
    import hdbscan
    HDBSCAN_AVAILABLE = True
except ImportError:
    HDBSCAN_AVAILABLE = False
    logger.warning("HDBSCAN not available. Install with: pip install hdbscan for enhanced clustering")

# Default model settings
DEFAULT_MODEL_NAME = "all-MiniLM-L6-v2"  # 384-dimensional embeddings
DEFAULT_EMBEDDING_DIM = 384


def generate_embeddings(
    texts: List[str],
    model_name: str = DEFAULT_MODEL_NAME,
    batch_size: int = 32,
    device: str = None,
    show_progress: bool = True
) -> np.ndarray:
    """
    Generate embeddings for a list of texts using a sentence transformer model.
    
    Args:
        texts: List of text strings to encode
        model_name: Name of the SentenceTransformer model to use
        batch_size: Batch size for encoding
        device: Device to use (None for automatic selection)
        show_progress: Whether to show progress bar
        
    Returns:
        Array of embeddings with shape (n_texts, embedding_dim)
    """
    if not EMBEDDINGS_AVAILABLE:
        logger.error("SentenceTransformer not available. Cannot generate embeddings.")
        return None
    
    try:
        # Auto-detect device if not specified
        if device is None:
            device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize model
        model = SentenceTransformer(model_name, device=device)
        logger.info(f"Using {model_name} model on {device} for embeddings generation")
        
        # Generate embeddings
        start_time = time.time()
        embeddings = model.encode(
            texts,
            batch_size=batch_size,
            show_progress_bar=show_progress,
            convert_to_numpy=True
        )
        duration = time.time() - start_time
        
        # Log info about embeddings
        embedding_dim = embeddings.shape[1] if embeddings.shape[0] > 0 else 0
        logger.info(f"Generated {len(embeddings)} embeddings with dimension {embedding_dim} in {duration:.2f} seconds")
        
        return embeddings
        
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        return None


def cluster_embeddings(
    embeddings: np.ndarray,
    algorithm: str = "hdbscan",
    n_clusters: int = 5,
    **kwargs
) -> Tuple[List[int], Dict[str, Any]]:
    """
    Perform clustering on embeddings using the specified algorithm.
    
    Args:
        embeddings: Vector embeddings to cluster (n_samples, embedding_dim)
        algorithm: Clustering algorithm name ('kmeans', 'dbscan', 'hdbscan', 'agglomerative', 'affinity', 'optics')
        n_clusters: Number of clusters for algorithms that require it (like k-means)
        **kwargs: Additional parameters for clustering algorithms
        
    Returns:
        Tuple of (cluster_assignments, metadata_dict)
    """
    if not CLUSTERING_AVAILABLE:
        logger.error("Required clustering libraries not available")
        return [], {}
    
    if embeddings is None or len(embeddings) == 0:
        logger.error("No embeddings provided for clustering")
        return [], {}
        
    try:
        # Default results
        cluster_assignments = []
        metadata = {}
        
        if algorithm == "kmeans":
            km = KMeans(n_clusters=n_clusters, random_state=42, **kwargs)
            cluster_assignments = km.fit_predict(embeddings)
            metadata = {
                "algorithm": "kmeans",
                "n_clusters": n_clusters,
                "inertia": float(km.inertia_),
                "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                "parameters": kwargs
            }
            
        elif algorithm == "dbscan":
            # DBSCAN parameters
            eps = kwargs.get("eps", 0.5)
            min_samples = kwargs.get("min_samples", 5)
            
            db = DBSCAN(eps=eps, min_samples=min_samples, **kwargs)
            cluster_assignments = db.fit_predict(embeddings)
            
            # Count noise points (-1)
            n_noise = list(cluster_assignments).count(-1)
            n_clusters = len(set(cluster_assignments) - {-1})
            
            metadata = {
                "algorithm": "dbscan",
                "n_clusters": n_clusters,
                "n_noise": n_noise,
                "eps": eps,
                "min_samples": min_samples,
                "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                "parameters": kwargs
            }
            
        elif algorithm == "hdbscan":
            if HDBSCAN_AVAILABLE:
                # HDBSCAN parameters
                min_cluster_size = kwargs.get("min_cluster_size", 5)
                min_samples = kwargs.get("min_samples", None)
                cluster_selection_epsilon = kwargs.get("cluster_selection_epsilon", 0.0)
                
                hdb = hdbscan.HDBSCAN(
                    min_cluster_size=min_cluster_size,
                    min_samples=min_samples,
                    cluster_selection_epsilon=cluster_selection_epsilon,
                    **kwargs
                )
                cluster_assignments = hdb.fit_predict(embeddings)
                
                # Count noise points (-1)
                n_noise = list(cluster_assignments).count(-1)
                n_clusters = len(set(cluster_assignments) - {-1})
                
                metadata = {
                    "algorithm": "hdbscan",
                    "n_clusters": n_clusters,
                    "n_noise": n_noise,
                    "min_cluster_size": min_cluster_size,
                    "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                    "parameters": kwargs
                }
            else:
                # Fall back to KMeans if HDBSCAN is not available
                logger.warning("HDBSCAN not available, falling back to KMeans")
                km = KMeans(n_clusters=n_clusters, random_state=42)
                cluster_assignments = km.fit_predict(embeddings)
                metadata = {
                    "algorithm": "kmeans (fallback)",
                    "n_clusters": n_clusters,
                    "inertia": float(km.inertia_),
                    "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                    "parameters": {}
                }
                
        elif algorithm == "agglomerative":
            # Agglomerative clustering parameters
            linkage = kwargs.get("linkage", "ward")
            
            agg = AgglomerativeClustering(n_clusters=n_clusters, linkage=linkage, **kwargs)
            cluster_assignments = agg.fit_predict(embeddings)
            
            metadata = {
                "algorithm": "agglomerative",
                "n_clusters": n_clusters,
                "linkage": linkage,
                "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                "parameters": kwargs
            }
            
        elif algorithm == "affinity":
            # Affinity propagation parameters
            damping = kwargs.get("damping", 0.5)
            
            af = AffinityPropagation(damping=damping, random_state=42, **kwargs)
            cluster_assignments = af.fit_predict(embeddings)
            
            n_clusters = len(set(cluster_assignments))
            metadata = {
                "algorithm": "affinity",
                "n_clusters": n_clusters,
                "damping": damping,
                "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                "parameters": kwargs
            }
            
        elif algorithm == "optics":
            # OPTICS parameters
            min_samples = kwargs.get("min_samples", 5)
            xi = kwargs.get("xi", 0.05)
            
            op = OPTICS(min_samples=min_samples, xi=xi, **kwargs)
            cluster_assignments = op.fit_predict(embeddings)
            
            # Count noise points (-1)
            n_noise = list(cluster_assignments).count(-1)
            n_clusters = len(set(cluster_assignments) - {-1})
            
            metadata = {
                "algorithm": "optics",
                "n_clusters": n_clusters,
                "n_noise": n_noise,
                "min_samples": min_samples,
                "xi": xi,
                "cluster_sizes": dict(collections.Counter(cluster_assignments)),
                "parameters": kwargs
            }
            
        else:
            logger.error(f"Unknown clustering algorithm: {algorithm}")
            return [], {}
            
        # Log clustering results
        logger.info(f"Clustering with {algorithm}: found {metadata.get('n_clusters', 0)} clusters " +
                   f"from {len(embeddings)} embeddings")
                   
        return cluster_assignments, metadata
        
    except Exception as e:
        logger.error(f"Error clustering embeddings: {e}")
        return [], {}
        

def extract_cluster_keywords(texts: List[str], 
                           cluster_assignments: List[int], 
                           cluster_id: int, 
                           stopwords: Set[str] = None,
                           top_n: int = 10) -> List[str]:
    """
    Extract representative keywords for a specific cluster.
    
    Args:
        texts: List of text documents
        cluster_assignments: Cluster assignment for each document
        cluster_id: The cluster ID to extract keywords for
        stopwords: Set of stopwords to exclude
        top_n: Number of top keywords to extract
        
    Returns:
        List of keywords characteristic of the cluster
    """
    # Make sure cluster assignments match texts
    if len(texts) != len(cluster_assignments):
        logger.warning(f"Number of texts ({len(texts)}) doesn't match cluster assignments ({len(cluster_assignments)})")
        return []
        
    # Get texts belonging to this cluster
    cluster_texts = [texts[i] for i, c in enumerate(cluster_assignments) if c == cluster_id]
    
    if not cluster_texts:
        logger.warning(f"No texts found for cluster {cluster_id}")
        return []
        
    # Join all texts in the cluster
    combined_text = " ".join(cluster_texts)
    
    # Extract words (filtering out stopwords)
    words = re.findall(r'\b\w+\b', combined_text.lower())
    if stopwords:
        filtered_words = [word for word in words if word not in stopwords and len(word) > 2]
    else:
        filtered_words = [word for word in words if len(word) > 2]
        
    # Get most common words
    counter = collections.Counter(filtered_words)
    keywords = [word for word, _ in counter.most_common(top_n)]
    
    return keywords


def analyze_vector_similarities(embeddings: np.ndarray, cluster_assignments: List[int]) -> Dict[int, float]:
    """
    Analyze similarities within clusters.
    
    Args:
        embeddings: Vector embeddings
        cluster_assignments: Cluster assignment for each embedding
        
    Returns:
        Dictionary mapping cluster IDs to average within-cluster similarity
    """
    if not CLUSTERING_AVAILABLE:
        logger.error("Scikit-learn not available for similarity analysis")
        return {}
        
    if embeddings is None or len(embeddings) == 0 or len(cluster_assignments) == 0:
        return {}
        
    try:
        # Get unique cluster IDs (excluding noise points marked as -1)
        unique_clusters = set(cluster_assignments)
        if -1 in unique_clusters:
            unique_clusters.remove(-1)
            
        cluster_similarities = {}
        
        # Calculate average similarity within each cluster
        for cluster_id in unique_clusters:
            # Get indices of embeddings in this cluster
            indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
            
            # Skip if cluster has too few points
            if len(indices) <= 1:
                cluster_similarities[cluster_id] = 1.0  # Perfect similarity for a single point
                continue
                
            # Get embeddings for this cluster
            cluster_embeddings = embeddings[indices]
            
            # Calculate pairwise cosine similarities
            sim_matrix = cosine_similarity(cluster_embeddings)
            
            # Calculate average similarity (excluding self-similarity)
            np.fill_diagonal(sim_matrix, 0)  # Zero out self-similarities
            if sim_matrix.size > len(indices):  # Avoid division by zero
                avg_similarity = np.sum(sim_matrix) / (sim_matrix.size - len(indices))
            else:
                avg_similarity = 0.0
                
            cluster_similarities[cluster_id] = float(avg_similarity)
            
        return cluster_similarities
            
    except Exception as e:
        logger.error(f"Error analyzing cluster similarities: {e}")
        return {}


def find_similar_items(db, 
                     embedding: List[float] = None,
                     url: str = None,
                     text: str = None,
                     table: str = "coolness_data",
                     embedding_field: str = "embedding",
                     model_name: str = DEFAULT_MODEL_NAME,
                     limit: int = 5,
                     threshold: float = 0.7) -> List[Dict[str, Any]]:
    """
    Find similar items in the database using vector similarity search.
    
    Args:
        db: Database connection
        embedding: Pre-computed embedding to search with
        url: URL to find embedding for and search
        text: Text to encode and search
        table: Table name to search in
        embedding_field: Column name for embeddings
        model_name: Model name for encoding text
        limit: Maximum number of results
        threshold: Minimum similarity threshold (0-1)
        
    Returns:
        List of similar items with metadata
    """
    if not db:
        logger.error("Database connection required")
        return []
        
    # We need at least one of these parameters
    if embedding is None and url is None and text is None:
        logger.error("Must provide embedding, url, or text")
        return []
        
    try:
        cursor = db.cursor(cursor_factory=DictCursor)
        
        # Get search embedding
        search_embedding = None
        
        # Case 1: Use provided embedding
        if embedding is not None:
            search_embedding = embedding
            
        # Case 2: Look up embedding for URL
        elif url is not None:
            cursor.execute(
                f"SELECT {embedding_field} FROM {table} WHERE url = %s "
                f"ORDER BY crawl_timestamp DESC LIMIT 1",
                (url,)
            )
            result = cursor.fetchone()
            if result and result[embedding_field] is not None:
                search_embedding = result[embedding_field]
                
        # Case 3: Generate embedding from text
        elif text is not None and EMBEDDINGS_AVAILABLE:
            embeddings = generate_embeddings([text], model_name=model_name, show_progress=False)
            if embeddings is not None and len(embeddings) > 0:
                search_embedding = embeddings[0].tolist()
                
        if not search_embedding:
            logger.error("Could not obtain embedding for search")
            return []
            
        # Perform vector similarity search
        cursor.execute(f"""
            SELECT 
                url,
                coolness_score,
                c.category_name,
                1 - ({embedding_field} <=> %s::vector) AS similarity
            FROM 
                {table} cd
            LEFT JOIN 
                categories c ON cd.category_id = c.id
            WHERE 
                1 - ({embedding_field} <=> %s::vector) > %s
            ORDER BY 
                similarity DESC
            LIMIT %s
        """, (
            str(search_embedding),
            str(search_embedding),
            threshold,
            limit
        ))
        
        results = []
        for row in cursor:
            results.append({
                'url': row['url'],
                'coolness_score': row['coolness_score'],
                'category': row['category_name'],
                'similarity': row['similarity']
            })
            
        return results
        
    except Exception as e:
        logger.error(f"Error performing vector similarity search: {e}")
        return []
