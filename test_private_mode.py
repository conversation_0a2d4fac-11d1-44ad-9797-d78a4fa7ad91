#!/usr/bin/env python3
"""
Test script to verify the Trend Crawler application is correctly configured for private mode.
"""

import os
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
DEFAULT_ADMIN = "admin"  
DEFAULT_PASSWORD = "admin"

def test_registration_disabled():
    """Test that registration endpoints are disabled"""
    print("Testing registration endpoint...")
    
    # Test GET /register should be disabled
    try:
        response = requests.get(f"{BASE_URL}/register")
        if response.status_code == 404:
            print("✓ GET /register is disabled (404)")
        else:
            print(f"✗ GET /register returned status {response.status_code} (expected 404)")
    except requests.exceptions.RequestException as e:
        print(f"✗ Error accessing /register: {e}")

def test_login_page():
    """Test that login page shows correct message about registration"""
    print("Testing login page...")
    
    try:
        response = requests.get(f"{BASE_URL}/login")
        if response.status_code == 200:
            content = response.text
            if "Registration is disabled" in content and "Contact an administrator" in content:
                print("✓ Login page shows correct private mode message")
            else:
                print("✗ Login page does not show private mode message")
        else:
            print(f"✗ Login page returned status {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Error accessing login page: {e}")

def test_admin_login():
    """Test admin login functionality"""
    print("Testing admin login...")
    
    try:
        # Attempt login
        login_data = {
            "username": DEFAULT_ADMIN,
            "password": DEFAULT_PASSWORD
        }
        
        response = requests.post(f"{BASE_URL}/api/v1/auth/token", data=login_data)
        
        if response.status_code == 200:
            token_data = response.json()
            if "access_token" in token_data:
                print("✓ Admin login successful")
                return token_data["access_token"]
            else:
                print("✗ Login response missing access_token")
        else:
            print(f"✗ Admin login failed with status {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Error during admin login: {e}")
    
    return None

def test_admin_user_creation(token):
    """Test that admins can create users"""
    if not token:
        print("Skipping user creation test (no admin token)")
        return
    
    print("Testing admin user creation...")
    
    headers = {"Authorization": f"Bearer {token}"}
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>", 
        "password": "testpass123",
        "full_name": "Test User",
        "is_admin": False
    }
    
    try:
        # First try to delete user if exists
        requests.delete(f"{BASE_URL}/api/v1/users/testuser", headers=headers)
        
        # Create new user
        response = requests.post(f"{BASE_URL}/api/v1/users/", json=user_data, headers=headers)
        
        if response.status_code == 200:
            print("✓ Admin can create users")
            
            # Clean up - delete test user
            delete_response = requests.delete(f"{BASE_URL}/api/v1/users/testuser", headers=headers)
            if delete_response.status_code == 200:
                print("✓ Test user cleaned up")
        else:
            print(f"✗ User creation failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Error during user creation test: {e}")

def test_admin_promotion(token):
    """Test admin promotion/demotion functionality"""
    if not token:
        print("Skipping admin promotion test (no admin token)")
        return
    
    print("Testing admin promotion functionality...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # First create a test user
    user_data = {
        "username": "promotetest",
        "email": "<EMAIL>",
        "password": "testpass123", 
        "full_name": "Promote Test User",
        "is_admin": False
    }
    
    try:
        # Clean up any existing test user
        requests.delete(f"{BASE_URL}/api/v1/users/promotetest", headers=headers)
        
        # Create test user
        create_response = requests.post(f"{BASE_URL}/api/v1/users/", json=user_data, headers=headers)
        
        if create_response.status_code != 200:
            print(f"✗ Could not create test user for promotion test: {create_response.status_code}")
            return
        
        # Test promoting user to admin
        promote_data = {"promote": True}
        promote_response = requests.post(f"{BASE_URL}/api/v1/users/promotetest/promote-admin", 
                                       data=promote_data, headers=headers)
        
        if promote_response.status_code == 200:
            result = promote_response.json()
            if "promoted to admin" in result.get("message", ""):
                print("✓ User promotion to admin works")
                
                # Test demoting user
                demote_data = {"promote": False}
                demote_response = requests.post(f"{BASE_URL}/api/v1/users/promotetest/promote-admin",
                                              data=demote_data, headers=headers)
                
                if demote_response.status_code == 200:
                    result = demote_response.json()
                    if "demoted from admin" in result.get("message", ""):
                        print("✓ User demotion from admin works")
                    else:
                        print("✗ User demotion message incorrect")
                else:
                    print(f"✗ User demotion failed: {demote_response.status_code}")
            else:
                print("✗ User promotion message incorrect")
        else:
            print(f"✗ User promotion failed: {promote_response.status_code}")
            print(f"Response: {promote_response.text}")
        
        # Clean up
        requests.delete(f"{BASE_URL}/api/v1/users/promotetest", headers=headers)
        
    except requests.exceptions.RequestException as e:
        print(f"✗ Error during admin promotion test: {e}")

def main():
    """Run all private mode tests"""
    print("=" * 50)
    print("TREND CRAWLER PRIVATE MODE TEST")
    print("=" * 50)
    print()
    
    # Test registration is disabled
    test_registration_disabled()
    print()
    
    # Test login page messaging
    test_login_page()
    print()
    
    # Test admin login
    token = test_admin_login()
    print()
    
    # Test admin functionality
    test_admin_user_creation(token)
    print()
    
    test_admin_promotion(token)
    print()
    
    print("=" * 50)
    print("PRIVATE MODE TESTING COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    main()
