#!/usr/bin/env python3
"""
Test navigation click handling by simulating browser behavior
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_navigation_access():
    """Test navigation access with and without authentication"""
    
    print("=== Testing Navigation Click Behavior ===\n")
    
    # Test routes that should require authentication
    protected_routes = ["/scrapers", "/proxies", "/captchas", "/alerts", "/system", "/account"]
    
    print("1. Testing protected routes WITHOUT authentication:")
    print("-" * 50)
    
    for route in protected_routes:
        try:
            response = requests.get(f"{BASE_URL}{route}", 
                                  headers={'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'},
                                  allow_redirects=False)
            print(f"{route}: {response.status_code} {response.reason}")
            
            if response.status_code == 307:
                location = response.headers.get('Location', 'No location header')
                print(f"  -> Redirects to: {location}")
            elif response.status_code == 401:
                print(f"  -> 401 Unauthorized (this should trigger login redirect in browser)")
                
        except Exception as e:
            print(f"{route}: ERROR - {e}")
    
    print(f"\n2. Testing with admin authentication:")
    print("-" * 50)
    
    # Login to get token
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get('access_token')
            print(f"✅ Login successful, got token")
            
            # Test protected routes with authentication
            for route in protected_routes:
                try:
                    response = requests.get(f"{BASE_URL}{route}", 
                                          headers={
                                              'Authorization': f'Bearer {token}',
                                              'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                                          },
                                          allow_redirects=False)
                    print(f"{route}: {response.status_code} {response.reason}")
                    
                    if response.status_code == 200:
                        print(f"  -> ✅ Access granted")
                    elif response.status_code == 307:
                        location = response.headers.get('Location', 'No location header')
                        print(f"  -> Redirects to: {location}")
                        
                except Exception as e:
                    print(f"{route}: ERROR - {e}")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            
    except Exception as e:
        print(f"❌ Login error: {e}")

if __name__ == "__main__":
    test_navigation_access()
