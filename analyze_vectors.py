def analyze_trends_with_vectors(
    coolness_data: Dict[str, Dict[str, Any]], 
    embeddings: np.n<PERSON><PERSON>,
    cluster_algorithm: str = "hdbscan",
    n_clusters: int = 5
) -> Dict[str, Dict[str, Any]]:
    """
    Analyze trends using vector embeddings and clustering.
    
    Args:
        coolness_data: Dictionary of coolness data
        embeddings: Document embeddings for vector analysis
        cluster_algorithm: Clustering algorithm to use
        n_clusters: Number of clusters for algorithms that need it
        
    Returns:
        Updated coolness_data dictionary with cluster information
    """
    if not VECTOR_EMBEDDINGS_AVAILABLE or not SKLEARN_AVAILABLE:
        logger.warning("Vector embeddings or sklearn not available. Can't perform vector trend analysis.")
        return coolness_data
        
    if embeddings is None or len(embeddings) == 0:
        logger.warning("No embeddings provided for vector trend analysis")
        return coolness_data
        
    try:
        # Get URLs in the same order as we have embeddings
        urls = [url for url in coolness_data.keys() if url != "__metadata__"]
        texts = [coolness_data[url].get('text', '') for url in urls]
        
        # Perform clustering on the embeddings
        logger.info(f"Clustering {len(embeddings)} documents using {cluster_algorithm}")
        cluster_assignments, cluster_metadata = cluster_embeddings(
            embeddings, 
            algorithm=cluster_algorithm,
            n_clusters=n_clusters
        )
        
        if not cluster_assignments:
            logger.error("Clustering failed")
            return coolness_data
            
        # Extract keywords for each cluster
        cluster_keywords = {}
        for cluster_id in set(cluster_assignments):
            if cluster_id == -1:  # Skip noise cluster
                continue
            keywords = extract_cluster_keywords(texts, cluster_assignments, cluster_id, stopwords=STOPWORDS)
            cluster_keywords[cluster_id] = keywords
            
        # Calculate coherence (similarity) within each cluster
        cluster_similarities = analyze_vector_similarities(embeddings, cluster_assignments)
        
        # Update coolness_data with cluster information
        for i, url in enumerate(urls):
            if i < len(cluster_assignments):
                # Assign cluster ID
                cluster_id = cluster_assignments[i]
                coolness_data[url]['vector_cluster_id'] = cluster_id
                
                # Add cluster keywords if not a noise point
                if cluster_id != -1:
                    coolness_data[url]['vector_cluster_keywords'] = cluster_keywords.get(cluster_id, [])
                    coolness_data[url]['vector_cluster_coherence'] = cluster_similarities.get(cluster_id, 0.0)
                else:
                    coolness_data[url]['vector_cluster_keywords'] = []
                    coolness_data[url]['vector_cluster_coherence'] = 0.0
                    
        # Add global metadata for the vectors and clusters
        metadata = {
            'vector_analysis': {
                'algorithm': cluster_algorithm,
                'n_clusters': len(set(cluster_assignments)) - (1 if -1 in cluster_assignments else 0),
                'n_noise': list(cluster_assignments).count(-1),
                'cluster_keywords': cluster_keywords,
                'cluster_similarities': cluster_similarities,
                'cluster_metadata': cluster_metadata
            }
        }
        
        # Store the metadata in a special key
        coolness_data['__metadata__'] = metadata
        
        logger.info(f"Vector-based trend analysis complete: {len(cluster_keywords)} clusters identified")
        return coolness_data
    except Exception as e:
        logger.error(f"Error in vector-based trend analysis: {e}")
        return coolness_data
