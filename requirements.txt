# Core requirements
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.15.2
playwright==1.40.0
pandas==2.1.3
numpy==1.26.2
pyppeteer==1.0.2
websockets==10.4  # Pinned version for pyppeteer compatibility
tqdm==4.66.1
pytest==8.3.5
pytest-asyncio==0.23.5
aiohttp==3.9.1
nest-asyncio==1.6.0

# Twitter scraping dependencies
ntscraper==0.6.0
twitter-scraper-selenium==2.0.4
snscrape==0.7.0.20230622

# TradingView scraping dependencies
tradingview-scraper==1.1.0

# Anti-scraping and protection
undetected-chromedriver==3.5.4
selenium-stealth==1.0.6
fake-useragent==1.3.0
pycryptodome==3.19.0
requests-random-user-agent==2023.5.26
bs4==0.0.1
lxml==5.0.1
pyopenssl==23.2.0
rotating-proxy-py==1.0.0

# Proxy management
proxybroker==0.3.2
python-socks==2.3.0
requests[socks]==2.31.0
proxy-checker==0.6
ProxyPool==1.2.8

# CAPTCHA solving
2captcha-python==1.2.2
anticaptchaofficial==1.0.56

# NLP and classification
transformers==4.35.2
torch==2.1.1
scikit-learn==1.3.2  # Updated to ensure compatibility
nltk==3.8.1
sentence-transformers==2.2.2

# Monitoring and metrics
prometheus-client==0.17.1
psutil==5.9.6

# Database and storage
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
pgvector==0.2.5
asyncpg==0.29.0

# Utilities
python-dotenv==1.0.0
retry==0.9.2
tenacity==8.2.3

# API and server
fastapi==0.95.1
uvicorn==0.22.0
pydantic==1.10.8

# Security
cryptography==39.0.1
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.0.1

# NanoLLM system
alembic==1.11.1

# Web dashboard requirements
fastapi
uvicorn
pydantic
python-jose[cryptography]
passlib[bcrypt]
python-multipart
psycopg2-binary
psutil
jinja2
aiofiles
