-- PostgreSQL with pgvector extension schema for trend-crawler

-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Table for raw crawled content
CREATE TABLE IF NOT EXISTS crawled_content (
    id BIGSERIAL PRIMARY KEY,
    source_platform VARCHAR(50) NOT NULL, -- e.g., 'twitter', 'reddit', 'news_api', 'webpage'
    source_item_id TEXT NOT NULL,         -- Original ID from the source platform (e.g., tweet_id)
    content_text TEXT,                    -- The main textual content for embedding
    full_content JSONB,                   -- Full raw response/data from source (e.g., entire tweet object)
    author_id TEXT,
    author_username TEXT,
    url TEXT,
    published_at TIMESTAMPTZ,
    scraped_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    language VARCHAR(10),                 -- Detected language of content_text
    -- Vector embedding of content_text
    embedding VECTOR(384), -- Using 384 dimensions for all-MiniLM-L6-v2
    -- Other metadata
    engagement_metrics JSONB,             -- e.g., {'likes': 100, 'retweets': 50, 'views': 1000}
    keywords TEXT[],                      -- Extracted keywords (e.g., hashtags, named entities)
    sentiment_score REAL,                 -- e.g., -1.0 to 1.0
    sentiment_label VARCHAR(20),          -- e.g., 'positive', 'negative', 'neutral'
    status VARCHAR(50) DEFAULT 'pending_embedding', -- 'pending_embedding', 'embedding_complete', 'failed'
    model_version VARCHAR(50),            -- e.g., 'minilm_l6_v2', to track which model created the embedding
    embedding_generated_at TIMESTAMPTZ
);

-- Unique constraint to prevent duplicate entries from the same source
ALTER TABLE crawled_content ADD CONSTRAINT unique_source_item UNIQUE (source_platform, source_item_id);

-- Table for identified trends (clusters or themes)
CREATE TABLE IF NOT EXISTS trends (
    id SERIAL PRIMARY KEY,
    name TEXT,                            -- Human-readable name/summary of the trend
    description TEXT,
    -- Representative embedding for the trend (e.g., centroid of item embeddings)
    trend_embedding VECTOR(384),
    first_seen_item_id BIGINT REFERENCES crawled_content(id),
    first_seen_at TIMESTAMPTZ,
    last_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'emerging', -- 'emerging', 'growing', 'peaking', 'fading', 'archived'
    metadata JSONB                        -- e.g., {'peak_velocity': 100 items/hr, 'dominant_sentiment': 'positive'}
);

-- Junction table to link content items to trends (many-to-many)
CREATE TABLE IF NOT EXISTS trend_content_map (
    trend_id INTEGER REFERENCES trends(id) ON DELETE CASCADE,
    content_id BIGINT REFERENCES crawled_content(id) ON DELETE CASCADE,
    similarity_to_trend REAL,             -- Cosine similarity or other distance metric
    assigned_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (trend_id, content_id)
);

-- Indexes for performance
-- HNSW index for fast similarity search (better speed/recall tradeoff for ANN)
CREATE INDEX idx_crawled_content_embedding_hnsw ON crawled_content USING hnsw (embedding vector_cosine_ops);

-- Standard B-tree indexes for filtering and sorting
CREATE INDEX idx_crawled_content_published_at ON crawled_content (published_at DESC);
CREATE INDEX idx_crawled_content_scraped_at ON crawled_content (scraped_at DESC);
CREATE INDEX idx_crawled_content_source_platform ON crawled_content (source_platform);
CREATE INDEX idx_crawled_content_language ON crawled_content (language);
CREATE INDEX idx_crawled_content_status ON crawled_content (status);
CREATE INDEX idx_trends_last_updated_at ON trends (last_updated_at DESC);
CREATE INDEX idx_trend_content_map_content_id ON trend_content_map (content_id);

-- Full-text search index on content_text
ALTER TABLE crawled_content ADD COLUMN content_text_tsv TSVECTOR;
CREATE INDEX idx_crawled_content_tsv ON crawled_content USING GIN(content_text_tsv);

-- Function to update tsvector column
CREATE OR REPLACE FUNCTION update_content_text_tsv()
RETURNS TRIGGER AS $$
BEGIN
    NEW.content_text_tsv := to_tsvector('english', COALESCE(NEW.content_text, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update tsvector column
CREATE TRIGGER trg_update_content_text_tsv
BEFORE INSERT OR UPDATE ON crawled_content
FOR EACH ROW EXECUTE FUNCTION update_content_text_tsv();
