#!/usr/bin/env python3
"""
Test script for ML-based CAPTCHA solvers.

This script demonstrates how to use the ML-based CAPTCHA solvers
integrated with the captcha_solver module.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import captcha solver
from captcha_solver import CaptchaSolver, ML_SOLVERS_AVAILABLE, PYTORCH_AVAILABLE, TENSORFLOW_AVAILABLE

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test ML-based CAPTCHA solvers')
    parser.add_argument('--image', type=str, help='Path to CAPTCHA image file')
    parser.add_argument('--model', type=str, help='Path to ML model file')
    parser.add_argument('--solver', type=str, choices=['pytorch', 'tensorflow', 'auto'], 
                        default='auto', help='Solver to use')
    parser.add_argument('--api-key', type=str, help='API key for external service (fallback)')
    parser.add_argument('--service', type=str, choices=['2captcha', 'anticaptcha', 'capsolver'], 
                        help='External service to use (fallback)')
    parser.add_argument('--download-samples', action='store_true', 
                        help='Download sample CAPTCHA images for testing')
    return parser.parse_args()

def download_sample_captchas(output_dir: str = 'sample_captchas'):
    """
    Download sample CAPTCHA images for testing.
    
    Args:
        output_dir: Directory to save sample images
    """
    import requests
    from tqdm import tqdm
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Sample CAPTCHA URLs
    sample_urls = [
        'https://raw.githubusercontent.com/JackonYang/captcha-tensorflow-pytorch/master/images/0.jpg',
        'https://raw.githubusercontent.com/JackonYang/captcha-tensorflow-pytorch/master/images/1.jpg',
        'https://raw.githubusercontent.com/JackonYang/captcha-tensorflow-pytorch/master/images/2.jpg',
        'https://raw.githubusercontent.com/JackonYang/captcha-tensorflow-pytorch/master/images/3.jpg',
        'https://raw.githubusercontent.com/JackonYang/captcha-tensorflow-pytorch/master/images/4.jpg',
    ]
    
    logger.info(f"Downloading {len(sample_urls)} sample CAPTCHA images to {output_path}")
    
    for i, url in enumerate(tqdm(sample_urls)):
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # Save image
            output_file = output_path / f"sample_{i}.jpg"
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.debug(f"Downloaded {url} to {output_file}")
        except Exception as e:
            logger.error(f"Error downloading {url}: {e}")
    
    logger.info(f"Downloaded {len(sample_urls)} sample CAPTCHA images to {output_path}")
    return output_path

def test_solver(image_path: str, model_path: Optional[str] = None, 
                solver_type: str = 'auto', api_key: Optional[str] = None,
                service: Optional[str] = None) -> Dict[str, Any]:
    """
    Test CAPTCHA solver with the given image.
    
    Args:
        image_path: Path to CAPTCHA image file
        model_path: Path to ML model file
        solver_type: Type of solver to use
        api_key: API key for external service
        service: External service to use
        
    Returns:
        Dictionary with test results
    """
    results = {
        'image_path': image_path,
        'solver_type': solver_type,
        'model_path': model_path,
        'solution': None,
        'success': False,
        'error': None,
        'elapsed_time': 0.0
    }
    
    try:
        # Check if image exists
        if not os.path.exists(image_path):
            results['error'] = f"Image file not found: {image_path}"
            return results
        
        # Initialize solver
        solver = CaptchaSolver(
            api_key=api_key,
            service=service or solver_type,
            ml_model_path=model_path,
            use_local_ml=True
        )
        
        # Solve CAPTCHA
        solution = solver.solve_image_captcha(image_path=image_path)
        
        # Update results
        results['solution'] = solution
        results['success'] = solution is not None
        results['elapsed_time'] = solver.metrics['avg_solution_time']
        
        # Get service used
        if solution and solver.metrics['by_service']:
            for service_name, metrics in solver.metrics['by_service'].items():
                if metrics['successes'] > 0:
                    results['solver_used'] = service_name
                    break
        
    except Exception as e:
        results['error'] = str(e)
        logger.error(f"Error testing solver: {e}")
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Check if ML solvers are available
    if not ML_SOLVERS_AVAILABLE:
        logger.warning("ML-based CAPTCHA solvers are not available. Please install the required dependencies.")
        if args.solver in ['pytorch', 'tensorflow']:
            logger.error(f"{args.solver} solver requested but not available.")
            sys.exit(1)
    
    # Download sample captchas if requested
    if args.download_samples:
        sample_dir = download_sample_captchas()
        if not args.image:
            # Use the first sample image
            sample_images = list(sample_dir.glob('*.jpg'))
            if sample_images:
                args.image = str(sample_images[0])
                logger.info(f"Using sample image: {args.image}")
    
    # Check if image is provided
    if not args.image:
        logger.error("No image provided. Use --image or --download-samples.")
        sys.exit(1)
    
    # Test solver
    results = test_solver(
        image_path=args.image,
        model_path=args.model,
        solver_type=args.solver,
        api_key=args.api_key,
        service=args.service
    )
    
    # Print results
    print("\n=== CAPTCHA Solver Test Results ===")
    print(f"Image: {results['image_path']}")
    print(f"Solver Type: {results['solver_type']}")
    if results['model_path']:
        print(f"Model Path: {results['model_path']}")
    
    if results['success']:
        print(f"Solution: {results['solution']}")
        print(f"Solver Used: {results.get('solver_used', 'unknown')}")
        print(f"Elapsed Time: {results['elapsed_time']:.2f} seconds")
    else:
        print(f"Error: {results['error'] or 'Failed to solve CAPTCHA'}")
    
    print("=====================================\n")
    
    return 0 if results['success'] else 1

if __name__ == '__main__':
    sys.exit(main())
