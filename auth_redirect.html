<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting - Trend Crawler</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        .redirect-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }
        .redirect-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-xl);
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 500px;
        }
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 1rem;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="redirect-card">
            <div class="spinner"></div>
            <h2>Authentication Required</h2>
            <p>Checking your authentication status...</p>
            <p id="redirect-message">Please wait while we redirect you to the login page.</p>
        </div>
    </div>

    <script src="static/js/dashboard.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const messageElement = document.getElementById('redirect-message');
            
            // Simulate authentication check
            setTimeout(() => {
                messageElement.textContent = 'Redirecting to login...';
                setTimeout(() => {
                    window.location.href = '/login';
                }, 1000);
            }, 1500);
        });
    </script>
</body>
</html>
